"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_motion_f"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/Feature.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Feature: function() { return /* binding */ Feature; }\n/* harmony export */ });\nclass Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL0ZlYXR1cmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFbUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9tb3Rpb24vZmVhdHVyZXMvRmVhdHVyZS5tanM/MWFkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJjbGFzcyBGZWF0dXJlIHtcbiAgICBjb25zdHJ1Y3Rvcihub2RlKSB7XG4gICAgICAgIHRoaXMuaXNNb3VudGVkID0gZmFsc2U7XG4gICAgICAgIHRoaXMubm9kZSA9IG5vZGU7XG4gICAgfVxuICAgIHVwZGF0ZSgpIHsgfVxufVxuXG5leHBvcnQgeyBGZWF0dXJlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExitAnimationFeature: function() { return /* binding */ ExitAnimationFeature; }\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\nlet id = 0;\nclass ExitAnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete, custom } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent, { custom: custom !== null && custom !== void 0 ? custom : this.node.getProps().custom });\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => onExitComplete(this.id));\n        }\n    }\n    mount() {\n        const { register } = this.node.presenceContext || {};\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/animation/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationFeature: function() { return /* binding */ AnimationFeature; }\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../animation/utils/is-animation-controls.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../render/utils/animation-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/animation-state.mjs\");\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nclass AnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = (0,_render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__.createAnimationState)(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        this.unmount();\n        if ((0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__.isAnimationControls)(animate)) {\n            this.unmount = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animations.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/animations.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: function() { return /* binding */ animations; }\n/* harmony export */ });\n/* harmony import */ var _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\");\n/* harmony import */ var _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/exit.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\");\n\n\n\nconst animations = {\n    animation: {\n        Feature: _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__.AnimationFeature,\n    },\n    exit: {\n        Feature: _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__.ExitAnimationFeature,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2FuaW1hdGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5RDtBQUNHOztBQUU1RDtBQUNBO0FBQ0EsaUJBQWlCLGtFQUFnQjtBQUNqQyxLQUFLO0FBQ0w7QUFDQSxpQkFBaUIscUVBQW9CO0FBQ3JDLEtBQUs7QUFDTDs7QUFFc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9tb3Rpb24vZmVhdHVyZXMvYW5pbWF0aW9ucy5tanM/ZTQ1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBbmltYXRpb25GZWF0dXJlIH0gZnJvbSAnLi9hbmltYXRpb24vaW5kZXgubWpzJztcbmltcG9ydCB7IEV4aXRBbmltYXRpb25GZWF0dXJlIH0gZnJvbSAnLi9hbmltYXRpb24vZXhpdC5tanMnO1xuXG5jb25zdCBhbmltYXRpb25zID0ge1xuICAgIGFuaW1hdGlvbjoge1xuICAgICAgICBGZWF0dXJlOiBBbmltYXRpb25GZWF0dXJlLFxuICAgIH0sXG4gICAgZXhpdDoge1xuICAgICAgICBGZWF0dXJlOiBFeGl0QW5pbWF0aW9uRmVhdHVyZSxcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgYW5pbWF0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animations.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/definitions.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   featureDefinitions: function() { return /* binding */ featureDefinitions; }\n/* harmony export */ });\nconst featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2RlZmluaXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9tb3Rpb24vZmVhdHVyZXMvZGVmaW5pdGlvbnMubWpzPzYyNDEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZmVhdHVyZVByb3BzID0ge1xuICAgIGFuaW1hdGlvbjogW1xuICAgICAgICBcImFuaW1hdGVcIixcbiAgICAgICAgXCJ2YXJpYW50c1wiLFxuICAgICAgICBcIndoaWxlSG92ZXJcIixcbiAgICAgICAgXCJ3aGlsZVRhcFwiLFxuICAgICAgICBcImV4aXRcIixcbiAgICAgICAgXCJ3aGlsZUluVmlld1wiLFxuICAgICAgICBcIndoaWxlRm9jdXNcIixcbiAgICAgICAgXCJ3aGlsZURyYWdcIixcbiAgICBdLFxuICAgIGV4aXQ6IFtcImV4aXRcIl0sXG4gICAgZHJhZzogW1wiZHJhZ1wiLCBcImRyYWdDb250cm9sc1wiXSxcbiAgICBmb2N1czogW1wid2hpbGVGb2N1c1wiXSxcbiAgICBob3ZlcjogW1wid2hpbGVIb3ZlclwiLCBcIm9uSG92ZXJTdGFydFwiLCBcIm9uSG92ZXJFbmRcIl0sXG4gICAgdGFwOiBbXCJ3aGlsZVRhcFwiLCBcIm9uVGFwXCIsIFwib25UYXBTdGFydFwiLCBcIm9uVGFwQ2FuY2VsXCJdLFxuICAgIHBhbjogW1wib25QYW5cIiwgXCJvblBhblN0YXJ0XCIsIFwib25QYW5TZXNzaW9uU3RhcnRcIiwgXCJvblBhbkVuZFwiXSxcbiAgICBpblZpZXc6IFtcIndoaWxlSW5WaWV3XCIsIFwib25WaWV3cG9ydEVudGVyXCIsIFwib25WaWV3cG9ydExlYXZlXCJdLFxuICAgIGxheW91dDogW1wibGF5b3V0XCIsIFwibGF5b3V0SWRcIl0sXG59O1xuY29uc3QgZmVhdHVyZURlZmluaXRpb25zID0ge307XG5mb3IgKGNvbnN0IGtleSBpbiBmZWF0dXJlUHJvcHMpIHtcbiAgICBmZWF0dXJlRGVmaW5pdGlvbnNba2V5XSA9IHtcbiAgICAgICAgaXNFbmFibGVkOiAocHJvcHMpID0+IGZlYXR1cmVQcm9wc1trZXldLnNvbWUoKG5hbWUpID0+ICEhcHJvcHNbbmFtZV0pLFxuICAgIH07XG59XG5cbmV4cG9ydCB7IGZlYXR1cmVEZWZpbml0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/drag.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/drag.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drag: function() { return /* binding */ drag; }\n/* harmony export */ });\n/* harmony import */ var _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/drag/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/index.mjs\");\n/* harmony import */ var _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../gestures/pan/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/index.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n\n\n\n\n\nconst drag = {\n    pan: {\n        Feature: _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__.PanGesture,\n    },\n    drag: {\n        Feature: _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__.DragGesture,\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2RyYWcubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTREO0FBQ0Y7QUFDQztBQUN1Qjs7QUFFbEY7QUFDQTtBQUNBLGlCQUFpQiwrREFBVTtBQUMzQixLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsaUVBQVc7QUFDNUIsd0JBQXdCLHVGQUFrQjtBQUMxQyxxQkFBcUI7QUFDckIsS0FBSztBQUNMOztBQUVnQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9kcmFnLm1qcz9iYTMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERyYWdHZXN0dXJlIH0gZnJvbSAnLi4vLi4vZ2VzdHVyZXMvZHJhZy9pbmRleC5tanMnO1xuaW1wb3J0IHsgUGFuR2VzdHVyZSB9IGZyb20gJy4uLy4uL2dlc3R1cmVzL3Bhbi9pbmRleC5tanMnO1xuaW1wb3J0IHsgTWVhc3VyZUxheW91dCB9IGZyb20gJy4vbGF5b3V0L01lYXN1cmVMYXlvdXQubWpzJztcbmltcG9ydCB7IEhUTUxQcm9qZWN0aW9uTm9kZSB9IGZyb20gJy4uLy4uL3Byb2plY3Rpb24vbm9kZS9IVE1MUHJvamVjdGlvbk5vZGUubWpzJztcblxuY29uc3QgZHJhZyA9IHtcbiAgICBwYW46IHtcbiAgICAgICAgRmVhdHVyZTogUGFuR2VzdHVyZSxcbiAgICB9LFxuICAgIGRyYWc6IHtcbiAgICAgICAgRmVhdHVyZTogRHJhZ0dlc3R1cmUsXG4gICAgICAgIFByb2plY3Rpb25Ob2RlOiBIVE1MUHJvamVjdGlvbk5vZGUsXG4gICAgICAgIE1lYXN1cmVMYXlvdXQsXG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGRyYWcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/drag.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/gestures.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/gestures.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gestureAnimations: function() { return /* binding */ gestureAnimations; }\n/* harmony export */ });\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../gestures/hover.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../gestures/focus.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/focus.mjs\");\n/* harmony import */ var _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/press.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/press.mjs\");\n/* harmony import */ var _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\");\n\n\n\n\n\nconst gestureAnimations = {\n    inView: {\n        Feature: _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__.InViewFeature,\n    },\n    tap: {\n        Feature: _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__.PressGesture,\n    },\n    focus: {\n        Feature: _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__.FocusGesture,\n    },\n    hover: {\n        Feature: _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__.HoverGesture,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2dlc3R1cmVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNBO0FBQ0E7QUFDSDs7QUFFckQ7QUFDQTtBQUNBLGlCQUFpQiw4REFBYTtBQUM5QixLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsNkRBQVk7QUFDN0IsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLDZEQUFZO0FBQzdCLEtBQUs7QUFDTDtBQUNBLGlCQUFpQiw2REFBWTtBQUM3QixLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2dlc3R1cmVzLm1qcz82NTU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEhvdmVyR2VzdHVyZSB9IGZyb20gJy4uLy4uL2dlc3R1cmVzL2hvdmVyLm1qcyc7XG5pbXBvcnQgeyBGb2N1c0dlc3R1cmUgfSBmcm9tICcuLi8uLi9nZXN0dXJlcy9mb2N1cy5tanMnO1xuaW1wb3J0IHsgUHJlc3NHZXN0dXJlIH0gZnJvbSAnLi4vLi4vZ2VzdHVyZXMvcHJlc3MubWpzJztcbmltcG9ydCB7IEluVmlld0ZlYXR1cmUgfSBmcm9tICcuL3ZpZXdwb3J0L2luZGV4Lm1qcyc7XG5cbmNvbnN0IGdlc3R1cmVBbmltYXRpb25zID0ge1xuICAgIGluVmlldzoge1xuICAgICAgICBGZWF0dXJlOiBJblZpZXdGZWF0dXJlLFxuICAgIH0sXG4gICAgdGFwOiB7XG4gICAgICAgIEZlYXR1cmU6IFByZXNzR2VzdHVyZSxcbiAgICB9LFxuICAgIGZvY3VzOiB7XG4gICAgICAgIEZlYXR1cmU6IEZvY3VzR2VzdHVyZSxcbiAgICB9LFxuICAgIGhvdmVyOiB7XG4gICAgICAgIEZlYXR1cmU6IEhvdmVyR2VzdHVyZSxcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgZ2VzdHVyZUFuaW1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/gestures.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/layout.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   layout: function() { return /* binding */ layout; }\n/* harmony export */ });\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n\n\n\nconst layout = {\n    layout: {\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xheW91dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtGO0FBQ3ZCOztBQUUzRDtBQUNBO0FBQ0Esd0JBQXdCLHVGQUFrQjtBQUMxQyxxQkFBcUI7QUFDckIsS0FBSztBQUNMOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9sYXlvdXQubWpzPzdlMzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSFRNTFByb2plY3Rpb25Ob2RlIH0gZnJvbSAnLi4vLi4vcHJvamVjdGlvbi9ub2RlL0hUTUxQcm9qZWN0aW9uTm9kZS5tanMnO1xuaW1wb3J0IHsgTWVhc3VyZUxheW91dCB9IGZyb20gJy4vbGF5b3V0L01lYXN1cmVMYXlvdXQubWpzJztcblxuY29uc3QgbGF5b3V0ID0ge1xuICAgIGxheW91dDoge1xuICAgICAgICBQcm9qZWN0aW9uTm9kZTogSFRNTFByb2plY3Rpb25Ob2RlLFxuICAgICAgICBNZWFzdXJlTGF5b3V0LFxuICAgIH0sXG59O1xuXG5leHBvcnQgeyBsYXlvdXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeasureLayout: function() { return /* binding */ MeasureLayout; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/AnimatePresence/use-presence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../context/SwitchLayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../projection/node/state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/state.mjs\");\n/* harmony import */ var _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../projection/styles/scale-border-radius.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs\");\n/* harmony import */ var _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../projection/styles/scale-box-shadow.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs\");\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../projection/styles/scale-correction.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\n\nclass MeasureLayoutWithContext extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        (0,_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__.addScaleCorrector)(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            projection.root.didUpdate();\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_2__.globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const projection = visualElement.projection;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            queueMicrotask(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = (0,_components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_4__.usePresence)();\n    const layoutGroup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__.LayoutGroupContext);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ..._projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__.correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__.correctBorderRadius,\n    borderTopRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__.correctBorderRadius,\n    borderBottomLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__.correctBorderRadius,\n    borderBottomRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_7__.correctBorderRadius,\n    boxShadow: _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBoxShadow,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xheW91dC9NZWFzdXJlTGF5b3V0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQW1EO0FBQ2dDO0FBQ047QUFDWTtBQUNkO0FBQ2M7QUFDTjtBQUNDO0FBQy9COztBQUVyRCx1Q0FBdUMsNENBQXdCO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwREFBMEQ7QUFDMUUsZ0JBQWdCLGFBQWE7QUFDN0IsUUFBUSwwRkFBaUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFFBQVEsNkVBQXFCO0FBQzdCO0FBQ0E7QUFDQSxnQkFBZ0IsbURBQW1EO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsdURBQUs7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsaUVBQWlFO0FBQ2pGLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixlQUFlO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLHlGQUFXO0FBQ2pELHdCQUF3QixpREFBVSxDQUFDLCtFQUFrQjtBQUNyRCxZQUFZLGdEQUE0Qiw2QkFBNkIsdURBQXVELGlEQUFVLENBQUMsMkZBQXdCLHFEQUFxRDtBQUNwTjtBQUNBO0FBQ0E7QUFDQSxXQUFXLDJGQUFtQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wseUJBQXlCLDJGQUFtQjtBQUM1QywwQkFBMEIsMkZBQW1CO0FBQzdDLDRCQUE0QiwyRkFBbUI7QUFDL0MsNkJBQTZCLDJGQUFtQjtBQUNoRCxlQUFlLHFGQUFnQjtBQUMvQjs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9tb3Rpb24vZmVhdHVyZXMvbGF5b3V0L01lYXN1cmVMYXlvdXQubWpzPzhkMTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0X19kZWZhdWx0LCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQcmVzZW5jZSB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvQW5pbWF0ZVByZXNlbmNlL3VzZS1wcmVzZW5jZS5tanMnO1xuaW1wb3J0IHsgTGF5b3V0R3JvdXBDb250ZXh0IH0gZnJvbSAnLi4vLi4vLi4vY29udGV4dC9MYXlvdXRHcm91cENvbnRleHQubWpzJztcbmltcG9ydCB7IFN3aXRjaExheW91dEdyb3VwQ29udGV4dCB9IGZyb20gJy4uLy4uLy4uL2NvbnRleHQvU3dpdGNoTGF5b3V0R3JvdXBDb250ZXh0Lm1qcyc7XG5pbXBvcnQgeyBnbG9iYWxQcm9qZWN0aW9uU3RhdGUgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL25vZGUvc3RhdGUubWpzJztcbmltcG9ydCB7IGNvcnJlY3RCb3JkZXJSYWRpdXMgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL3N0eWxlcy9zY2FsZS1ib3JkZXItcmFkaXVzLm1qcyc7XG5pbXBvcnQgeyBjb3JyZWN0Qm94U2hhZG93IH0gZnJvbSAnLi4vLi4vLi4vcHJvamVjdGlvbi9zdHlsZXMvc2NhbGUtYm94LXNoYWRvdy5tanMnO1xuaW1wb3J0IHsgYWRkU2NhbGVDb3JyZWN0b3IgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL3N0eWxlcy9zY2FsZS1jb3JyZWN0aW9uLm1qcyc7XG5pbXBvcnQgeyBmcmFtZSB9IGZyb20gJy4uLy4uLy4uL2ZyYW1lbG9vcC9mcmFtZS5tanMnO1xuXG5jbGFzcyBNZWFzdXJlTGF5b3V0V2l0aENvbnRleHQgZXh0ZW5kcyBSZWFjdF9fZGVmYXVsdC5Db21wb25lbnQge1xuICAgIC8qKlxuICAgICAqIFRoaXMgb25seSBtb3VudHMgcHJvamVjdGlvbiBub2RlcyBmb3IgY29tcG9uZW50cyB0aGF0XG4gICAgICogbmVlZCBtZWFzdXJpbmcsIHdlIG1pZ2h0IHdhbnQgdG8gZG8gaXQgZm9yIGFsbCBjb21wb25lbnRzXG4gICAgICogaW4gb3JkZXIgdG8gaW5jb3Jwb3JhdGUgdHJhbnNmb3Jtc1xuICAgICAqL1xuICAgIGNvbXBvbmVudERpZE1vdW50KCkge1xuICAgICAgICBjb25zdCB7IHZpc3VhbEVsZW1lbnQsIGxheW91dEdyb3VwLCBzd2l0Y2hMYXlvdXRHcm91cCwgbGF5b3V0SWQgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIGNvbnN0IHsgcHJvamVjdGlvbiB9ID0gdmlzdWFsRWxlbWVudDtcbiAgICAgICAgYWRkU2NhbGVDb3JyZWN0b3IoZGVmYXVsdFNjYWxlQ29ycmVjdG9ycyk7XG4gICAgICAgIGlmIChwcm9qZWN0aW9uKSB7XG4gICAgICAgICAgICBpZiAobGF5b3V0R3JvdXAuZ3JvdXApXG4gICAgICAgICAgICAgICAgbGF5b3V0R3JvdXAuZ3JvdXAuYWRkKHByb2plY3Rpb24pO1xuICAgICAgICAgICAgaWYgKHN3aXRjaExheW91dEdyb3VwICYmIHN3aXRjaExheW91dEdyb3VwLnJlZ2lzdGVyICYmIGxheW91dElkKSB7XG4gICAgICAgICAgICAgICAgc3dpdGNoTGF5b3V0R3JvdXAucmVnaXN0ZXIocHJvamVjdGlvbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwcm9qZWN0aW9uLnJvb3QuZGlkVXBkYXRlKCk7XG4gICAgICAgICAgICBwcm9qZWN0aW9uLmFkZEV2ZW50TGlzdGVuZXIoXCJhbmltYXRpb25Db21wbGV0ZVwiLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zYWZlVG9SZW1vdmUoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcHJvamVjdGlvbi5zZXRPcHRpb25zKHtcbiAgICAgICAgICAgICAgICAuLi5wcm9qZWN0aW9uLm9wdGlvbnMsXG4gICAgICAgICAgICAgICAgb25FeGl0Q29tcGxldGU6ICgpID0+IHRoaXMuc2FmZVRvUmVtb3ZlKCksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBnbG9iYWxQcm9qZWN0aW9uU3RhdGUuaGFzRXZlclVwZGF0ZWQgPSB0cnVlO1xuICAgIH1cbiAgICBnZXRTbmFwc2hvdEJlZm9yZVVwZGF0ZShwcmV2UHJvcHMpIHtcbiAgICAgICAgY29uc3QgeyBsYXlvdXREZXBlbmRlbmN5LCB2aXN1YWxFbGVtZW50LCBkcmFnLCBpc1ByZXNlbnQgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIGNvbnN0IHByb2plY3Rpb24gPSB2aXN1YWxFbGVtZW50LnByb2plY3Rpb247XG4gICAgICAgIGlmICghcHJvamVjdGlvbilcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAvKipcbiAgICAgICAgICogVE9ETzogV2UgdXNlIHRoaXMgZGF0YSBpbiByZWxlZ2F0ZSB0byBkZXRlcm1pbmUgd2hldGhlciB0b1xuICAgICAgICAgKiBwcm9tb3RlIGEgcHJldmlvdXMgZWxlbWVudC4gVGhlcmUncyBubyBndWFyYW50ZWUgaXRzIHByZXNlbmNlIGRhdGFcbiAgICAgICAgICogd2lsbCBoYXZlIHVwZGF0ZWQgYnkgdGhpcyBwb2ludCAtIGlmIGEgYnVnIGxpa2UgdGhpcyBhcmlzZXMgaXQgd2lsbFxuICAgICAgICAgKiBoYXZlIHRvIGJlIHRoYXQgd2UgbWFya0ZvclJlbGVnYXRpb24gYW5kIHRoZW4gZmluZCBhIG5ldyBsZWFkIHNvbWUgb3RoZXIgd2F5LFxuICAgICAgICAgKiBwZXJoYXBzIGluIGRpZFVwZGF0ZVxuICAgICAgICAgKi9cbiAgICAgICAgcHJvamVjdGlvbi5pc1ByZXNlbnQgPSBpc1ByZXNlbnQ7XG4gICAgICAgIGlmIChkcmFnIHx8XG4gICAgICAgICAgICBwcmV2UHJvcHMubGF5b3V0RGVwZW5kZW5jeSAhPT0gbGF5b3V0RGVwZW5kZW5jeSB8fFxuICAgICAgICAgICAgbGF5b3V0RGVwZW5kZW5jeSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBwcm9qZWN0aW9uLndpbGxVcGRhdGUoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc2FmZVRvUmVtb3ZlKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByZXZQcm9wcy5pc1ByZXNlbnQgIT09IGlzUHJlc2VudCkge1xuICAgICAgICAgICAgaWYgKGlzUHJlc2VudCkge1xuICAgICAgICAgICAgICAgIHByb2plY3Rpb24ucHJvbW90ZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoIXByb2plY3Rpb24ucmVsZWdhdGUoKSkge1xuICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAqIElmIHRoZXJlJ3MgYW5vdGhlciBzdGFjayBtZW1iZXIgdGFraW5nIG92ZXIgZnJvbSB0aGlzIG9uZSxcbiAgICAgICAgICAgICAgICAgKiBpdCdzIGluIGNoYXJnZSBvZiB0aGUgZXhpdCBhbmltYXRpb24gYW5kIHRoZXJlZm9yZSBzaG91bGRcbiAgICAgICAgICAgICAgICAgKiBiZSBpbiBjaGFyZ2Ugb2YgdGhlIHNhZmUgdG8gcmVtb3ZlLiBPdGhlcndpc2Ugd2UgY2FsbCBpdCBoZXJlLlxuICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgIGZyYW1lLnBvc3RSZW5kZXIoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGFjayA9IHByb2plY3Rpb24uZ2V0U3RhY2soKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFzdGFjayB8fCAhc3RhY2subWVtYmVycy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2FmZVRvUmVtb3ZlKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgY29tcG9uZW50RGlkVXBkYXRlKCkge1xuICAgICAgICBjb25zdCB7IHByb2plY3Rpb24gfSA9IHRoaXMucHJvcHMudmlzdWFsRWxlbWVudDtcbiAgICAgICAgaWYgKHByb2plY3Rpb24pIHtcbiAgICAgICAgICAgIHByb2plY3Rpb24ucm9vdC5kaWRVcGRhdGUoKTtcbiAgICAgICAgICAgIHF1ZXVlTWljcm90YXNrKCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb2plY3Rpb24uY3VycmVudEFuaW1hdGlvbiAmJiBwcm9qZWN0aW9uLmlzTGVhZCgpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2FmZVRvUmVtb3ZlKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29tcG9uZW50V2lsbFVubW91bnQoKSB7XG4gICAgICAgIGNvbnN0IHsgdmlzdWFsRWxlbWVudCwgbGF5b3V0R3JvdXAsIHN3aXRjaExheW91dEdyb3VwOiBwcm9tb3RlQ29udGV4dCwgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIGNvbnN0IHsgcHJvamVjdGlvbiB9ID0gdmlzdWFsRWxlbWVudDtcbiAgICAgICAgaWYgKHByb2plY3Rpb24pIHtcbiAgICAgICAgICAgIHByb2plY3Rpb24uc2NoZWR1bGVDaGVja0FmdGVyVW5tb3VudCgpO1xuICAgICAgICAgICAgaWYgKGxheW91dEdyb3VwICYmIGxheW91dEdyb3VwLmdyb3VwKVxuICAgICAgICAgICAgICAgIGxheW91dEdyb3VwLmdyb3VwLnJlbW92ZShwcm9qZWN0aW9uKTtcbiAgICAgICAgICAgIGlmIChwcm9tb3RlQ29udGV4dCAmJiBwcm9tb3RlQ29udGV4dC5kZXJlZ2lzdGVyKVxuICAgICAgICAgICAgICAgIHByb21vdGVDb250ZXh0LmRlcmVnaXN0ZXIocHJvamVjdGlvbik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc2FmZVRvUmVtb3ZlKCkge1xuICAgICAgICBjb25zdCB7IHNhZmVUb1JlbW92ZSB9ID0gdGhpcy5wcm9wcztcbiAgICAgICAgc2FmZVRvUmVtb3ZlICYmIHNhZmVUb1JlbW92ZSgpO1xuICAgIH1cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cbmZ1bmN0aW9uIE1lYXN1cmVMYXlvdXQocHJvcHMpIHtcbiAgICBjb25zdCBbaXNQcmVzZW50LCBzYWZlVG9SZW1vdmVdID0gdXNlUHJlc2VuY2UoKTtcbiAgICBjb25zdCBsYXlvdXRHcm91cCA9IHVzZUNvbnRleHQoTGF5b3V0R3JvdXBDb250ZXh0KTtcbiAgICByZXR1cm4gKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoTWVhc3VyZUxheW91dFdpdGhDb250ZXh0LCB7IC4uLnByb3BzLCBsYXlvdXRHcm91cDogbGF5b3V0R3JvdXAsIHN3aXRjaExheW91dEdyb3VwOiB1c2VDb250ZXh0KFN3aXRjaExheW91dEdyb3VwQ29udGV4dCksIGlzUHJlc2VudDogaXNQcmVzZW50LCBzYWZlVG9SZW1vdmU6IHNhZmVUb1JlbW92ZSB9KSk7XG59XG5jb25zdCBkZWZhdWx0U2NhbGVDb3JyZWN0b3JzID0ge1xuICAgIGJvcmRlclJhZGl1czoge1xuICAgICAgICAuLi5jb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgICAgICBhcHBseVRvOiBbXG4gICAgICAgICAgICBcImJvcmRlclRvcExlZnRSYWRpdXNcIixcbiAgICAgICAgICAgIFwiYm9yZGVyVG9wUmlnaHRSYWRpdXNcIixcbiAgICAgICAgICAgIFwiYm9yZGVyQm90dG9tTGVmdFJhZGl1c1wiLFxuICAgICAgICAgICAgXCJib3JkZXJCb3R0b21SaWdodFJhZGl1c1wiLFxuICAgICAgICBdLFxuICAgIH0sXG4gICAgYm9yZGVyVG9wTGVmdFJhZGl1czogY29ycmVjdEJvcmRlclJhZGl1cyxcbiAgICBib3JkZXJUb3BSaWdodFJhZGl1czogY29ycmVjdEJvcmRlclJhZGl1cyxcbiAgICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiBjb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgIGJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzOiBjb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgIGJveFNoYWRvdzogY29ycmVjdEJveFNoYWRvdyxcbn07XG5cbmV4cG9ydCB7IE1lYXN1cmVMYXlvdXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/load-features.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadFeatures: function() { return /* binding */ loadFeatures; }\n/* harmony export */ });\n/* harmony import */ var _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./definitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key] = {\n            ..._definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xvYWQtZmVhdHVyZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVEOztBQUV2RDtBQUNBO0FBQ0EsUUFBUSxnRUFBa0I7QUFDMUIsZUFBZSxnRUFBa0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xvYWQtZmVhdHVyZXMubWpzP2IwZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZmVhdHVyZURlZmluaXRpb25zIH0gZnJvbSAnLi9kZWZpbml0aW9ucy5tanMnO1xuXG5mdW5jdGlvbiBsb2FkRmVhdHVyZXMoZmVhdHVyZXMpIHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBmZWF0dXJlcykge1xuICAgICAgICBmZWF0dXJlRGVmaW5pdGlvbnNba2V5XSA9IHtcbiAgICAgICAgICAgIC4uLmZlYXR1cmVEZWZpbml0aW9uc1trZXldLFxuICAgICAgICAgICAgLi4uZmVhdHVyZXNba2V5XSxcbiAgICAgICAgfTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGxvYWRGZWF0dXJlcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InViewFeature: function() { return /* binding */ InViewFeature; }\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _observers_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./observers.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\");\n\n\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return (0,_observers_mjs__WEBPACK_IMPORTED_MODULE_1__.observeIntersection)(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL3ZpZXdwb3J0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUM7QUFDYTs7QUFFdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsaURBQU87QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZ0JBQWdCO0FBQ2hDLGdCQUFnQixrREFBa0Q7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGlCQUFpQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1DQUFtQztBQUN2RDtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1FQUFtQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixtQkFBbUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsZUFBZSxJQUFJLDhCQUE4QixJQUFJO0FBQ3pGO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL3ZpZXdwb3J0L2luZGV4Lm1qcz85YzVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZlYXR1cmUgfSBmcm9tICcuLi9GZWF0dXJlLm1qcyc7XG5pbXBvcnQgeyBvYnNlcnZlSW50ZXJzZWN0aW9uIH0gZnJvbSAnLi9vYnNlcnZlcnMubWpzJztcblxuY29uc3QgdGhyZXNob2xkTmFtZXMgPSB7XG4gICAgc29tZTogMCxcbiAgICBhbGw6IDEsXG59O1xuY2xhc3MgSW5WaWV3RmVhdHVyZSBleHRlbmRzIEZlYXR1cmUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLmhhc0VudGVyZWRWaWV3ID0gZmFsc2U7XG4gICAgICAgIHRoaXMuaXNJblZpZXcgPSBmYWxzZTtcbiAgICB9XG4gICAgc3RhcnRPYnNlcnZlcigpIHtcbiAgICAgICAgdGhpcy51bm1vdW50KCk7XG4gICAgICAgIGNvbnN0IHsgdmlld3BvcnQgPSB7fSB9ID0gdGhpcy5ub2RlLmdldFByb3BzKCk7XG4gICAgICAgIGNvbnN0IHsgcm9vdCwgbWFyZ2luOiByb290TWFyZ2luLCBhbW91bnQgPSBcInNvbWVcIiwgb25jZSB9ID0gdmlld3BvcnQ7XG4gICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICByb290OiByb290ID8gcm9vdC5jdXJyZW50IDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgcm9vdE1hcmdpbixcbiAgICAgICAgICAgIHRocmVzaG9sZDogdHlwZW9mIGFtb3VudCA9PT0gXCJudW1iZXJcIiA/IGFtb3VudCA6IHRocmVzaG9sZE5hbWVzW2Ftb3VudF0sXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IG9uSW50ZXJzZWN0aW9uVXBkYXRlID0gKGVudHJ5KSA9PiB7XG4gICAgICAgICAgICBjb25zdCB7IGlzSW50ZXJzZWN0aW5nIH0gPSBlbnRyeTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgdGhlcmUncyBiZWVuIG5vIGNoYW5nZSBpbiB0aGUgdmlld3BvcnQgc3RhdGUsIGVhcmx5IHJldHVybi5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNJblZpZXcgPT09IGlzSW50ZXJzZWN0aW5nKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIHRoaXMuaXNJblZpZXcgPSBpc0ludGVyc2VjdGluZztcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSGFuZGxlIGhhc0VudGVyZWRWaWV3LiBJZiB0aGlzIGlzIG9ubHkgbWVhbnQgdG8gcnVuIG9uY2UsIGFuZFxuICAgICAgICAgICAgICogZWxlbWVudCBpc24ndCB2aXNpYmxlLCBlYXJseSByZXR1cm4uIE90aGVyd2lzZSBzZXQgaGFzRW50ZXJlZFZpZXcgdG8gdHJ1ZS5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKG9uY2UgJiYgIWlzSW50ZXJzZWN0aW5nICYmIHRoaXMuaGFzRW50ZXJlZFZpZXcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpc0ludGVyc2VjdGluZykge1xuICAgICAgICAgICAgICAgIHRoaXMuaGFzRW50ZXJlZFZpZXcgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMubm9kZS5hbmltYXRpb25TdGF0ZSkge1xuICAgICAgICAgICAgICAgIHRoaXMubm9kZS5hbmltYXRpb25TdGF0ZS5zZXRBY3RpdmUoXCJ3aGlsZUluVmlld1wiLCBpc0ludGVyc2VjdGluZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFVzZSB0aGUgbGF0ZXN0IGNvbW1pdHRlZCBwcm9wcyByYXRoZXIgdGhhbiB0aGUgb25lcyBpbiBzY29wZVxuICAgICAgICAgICAgICogd2hlbiB0aGlzIG9ic2VydmVyIGlzIGNyZWF0ZWRcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY29uc3QgeyBvblZpZXdwb3J0RW50ZXIsIG9uVmlld3BvcnRMZWF2ZSB9ID0gdGhpcy5ub2RlLmdldFByb3BzKCk7XG4gICAgICAgICAgICBjb25zdCBjYWxsYmFjayA9IGlzSW50ZXJzZWN0aW5nID8gb25WaWV3cG9ydEVudGVyIDogb25WaWV3cG9ydExlYXZlO1xuICAgICAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soZW50cnkpO1xuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gb2JzZXJ2ZUludGVyc2VjdGlvbih0aGlzLm5vZGUuY3VycmVudCwgb3B0aW9ucywgb25JbnRlcnNlY3Rpb25VcGRhdGUpO1xuICAgIH1cbiAgICBtb3VudCgpIHtcbiAgICAgICAgdGhpcy5zdGFydE9ic2VydmVyKCk7XG4gICAgfVxuICAgIHVwZGF0ZSgpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBJbnRlcnNlY3Rpb25PYnNlcnZlciA9PT0gXCJ1bmRlZmluZWRcIilcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3QgeyBwcm9wcywgcHJldlByb3BzIH0gPSB0aGlzLm5vZGU7XG4gICAgICAgIGNvbnN0IGhhc09wdGlvbnNDaGFuZ2VkID0gW1wiYW1vdW50XCIsIFwibWFyZ2luXCIsIFwicm9vdFwiXS5zb21lKGhhc1ZpZXdwb3J0T3B0aW9uQ2hhbmdlZChwcm9wcywgcHJldlByb3BzKSk7XG4gICAgICAgIGlmIChoYXNPcHRpb25zQ2hhbmdlZCkge1xuICAgICAgICAgICAgdGhpcy5zdGFydE9ic2VydmVyKCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgdW5tb3VudCgpIHsgfVxufVxuZnVuY3Rpb24gaGFzVmlld3BvcnRPcHRpb25DaGFuZ2VkKHsgdmlld3BvcnQgPSB7fSB9LCB7IHZpZXdwb3J0OiBwcmV2Vmlld3BvcnQgPSB7fSB9ID0ge30pIHtcbiAgICByZXR1cm4gKG5hbWUpID0+IHZpZXdwb3J0W25hbWVdICE9PSBwcmV2Vmlld3BvcnRbbmFtZV07XG59XG5cbmV4cG9ydCB7IEluVmlld0ZlYXR1cmUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeIntersection: function() { return /* binding */ observeIntersection; }\n/* harmony export */ });\n/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMotionComponent: function() { return /* binding */ createMotionComponent; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features/load-features.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/SwitchLayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_1__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_2__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_3__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_4__.isBrowser) {\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__.useVisualElement)(Component, visualState, configAndProps, createVisualElement);\n            /**\n             * Load Motion gesture and animation features. These are rendered as renderless\n             * components so each feature can optionally make use of React lifecycle methods.\n             */\n            const initialLayoutGroupConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.SwitchLayoutGroupContext);\n            const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_7__.LazyContext).strict;\n            if (context.visualElement) {\n                MeasureLayout = context.visualElement.loadFeatures(\n                // Note: Pass the full new combined props to correctly re-render dynamic feature components.\n                configAndProps, isStrict, preloadedFeatures, initialLayoutGroupConfig);\n            }\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_8__.MotionContext.Provider, { value: context },\n            MeasureLayout && context.visualElement ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null,\n            useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_9__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)));\n    }\n    const ForwardRefComponent = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(MotionComponent);\n    ForwardRefComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_10__.motionComponentSymbol] = Component;\n    return ForwardRefComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isForcedMotionValue: function() { return /* binding */ isForcedMotionValue; }\n/* harmony export */ });\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/styles/scale-correction.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n/* harmony import */ var _render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n\n\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (_render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__.scaleCorrectors[key] || key === \"opacity\")));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL2lzLWZvcmNlZC1tb3Rpb24tdmFsdWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErRTtBQUNSOztBQUV2RSxvQ0FBb0Msa0JBQWtCO0FBQ3RELFlBQVksNEVBQWM7QUFDMUI7QUFDQTtBQUNBLGVBQWUsb0ZBQWU7QUFDOUI7O0FBRStCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL2lzLWZvcmNlZC1tb3Rpb24tdmFsdWUubWpzP2U4ZTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2NhbGVDb3JyZWN0b3JzIH0gZnJvbSAnLi4vLi4vcHJvamVjdGlvbi9zdHlsZXMvc2NhbGUtY29ycmVjdGlvbi5tanMnO1xuaW1wb3J0IHsgdHJhbnNmb3JtUHJvcHMgfSBmcm9tICcuLi8uLi9yZW5kZXIvaHRtbC91dGlscy90cmFuc2Zvcm0ubWpzJztcblxuZnVuY3Rpb24gaXNGb3JjZWRNb3Rpb25WYWx1ZShrZXksIHsgbGF5b3V0LCBsYXlvdXRJZCB9KSB7XG4gICAgcmV0dXJuICh0cmFuc2Zvcm1Qcm9wcy5oYXMoa2V5KSB8fFxuICAgICAgICBrZXkuc3RhcnRzV2l0aChcIm9yaWdpblwiKSB8fFxuICAgICAgICAoKGxheW91dCB8fCBsYXlvdXRJZCAhPT0gdW5kZWZpbmVkKSAmJlxuICAgICAgICAgICAgKCEhc2NhbGVDb3JyZWN0b3JzW2tleV0gfHwga2V5ID09PSBcIm9wYWNpdHlcIikpKTtcbn1cblxuZXhwb3J0IHsgaXNGb3JjZWRNb3Rpb25WYWx1ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   motionComponentSymbol: function() { return /* binding */ motionComponentSymbol; }\n/* harmony export */ });\nconst motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL3N5bWJvbC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi91dGlscy9zeW1ib2wubWpzP2IwNTMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW90aW9uQ29tcG9uZW50U3ltYm9sID0gU3ltYm9sLmZvcihcIm1vdGlvbkNvbXBvbmVudFN5bWJvbFwiKTtcblxuZXhwb3J0IHsgbW90aW9uQ29tcG9uZW50U3ltYm9sIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionRef: function() { return /* binding */ useMotionRef; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n\n\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((instance) => {\n        instance && visualState.mount && visualState.mount(instance);\n        if (visualElement) {\n            instance\n                ? visualElement.mount(instance)\n                : visualElement.unmount();\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__.isRefObject)(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to React if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVisualElement: function() { return /* binding */ useVisualElement; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LazyContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../animation/optimized-appear/data-id.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs\");\n\n\n\n\n\n\n\n\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n    const { visualElement: parent } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const lazyContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LazyContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext);\n    const reducedMotionConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__.MotionConfigContext).reducedMotion;\n    const visualElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        visualElement && visualElement.update(props, presenceContext);\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const wantsHandoff = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Boolean(props[_animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_5__.optimizedAppearDataAttribute] && !window.HandoffComplete));\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(() => {\n        if (!visualElement)\n            return;\n        visualElement.render();\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!visualElement)\n            return;\n        visualElement.updateFeatures();\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            wantsHandoff.current = false;\n            // This ensures all future calls to animateChanges() will run in useEffect\n            window.HandoffComplete = true;\n        }\n    });\n    return visualElement;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeUseVisualState: function() { return /* binding */ makeUseVisualState; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../render/utils/resolve-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../value/utils/resolve-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../render/utils/is-controlling-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n\n\n\n\n\n\n\n\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, onMount, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    if (onMount) {\n        state.mount = (instance) => onMount(props, instance, state);\n    }\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__.PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.resolveMotionValue)(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isControllingVariants)(props);\n    const isVariantNode$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isVariantNode)(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !(0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__.isAnimationControls)(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        list.forEach((definition) => {\n            const resolved = (0,_render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__.resolveVariantFromProps)(props, definition);\n            if (!resolved)\n                return;\n            const { transitionEnd, transition, ...target } = resolved;\n            for (const key in target) {\n                let valueTarget = target[key];\n                if (Array.isArray(valueTarget)) {\n                    /**\n                     * Take final keyframe if the initial animation is blocked because\n                     * we want to initialise at the end of that blocked animation.\n                     */\n                    const index = isInitialAnimationBlocked\n                        ? valueTarget.length - 1\n                        : 0;\n                    valueTarget = valueTarget[index];\n                }\n                if (valueTarget !== null) {\n                    values[key] = valueTarget;\n                }\n            }\n            for (const key in transitionEnd)\n                values[key] = transitionEnd[key];\n        });\n    }\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidMotionProp: function() { return /* binding */ isValidMotionProp; }\n/* harmony export */ });\n/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"transformValues\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\n"));

/***/ })

}]);