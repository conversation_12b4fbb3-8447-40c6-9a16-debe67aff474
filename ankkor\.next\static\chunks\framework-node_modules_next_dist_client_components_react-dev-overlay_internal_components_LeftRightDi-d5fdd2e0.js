"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.js ***!
  \***************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"LeftRightDialogHeader\", ({\n    enumerable: true,\n    get: function() {\n        return LeftRightDialogHeader;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _CloseIcon = __webpack_require__(/*! ../../icons/CloseIcon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js\");\nconst LeftRightDialogHeader = function LeftRightDialogHeader(param) {\n    _s();\n    let { children, className, previous, next, close } = param;\n    const buttonLeft = _react.useRef(null);\n    const buttonRight = _react.useRef(null);\n    const buttonClose = _react.useRef(null);\n    const [nav, setNav] = _react.useState(null);\n    const onNav = _react.useCallback((el)=>{\n        setNav(el);\n    }, []);\n    _react.useEffect(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        const d = self.document;\n        function handler(e) {\n            if (e.key === \"ArrowLeft\") {\n                e.preventDefault();\n                e.stopPropagation();\n                if (buttonLeft.current) {\n                    buttonLeft.current.focus();\n                }\n                previous && previous();\n            } else if (e.key === \"ArrowRight\") {\n                e.preventDefault();\n                e.stopPropagation();\n                if (buttonRight.current) {\n                    buttonRight.current.focus();\n                }\n                next && next();\n            } else if (e.key === \"Escape\") {\n                e.preventDefault();\n                e.stopPropagation();\n                if (root instanceof ShadowRoot) {\n                    const a = root.activeElement;\n                    if (a && a !== buttonClose.current && a instanceof HTMLElement) {\n                        a.blur();\n                        return;\n                    }\n                }\n                close == null ? void 0 : close();\n            }\n        }\n        root.addEventListener(\"keydown\", handler);\n        if (root !== d) {\n            d.addEventListener(\"keydown\", handler);\n        }\n        return function() {\n            root.removeEventListener(\"keydown\", handler);\n            if (root !== d) {\n                d.removeEventListener(\"keydown\", handler);\n            }\n        };\n    }, [\n        close,\n        nav,\n        next,\n        previous\n    ]);\n    // Unlock focus for browsers like Firefox, that break all user focus if the\n    // currently focused item becomes disabled.\n    _react.useEffect(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        // Always true, but we do this for TypeScript:\n        if (root instanceof ShadowRoot) {\n            const a = root.activeElement;\n            if (previous == null) {\n                if (buttonLeft.current && a === buttonLeft.current) {\n                    buttonLeft.current.blur();\n                }\n            } else if (next == null) {\n                if (buttonRight.current && a === buttonRight.current) {\n                    buttonRight.current.blur();\n                }\n            }\n        }\n    }, [\n        nav,\n        next,\n        previous\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-left-right\": true,\n        className: className,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"nav\", {\n                ref: onNav,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        ref: buttonLeft,\n                        type: \"button\",\n                        disabled: previous == null ? true : undefined,\n                        \"aria-disabled\": previous == null ? true : undefined,\n                        onClick: previous != null ? previous : undefined,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                            viewBox: \"0 0 14 14\",\n                            fill: \"none\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                                    children: \"previous\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                    d: \"M6.99996 1.16666L1.16663 6.99999L6.99996 12.8333M12.8333 6.99999H1.99996H12.8333Z\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\"\n                                })\n                            ]\n                        })\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        ref: buttonRight,\n                        type: \"button\",\n                        disabled: next == null ? true : undefined,\n                        \"aria-disabled\": next == null ? true : undefined,\n                        onClick: next != null ? next : undefined,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                            viewBox: \"0 0 14 14\",\n                            fill: \"none\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                                    children: \"next\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                    d: \"M6.99996 1.16666L12.8333 6.99999L6.99996 12.8333M1.16663 6.99999H12H1.16663Z\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\"\n                                })\n                            ]\n                        })\n                    }),\n                    children\n                ]\n            }),\n            close ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                \"data-nextjs-errors-dialog-left-right-close-button\": true,\n                ref: buttonClose,\n                type: \"button\",\n                onClick: close,\n                \"aria-label\": \"Close\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_CloseIcon.CloseIcon, {})\n                })\n            }) : null\n        ]\n    });\n};\n_s(LeftRightDialogHeader, \"BTIclYWDjFVmQ0IbTZR6SGMkDDk=\");\n_c = LeftRightDialogHeader;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=LeftRightDialogHeader.js.map\nvar _c;\n$RefreshReg$(_c, \"LeftRightDialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9MZWZ0UmlnaHREaWFsb2dIZWFkZXIvTGVmdFJpZ2h0RGlhbG9nSGVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozt5REF3S1NBOzs7ZUFBQUE7Ozs7OzZFQXhLYzt1Q0FDRztBQVUxQixNQUFNQSx3QkFDSixTQUFTQSxzQkFBc0JDLEtBTTlCOztJQU44QixNQUM3QkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFFBQVEsRUFDUkMsSUFBSSxFQUNKQyxLQUFLLEVBQ04sR0FOOEJMO0lBTzdCLE1BQU1NLGFBQWFDLE9BQU1DLE1BQU0sQ0FBMkI7SUFDMUQsTUFBTUMsY0FBY0YsT0FBTUMsTUFBTSxDQUEyQjtJQUMzRCxNQUFNRSxjQUFjSCxPQUFNQyxNQUFNLENBQTJCO0lBRTNELE1BQU0sQ0FBQ0csS0FBS0MsT0FBTyxHQUFHTCxPQUFNTSxRQUFRLENBQXFCO0lBQ3pELE1BQU1DLFFBQVFQLE9BQU1RLFdBQVcsQ0FBQyxDQUFDQztRQUMvQkosT0FBT0k7SUFDVCxHQUFHLEVBQUU7SUFFTFQsT0FBTVUsU0FBUyxDQUFDO1FBQ2QsSUFBSU4sT0FBTyxNQUFNO1lBQ2Y7UUFDRjtRQUVBLE1BQU1PLE9BQU9QLElBQUlRLFdBQVc7UUFDNUIsTUFBTUMsSUFBSUMsS0FBS0MsUUFBUTtRQUV2QixTQUFTQyxRQUFRQyxDQUFnQjtZQUMvQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssYUFBYTtnQkFDekJELEVBQUVFLGNBQWM7Z0JBQ2hCRixFQUFFRyxlQUFlO2dCQUNqQixJQUFJckIsV0FBV3NCLE9BQU8sRUFBRTtvQkFDdEJ0QixXQUFXc0IsT0FBTyxDQUFDQyxLQUFLO2dCQUMxQjtnQkFDQTFCLFlBQVlBO1lBQ2QsT0FBTyxJQUFJcUIsRUFBRUMsR0FBRyxLQUFLLGNBQWM7Z0JBQ2pDRCxFQUFFRSxjQUFjO2dCQUNoQkYsRUFBRUcsZUFBZTtnQkFDakIsSUFBSWxCLFlBQVltQixPQUFPLEVBQUU7b0JBQ3ZCbkIsWUFBWW1CLE9BQU8sQ0FBQ0MsS0FBSztnQkFDM0I7Z0JBQ0F6QixRQUFRQTtZQUNWLE9BQU8sSUFBSW9CLEVBQUVDLEdBQUcsS0FBSyxVQUFVO2dCQUM3QkQsRUFBRUUsY0FBYztnQkFDaEJGLEVBQUVHLGVBQWU7Z0JBQ2pCLElBQUlULGdCQUFnQlksWUFBWTtvQkFDOUIsTUFBTUMsSUFBSWIsS0FBS2MsYUFBYTtvQkFDNUIsSUFBSUQsS0FBS0EsTUFBTXJCLFlBQVlrQixPQUFPLElBQUlHLGFBQWFFLGFBQWE7d0JBQzlERixFQUFFRyxJQUFJO3dCQUNOO29CQUNGO2dCQUNGO2dCQUVBN0IsU0FBQUEsT0FBQUEsS0FBQUEsSUFBQUE7WUFDRjtRQUNGO1FBRUFhLEtBQUtpQixnQkFBZ0IsQ0FBQyxXQUFXWjtRQUNqQyxJQUFJTCxTQUFTRSxHQUFHO1lBQ2RBLEVBQUVlLGdCQUFnQixDQUFDLFdBQVdaO1FBQ2hDO1FBQ0EsT0FBTztZQUNMTCxLQUFLa0IsbUJBQW1CLENBQUMsV0FBV2I7WUFDcEMsSUFBSUwsU0FBU0UsR0FBRztnQkFDZEEsRUFBRWdCLG1CQUFtQixDQUFDLFdBQVdiO1lBQ25DO1FBQ0Y7SUFDRixHQUFHO1FBQUNsQjtRQUFPTTtRQUFLUDtRQUFNRDtLQUFTO0lBRS9CLDJFQUEyRTtJQUMzRSwyQ0FBMkM7SUFDM0NJLE9BQU1VLFNBQVMsQ0FBQztRQUNkLElBQUlOLE9BQU8sTUFBTTtZQUNmO1FBQ0Y7UUFFQSxNQUFNTyxPQUFPUCxJQUFJUSxXQUFXO1FBQzVCLDhDQUE4QztRQUM5QyxJQUFJRCxnQkFBZ0JZLFlBQVk7WUFDOUIsTUFBTUMsSUFBSWIsS0FBS2MsYUFBYTtZQUU1QixJQUFJN0IsWUFBWSxNQUFNO2dCQUNwQixJQUFJRyxXQUFXc0IsT0FBTyxJQUFJRyxNQUFNekIsV0FBV3NCLE9BQU8sRUFBRTtvQkFDbER0QixXQUFXc0IsT0FBTyxDQUFDTSxJQUFJO2dCQUN6QjtZQUNGLE9BQU8sSUFBSTlCLFFBQVEsTUFBTTtnQkFDdkIsSUFBSUssWUFBWW1CLE9BQU8sSUFBSUcsTUFBTXRCLFlBQVltQixPQUFPLEVBQUU7b0JBQ3BEbkIsWUFBWW1CLE9BQU8sQ0FBQ00sSUFBSTtnQkFDMUI7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDdkI7UUFBS1A7UUFBTUQ7S0FBUztJQUV4QixPQUNFLFdBREYsR0FDRSxJQUFBa0MsWUFBQUMsSUFBQSxFQUFDQyxPQUFBQTtRQUFJQyxpQ0FBNkI7UUFBQ3RDLFdBQVdBOzswQkFDNUMsSUFBQW1DLFlBQUFDLElBQUEsRUFBQzNCLE9BQUFBO2dCQUFJOEIsS0FBSzNCOztrQ0FDUixJQUFBdUIsWUFBQUssR0FBQSxFQUFDQyxVQUFBQTt3QkFDQ0YsS0FBS25DO3dCQUNMc0MsTUFBSzt3QkFDTEMsVUFBVTFDLFlBQVksT0FBTyxPQUFPMkM7d0JBQ3BDQyxpQkFBZTVDLFlBQVksT0FBTyxPQUFPMkM7d0JBQ3pDRSxTQUFTN0MsWUFBQUEsT0FBQUEsV0FBWTJDO2tDQUVyQixrQkFBQVQsWUFBQUMsSUFBQSxFQUFDVyxPQUFBQTs0QkFDQ0MsU0FBUTs0QkFDUkMsTUFBSzs0QkFDTEMsT0FBTTs7OENBRU4sSUFBQWYsWUFBQUssR0FBQSxFQUFDVyxTQUFBQTs4Q0FBTTs7OENBQ1AsSUFBQWhCLFlBQUFLLEdBQUEsRUFBQ1ksUUFBQUE7b0NBQ0NsQyxHQUFFO29DQUNGbUMsUUFBTztvQ0FDUEMsYUFBWTtvQ0FDWkMsZUFBYztvQ0FDZEMsZ0JBQWU7Ozs7O2tDQUlyQixJQUFBckIsWUFBQUssR0FBQSxFQUFDQyxVQUFBQTt3QkFDQ0YsS0FBS2hDO3dCQUNMbUMsTUFBSzt3QkFDTEMsVUFBVXpDLFFBQVEsT0FBTyxPQUFPMEM7d0JBQ2hDQyxpQkFBZTNDLFFBQVEsT0FBTyxPQUFPMEM7d0JBQ3JDRSxTQUFTNUMsUUFBQUEsT0FBQUEsT0FBUTBDO2tDQUVqQixrQkFBQVQsWUFBQUMsSUFBQSxFQUFDVyxPQUFBQTs0QkFDQ0MsU0FBUTs0QkFDUkMsTUFBSzs0QkFDTEMsT0FBTTs7OENBRU4sSUFBQWYsWUFBQUssR0FBQSxFQUFDVyxTQUFBQTs4Q0FBTTs7OENBQ1AsSUFBQWhCLFlBQUFLLEdBQUEsRUFBQ1ksUUFBQUE7b0NBQ0NsQyxHQUFFO29DQUNGbUMsUUFBTztvQ0FDUEMsYUFBWTtvQ0FDWkMsZUFBYztvQ0FDZEMsZ0JBQWU7Ozs7O29CQUlwQnpEOzs7WUFFRkksUUFDQyxXQUREQSxHQUNDLElBQUFnQyxZQUFBSyxHQUFBLEVBQUNDLFVBQUFBO2dCQUNDZ0IscURBQWlEO2dCQUNqRGxCLEtBQUsvQjtnQkFDTGtDLE1BQUs7Z0JBQ0xJLFNBQVMzQztnQkFDVHVELGNBQVc7MEJBRVgsa0JBQUF2QixZQUFBSyxHQUFBLEVBQUNtQixRQUFBQTtvQkFBS0MsZUFBWTs4QkFDaEIsa0JBQUF6QixZQUFBSyxHQUFBLEVBQUNxQixXQUFBQSxTQUFTOztpQkFHWjs7O0FBR1Y7R0EzSkloRTtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvTGVmdFJpZ2h0RGlhbG9nSGVhZGVyL0xlZnRSaWdodERpYWxvZ0hlYWRlci50c3g/Nzk5ZiJdLCJuYW1lcyI6WyJMZWZ0UmlnaHREaWFsb2dIZWFkZXIiLCJwYXJhbSIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicHJldmlvdXMiLCJuZXh0IiwiY2xvc2UiLCJidXR0b25MZWZ0IiwiUmVhY3QiLCJ1c2VSZWYiLCJidXR0b25SaWdodCIsImJ1dHRvbkNsb3NlIiwibmF2Iiwic2V0TmF2IiwidXNlU3RhdGUiLCJvbk5hdiIsInVzZUNhbGxiYWNrIiwiZWwiLCJ1c2VFZmZlY3QiLCJyb290IiwiZ2V0Um9vdE5vZGUiLCJkIiwic2VsZiIsImRvY3VtZW50IiwiaGFuZGxlciIsImUiLCJrZXkiLCJwcmV2ZW50RGVmYXVsdCIsInN0b3BQcm9wYWdhdGlvbiIsImN1cnJlbnQiLCJmb2N1cyIsIlNoYWRvd1Jvb3QiLCJhIiwiYWN0aXZlRWxlbWVudCIsIkhUTUxFbGVtZW50IiwiYmx1ciIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiX2pzeHJ1bnRpbWUiLCJqc3hzIiwiZGl2IiwiZGF0YS1uZXh0anMtZGlhbG9nLWxlZnQtcmlnaHQiLCJyZWYiLCJqc3giLCJidXR0b24iLCJ0eXBlIiwiZGlzYWJsZWQiLCJ1bmRlZmluZWQiLCJhcmlhLWRpc2FibGVkIiwib25DbGljayIsInN2ZyIsInZpZXdCb3giLCJmaWxsIiwieG1sbnMiLCJ0aXRsZSIsInBhdGgiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImRhdGEtbmV4dGpzLWVycm9ycy1kaWFsb2ctbGVmdC1yaWdodC1jbG9zZS1idXR0b24iLCJhcmlhLWxhYmVsIiwic3BhbiIsImFyaWEtaGlkZGVuIiwiQ2xvc2VJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.js ***!
  \***********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    LeftRightDialogHeader: function() {\n        return _LeftRightDialogHeader.LeftRightDialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _LeftRightDialogHeader = __webpack_require__(/*! ./LeftRightDialogHeader */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9MZWZ0UmlnaHREaWFsb2dIZWFkZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLHVCQUFxQjtlQUFyQkEsdUJBQUFBLHFCQUFxQjs7SUFDckJDLFFBQU07ZUFBTkEsUUFBQUEsTUFBTTs7O21EQUR1QjtvQ0FDZiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvTGVmdFJpZ2h0RGlhbG9nSGVhZGVyL2luZGV4LnRzP2JmNzEiXSwibmFtZXMiOlsiTGVmdFJpZ2h0RGlhbG9nSGVhZGVyIiwic3R5bGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js ***!
  \************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-dialog-left-right] {\\n    display: flex;\\n    flex-direction: row;\\n    align-content: center;\\n    align-items: center;\\n    justify-content: space-between;\\n  }\\n  [data-nextjs-dialog-left-right] > nav {\\n    flex: 1;\\n    display: flex;\\n    align-items: center;\\n    margin-right: var(--size-gap);\\n  }\\n  [data-nextjs-dialog-left-right] > nav > button {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n\\n    width: calc(var(--size-gap-double) + var(--size-gap));\\n    height: calc(var(--size-gap-double) + var(--size-gap));\\n    font-size: 0;\\n    border: none;\\n    background-color: rgba(255, 85, 85, 0.1);\\n    color: var(--color-ansi-red);\\n    cursor: pointer;\\n    transition: background-color 0.25s ease;\\n  }\\n  [data-nextjs-dialog-left-right] > nav > button > svg {\\n    width: auto;\\n    height: calc(var(--size-gap) + var(--size-gap-half));\\n  }\\n  [data-nextjs-dialog-left-right] > nav > button:hover {\\n    background-color: rgba(255, 85, 85, 0.2);\\n  }\\n  [data-nextjs-dialog-left-right] > nav > button:disabled {\\n    background-color: rgba(255, 85, 85, 0.1);\\n    color: rgba(255, 85, 85, 0.4);\\n    cursor: not-allowed;\\n  }\\n\\n  [data-nextjs-dialog-left-right] > nav > button:first-of-type {\\n    border-radius: var(--size-gap-half) 0 0 var(--size-gap-half);\\n    margin-right: 1px;\\n  }\\n  [data-nextjs-dialog-left-right] > nav > button:last-of-type {\\n    border-radius: 0 var(--size-gap-half) var(--size-gap-half) 0;\\n  }\\n\\n  [data-nextjs-dialog-left-right] > button:last-of-type {\\n    border: 0;\\n    padding: 0;\\n\\n    background-color: transparent;\\n    appearance: none;\\n\\n    opacity: 0.4;\\n    transition: opacity 0.25s ease;\\n\\n    color: var(--color-font);\\n  }\\n  [data-nextjs-dialog-left-right] > button:last-of-type:hover {\\n    opacity: 0.7;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9MZWZ0UmlnaHREaWFsb2dIZWFkZXIvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBb0VTQTs7O2VBQUFBOzs7OzBDQXBFbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvTGVmdFJpZ2h0RGlhbG9nSGVhZGVyL3N0eWxlcy50cz85ZDkyIl0sIm5hbWVzIjpbInN0eWxlcyIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js\n"));

/***/ })

}]);