"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js ***!
  \********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Base\", ({\n    enumerable: true,\n    get: function() {\n        return Base;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          --size-gap-half: 4px;\\n          --size-gap: 8px;\\n          --size-gap-double: 16px;\\n          --size-gap-triple: 24px;\\n          --size-gap-quad: 32px;\\n\\n          --size-font-small: 14px;\\n          --size-font: 16px;\\n          --size-font-big: 20px;\\n          --size-font-bigger: 24px;\\n\\n          --color-background: white;\\n          --color-font: #757575;\\n          --color-backdrop: rgba(17, 17, 17, 0.2);\\n\\n          --color-title-color: #1f1f1f;\\n          --color-stack-h6: #222;\\n          --color-stack-headline: #666;\\n          --color-stack-subline: #999;\\n          --color-stack-notes: #777;\\n\\n          --color-accents-1: #808080;\\n          --color-accents-2: #222222;\\n          --color-accents-3: #404040;\\n\\n          --color-text-color-red-1: #ff5555;\\n          --color-text-background-red-1: #fff9f9;\\n\\n          --font-stack-monospace: 'SFMono-Regular', Consolas, 'Liberation Mono',\\n            Menlo, Courier, monospace;\\n          --font-stack-sans: -apple-system, 'Source Sans Pro', sans-serif;\\n\\n          --color-ansi-selection: rgba(95, 126, 151, 0.48);\\n          --color-ansi-bg: #111111;\\n          --color-ansi-fg: #cccccc;\\n\\n          --color-ansi-white: #777777;\\n          --color-ansi-black: #141414;\\n          --color-ansi-blue: #00aaff;\\n          --color-ansi-cyan: #88ddff;\\n          --color-ansi-green: #98ec65;\\n          --color-ansi-magenta: #aa88ff;\\n          --color-ansi-red: #ff5555;\\n          --color-ansi-yellow: #ffcc33;\\n          --color-ansi-bright-white: #ffffff;\\n          --color-ansi-bright-black: #777777;\\n          --color-ansi-bright-blue: #33bbff;\\n          --color-ansi-bright-cyan: #bbecff;\\n          --color-ansi-bright-green: #b6f292;\\n          --color-ansi-bright-magenta: #cebbff;\\n          --color-ansi-bright-red: #ff8888;\\n          --color-ansi-bright-yellow: #ffd966;\\n        }\\n\\n        @media (prefers-color-scheme: dark) {\\n          :host {\\n            --color-background: rgb(28, 28, 30);\\n            --color-font: white;\\n            --color-backdrop: rgb(44, 44, 46);\\n\\n            --color-title-color: #fafafa;\\n            --color-stack-h6: rgb(200, 200, 204);\\n            --color-stack-headline: rgb(99, 99, 102);\\n            --color-stack-notes: #a9a9a9;\\n            --color-stack-subline: rgb(121, 121, 121);\\n\\n            --color-accents-3: rgb(118, 118, 118);\\n\\n            --color-text-background-red-1: #2a1e1e;\\n          }\\n        }\\n\\n        .mono {\\n          font-family: var(--font-stack-monospace);\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-bottom: var(--size-gap);\\n          font-weight: 500;\\n          line-height: 1.5;\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Base() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _nooptemplate.noop)(_templateObject())\n    });\n}\n_c = Base;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Base.js.map\nvar _c;\n$RefreshReg$(_c, \"Base\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvc3R5bGVzL0Jhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozt3Q0FHZ0JBOzs7ZUFBQUE7Ozs7Ozs2RUFITzswQ0FDSzs7Ozs7Ozs7OztBQUVyQixTQUFTQTtJQUNkLE9BQ0UsV0FERixHQUNFLElBQUFDLFlBQUFDLEdBQUEsRUFBQ0MsU0FBQUE7c0JBQ0VDLGNBQUFBLElBQUcsRUFBQUM7O0FBMkZWO0tBOUZnQkwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9zdHlsZXMvQmFzZS50c3g/MWZiMCJdLCJuYW1lcyI6WyJCYXNlIiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJzdHlsZSIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ComponentStyles\", ({\n    enumerable: true,\n    get: function() {\n        return ComponentStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _styles = __webpack_require__(/*! ../components/CodeFrame/styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js\");\nconst _Dialog = __webpack_require__(/*! ../components/Dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\");\nconst _styles1 = __webpack_require__(/*! ../components/LeftRightDialogHeader/styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.js\");\nconst _styles2 = __webpack_require__(/*! ../components/Overlay/styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/styles.js\");\nconst _styles3 = __webpack_require__(/*! ../components/Terminal/styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Terminal/styles.js\");\nconst _Toast = __webpack_require__(/*! ../components/Toast */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Toast/index.js\");\nconst _VersionStalenessInfo = __webpack_require__(/*! ../components/VersionStalenessInfo */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.js\");\nconst _BuildError = __webpack_require__(/*! ../container/BuildError */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js\");\nconst _Errors = __webpack_require__(/*! ../container/Errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js\");\nconst _RuntimeError = __webpack_require__(/*! ../container/RuntimeError */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/RuntimeError/index.js\");\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction ComponentStyles() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _nooptemplate.noop)(_templateObject(), _styles2.styles, _Toast.styles, _Dialog.styles, _styles1.styles, _styles.styles, _styles3.styles, _BuildError.styles, _Errors.styles, _RuntimeError.styles, _VersionStalenessInfo.styles)\n    });\n}\n_c = ComponentStyles;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ComponentStyles.js.map\nvar _c;\n$RefreshReg$(_c, \"ComponentStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvc3R5bGVzL0NvbXBvbmVudFN0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQVlnQkE7OztlQUFBQTs7Ozs7b0NBWm9CO29DQUNIO3FDQUNlO3FDQUNkO3FDQUNDO21DQUNIO2tEQUNXO3dDQUNBO29DQUNJOzBDQUNPOzBDQUMxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFckIsU0FBU0E7SUFDZCxPQUNFLFdBREYsR0FDRSxJQUFBQyxZQUFBQyxHQUFBLEVBQUNDLFNBQUFBO3NCQUNFQyxjQUFBQSxJQUFHLEVBQUFDLG1CQUNBQyxTQUFBQSxNQUFPLEVBQ1BDLE9BQUFBLE1BQUssRUFDTEMsUUFBQUEsTUFBTSxFQUNOQyxTQUFBQSxNQUFxQixFQUNyQkMsUUFBQUEsTUFBUyxFQUNUQyxTQUFBQSxNQUFRLEVBQ1JDLFlBQUFBLE1BQWdCLEVBQ2hCQyxRQUFBQSxNQUFvQixFQUNwQkMsY0FBQUEsTUFBMkIsRUFDM0JDLHNCQUFBQSxNQUFnQjs7QUFJMUI7S0FqQmdCZiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL3N0eWxlcy9Db21wb25lbnRTdHlsZXMudHN4PzA5NmMiXSwibmFtZXMiOlsiQ29tcG9uZW50U3R5bGVzIiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJzdHlsZSIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCIsIm92ZXJsYXkiLCJ0b2FzdCIsImRpYWxvZyIsImxlZnRSaWdodERpYWxvZ0hlYWRlciIsImNvZGVGcmFtZSIsInRlcm1pbmFsIiwiYnVpbGRFcnJvclN0eWxlcyIsImNvbnRhaW5lckVycm9yU3R5bGVzIiwiY29udGFpbmVyUnVudGltZUVycm9yU3R5bGVzIiwidmVyc2lvblN0YWxlbmVzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/CssReset.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/CssReset.js ***!
  \************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CssReset\", ({\n    enumerable: true,\n    get: function() {\n        return CssReset;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _nooptemplate = __webpack_require__(/*! ../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          all: initial;\\n\\n          /* the direction property is not reset by 'all' */\\n          direction: ltr;\\n        }\\n\\n        /*!\\n         * Bootstrap Reboot v4.4.1 (https://getbootstrap.com/)\\n         * Copyright 2011-2019 The Bootstrap Authors\\n         * Copyright 2011-2019 Twitter, Inc.\\n         * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\\n         * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)\\n         */\\n        *,\\n        *::before,\\n        *::after {\\n          box-sizing: border-box;\\n        }\\n\\n        :host {\\n          font-family: sans-serif;\\n          line-height: 1.15;\\n          -webkit-text-size-adjust: 100%;\\n          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\n        }\\n\\n        article,\\n        aside,\\n        figcaption,\\n        figure,\\n        footer,\\n        header,\\n        hgroup,\\n        main,\\n        nav,\\n        section {\\n          display: block;\\n        }\\n\\n        :host {\\n          margin: 0;\\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\\n            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,\\n            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\\n            'Noto Color Emoji';\\n          font-size: 16px;\\n          font-weight: 400;\\n          line-height: 1.5;\\n          color: var(--color-font);\\n          text-align: left;\\n          background-color: #fff;\\n        }\\n\\n        [tabindex='-1']:focus:not(:focus-visible) {\\n          outline: 0 !important;\\n        }\\n\\n        hr {\\n          box-sizing: content-box;\\n          height: 0;\\n          overflow: visible;\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-top: 0;\\n          margin-bottom: 8px;\\n        }\\n\\n        p {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        abbr[title],\\n        abbr[data-original-title] {\\n          text-decoration: underline;\\n          -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n          cursor: help;\\n          border-bottom: 0;\\n          -webkit-text-decoration-skip-ink: none;\\n          text-decoration-skip-ink: none;\\n        }\\n\\n        address {\\n          margin-bottom: 16px;\\n          font-style: normal;\\n          line-height: inherit;\\n        }\\n\\n        ol,\\n        ul,\\n        dl {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        ol ol,\\n        ul ul,\\n        ol ul,\\n        ul ol {\\n          margin-bottom: 0;\\n        }\\n\\n        dt {\\n          font-weight: 700;\\n        }\\n\\n        dd {\\n          margin-bottom: 8px;\\n          margin-left: 0;\\n        }\\n\\n        blockquote {\\n          margin: 0 0 16px;\\n        }\\n\\n        b,\\n        strong {\\n          font-weight: bolder;\\n        }\\n\\n        small {\\n          font-size: 80%;\\n        }\\n\\n        sub,\\n        sup {\\n          position: relative;\\n          font-size: 75%;\\n          line-height: 0;\\n          vertical-align: baseline;\\n        }\\n\\n        sub {\\n          bottom: -0.25em;\\n        }\\n\\n        sup {\\n          top: -0.5em;\\n        }\\n\\n        a {\\n          color: #007bff;\\n          text-decoration: none;\\n          background-color: transparent;\\n        }\\n\\n        a:hover {\\n          color: #0056b3;\\n          text-decoration: underline;\\n        }\\n\\n        a:not([href]) {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        a:not([href]):hover {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        pre,\\n        code,\\n        kbd,\\n        samp {\\n          font-family: SFMono-Regular, Menlo, Monaco, Consolas,\\n            'Liberation Mono', 'Courier New', monospace;\\n          font-size: 1em;\\n        }\\n\\n        pre {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n          overflow: auto;\\n        }\\n\\n        figure {\\n          margin: 0 0 16px;\\n        }\\n\\n        img {\\n          vertical-align: middle;\\n          border-style: none;\\n        }\\n\\n        svg {\\n          overflow: hidden;\\n          vertical-align: middle;\\n        }\\n\\n        table {\\n          border-collapse: collapse;\\n        }\\n\\n        caption {\\n          padding-top: 12px;\\n          padding-bottom: 12px;\\n          color: #6c757d;\\n          text-align: left;\\n          caption-side: bottom;\\n        }\\n\\n        th {\\n          text-align: inherit;\\n        }\\n\\n        label {\\n          display: inline-block;\\n          margin-bottom: 8px;\\n        }\\n\\n        button {\\n          border-radius: 0;\\n        }\\n\\n        button:focus {\\n          outline: 1px dotted;\\n          outline: 5px auto -webkit-focus-ring-color;\\n        }\\n\\n        input,\\n        button,\\n        select,\\n        optgroup,\\n        textarea {\\n          margin: 0;\\n          font-family: inherit;\\n          font-size: inherit;\\n          line-height: inherit;\\n        }\\n\\n        button,\\n        input {\\n          overflow: visible;\\n        }\\n\\n        button,\\n        select {\\n          text-transform: none;\\n        }\\n\\n        select {\\n          word-wrap: normal;\\n        }\\n\\n        button,\\n        [type='button'],\\n        [type='reset'],\\n        [type='submit'] {\\n          -webkit-appearance: button;\\n        }\\n\\n        button:not(:disabled),\\n        [type='button']:not(:disabled),\\n        [type='reset']:not(:disabled),\\n        [type='submit']:not(:disabled) {\\n          cursor: pointer;\\n        }\\n\\n        button::-moz-focus-inner,\\n        [type='button']::-moz-focus-inner,\\n        [type='reset']::-moz-focus-inner,\\n        [type='submit']::-moz-focus-inner {\\n          padding: 0;\\n          border-style: none;\\n        }\\n\\n        input[type='radio'],\\n        input[type='checkbox'] {\\n          box-sizing: border-box;\\n          padding: 0;\\n        }\\n\\n        input[type='date'],\\n        input[type='time'],\\n        input[type='datetime-local'],\\n        input[type='month'] {\\n          -webkit-appearance: listbox;\\n        }\\n\\n        textarea {\\n          overflow: auto;\\n          resize: vertical;\\n        }\\n\\n        fieldset {\\n          min-width: 0;\\n          padding: 0;\\n          margin: 0;\\n          border: 0;\\n        }\\n\\n        legend {\\n          display: block;\\n          width: 100%;\\n          max-width: 100%;\\n          padding: 0;\\n          margin-bottom: 8px;\\n          font-size: 24px;\\n          line-height: inherit;\\n          color: inherit;\\n          white-space: normal;\\n        }\\n\\n        progress {\\n          vertical-align: baseline;\\n        }\\n\\n        [type='number']::-webkit-inner-spin-button,\\n        [type='number']::-webkit-outer-spin-button {\\n          height: auto;\\n        }\\n\\n        [type='search'] {\\n          outline-offset: -2px;\\n          -webkit-appearance: none;\\n        }\\n\\n        [type='search']::-webkit-search-decoration {\\n          -webkit-appearance: none;\\n        }\\n\\n        ::-webkit-file-upload-button {\\n          font: inherit;\\n          -webkit-appearance: button;\\n        }\\n\\n        output {\\n          display: inline-block;\\n        }\\n\\n        summary {\\n          display: list-item;\\n          cursor: pointer;\\n        }\\n\\n        template {\\n          display: none;\\n        }\\n\\n        [hidden] {\\n          display: none !important;\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction CssReset() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _nooptemplate.noop)(_templateObject())\n    });\n}\n_c = CssReset;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CssReset.js.map\nvar _c;\n$RefreshReg$(_c, \"CssReset\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvc3R5bGVzL0Nzc1Jlc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7NENBR2dCQTs7O2VBQUFBOzs7Ozs7NkVBSE87MENBQ0s7Ozs7Ozs7Ozs7QUFFckIsU0FBU0E7SUFDZCxPQUNFLFdBREYsR0FDRSxJQUFBQyxZQUFBQyxHQUFBLEVBQUNDLFNBQUFBO3NCQUNFQyxjQUFBQSxJQUFHLEVBQUFDOztBQW1XVjtLQXRXZ0JMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvc3R5bGVzL0Nzc1Jlc2V0LnRzeD8zYzVmIl0sIm5hbWVzIjpbIkNzc1Jlc2V0IiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJzdHlsZSIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/CssReset.js\n"));

/***/ })

}]);