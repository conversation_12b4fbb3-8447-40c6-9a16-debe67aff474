/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_c"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/search-params.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/search-params.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createDynamicallyTrackedSearchParams: function() {\n        return createDynamicallyTrackedSearchParams;\n    },\n    createUntrackedSearchParams: function() {\n        return createUntrackedSearchParams;\n    }\n});\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../../server/app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _reflect = __webpack_require__(/*! ../../server/web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nfunction createUntrackedSearchParams(searchParams) {\n    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (store && store.forceStatic) {\n        return {};\n    } else {\n        return searchParams;\n    }\n}\nfunction createDynamicallyTrackedSearchParams(searchParams) {\n    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (!store) {\n        // we assume we are in a route handler or page render. just return the searchParams\n        return searchParams;\n    } else if (store.forceStatic) {\n        // If we forced static we omit searchParams entirely. This is true both during SSR\n        // and browser render because we need there to be parity between these environments\n        return {};\n    } else if (!store.isStaticGeneration && !store.dynamicShouldError) {\n        // during dynamic renders we don't actually have to track anything so we just return\n        // the searchParams directly. However if dynamic data access should error then we\n        // still want to track access. This covers the case in Dev where all renders are dynamic\n        // but we still want to error if you use a dynamic data source because it will fail the build\n        // or revalidate if you do.\n        return searchParams;\n    } else {\n        // We need to track dynamic access with a Proxy. We implement get, has, and ownKeys because\n        // these can all be used to exfiltrate information about searchParams.\n        return new Proxy({}, {\n            get (target, prop, receiver) {\n                if (typeof prop === \"string\") {\n                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams.\" + prop);\n                }\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === \"string\") {\n                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams.\" + prop);\n                }\n                return Reflect.has(target, prop);\n            },\n            ownKeys (target) {\n                (0, _dynamicrendering.trackDynamicDataAccessed)(store, \"searchParams\");\n                return Reflect.ownKeys(target);\n            }\n        });\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=search-params.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/search-params.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-async-storage.external.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\"TURBOPACK { transition: next-shared }\";\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _staticgenerationasyncstorageinstance.staticGenerationAsyncStorage;\n    }\n}));\nconst _staticgenerationasyncstorageinstance = __webpack_require__(/*! ./static-generation-async-storage-instance */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage-instance.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-async-storage.external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMiLCJtYXBwaW5ncyI6IkFBU0U7Ozs7O2dFQW1ET0E7OztlQUFBQSxzQ0FBQUEsNEJBQTRCOzs7a0VBbERRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYXN5bmMtc3RvcmFnZS5leHRlcm5hbC50cz9jZWMwIl0sIm5hbWVzIjpbInN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = \"NEXT_STATIC_GEN_BAILOUT\";\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== \"object\" || error === null || !(\"code\" in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFFYUEsdUJBQXFCO2VBQXJCQTs7SUFJR0MseUJBQXVCO2VBQXZCQTs7O0FBTmhCLE1BQU1DLDBCQUEwQjtBQUV6QixNQUFNRiw4QkFBOEJHOzs7YUFDekJDLElBQUFBLEdBQU9GOztBQUN6QjtBQUVPLFNBQVNELHdCQUNkSSxLQUFjO0lBRWQsSUFBSSxPQUFPQSxVQUFVLFlBQVlBLFVBQVUsUUFBUSxDQUFFLFdBQVVBLEtBQUFBLEdBQVE7UUFDckUsT0FBTztJQUNUO0lBRUEsT0FBT0EsTUFBTUQsSUFBSSxLQUFLRjtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQudHM/YzFkMyJdLCJuYW1lcyI6WyJTdGF0aWNHZW5CYWlsb3V0RXJyb3IiLCJpc1N0YXRpY0dlbkJhaWxvdXRFcnJvciIsIk5FWFRfU1RBVElDX0dFTl9CQUlMT1VUIiwiRXJyb3IiLCJjb2RlIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/unresolved-thenable.js ***!
  \*************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"unresolvedThenable\", ({\n    enumerable: true,\n    get: function() {\n        return unresolvedThenable;\n    }\n}));\nconst unresolvedThenable = {\n    then: ()=>{}\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=unresolved-thenable.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5yZXNvbHZlZC10aGVuYWJsZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7Q0FFQzs7OztzREFDWUE7OztlQUFBQTs7O0FBQU4sTUFBTUEscUJBQXFCO0lBQ2hDQyxNQUFNLEtBQU87QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3VucmVzb2x2ZWQtdGhlbmFibGUudHM/MGQ0ZCJdLCJuYW1lcyI6WyJ1bnJlc29sdmVkVGhlbmFibGUiLCJ0aGVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer-with-devtools.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-reducer-with-devtools.js ***!
  \*******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    useReducerWithReduxDevtools: function() {\n        return useReducerWithReduxDevtools;\n    },\n    useUnwrapState: function() {\n        return useUnwrapState;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nfunction normalizeRouterState(val) {\n    if (val instanceof Map) {\n        const obj = {};\n        for (const [key, value] of val.entries()){\n            if (typeof value === \"function\") {\n                obj[key] = \"fn()\";\n                continue;\n            }\n            if (typeof value === \"object\" && value !== null) {\n                if (value.$$typeof) {\n                    obj[key] = value.$$typeof.toString();\n                    continue;\n                }\n                if (value._bundlerConfig) {\n                    obj[key] = \"FlightData\";\n                    continue;\n                }\n            }\n            obj[key] = normalizeRouterState(value);\n        }\n        return obj;\n    }\n    if (typeof val === \"object\" && val !== null) {\n        const obj = {};\n        for(const key in val){\n            const value = val[key];\n            if (typeof value === \"function\") {\n                obj[key] = \"fn()\";\n                continue;\n            }\n            if (typeof value === \"object\" && value !== null) {\n                if (value.$$typeof) {\n                    obj[key] = value.$$typeof.toString();\n                    continue;\n                }\n                if (value.hasOwnProperty(\"_bundlerConfig\")) {\n                    obj[key] = \"FlightData\";\n                    continue;\n                }\n            }\n            obj[key] = normalizeRouterState(value);\n        }\n        return obj;\n    }\n    if (Array.isArray(val)) {\n        return val.map(normalizeRouterState);\n    }\n    return val;\n}\nfunction useUnwrapState(state) {\n    // reducer actions can be async, so sometimes we need to suspend until the state is resolved\n    if ((0, _routerreducertypes.isThenable)(state)) {\n        const result = (0, _react.use)(state);\n        return result;\n    }\n    return state;\n}\nfunction useReducerWithReduxDevtoolsNoop(initialState) {\n    return [\n        initialState,\n        ()=>{},\n        ()=>{}\n    ];\n}\nfunction useReducerWithReduxDevtoolsImpl(initialState) {\n    _s();\n    const [state, setState] = _react.default.useState(initialState);\n    const actionQueue = (0, _react.useContext)(_actionqueue.ActionQueueContext);\n    if (!actionQueue) {\n        throw new Error(\"Invariant: Missing ActionQueueContext\");\n    }\n    const devtoolsConnectionRef = (0, _react.useRef)();\n    const enabledRef = (0, _react.useRef)();\n    (0, _react.useEffect)(()=>{\n        if (devtoolsConnectionRef.current || enabledRef.current === false) {\n            return;\n        }\n        if (enabledRef.current === undefined && typeof window.__REDUX_DEVTOOLS_EXTENSION__ === \"undefined\") {\n            enabledRef.current = false;\n            return;\n        }\n        devtoolsConnectionRef.current = window.__REDUX_DEVTOOLS_EXTENSION__.connect({\n            instanceId: 8000,\n            name: \"next-router\"\n        });\n        if (devtoolsConnectionRef.current) {\n            devtoolsConnectionRef.current.init(normalizeRouterState(initialState));\n            if (actionQueue) {\n                actionQueue.devToolsInstance = devtoolsConnectionRef.current;\n            }\n        }\n        return ()=>{\n            devtoolsConnectionRef.current = undefined;\n        };\n    }, [\n        initialState,\n        actionQueue\n    ]);\n    const dispatch = (0, _react.useCallback)((action)=>{\n        if (!actionQueue.state) {\n            // we lazy initialize the mutable action queue state since the data needed\n            // to generate the state is not available when the actionQueue context is created\n            actionQueue.state = initialState;\n        }\n        actionQueue.dispatch(action, setState);\n    }, [\n        actionQueue,\n        initialState\n    ]);\n    // Sync is called after a state update in the HistoryUpdater,\n    // for debugging purposes. Since the reducer state may be a Promise,\n    // we let the app router use() it and sync on the resolved value if\n    // something changed.\n    // Using the `state` here would be referentially unstable and cause\n    // undesirable re-renders and history updates.\n    const sync = (0, _react.useCallback)((resolvedState)=>{\n        if (devtoolsConnectionRef.current) {\n            devtoolsConnectionRef.current.send({\n                type: \"RENDER_SYNC\"\n            }, normalizeRouterState(resolvedState));\n        }\n    }, []);\n    return [\n        state,\n        dispatch,\n        sync\n    ];\n}\n_s(useReducerWithReduxDevtoolsImpl, \"2/eSVXfk2V5ZKttKXeUPXMa6Sd8=\");\nconst useReducerWithReduxDevtools = typeof window !== \"undefined\" ? useReducerWithReduxDevtoolsImpl : useReducerWithReduxDevtoolsNoop;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-reducer-with-devtools.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer-with-devtools.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/noop-turbopack-hmr.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// The Turbopack HMR client can't be properly omitted at the moment (WEB-1589),\n// so instead we remap its import to this file in webpack builds.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"connect\", ({\n    enumerable: true,\n    get: function() {\n        return connect;\n    }\n}));\nfunction connect() {}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-turbopack-hmr.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9ub29wLXR1cmJvcGFjay1obXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsK0VBQStFO0FBQy9FLGlFQUFpRTs7Ozs7MkNBQ2pEQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSxXQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2Rldi9ub29wLXR1cmJvcGFjay1obXIudHM/ZTg2NSJdLCJuYW1lcyI6WyJjb25uZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/static-generation-async-storage-instance.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-async-storage-instance.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationAsyncStorage;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/client/components/async-local-storage.js\");\nconst staticGenerationAsyncStorage = (0, _asynclocalstorage.createAsyncLocalStorage)();\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-async-storage-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztnRUFHYUE7OztlQUFBQTs7OytDQUYyQjtBQUVqQyxNQUFNQSwrQkFDWEMsQ0FBQUEsR0FBQUEsbUJBQUFBLHVCQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UudHM/OGY2MiJdLCJuYW1lcyI6WyJzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlIiwiY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/static-generation-async-storage-instance.js\n"));

/***/ })

}]);