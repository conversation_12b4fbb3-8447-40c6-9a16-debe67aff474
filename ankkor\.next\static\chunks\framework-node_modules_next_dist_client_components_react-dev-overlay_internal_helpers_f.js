"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.js ***!
  \****************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return formatWebpackMessages;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\nconst friendlySyntaxErrorLabel = \"Syntax error:\";\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS = \"\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.\";\nfunction isLikelyASyntaxError(message) {\n    return (0, _stripansi.default)(message).includes(friendlySyntaxErrorLabel);\n}\nlet hadMissingSassError = false;\n// Cleans up webpack error messages.\nfunction formatMessage(message, verbose, importTraceNote) {\n    // TODO: Replace this once webpack 5 is stable\n    if (typeof message === \"object\" && message.message) {\n        const filteredModuleTrace = message.moduleTrace && message.moduleTrace.filter((trace)=>!/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(trace.originName));\n        let body = message.message;\n        const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS);\n        if (breakingChangeIndex >= 0) {\n            body = body.slice(0, breakingChangeIndex);\n        }\n        message = (message.moduleName ? (0, _stripansi.default)(message.moduleName) + \"\\n\" : \"\") + (message.file ? (0, _stripansi.default)(message.file) + \"\\n\" : \"\") + body + (message.details && verbose ? \"\\n\" + message.details : \"\") + (filteredModuleTrace && filteredModuleTrace.length ? (importTraceNote || \"\\n\\nImport trace for requested module:\") + filteredModuleTrace.map((trace)=>\"\\n\" + trace.moduleName).join(\"\") : \"\") + (message.stack && verbose ? \"\\n\" + message.stack : \"\");\n    }\n    let lines = message.split(\"\\n\");\n    // Strip Webpack-added headers off errors/warnings\n    // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n    lines = lines.filter((line)=>!/Module [A-z ]+\\(from/.test(line));\n    // Transform parsing error into syntax error\n    // TODO: move this to our ESLint formatter?\n    lines = lines.map((line)=>{\n        const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n        if (!parsingError) {\n            return line;\n        }\n        const [, errorLine, errorColumn, errorMessage] = parsingError;\n        return friendlySyntaxErrorLabel + \" \" + errorMessage + \" (\" + errorLine + \":\" + errorColumn + \")\";\n    });\n    message = lines.join(\"\\n\");\n    // Smoosh syntax errors (commonly found in CSS)\n    message = message.replace(/SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g, \"\" + friendlySyntaxErrorLabel + \" $3 ($1:$2)\\n\");\n    // Clean up export errors\n    message = message.replace(/^.*export '(.+?)' was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$2'.\");\n    message = message.replace(/^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$2' does not contain a default export (imported as '$1').\");\n    message = message.replace(/^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$3' (imported as '$2').\");\n    lines = message.split(\"\\n\");\n    // Remove leading newline\n    if (lines.length > 2 && lines[1].trim() === \"\") {\n        lines.splice(1, 1);\n    }\n    // Cleans up verbose \"module not found\" messages for files and packages.\n    if (lines[1] && lines[1].indexOf(\"Module not found: \") === 0) {\n        lines = [\n            lines[0],\n            lines[1].replace(\"Error: \", \"\").replace(\"Module not found: Cannot find file:\", \"Cannot find file:\"),\n            ...lines.slice(2)\n        ];\n    }\n    // Add helpful message for users trying to use Sass for the first time\n    if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n        // ./file.module.scss (<<loader info>>) => ./file.module.scss\n        const firstLine = lines[0].split(\"!\");\n        lines[0] = firstLine[firstLine.length - 1];\n        lines[1] = \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\";\n        lines[1] += \"Run `npm i sass` or `yarn add sass` inside your workspace.\\n\";\n        lines[1] += \"\\nLearn more: https://nextjs.org/docs/messages/install-sass\";\n        // dispose of unhelpful stack trace\n        lines = lines.slice(0, 2);\n        hadMissingSassError = true;\n    } else if (hadMissingSassError && message.match(/(sass-loader|resolve-url-loader: CSS error)/)) {\n        // dispose of unhelpful stack trace following missing sass module\n        lines = [];\n    }\n    if (!verbose) {\n        message = lines.join(\"\\n\");\n        // Internal stacks are generally useless so we strip them... with the\n        // exception of stacks containing `webpack:` because they're normally\n        // from user code generated by Webpack. For more information see\n        // https://github.com/facebook/create-react-app/pull/1050\n        message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, \"\") // at ... ...:x:y\n        ;\n        message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, \"\") // at <anonymous>\n        ;\n        message = message.replace(/File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g, \"\");\n        lines = message.split(\"\\n\");\n    }\n    // Remove duplicated newlines\n    lines = lines.filter((line, index, arr)=>index === 0 || line.trim() !== \"\" || line.trim() !== arr[index - 1].trim());\n    // Reassemble the message\n    message = lines.join(\"\\n\");\n    return message.trim();\n}\nfunction formatWebpackMessages(json, verbose) {\n    const formattedErrors = json.errors.map((message)=>{\n        const isUnknownNextFontError = message.message.includes(\"An error occurred in `next/font`.\");\n        return formatMessage(message, isUnknownNextFontError || verbose);\n    });\n    const formattedWarnings = json.warnings.map((message)=>{\n        return formatMessage(message, verbose);\n    });\n    // Reorder errors to put the most relevant ones first.\n    let reactServerComponentsError = -1;\n    for(let i = 0; i < formattedErrors.length; i++){\n        const error = formattedErrors[i];\n        if (error.includes(\"ReactServerComponentsError\")) {\n            reactServerComponentsError = i;\n            break;\n        }\n    }\n    // Move the reactServerComponentsError to the top if it exists\n    if (reactServerComponentsError !== -1) {\n        const error = formattedErrors.splice(reactServerComponentsError, 1);\n        formattedErrors.unshift(error[0]);\n    }\n    const result = {\n        ...json,\n        errors: formattedErrors,\n        warnings: formattedWarnings\n    };\n    if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n        // If there are any syntax errors, show just them.\n        result.errors = result.errors.filter(isLikelyASyntaxError);\n        result.warnings = [];\n    }\n    return result;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=format-webpack-messages.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSocketUrl\", ({\n    enumerable: true,\n    get: function() {\n        return getSocketUrl;\n    }\n}));\nconst _normalizedassetprefix = __webpack_require__(/*! ../../../../../shared/lib/normalized-asset-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\");\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = window.location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === \"http:\" ? \"ws:\" : \"wss:\";\n}\nfunction getSocketUrl(assetPrefix) {\n    const prefix = (0, _normalizedassetprefix.normalizedAssetPrefix)(assetPrefix);\n    const protocol = getSocketProtocol(assetPrefix || \"\");\n    if (URL.canParse(prefix)) {\n        // since normalized asset prefix is ensured to be a URL format,\n        // we can safely replace the protocol\n        return prefix.replace(/^http/, \"ws\");\n    }\n    const { hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\") + prefix;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-socket-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXQtc29ja2V0LXVybC5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWFnQkE7OztlQUFBQTs7O21EQWJzQjtBQUV0QyxTQUFTQyxrQkFBa0JDLFdBQW1CO0lBQzVDLElBQUlDLFdBQVdDLE9BQU9DLFFBQVEsQ0FBQ0YsUUFBUTtJQUV2QyxJQUFJO1FBQ0YsdUJBQXVCO1FBQ3ZCQSxXQUFXLElBQUlHLElBQUlKLGFBQWFDLFFBQVE7SUFDMUMsRUFBRSxPQUFBSSxHQUFNLENBQUM7SUFFVCxPQUFPSixhQUFhLFVBQVUsUUFBUTtBQUN4QztBQUVPLFNBQVNILGFBQWFFLFdBQStCO0lBQzFELE1BQU1NLFNBQVNDLENBQUFBLEdBQUFBLHVCQUFBQSxxQkFBcUIsRUFBQ1A7SUFDckMsTUFBTUMsV0FBV0Ysa0JBQWtCQyxlQUFlO0lBRWxELElBQUlJLElBQUlJLFFBQVEsQ0FBQ0YsU0FBUztRQUN4QiwrREFBK0Q7UUFDL0QscUNBQXFDO1FBQ3JDLE9BQU9BLE9BQU9HLE9BQU8sQ0FBQyxTQUFTO0lBQ2pDO0lBRUEsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLElBQUksRUFBRSxHQUFHVCxPQUFPQyxRQUFRO0lBQzFDLE9BQU9GLFdBQVksT0FBSVMsV0FBV0MsQ0FBQUEsT0FBTyxNQUFJQSxPQUFTLE1BQUtMO0FBQzdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXQtc29ja2V0LXVybC50cz83ZmIwIl0sIm5hbWVzIjpbImdldFNvY2tldFVybCIsImdldFNvY2tldFByb3RvY29sIiwiYXNzZXRQcmVmaXgiLCJwcm90b2NvbCIsIndpbmRvdyIsImxvY2F0aW9uIiwiVVJMIiwiZSIsInByZWZpeCIsIm5vcm1hbGl6ZWRBc3NldFByZWZpeCIsImNhblBhcnNlIiwicmVwbGFjZSIsImhvc3RuYW1lIiwicG9ydCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/get-socket-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getErrorByType\", ({\n    enumerable: true,\n    get: function() {\n        return getErrorByType;\n    }\n}));\nconst _shared = __webpack_require__(/*! ../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _stackframe = __webpack_require__(/*! ./stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nasync function getErrorByType(ev, isAppDir) {\n    const { id, event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                const readyRuntimeError = {\n                    id,\n                    runtime: true,\n                    error: event.reason,\n                    frames: await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir, event.reason.toString())\n                };\n                if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                    readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                }\n                return readyRuntimeError;\n            }\n        default:\n            {\n                break;\n            }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    throw new Error(\"type system invariant violation\");\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=getErrorByType.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXRFcnJvckJ5VHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O2tEQWtCc0JBOzs7ZUFBQUE7OztvQ0FmZjt3Q0FFZ0M7eUNBR1I7QUFVeEIsZUFBZUEsZUFDcEJDLEVBQXVCLEVBQ3ZCQyxRQUFpQjtJQUVqQixNQUFNLEVBQUVDLEVBQUUsRUFBRUMsS0FBSyxFQUFFLEdBQUdIO0lBQ3RCLE9BQVFHLE1BQU1DLElBQUk7UUFDaEIsS0FBS0MsUUFBQUEsc0JBQXNCO1FBQzNCLEtBQUtDLFFBQUFBLDBCQUEwQjtZQUFFO2dCQUMvQixNQUFNQyxvQkFBdUM7b0JBQzNDTDtvQkFDQU0sU0FBUztvQkFDVEMsT0FBT04sTUFBTU8sTUFBTTtvQkFDbkJDLFFBQVEsTUFBTUMsQ0FBQUEsR0FBQUEsWUFBQUEsc0JBQXNCLEVBQ2xDVCxNQUFNUSxNQUFNLEVBQ1pFLENBQUFBLEdBQUFBLGFBQUFBLGNBQWMsRUFBQ1YsTUFBTU8sTUFBTSxHQUMzQlQsVUFDQUUsTUFBTU8sTUFBTSxDQUFDSSxRQUFRO2dCQUV6QjtnQkFDQSxJQUFJWCxNQUFNQyxJQUFJLEtBQUtDLFFBQUFBLHNCQUFzQixFQUFFO29CQUN6Q0Usa0JBQWtCUSxvQkFBb0IsR0FBR1osTUFBTVksb0JBQW9CO2dCQUNyRTtnQkFDQSxPQUFPUjtZQUNUO1FBQ0E7WUFBUztnQkFDUDtZQUNGO0lBQ0Y7SUFDQSw2REFBNkQ7SUFDN0QsTUFBTVMsSUFBV2I7SUFDakIsTUFBTSxJQUFJYyxNQUFNO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9nZXRFcnJvckJ5VHlwZS50cz9hYjdlIl0sIm5hbWVzIjpbImdldEVycm9yQnlUeXBlIiwiZXYiLCJpc0FwcERpciIsImlkIiwiZXZlbnQiLCJ0eXBlIiwiQUNUSU9OX1VOSEFORExFRF9FUlJPUiIsIkFDVElPTl9VTkhBTkRMRURfUkVKRUNUSU9OIiwicmVhZHlSdW50aW1lRXJyb3IiLCJydW50aW1lIiwiZXJyb3IiLCJyZWFzb24iLCJmcmFtZXMiLCJnZXRPcmlnaW5hbFN0YWNrRnJhbWVzIiwiZ2V0RXJyb3JTb3VyY2UiLCJ0b1N0cmluZyIsImNvbXBvbmVudFN0YWNrRnJhbWVzIiwiXyIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/getErrorByType.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js ***!
  \************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"groupStackFramesByFramework\", ({\n    enumerable: true,\n    get: function() {\n        return groupStackFramesByFramework;\n    }\n}));\nfunction groupStackFramesByFramework(stackFrames) {\n    const stackFramesGroupedByFramework = [];\n    for (const stackFrame of stackFrames){\n        const currentGroup = stackFramesGroupedByFramework[stackFramesGroupedByFramework.length - 1];\n        const framework = stackFrame.sourcePackage;\n        if (currentGroup && currentGroup.framework === framework) {\n            currentGroup.stackFrames.push(stackFrame);\n        } else {\n            stackFramesGroupedByFramework.push({\n                framework: framework,\n                stackFrames: [\n                    stackFrame\n                ]\n            });\n        }\n    }\n    return stackFramesGroupedByFramework;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=group-stack-frames-by-framework.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ncm91cC1zdGFjay1mcmFtZXMtYnktZnJhbWV3b3JrLmpzIiwibWFwcGluZ3MiOiI7Ozs7K0RBNkJnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsNEJBQ2RDLFdBQWlDO0lBRWpDLE1BQU1DLGdDQUFvRCxFQUFFO0lBRTVELEtBQUssTUFBTUMsY0FBY0YsWUFBYTtRQUNwQyxNQUFNRyxlQUNKRiw2QkFBNkIsQ0FBQ0EsOEJBQThCRyxNQUFNLEdBQUcsRUFBRTtRQUN6RSxNQUFNQyxZQUFZSCxXQUFXSSxhQUFhO1FBRTFDLElBQUlILGdCQUFnQkEsYUFBYUUsU0FBUyxLQUFLQSxXQUFXO1lBQ3hERixhQUFhSCxXQUFXLENBQUNPLElBQUksQ0FBQ0w7UUFDaEMsT0FBTztZQUNMRCw4QkFBOEJNLElBQUksQ0FBQztnQkFDakNGLFdBQVdBO2dCQUNYTCxhQUFhO29CQUFDRTtpQkFBVztZQUMzQjtRQUNGO0lBQ0Y7SUFFQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaGVscGVycy9ncm91cC1zdGFjay1mcmFtZXMtYnktZnJhbWV3b3JrLnRzPzVkNWEiXSwibmFtZXMiOlsiZ3JvdXBTdGFja0ZyYW1lc0J5RnJhbWV3b3JrIiwic3RhY2tGcmFtZXMiLCJzdGFja0ZyYW1lc0dyb3VwZWRCeUZyYW1ld29yayIsInN0YWNrRnJhbWUiLCJjdXJyZW50R3JvdXAiLCJsZW5ndGgiLCJmcmFtZXdvcmsiLCJzb3VyY2VQYWNrYWdlIiwicHVzaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/group-stack-frames-by-framework.js\n"));

/***/ })

}]);