"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/collection/shirts/page-src_app_collection_shirts_page_tsx-4be0719e"],{

/***/ "(app-pages-browser)/./src/app/collection/shirts/page.tsx":
/*!********************************************!*\
  !*** ./src/app/collection/shirts/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShirtsCollectionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_product_ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/product/ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/* harmony import */ var _lib_productUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/productUtils */ \"(app-pages-browser)/./src/lib/productUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ShirtsCollectionPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isFilterOpen, setIsFilterOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        25000\n    ]);\n    const [sortOption, setSortOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use the page loading hook\n    (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(isLoading, \"fabric\");\n    // Fetch products from WooCommerce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                var _categoryData_products_nodes, _categoryData_products, _categoryData_products_nodes1, _categoryData_products1, _categoryData_products_nodes2, _categoryData_products2, _categoryData_products_nodes3, _categoryData_products3, _categoryData_products_nodes4, _categoryData_products4, _categoryData_products5, _categoryData_products6;\n                setIsLoading(true);\n                setError(null);\n                console.log(\"\\uD83D\\uDD0D Starting to fetch shirts from WooCommerce...\");\n                // First, let's test the WooCommerce connection\n                let connectionTest = null;\n                try {\n                    console.log(\"\\uD83E\\uDDEA Testing WooCommerce connection...\");\n                    const { testWooCommerceConnection } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                    connectionTest = await testWooCommerceConnection();\n                    console.log(\"\\uD83D\\uDD17 Connection test result:\", connectionTest);\n                } catch (err) {\n                    console.log(\"❌ Failed to test connection:\", err);\n                }\n                // Then, let's test if we can fetch all categories to see what's available\n                let allCategories = null;\n                try {\n                    console.log(\"\\uD83D\\uDCCB Fetching all categories to debug...\");\n                    const { getAllCategories } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                    allCategories = await getAllCategories(50);\n                    console.log(\"\\uD83D\\uDCC2 Available categories:\", allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>({\n                            name: cat.name,\n                            slug: cat.slug,\n                            id: cat.id,\n                            count: cat.count\n                        })));\n                } catch (err) {\n                    console.log(\"❌ Failed to fetch categories:\", err);\n                }\n                // Try multiple approaches to fetch shirts\n                let categoryData = null;\n                let fetchMethod = \"\";\n                // Method 1: Try with category slug 'shirts'\n                try {\n                    var _categoryData_products_nodes5, _categoryData_products7;\n                    console.log('\\uD83D\\uDCCB Attempting to fetch with category slug: \"shirts\"');\n                    categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(\"shirts\", {\n                        first: 100\n                    });\n                    fetchMethod = \"slug: shirts\";\n                    if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products7 = categoryData.products) === null || _categoryData_products7 === void 0 ? void 0 : (_categoryData_products_nodes5 = _categoryData_products7.nodes) === null || _categoryData_products_nodes5 === void 0 ? void 0 : _categoryData_products_nodes5.length) > 0) {\n                        console.log(\"✅ Success with method 1 (slug: shirts)\");\n                    } else {\n                        console.log(\"⚠️ Method 1 returned empty or null:\", categoryData);\n                    }\n                } catch (err) {\n                    console.log(\"❌ Method 1 failed:\", err);\n                }\n                // Method 2: Try with different category variations if method 1 failed\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products = categoryData.products) === null || _categoryData_products === void 0 ? void 0 : (_categoryData_products_nodes = _categoryData_products.nodes) === null || _categoryData_products_nodes === void 0 ? void 0 : _categoryData_products_nodes.length)) {\n                    const alternativeNames = [\n                        \"shirt\",\n                        \"Shirts\",\n                        \"SHIRTS\",\n                        \"men-shirts\",\n                        \"mens-shirts\",\n                        \"clothing\",\n                        \"apparel\"\n                    ];\n                    for (const altName of alternativeNames){\n                        try {\n                            var _categoryData_products_nodes6, _categoryData_products8;\n                            console.log('\\uD83D\\uDCCB Attempting to fetch with category: \"'.concat(altName, '\"'));\n                            categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(altName, {\n                                first: 100\n                            });\n                            fetchMethod = \"slug: \".concat(altName);\n                            if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products8 = categoryData.products) === null || _categoryData_products8 === void 0 ? void 0 : (_categoryData_products_nodes6 = _categoryData_products8.nodes) === null || _categoryData_products_nodes6 === void 0 ? void 0 : _categoryData_products_nodes6.length) > 0) {\n                                console.log(\"✅ Success with alternative name: \".concat(altName));\n                                break;\n                            } else {\n                                console.log(\"⚠️ No products found for category: \".concat(altName));\n                            }\n                        } catch (err) {\n                            console.log(\"❌ Failed with \".concat(altName, \":\"), err);\n                        }\n                    }\n                }\n                // Method 3: Try to find the correct category from the list of all categories\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products1 = categoryData.products) === null || _categoryData_products1 === void 0 ? void 0 : (_categoryData_products_nodes1 = _categoryData_products1.nodes) === null || _categoryData_products_nodes1 === void 0 ? void 0 : _categoryData_products_nodes1.length) && (allCategories === null || allCategories === void 0 ? void 0 : allCategories.length) > 0) {\n                    console.log(\"\\uD83D\\uDCCB Searching for shirt-related categories in available categories...\");\n                    const shirtCategory = allCategories.find((cat)=>{\n                        var _cat_name, _cat_slug;\n                        const name = ((_cat_name = cat.name) === null || _cat_name === void 0 ? void 0 : _cat_name.toLowerCase()) || \"\";\n                        const slug = ((_cat_slug = cat.slug) === null || _cat_slug === void 0 ? void 0 : _cat_slug.toLowerCase()) || \"\";\n                        return name.includes(\"shirt\") || slug.includes(\"shirt\") || name.includes(\"clothing\") || slug.includes(\"clothing\") || name.includes(\"apparel\") || slug.includes(\"apparel\");\n                    });\n                    if (shirtCategory) {\n                        console.log(\"\\uD83D\\uDCCB Found potential shirt category: \".concat(shirtCategory.name, \" (\").concat(shirtCategory.slug, \")\"));\n                        try {\n                            var _categoryData_products_nodes7, _categoryData_products9;\n                            categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(shirtCategory.slug, {\n                                first: 100\n                            });\n                            fetchMethod = \"found category: \".concat(shirtCategory.slug);\n                            if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products9 = categoryData.products) === null || _categoryData_products9 === void 0 ? void 0 : (_categoryData_products_nodes7 = _categoryData_products9.nodes) === null || _categoryData_products_nodes7 === void 0 ? void 0 : _categoryData_products_nodes7.length) > 0) {\n                                console.log(\"✅ Success with found category: \".concat(shirtCategory.slug));\n                            }\n                        } catch (err) {\n                            console.log(\"❌ Failed with found category \".concat(shirtCategory.slug, \":\"), err);\n                        }\n                    }\n                }\n                // Method 4: If still no results, try fetching all products and filter by keywords\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products2 = categoryData.products) === null || _categoryData_products2 === void 0 ? void 0 : (_categoryData_products_nodes2 = _categoryData_products2.nodes) === null || _categoryData_products_nodes2 === void 0 ? void 0 : _categoryData_products_nodes2.length)) {\n                    try {\n                        console.log(\"\\uD83D\\uDCCB Attempting to fetch all products and filter by keywords...\");\n                        const { getAllProducts } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                        const allProducts = await getAllProducts(100);\n                        fetchMethod = \"all products filtered by keywords\";\n                        if ((allProducts === null || allProducts === void 0 ? void 0 : allProducts.length) > 0) {\n                            // Filter products that might be shirts\n                            const filteredProducts = allProducts.filter((product)=>{\n                                var _product_name, _product_title, _product_description, _product_shortDescription, _product_productCategories;\n                                const title = ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase()) || ((_product_title = product.title) === null || _product_title === void 0 ? void 0 : _product_title.toLowerCase()) || \"\";\n                                const description = ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase()) || ((_product_shortDescription = product.shortDescription) === null || _product_shortDescription === void 0 ? void 0 : _product_shortDescription.toLowerCase()) || \"\";\n                                const categories = ((_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : _product_productCategories.nodes) || product.categories || [];\n                                // Check if product title or description contains shirt-related keywords\n                                const shirtKeywords = [\n                                    \"shirt\",\n                                    \"formal\",\n                                    \"casual\",\n                                    \"dress\",\n                                    \"button\",\n                                    \"collar\",\n                                    \"sleeve\"\n                                ];\n                                const hasShirtKeyword = shirtKeywords.some((keyword)=>title.includes(keyword) || description.includes(keyword));\n                                // Check if product belongs to shirts category\n                                const hasShirtCategory = categories.some((cat)=>{\n                                    var _cat_name, _cat_slug;\n                                    const catName = ((_cat_name = cat.name) === null || _cat_name === void 0 ? void 0 : _cat_name.toLowerCase()) || ((_cat_slug = cat.slug) === null || _cat_slug === void 0 ? void 0 : _cat_slug.toLowerCase()) || \"\";\n                                    return catName.includes(\"shirt\") || catName.includes(\"clothing\") || catName.includes(\"apparel\");\n                                });\n                                return hasShirtKeyword || hasShirtCategory;\n                            });\n                            // Create a mock category structure\n                            categoryData = {\n                                products: {\n                                    nodes: filteredProducts\n                                }\n                            };\n                            console.log(\"✅ Filtered \".concat(filteredProducts.length, \" shirt products from all products\"));\n                        }\n                    } catch (err) {\n                        console.log(\"❌ Method 4 failed:\", err);\n                    }\n                }\n                // Set debug information\n                setDebugInfo({\n                    fetchMethod,\n                    totalProducts: (categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products3 = categoryData.products) === null || _categoryData_products3 === void 0 ? void 0 : (_categoryData_products_nodes3 = _categoryData_products3.nodes) === null || _categoryData_products_nodes3 === void 0 ? void 0 : _categoryData_products_nodes3.length) || 0,\n                    connectionTest: connectionTest || \"No connection test performed\",\n                    availableCategories: (allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>({\n                            name: cat.name,\n                            slug: cat.slug,\n                            count: cat.count\n                        }))) || [],\n                    categoryData: categoryData ? JSON.stringify(categoryData, null, 2) : \"No data\",\n                    timestamp: new Date().toISOString()\n                });\n                console.log(\"\\uD83D\\uDCCA Debug Info:\", {\n                    fetchMethod,\n                    totalProducts: (categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products4 = categoryData.products) === null || _categoryData_products4 === void 0 ? void 0 : (_categoryData_products_nodes4 = _categoryData_products4.nodes) === null || _categoryData_products_nodes4 === void 0 ? void 0 : _categoryData_products_nodes4.length) || 0,\n                    hasData: !!categoryData,\n                    hasProducts: !!(categoryData === null || categoryData === void 0 ? void 0 : categoryData.products),\n                    hasNodes: !!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products5 = categoryData.products) === null || _categoryData_products5 === void 0 ? void 0 : _categoryData_products5.nodes),\n                    availableCategories: (allCategories === null || allCategories === void 0 ? void 0 : allCategories.length) || 0\n                });\n                if (!categoryData || !((_categoryData_products6 = categoryData.products) === null || _categoryData_products6 === void 0 ? void 0 : _categoryData_products6.nodes) || categoryData.products.nodes.length === 0) {\n                    console.log(\"❌ No shirt products found in any category\");\n                    setError(\"No shirt products found using method: \".concat(fetchMethod, \". Available categories: \").concat((allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>cat.name).join(\", \")) || \"None found\", \". Please check your WooCommerce shirts category setup.\"));\n                    setIsLoading(false);\n                    return;\n                }\n                const allProducts = categoryData.products.nodes;\n                console.log(\"\\uD83D\\uDCE6 Found \".concat(allProducts.length, \" products, normalizing...\"));\n                // Normalize the products\n                const transformedProducts = allProducts.map((product, index)=>{\n                    try {\n                        console.log(\"\\uD83D\\uDD04 Normalizing product \".concat(index + 1, \":\"), product.name || product.title);\n                        const normalizedProduct = (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.normalizeProduct)(product);\n                        if (normalizedProduct) {\n                            // Ensure currencyCode is included\n                            normalizedProduct.currencyCode = \"INR\";\n                            console.log(\"✅ Successfully normalized: \".concat(normalizedProduct.title));\n                            return normalizedProduct;\n                        } else {\n                            console.log(\"⚠️ Failed to normalize product: \".concat(product.name || product.title));\n                            return null;\n                        }\n                    } catch (err) {\n                        console.error(\"❌ Error normalizing product \".concat(index + 1, \":\"), err);\n                        return null;\n                    }\n                }).filter(Boolean);\n                console.log(\"\\uD83C\\uDF89 Successfully processed \".concat(transformedProducts.length, \" shirt products\"));\n                console.log(\"\\uD83D\\uDCE6 Setting products:\", transformedProducts.map((p)=>{\n                    var _p_priceRange_minVariantPrice, _p_priceRange;\n                    return {\n                        title: p.title,\n                        price: (_p_priceRange = p.priceRange) === null || _p_priceRange === void 0 ? void 0 : (_p_priceRange_minVariantPrice = _p_priceRange.minVariantPrice) === null || _p_priceRange_minVariantPrice === void 0 ? void 0 : _p_priceRange_minVariantPrice.amount,\n                        id: p.id\n                    };\n                }));\n                setProducts(transformedProducts);\n            } catch (err) {\n                console.error(\"\\uD83D\\uDCA5 Critical error fetching products:\", err);\n                setError(\"Failed to load products from WooCommerce: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchProducts();\n    }, []);\n    // Toggle filter drawer\n    const toggleFilter = ()=>{\n        setIsFilterOpen(!isFilterOpen);\n    };\n    // No filtering - show all products\n    const sortedProducts = products;\n    // Animation variants\n    const fadeIn = {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: 20,\n            transition: {\n                duration: 0.3\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#f8f8f5] pt-8 pb-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 mb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-serif font-bold mb-4 text-[#2c2c27]\",\n                            children: \"Shirts Collection\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-[#5c5c52] mb-8\",\n                            children: \"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-[300px] mb-16 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80\",\n                        alt: \"Ankkor Shirts Collection\",\n                        fill: true,\n                        sizes: \"(max-width: 768px) 100vw, 50vw\",\n                        className: \"object-cover image-animate\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-4\",\n                                    children: \"Signature Shirts\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg max-w-xl mx-auto\",\n                                    children: \"Impeccably tailored for the perfect fit\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-semibold\",\n                                children: \"Error loading shirts:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: \"Please check your WooCommerce configuration and ensure you have products in the 'shirts' category.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            debugInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-sm font-semibold\",\n                                        children: \"Debug Information\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40\",\n                                        children: JSON.stringify(debugInfo, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#2c2c27]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-[#5c5c52]\",\n                                children: \"Loading shirts...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end items-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[#5c5c52] text-sm\",\n                            children: [\n                                sortedProducts.length,\n                                \" products\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    isFilterOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black bg-opacity-50\",\n                                onClick: toggleFilter\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-serif text-lg text-[#2c2c27]\",\n                                                children: \"Filter & Sort\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleFilter,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-[#2c2c27]\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-[#8a8778] text-xs uppercase tracking-wider mb-4\",\n                                                children: \"Price Range\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-[#5c5c52] text-sm\",\n                                                                children: [\n                                                                    (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(\"INR\"),\n                                                                    priceRange[0]\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-[#5c5c52] text-sm\",\n                                                                children: [\n                                                                    (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(\"INR\"),\n                                                                    priceRange[1]\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        min: \"0\",\n                                                        max: \"25000\",\n                                                        value: priceRange[1],\n                                                        onChange: (e)=>setPriceRange([\n                                                                priceRange[0],\n                                                                parseInt(e.target.value)\n                                                            ]),\n                                                        className: \"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-[#8a8778] text-xs uppercase tracking-wider mb-4\",\n                                                children: \"Sort By\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: \"featured\",\n                                                        name: \"Featured\"\n                                                    },\n                                                    {\n                                                        id: \"price-asc\",\n                                                        name: \"Price: Low to High\"\n                                                    },\n                                                    {\n                                                        id: \"price-desc\",\n                                                        name: \"Price: High to Low\"\n                                                    },\n                                                    {\n                                                        id: \"rating\",\n                                                        name: \"Alphabetical\"\n                                                    },\n                                                    {\n                                                        id: \"newest\",\n                                                        name: \"Newest\"\n                                                    }\n                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSortOption(option.id),\n                                                        className: \"block w-full text-left py-1 \".concat(sortOption === option.id ? \"text-[#2c2c27] font-medium\" : \"text-[#5c5c52]\"),\n                                                        children: option.name\n                                                    }, option.id, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleFilter,\n                                        className: \"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider\",\n                                        children: \"Apply Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block w-64 shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-[#2c2c27] font-serif text-lg mb-6\",\n                                                    children: \"Price Range\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#5c5c52]\",\n                                                                    children: [\n                                                                        (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(\"INR\"),\n                                                                        priceRange[0]\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#5c5c52]\",\n                                                                    children: [\n                                                                        (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(\"INR\"),\n                                                                        priceRange[1]\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"0\",\n                                                            max: \"25000\",\n                                                            value: priceRange[1],\n                                                            onChange: (e)=>setPriceRange([\n                                                                    priceRange[0],\n                                                                    parseInt(e.target.value)\n                                                                ]),\n                                                            className: \"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-[#2c2c27] font-serif text-lg mb-6\",\n                                                    children: \"Sort By\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        {\n                                                            id: \"featured\",\n                                                            name: \"Featured\"\n                                                        },\n                                                        {\n                                                            id: \"price-asc\",\n                                                            name: \"Price: Low to High\"\n                                                        },\n                                                        {\n                                                            id: \"price-desc\",\n                                                            name: \"Price: High to Low\"\n                                                        },\n                                                        {\n                                                            id: \"rating\",\n                                                            name: \"Alphabetical\"\n                                                        },\n                                                        {\n                                                            id: \"newest\",\n                                                            name: \"Newest\"\n                                                        }\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSortOption(option.id),\n                                                            className: \"block w-full text-left py-1 \".concat(sortOption === option.id ? \"text-[#2c2c27] font-medium\" : \"text-[#5c5c52] hover:text-[#2c2c27] transition-colors\"),\n                                                            children: option.name\n                                                        }, option.id, false, {\n                                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex justify-between items-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-[#2c2c27] font-serif text-xl\",\n                                                children: \"Shirts Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[#5c5c52]\",\n                                                children: [\n                                                    sortedProducts.length,\n                                                    \" products\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isLoading && sortedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: sortedProducts.map((product)=>{\n                                            var _product__originalWooProduct, _product__originalWooProduct1, _product_priceRange_minVariantPrice, _product_priceRange, _product_images_, _product__originalWooProduct2, _product__originalWooProduct3, _product__originalWooProduct4, _product__originalWooProduct5, _product__originalWooProduct6, _product__originalWooProduct7;\n                                            // Extract and validate the variant ID for the product\n                                            let variantId = \"\";\n                                            let isValidVariant = false;\n                                            try {\n                                                // Check if variants exist and extract the first variant ID\n                                                if (product.variants && product.variants.length > 0) {\n                                                    const variant = product.variants[0];\n                                                    if (variant && variant.id) {\n                                                        variantId = variant.id;\n                                                        isValidVariant = true;\n                                                        // Ensure the variant ID is properly formatted\n                                                        if (!variantId.startsWith(\"gid://shopify/ProductVariant/\")) {\n                                                            // Extract numeric ID if possible and reformat\n                                                            const numericId = variantId.replace(/\\D/g, \"\");\n                                                            if (numericId) {\n                                                                variantId = \"gid://shopify/ProductVariant/\".concat(numericId);\n                                                            } else {\n                                                                console.warn(\"Cannot parse variant ID for product \".concat(product.title, \": \").concat(variantId));\n                                                                isValidVariant = false;\n                                                            }\n                                                        }\n                                                        console.log(\"Product \".concat(product.title, \" using variant ID: \").concat(variantId));\n                                                    }\n                                                }\n                                                // If no valid variant ID found, try to create a fallback from product ID\n                                                if (!isValidVariant && product.id) {\n                                                    // Only attempt fallback if product ID has a numeric component\n                                                    if (product.id.includes(\"/\")) {\n                                                        const parts = product.id.split(\"/\");\n                                                        const numericId = parts[parts.length - 1];\n                                                        if (numericId && /^\\d+$/.test(numericId)) {\n                                                            // Create a fallback ID - note this might not work if variants aren't 1:1 with products\n                                                            variantId = \"gid://shopify/ProductVariant/\".concat(numericId);\n                                                            console.warn(\"Using fallback variant ID for \".concat(product.title, \": \").concat(variantId));\n                                                            isValidVariant = true;\n                                                        }\n                                                    }\n                                                }\n                                            } catch (error) {\n                                                console.error(\"Error processing variant for product \".concat(product.title, \":\"), error);\n                                                isValidVariant = false;\n                                            }\n                                            // If we couldn't find a valid variant ID, log an error\n                                            if (!isValidVariant) {\n                                                console.error(\"No valid variant ID found for product: \".concat(product.title));\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                variants: fadeIn,\n                                                initial: \"initial\",\n                                                animate: \"animate\",\n                                                exit: \"exit\",\n                                                layout: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    id: product.id,\n                                                    name: product.title,\n                                                    slug: product.handle,\n                                                    price: ((_product__originalWooProduct = product._originalWooProduct) === null || _product__originalWooProduct === void 0 ? void 0 : _product__originalWooProduct.salePrice) || ((_product__originalWooProduct1 = product._originalWooProduct) === null || _product__originalWooProduct1 === void 0 ? void 0 : _product__originalWooProduct1.price) || ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : (_product_priceRange_minVariantPrice = _product_priceRange.minVariantPrice) === null || _product_priceRange_minVariantPrice === void 0 ? void 0 : _product_priceRange_minVariantPrice.amount) || \"0\",\n                                                    image: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"\",\n                                                    material: (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getMetafield)(product, \"custom_material\", undefined, \"Premium Fabric\"),\n                                                    isNew: true,\n                                                    stockStatus: ((_product__originalWooProduct2 = product._originalWooProduct) === null || _product__originalWooProduct2 === void 0 ? void 0 : _product__originalWooProduct2.stockStatus) || \"IN_STOCK\",\n                                                    compareAtPrice: product.compareAtPrice,\n                                                    regularPrice: (_product__originalWooProduct3 = product._originalWooProduct) === null || _product__originalWooProduct3 === void 0 ? void 0 : _product__originalWooProduct3.regularPrice,\n                                                    salePrice: (_product__originalWooProduct4 = product._originalWooProduct) === null || _product__originalWooProduct4 === void 0 ? void 0 : _product__originalWooProduct4.salePrice,\n                                                    onSale: ((_product__originalWooProduct5 = product._originalWooProduct) === null || _product__originalWooProduct5 === void 0 ? void 0 : _product__originalWooProduct5.onSale) || false,\n                                                    currencySymbol: (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(product.currencyCode),\n                                                    currencyCode: product.currencyCode || \"INR\",\n                                                    shortDescription: (_product__originalWooProduct6 = product._originalWooProduct) === null || _product__originalWooProduct6 === void 0 ? void 0 : _product__originalWooProduct6.shortDescription,\n                                                    type: (_product__originalWooProduct7 = product._originalWooProduct) === null || _product__originalWooProduct7 === void 0 ? void 0 : _product__originalWooProduct7.type\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isLoading && sortedProducts.length === 0 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#5c5c52] mb-4\",\n                                                children: \"No products found with the selected filters.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setPriceRange([\n                                                        0,\n                                                        25000\n                                                    ]);\n                                                },\n                                                className: \"text-[#2c2c27] underline\",\n                                                children: \"Reset filters\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(ShirtsCollectionPage, \"UeqlTi8Y7TubAWgfFuSzUjYWVrE=\", false, function() {\n    return [\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = ShirtsCollectionPage;\nvar _c;\n$RefreshReg$(_c, \"ShirtsCollectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/shirts/page.tsx\n"));

/***/ })

}]);