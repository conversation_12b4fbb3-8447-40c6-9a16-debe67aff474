"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_ap"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-announcer.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppRouterAnnouncer\", ({\n    enumerable: true,\n    get: function() {\n        return AppRouterAnnouncer;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst ANNOUNCER_TYPE = \"next-route-announcer\";\nconst ANNOUNCER_ID = \"__next-route-announcer__\";\nfunction getAnnouncerNode() {\n    var _existingAnnouncer_shadowRoot;\n    const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0];\n    if (existingAnnouncer == null ? void 0 : (_existingAnnouncer_shadowRoot = existingAnnouncer.shadowRoot) == null ? void 0 : _existingAnnouncer_shadowRoot.childNodes[0]) {\n        return existingAnnouncer.shadowRoot.childNodes[0];\n    } else {\n        const container = document.createElement(ANNOUNCER_TYPE);\n        container.style.cssText = \"position:absolute\";\n        const announcer = document.createElement(\"div\");\n        announcer.ariaLive = \"assertive\";\n        announcer.id = ANNOUNCER_ID;\n        announcer.role = \"alert\";\n        announcer.style.cssText = \"position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal\";\n        // Use shadow DOM here to avoid any potential CSS bleed\n        const shadow = container.attachShadow({\n            mode: \"open\"\n        });\n        shadow.appendChild(announcer);\n        document.body.appendChild(container);\n        return announcer;\n    }\n}\nfunction AppRouterAnnouncer(param) {\n    let { tree } = param;\n    const [portalNode, setPortalNode] = (0, _react.useState)(null);\n    (0, _react.useEffect)(()=>{\n        const announcer = getAnnouncerNode();\n        setPortalNode(announcer);\n        return ()=>{\n            const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0];\n            if (container == null ? void 0 : container.isConnected) {\n                document.body.removeChild(container);\n            }\n        };\n    }, []);\n    const [routeAnnouncement, setRouteAnnouncement] = (0, _react.useState)(\"\");\n    const previousTitle = (0, _react.useRef)();\n    (0, _react.useEffect)(()=>{\n        let currentTitle = \"\";\n        if (document.title) {\n            currentTitle = document.title;\n        } else {\n            const pageHeader = document.querySelector(\"h1\");\n            if (pageHeader) {\n                currentTitle = pageHeader.innerText || pageHeader.textContent || \"\";\n            }\n        }\n        // Only announce the title change, but not for the first load because screen\n        // readers do that automatically.\n        if (previousTitle.current !== undefined && previousTitle.current !== currentTitle) {\n            setRouteAnnouncement(currentTitle);\n        }\n        previousTitle.current = currentTitle;\n    }, [\n        tree\n    ]);\n    return portalNode ? /*#__PURE__*/ (0, _reactdom.createPortal)(routeAnnouncement, portalNode) : null;\n}\n_c = AppRouterAnnouncer;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-announcer.js.map\nvar _c;\n$RefreshReg$(_c, \"AppRouterAnnouncer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-headers.js ***!
  \************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION: function() {\n        return ACTION;\n    },\n    FLIGHT_PARAMETERS: function() {\n        return FLIGHT_PARAMETERS;\n    },\n    NEXT_DID_POSTPONE_HEADER: function() {\n        return NEXT_DID_POSTPONE_HEADER;\n    },\n    NEXT_ROUTER_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_STATE_TREE: function() {\n        return NEXT_ROUTER_STATE_TREE;\n    },\n    NEXT_RSC_UNION_QUERY: function() {\n        return NEXT_RSC_UNION_QUERY;\n    },\n    NEXT_URL: function() {\n        return NEXT_URL;\n    },\n    RSC_CONTENT_TYPE_HEADER: function() {\n        return RSC_CONTENT_TYPE_HEADER;\n    },\n    RSC_HEADER: function() {\n        return RSC_HEADER;\n    }\n});\nconst RSC_HEADER = \"RSC\";\nconst ACTION = \"Next-Action\";\nconst NEXT_ROUTER_STATE_TREE = \"Next-Router-State-Tree\";\nconst NEXT_ROUTER_PREFETCH_HEADER = \"Next-Router-Prefetch\";\nconst NEXT_URL = \"Next-Url\";\nconst RSC_CONTENT_TYPE_HEADER = \"text/x-component\";\nconst FLIGHT_PARAMETERS = [\n    [\n        RSC_HEADER\n    ],\n    [\n        NEXT_ROUTER_STATE_TREE\n    ],\n    [\n        NEXT_ROUTER_PREFETCH_HEADER\n    ]\n];\nconst NEXT_RSC_UNION_QUERY = \"_rsc\";\nconst NEXT_DID_POSTPONE_HEADER = \"x-nextjs-postponed\";\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-headers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUNhQSxRQUFNO2VBQU5BOztJQU9BQyxtQkFBaUI7ZUFBakJBOztJQVFBQywwQkFBd0I7ZUFBeEJBOztJQVpBQyw2QkFBMkI7ZUFBM0JBOztJQURBQyx3QkFBc0I7ZUFBdEJBOztJQVdBQyxzQkFBb0I7ZUFBcEJBOztJQVRBQyxVQUFRO2VBQVJBOztJQUNBQyx5QkFBdUI7ZUFBdkJBOztJQU5BQyxZQUFVO2VBQVZBOzs7QUFBTixNQUFNQSxhQUFhO0FBQ25CLE1BQU1SLFNBQVM7QUFFZixNQUFNSSx5QkFBeUI7QUFDL0IsTUFBTUQsOEJBQThCO0FBQ3BDLE1BQU1HLFdBQVc7QUFDakIsTUFBTUMsMEJBQTBCO0FBRWhDLE1BQU1OLG9CQUFvQjtJQUMvQjtRQUFDTztLQUFXO0lBQ1o7UUFBQ0o7S0FBdUI7SUFDeEI7UUFBQ0Q7S0FBNEI7Q0FDOUI7QUFFTSxNQUFNRSx1QkFBdUI7QUFFN0IsTUFBTUgsMkJBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1oZWFkZXJzLnRzPzY0OTMiXSwibmFtZXMiOlsiQUNUSU9OIiwiRkxJR0hUX1BBUkFNRVRFUlMiLCJORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIiLCJORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVIiLCJORVhUX1JPVVRFUl9TVEFURV9UUkVFIiwiTkVYVF9SU0NfVU5JT05fUVVFUlkiLCJORVhUX1VSTCIsIlJTQ19DT05URU5UX1RZUEVfSEVBREVSIiwiUlNDX0hFQURFUiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    getServerActionDispatcher: function() {\n        return getServerActionDispatcher;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducerwithdevtools = __webpack_require__(/*! ./use-reducer-with-devtools */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer-with-devtools.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _createinitialrouterstate = __webpack_require__(/*! ./router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _approuterheaders = __webpack_require__(/*! ./app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _segment = __webpack_require__(/*! ../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst isServer = typeof window === \"undefined\";\n// Ensure the initialParallelRoutes are not combined because of double-rendering in the browser with Strict Mode.\nlet initialParallelRoutes = isServer ? null : new Map();\nlet globalServerActionDispatcher = null;\nfunction getServerActionDispatcher() {\n    return globalServerActionDispatcher;\n}\nconst globalMutable = {};\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\n// this function performs a depth-first search of the tree to find the selected\n// params\nfunction getSelectedParams(currentTree, params) {\n    if (params === void 0) params = {};\n    const parallelRoutes = currentTree[1];\n    for (const parallelRoute of Object.values(parallelRoutes)){\n        const segment = parallelRoute[0];\n        const isDynamicParameter = Array.isArray(segment);\n        const segmentValue = isDynamicParameter ? segment[1] : segment;\n        if (!segmentValue || segmentValue.startsWith(_segment.PAGE_SEGMENT_KEY)) continue;\n        // Ensure catchAll and optional catchall are turned into an array\n        const isCatchAll = isDynamicParameter && (segment[2] === \"c\" || segment[2] === \"oc\");\n        if (isCatchAll) {\n            params[segment[0]] = segment[1].split(\"/\");\n        } else if (isDynamicParameter) {\n            params[segment[0]] = segment[1];\n        }\n        params = getSelectedParams(parallelRoute, params);\n    }\n    return params;\n}\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState, sync } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, \"\", canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, \"\", canonicalUrl);\n        }\n        sync(appRouterState);\n    }, [\n        appRouterState,\n        sync\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        lazyDataResolved: false,\n        loading: null\n    };\n}\nfunction useServerActionDispatcher(dispatch) {\n    const serverActionDispatcher = (0, _react.useCallback)((actionPayload)=>{\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                ...actionPayload,\n                type: _routerreducertypes.ACTION_SERVER_ACTION\n            });\n        });\n    }, [\n        dispatch\n    ]);\n    globalServerActionDispatcher = serverActionDispatcher;\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    //\n    // @ts-expect-error The second argument to `useDeferredValue` is only\n    // available in the experimental builds. When its disabled, it will always\n    // return `head`.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { buildId, initialHead, initialTree, urlParts, initialSeedData, couldBeIntercepted, assetPrefix, missingSlots } = param;\n    const initialState = (0, _react.useMemo)(()=>(0, _createinitialrouterstate.createInitialRouterState)({\n            buildId,\n            initialSeedData,\n            urlParts,\n            initialTree,\n            initialParallelRoutes,\n            location: !isServer ? window.location : null,\n            initialHead,\n            couldBeIntercepted\n        }), [\n        buildId,\n        initialSeedData,\n        urlParts,\n        initialTree,\n        initialHead,\n        couldBeIntercepted\n    ]);\n    const [reducerState, dispatch, sync] = (0, _usereducerwithdevtools.useReducerWithReduxDevtools)(initialState);\n    (0, _react.useEffect)(()=>{\n        // Ensure initialParallelRoutes is cleaned up from memory once it's used.\n        initialParallelRoutes = null;\n    }, []);\n    const { canonicalUrl } = (0, _usereducerwithdevtools.useUnwrapState)(reducerState);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl, typeof window === \"undefined\" ? \"http://n\" : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    useServerActionDispatcher(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch: (href, options)=>{\n                // Don't prefetch for bots as they don't navigate.\n                if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n                    return;\n                }\n                let url;\n                try {\n                    url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n                } catch (_) {\n                    throw new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n                }\n                // Don't prefetch during development (improves compilation performance)\n                if (true) {\n                    return;\n                }\n                // External urls can't be prefetched in the same way.\n                if (isExternalURL(url)) {\n                    return;\n                }\n                (0, _react.startTransition)(()=>{\n                    var _options_kind;\n                    dispatch({\n                        type: _routerreducertypes.ACTION_PREFETCH,\n                        url,\n                        kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                    });\n                });\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, \"replace\", (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, \"push\", (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            fastRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_FAST_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducerwithdevtools.useUnwrapState)(reducerState);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener(\"pageshow\", handlePageShow);\n        return ()=>{\n            window.removeEventListener(\"pageshow\", handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducerwithdevtools.useUnwrapState)(reducerState);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (param)=>{\n            let { state } = param;\n            if (!state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener(\"popstate\", onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener(\"popstate\", onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducerwithdevtools.useUnwrapState)(reducerState);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return getSelectedParams(tree);\n    }, [\n        tree\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        if (typeof window !== \"undefined\") {\n            const DevRootNotFoundBoundary = (__webpack_require__(/*! ./dev-root-not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js\").DevRootNotFoundBoundary);\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootNotFoundBoundary, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext.Provider, {\n                    value: missingSlots,\n                    children: content\n                })\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            children: content\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducerwithdevtools.useUnwrapState)(reducerState),\n                sync: sync\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: {\n                                buildId,\n                                changeByServerResponse,\n                                tree,\n                                focusAndScrollRef,\n                                nextUrl\n                            },\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: {\n                                        childNodes: cache.parallelRoutes,\n                                        tree,\n                                        // Root node always has `url`\n                                        // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n                                        url: canonicalUrl,\n                                        loading: cache.loading\n                                    },\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"q0MfV9e9/a1iyE4/7qfUaQkhGE8=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate,\n        useServerActionDispatcher\n    ];\n});\n_c2 = Router;\nfunction AppRouter(props) {\n    const { globalErrorComponent, ...rest } = props;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        errorComponent: globalErrorComponent,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            ...rest\n        })\n    });\n}\n_c3 = AppRouter;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/async-local-storage.js ***!
  \*************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createAsyncLocalStorage\", ({\n    enumerable: true,\n    get: function() {\n        return createAsyncLocalStorage;\n    }\n}));\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\"Invariant: AsyncLocalStorage accessed in runtime where it is not available\");\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=async-local-storage.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FzeW5jLWxvY2FsLXN0b3JhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7OzsyREFpQ2dCQTs7O2VBQUFBOzs7QUEvQmhCLE1BQU1DLDJDQUEyQyxJQUFJQyxNQUNuRDtBQUdGLE1BQU1DO0lBR0pDLFVBQWdCO1FBQ2QsTUFBTUg7SUFDUjtJQUVBSSxXQUE4QjtRQUM1Qiw0RUFBNEU7UUFDNUUsT0FBT0M7SUFDVDtJQUVBQyxNQUFZO1FBQ1YsTUFBTU47SUFDUjtJQUVBTyxPQUFhO1FBQ1gsTUFBTVA7SUFDUjtJQUVBUSxZQUFrQjtRQUNoQixNQUFNUjtJQUNSO0FBQ0Y7QUFFQSxNQUFNUywrQkFBK0JDLFdBQW9CQyxpQkFBaUI7QUFFbkUsU0FBU1o7SUFHZCxJQUFJVSw4QkFBOEI7UUFDaEMsT0FBTyxJQUFJQTtJQUNiO0lBQ0EsT0FBTyxJQUFJUDtBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvYXN5bmMtbG9jYWwtc3RvcmFnZS50cz83NzUyIl0sIm5hbWVzIjpbImNyZWF0ZUFzeW5jTG9jYWxTdG9yYWdlIiwic2hhcmVkQXN5bmNMb2NhbFN0b3JhZ2VOb3RBdmFpbGFibGVFcnJvciIsIkVycm9yIiwiRmFrZUFzeW5jTG9jYWxTdG9yYWdlIiwiZGlzYWJsZSIsImdldFN0b3JlIiwidW5kZWZpbmVkIiwicnVuIiwiZXhpdCIsImVudGVyV2l0aCIsIm1heWJlR2xvYmFsQXN5bmNMb2NhbFN0b3JhZ2UiLCJnbG9iYWxUaGlzIiwiQXN5bmNMb2NhbFN0b3JhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/async-local-storage.js\n"));

/***/ })

}]);