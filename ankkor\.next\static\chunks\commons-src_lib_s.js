"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_s"],{

/***/ "(app-pages-browser)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCartStore: function() { return /* binding */ useCartStore; },\n/* harmony export */   useWishlistStore: function() { return /* binding */ useWishlistStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _woocommerce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n\n\n\n// Safe localStorage operation that won't cause errors during SSR\nconst safeLocalStorage = {\n    getItem: (name)=>{\n        if (false) {}\n        try {\n            return localStorage.getItem(name);\n        } catch (error) {\n            console.error(\"localStorage.getItem error:\", error);\n            return null;\n        }\n    },\n    setItem: (name, value)=>{\n        if (false) {}\n        try {\n            localStorage.setItem(name, value);\n        } catch (error) {\n            console.error(\"localStorage.setItem error:\", error);\n        }\n    },\n    removeItem: (name)=>{\n        if (false) {}\n        try {\n            localStorage.removeItem(name);\n        } catch (error) {\n            console.error(\"localStorage.removeItem error:\", error);\n        }\n    }\n};\n// Helper function to safely update cart state\nconst updateCartState = (set, normalizedCart)=>{\n    try {\n        if (!normalizedCart || !normalizedCart.lines) {\n            console.error(\"Invalid normalized cart data\", normalizedCart);\n            return;\n        }\n        const itemCount = normalizedCart.lines.reduce((acc, line)=>acc + (line.quantity || 0), 0);\n        const items = normalizedCart.lines.map((line)=>{\n            var _line_merchandise_product_image;\n            return {\n                id: line.id,\n                variantId: line.merchandise.id,\n                productId: line.merchandise.product.id,\n                title: line.merchandise.product.title,\n                handle: line.merchandise.product.handle,\n                image: ((_line_merchandise_product_image = line.merchandise.product.image) === null || _line_merchandise_product_image === void 0 ? void 0 : _line_merchandise_product_image.url) || \"\",\n                price: line.merchandise.price,\n                quantity: line.quantity,\n                currencyCode: line.merchandise.currencyCode\n            };\n        });\n        set({\n            items,\n            subtotal: normalizedCart.cost.subtotalAmount.amount,\n            total: normalizedCart.cost.totalAmount.amount,\n            currencyCode: normalizedCart.cost.totalAmount.currencyCode,\n            itemCount,\n            checkoutUrl: normalizedCart.checkoutUrl,\n            isLoading: false\n        });\n    } catch (error) {\n        console.error(\"Error updating cart state:\", error);\n        // Fallback to clearing state but keeping cart ID\n        set({\n            items: [],\n            subtotal: \"0.00\",\n            total: \"0.00\",\n            itemCount: 0,\n            isLoading: false\n        });\n    }\n};\nconst useCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        cartId: null,\n        items: [],\n        isOpen: false,\n        isLoading: false,\n        subtotal: \"0.00\",\n        total: \"0.00\",\n        currencyCode: \"USD\",\n        itemCount: 0,\n        checkoutUrl: null,\n        initializationInProgress: false,\n        initializationError: null,\n        openCart: ()=>set({\n                isOpen: true\n            }),\n        closeCart: ()=>set({\n                isOpen: false\n            }),\n        toggleCart: ()=>set((state)=>({\n                    isOpen: !state.isOpen\n                })),\n        initCart: async ()=>{\n            const state = get();\n            // Prevent multiple concurrent initialization\n            if (state.initializationInProgress) {\n                console.log(\"Cart initialization already in progress, skipping\");\n                return null;\n            }\n            set({\n                isLoading: true,\n                initializationInProgress: true,\n                initializationError: null\n            });\n            try {\n                // Check if we already have a cart ID\n                if (state.cartId) {\n                    // Validate the existing cart - note: getCart no longer needs cartId\n                    try {\n                        const existingCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCart)();\n                        if (existingCart) {\n                            set({\n                                isLoading: false,\n                                initializationInProgress: false\n                            });\n                            return existingCart;\n                        }\n                    } catch (error) {\n                        console.log(\"Existing cart validation failed, creating new cart\");\n                    // Fall through to create a new cart\n                    }\n                }\n                // Create a new cart\n                const newCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                if (newCart && newCart.id) {\n                    set({\n                        cartId: newCart.id,\n                        checkoutUrl: newCart.checkoutUrl,\n                        isLoading: false,\n                        initializationInProgress: false\n                    });\n                    console.log(\"Cart initialized with ID:\", newCart.id);\n                    return newCart;\n                }\n                throw new Error(\"Failed to create cart: No cart ID returned\");\n            } catch (error) {\n                console.error(\"Failed to initialize cart:\", error);\n                set({\n                    isLoading: false,\n                    initializationInProgress: false,\n                    initializationError: error instanceof Error ? error.message : \"Unknown error initializing cart\"\n                });\n                return null;\n            }\n        },\n        addItem: async (item)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // Validate essential item properties\n                if (!item.variantId) {\n                    console.error(\"Cannot add item to cart: Missing variant ID\", item);\n                    set({\n                        isLoading: false\n                    });\n                    throw new Error(\"Missing variant ID for item\");\n                }\n                let cartId = get().cartId;\n                if (!cartId) {\n                    console.log(\"Cart not initialized, creating a new cart...\");\n                    const newCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                    if (newCart && newCart.id) {\n                        console.log(\"New cart created:\", newCart.id);\n                        cartId = newCart.id;\n                    } else {\n                        throw new Error(\"Failed to initialize cart\");\n                    }\n                }\n                // At this point cartId should be a valid string\n                if (!cartId) {\n                    throw new Error(\"Failed to initialize cart: No cart ID available\");\n                }\n                // Log the variant ID for debugging\n                console.log(\"Adding item to cart: \".concat(item.title, \" (\").concat(item.variantId, \"), quantity: \").concat(item.quantity));\n                try {\n                    const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.addToCart)(cartId, [\n                        {\n                            merchandiseId: item.variantId,\n                            quantity: item.quantity || 1\n                        }\n                    ]);\n                    if (!cart) {\n                        throw new Error(\"Failed to add item to cart: No cart returned\");\n                    }\n                    // Normalize and update cart state\n                    const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                    updateCartState(set, normalizedCart);\n                    set({\n                        isOpen: true\n                    }); // Open cart when item is added\n                    console.log(\"Item added to cart successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n                } catch (apiError) {\n                    console.error(\"Shopify API error when adding to cart:\", apiError);\n                    // Re-throw with more context\n                    if (apiError instanceof Error) {\n                        throw new Error(\"Failed to add item to cart: \".concat(apiError.message));\n                    } else {\n                        throw new Error(\"Failed to add item to cart: Unknown API error\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to add item to cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        updateItem: async (id, quantity)=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                if (!state.cartId) {\n                    throw new Error(\"Cart not initialized\");\n                }\n                console.log(\"Updating item in cart: \".concat(id, \", new quantity: \").concat(quantity));\n                // If quantity is 0 or less, remove the item\n                if (quantity <= 0) {\n                    console.log(\"Quantity is \".concat(quantity, \", removing item from cart\"));\n                    return get().removeItem(id);\n                }\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.updateCart)(state.cartId, [\n                    {\n                        id,\n                        quantity\n                    }\n                ]);\n                if (!cart) {\n                    throw new Error(\"Failed to update item: No cart returned\");\n                }\n                // Normalize and update cart state\n                const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                updateCartState(set, normalizedCart);\n                console.log(\"Item updated successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n            } catch (error) {\n                console.error(\"Failed to update item in cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        removeItem: async (id)=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                if (!state.cartId) {\n                    console.error(\"Cannot remove item: Cart not initialized\");\n                    throw new Error(\"Cart not initialized\");\n                }\n                console.log(\"Removing item from cart: \".concat(id));\n                // Get current cart state for comparison\n                const beforeItems = [\n                    ...state.items\n                ];\n                const itemBeingRemoved = beforeItems.find((item)=>item.id === id);\n                if (!itemBeingRemoved) {\n                    console.warn(\"Item with ID \".concat(id, \" not found in cart\"));\n                } else {\n                    console.log('Removing \"'.concat(itemBeingRemoved.title, '\" (').concat(itemBeingRemoved.variantId, \") from cart\"));\n                }\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.removeFromCart)(state.cartId, [\n                    id\n                ]);\n                if (!cart) {\n                    console.error(\"Failed to remove item: No cart returned from Shopify\");\n                    throw new Error(\"Failed to remove item: No cart returned\");\n                }\n                // Normalize and update cart state\n                const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                // Get updated items for comparison\n                const afterRemovalItems = normalizedCart.lines.map((line)=>({\n                        id: line.id,\n                        title: line.merchandise.product.title\n                    }));\n                console.log(\"Cart before removal:\", beforeItems.length, \"items\");\n                console.log(\"Cart after removal:\", afterRemovalItems.length, \"items\");\n                if (beforeItems.length === afterRemovalItems.length) {\n                    console.warn(\"Item count did not change after removal operation\");\n                }\n                updateCartState(set, normalizedCart);\n                console.log(\"Item removed successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n            } catch (error) {\n                console.error(\"Failed to remove item from cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        clearCart: async ()=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                // When clearing the cart, we simply create a new empty cart in Shopify\n                // and update our local state to reflect that\n                console.log(\"Clearing cart and creating a new one\");\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                if (!cart) {\n                    throw new Error(\"Failed to create new cart\");\n                }\n                set({\n                    cartId: cart.id,\n                    items: [],\n                    subtotal: \"0.00\",\n                    total: \"0.00\",\n                    itemCount: 0,\n                    checkoutUrl: cart.checkoutUrl,\n                    isLoading: false\n                });\n                console.log(\"Cart cleared successfully. New cart ID:\", cart.id);\n            } catch (error) {\n                console.error(\"Failed to clear cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        }\n    }), {\n    name: \"ankkor-cart\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>safeLocalStorage),\n    version: 1,\n    partialize: (state)=>({\n            cartId: state.cartId,\n            items: state.items,\n            subtotal: state.subtotal,\n            total: state.total,\n            currencyCode: state.currencyCode,\n            itemCount: state.itemCount,\n            checkoutUrl: state.checkoutUrl\n        })\n}));\nconst useWishlistStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        items: [],\n        isLoading: false,\n        addToWishlist: (item)=>{\n            set((state)=>{\n                // Check if item already exists in wishlist\n                if (state.items.some((wishlistItem)=>wishlistItem.id === item.id)) {\n                    return state; // Item already exists, don't add it again\n                }\n                return {\n                    items: [\n                        ...state.items,\n                        item\n                    ]\n                };\n            });\n        },\n        removeFromWishlist: (id)=>{\n            set((state)=>({\n                    items: state.items.filter((item)=>item.id !== id)\n                }));\n        },\n        clearWishlist: ()=>{\n            set({\n                items: []\n            });\n        },\n        isInWishlist: (id)=>{\n            return get().items.some((item)=>item.id === id);\n        }\n    }), {\n    name: \"ankkor-wishlist\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>safeLocalStorage),\n    partialize: (state)=>({\n            items: state.items\n        }),\n    skipHydration: true\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/store.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: function() { return /* binding */ cn; }\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combines multiple class names into a single string, handling Tailwind CSS conflicts\n * @param inputs - Class names to be combined\n * @returns A merged class name string\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFeEM7Ozs7Q0FJQyxHQUNNLFNBQVNFO0lBQUc7UUFBR0MsT0FBSCx1QkFBdUI7O0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG4vKipcbiAqIENvbWJpbmVzIG11bHRpcGxlIGNsYXNzIG5hbWVzIGludG8gYSBzaW5nbGUgc3RyaW5nLCBoYW5kbGluZyBUYWlsd2luZCBDU1MgY29uZmxpY3RzXG4gKiBAcGFyYW0gaW5wdXRzIC0gQ2xhc3MgbmFtZXMgdG8gYmUgY29tYmluZWRcbiAqIEByZXR1cm5zIEEgbWVyZ2VkIGNsYXNzIG5hbWUgc3RyaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

}]);