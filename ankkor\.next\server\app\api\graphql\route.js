"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/graphql/route";
exports.ids = ["app/api/graphql/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/graphql/route.ts */ \"(rsc)/./src/app/api/graphql/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/graphql/route\",\n        pathname: \"/api/graphql\",\n        filename: \"route\",\n        bundlePath: \"app/api/graphql/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\graphql\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/graphql/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/graphql/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/graphql/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\n * GraphQL Proxy to handle CORS issues when connecting to WooCommerce\n * This endpoint forwards GraphQL requests to the WordPress site and handles CORS\n */ async function POST(request) {\n    try {\n        // Get the GraphQL query from the request body\n        const body = await request.json();\n        // WordPress/WooCommerce GraphQL endpoint from env variables\n        const graphqlEndpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://maroon-lapwing-781450.hostingersite.com/graphql\";\n        console.log(\"\\uD83D\\uDD17 GraphQL Proxy - Using endpoint:\", graphqlEndpoint);\n        console.log(\"\\uD83D\\uDCCA GraphQL Proxy - Request body:\", JSON.stringify(body, null, 2));\n        // Get the origin for CORS\n        const origin = request.headers.get(\"origin\") || \"\";\n        // Prepare headers for the request to WooCommerce\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Accept\": \"application/json\"\n        };\n        // Forward session token if present in the request\n        const sessionHeader = request.headers.get(\"woocommerce-session\");\n        if (sessionHeader) {\n            headers[\"woocommerce-session\"] = sessionHeader;\n        }\n        // Forward cookies if present\n        const cookie = request.headers.get(\"cookie\");\n        if (cookie) {\n            headers[\"cookie\"] = cookie;\n        }\n        // Forward the request to WordPress GraphQL\n        const response = await fetch(graphqlEndpoint, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(body),\n            credentials: \"include\"\n        });\n        // Get the response data\n        const data = await response.json();\n        console.log(\"\\uD83D\\uDCCA GraphQL Proxy - Response status:\", response.status);\n        console.log(\"\\uD83D\\uDCCA GraphQL Proxy - Response data:\", JSON.stringify(data, null, 2));\n        // Prepare response headers - use actual origin instead of wildcard for credentials support\n        const responseHeaders = {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Vary\": \"Origin\"\n        };\n        // Forward session token from WooCommerce response if present\n        const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n        if (responseSessionHeader) {\n            responseHeaders[\"woocommerce-session\"] = responseSessionHeader;\n        }\n        // Forward any cookies from the WordPress response\n        const setCookieHeader = response.headers.get(\"set-cookie\");\n        if (setCookieHeader) {\n            responseHeaders[\"set-cookie\"] = setCookieHeader;\n        }\n        // Return the response with CORS headers\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error(\"GraphQL proxy error:\", error);\n        // Get the origin for error response\n        const origin = request.headers.get(\"origin\") || \"\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            errors: [\n                {\n                    message: error instanceof Error ? error.message : \"Unknown error occurred\"\n                }\n            ]\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": origin,\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n                \"Access-Control-Allow-Credentials\": \"true\",\n                \"Vary\": \"Origin\"\n            }\n        });\n    }\n}\n/**\n * Handle OPTIONS requests for CORS preflight\n */ async function OPTIONS(request) {\n    // Get the origin for CORS\n    const origin = request.headers.get(\"origin\") || \"\";\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Access-Control-Max-Age\": \"86400\",\n            \"Vary\": \"Origin\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/graphql/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();