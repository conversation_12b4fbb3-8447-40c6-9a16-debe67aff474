"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_router-reducer_co"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    computeChangedPath: function() {\n        return computeChangedPath;\n    },\n    extractPathFromFlightRouterState: function() {\n        return extractPathFromFlightRouterState;\n    }\n});\nconst _interceptionroutes = __webpack_require__(/*! ../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/./node_modules/next/dist/server/future/helpers/interception-routes.js\");\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _matchsegments = __webpack_require__(/*! ../match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst removeLeadingSlash = (segment)=>{\n    return segment[0] === \"/\" ? segment.slice(1) : segment;\n};\nconst segmentToPathname = (segment)=>{\n    if (typeof segment === \"string\") {\n        // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n        // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n        if (segment === \"children\") return \"\";\n        return segment;\n    }\n    return segment[1];\n};\nfunction normalizeSegments(segments) {\n    return segments.reduce((acc, segment)=>{\n        segment = removeLeadingSlash(segment);\n        if (segment === \"\" || (0, _segment.isGroupSegment)(segment)) {\n            return acc;\n        }\n        return acc + \"/\" + segment;\n    }, \"\") || \"/\";\n}\nfunction extractPathFromFlightRouterState(flightRouterState) {\n    const segment = Array.isArray(flightRouterState[0]) ? flightRouterState[0][1] : flightRouterState[0];\n    if (segment === _segment.DEFAULT_SEGMENT_KEY || _interceptionroutes.INTERCEPTION_ROUTE_MARKERS.some((m)=>segment.startsWith(m))) return undefined;\n    if (segment.startsWith(_segment.PAGE_SEGMENT_KEY)) return \"\";\n    const segments = [\n        segmentToPathname(segment)\n    ];\n    var _flightRouterState_;\n    const parallelRoutes = (_flightRouterState_ = flightRouterState[1]) != null ? _flightRouterState_ : {};\n    const childrenPath = parallelRoutes.children ? extractPathFromFlightRouterState(parallelRoutes.children) : undefined;\n    if (childrenPath !== undefined) {\n        segments.push(childrenPath);\n    } else {\n        for (const [key, value] of Object.entries(parallelRoutes)){\n            if (key === \"children\") continue;\n            const childPath = extractPathFromFlightRouterState(value);\n            if (childPath !== undefined) {\n                segments.push(childPath);\n            }\n        }\n    }\n    return normalizeSegments(segments);\n}\nfunction computeChangedPathImpl(treeA, treeB) {\n    const [segmentA, parallelRoutesA] = treeA;\n    const [segmentB, parallelRoutesB] = treeB;\n    const normalizedSegmentA = segmentToPathname(segmentA);\n    const normalizedSegmentB = segmentToPathname(segmentB);\n    if (_interceptionroutes.INTERCEPTION_ROUTE_MARKERS.some((m)=>normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m))) {\n        return \"\";\n    }\n    if (!(0, _matchsegments.matchSegment)(segmentA, segmentB)) {\n        var _extractPathFromFlightRouterState;\n        // once we find where the tree changed, we compute the rest of the path by traversing the tree\n        return (_extractPathFromFlightRouterState = extractPathFromFlightRouterState(treeB)) != null ? _extractPathFromFlightRouterState : \"\";\n    }\n    for(const parallelRouterKey in parallelRoutesA){\n        if (parallelRoutesB[parallelRouterKey]) {\n            const changedPath = computeChangedPathImpl(parallelRoutesA[parallelRouterKey], parallelRoutesB[parallelRouterKey]);\n            if (changedPath !== null) {\n                return segmentToPathname(segmentB) + \"/\" + changedPath;\n            }\n        }\n    }\n    return null;\n}\nfunction computeChangedPath(treeA, treeB) {\n    const changedPath = computeChangedPathImpl(treeA, treeB);\n    if (changedPath == null || changedPath === \"/\") {\n        return changedPath;\n    }\n    // lightweight normalization to remove route groups\n    return normalizeSegments(changedPath.split(\"/\"));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=compute-changed-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createHrefFromUrl\", ({\n    enumerable: true,\n    get: function() {\n        return createHrefFromUrl;\n    }\n}));\nfunction createHrefFromUrl(url, includeHash) {\n    if (includeHash === void 0) includeHash = true;\n    return url.pathname + url.search + (includeHash ? url.hash : \"\");\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-href-from-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvY3JlYXRlLWhyZWYtZnJvbS11cmwuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFBZ0JBOzs7ZUFBQUE7OztBQUFULFNBQVNBLGtCQUNkQyxHQUE4QyxFQUM5Q0MsV0FBMkI7SUFBM0JBLElBQUFBLGdCQUFBQSxLQUFBQSxHQUFBQSxjQUF1QjtJQUV2QixPQUFPRCxJQUFJRSxRQUFRLEdBQUdGLElBQUlHLE1BQU0sR0FBSUYsQ0FBQUEsY0FBY0QsSUFBSUksSUFBSSxHQUFHO0FBQy9EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvY3JlYXRlLWhyZWYtZnJvbS11cmwudHM/ZDI0NCJdLCJuYW1lcyI6WyJjcmVhdGVIcmVmRnJvbVVybCIsInVybCIsImluY2x1ZGVIYXNoIiwicGF0aG5hbWUiLCJzZWFyY2giLCJoYXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nfunction createInitialRouterState(param) {\n    let { buildId, initialTree, initialSeedData, urlParts, initialParallelRoutes, location, initialHead, couldBeIntercepted } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = urlParts.join(\"/\");\n    const isServer = !location;\n    const rsc = initialSeedData[2];\n    const cache = {\n        lazyData: null,\n        rsc: rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: isServer ? new Map() : initialParallelRoutes,\n        lazyDataResolved: false,\n        loading: initialSeedData[3]\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, initialTree, initialSeedData, initialHead);\n    }\n    var _ref;\n    const initialState = {\n        buildId,\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (location) {\n        // Seed the prefetch cache with this page's data.\n        // This is to prevent needlessly re-prefetching a page that is already reusable,\n        // and will avoid triggering a loading state/data fetch stall when navigating back to the page.\n        const url = new URL(\"\" + location.pathname + location.search, location.origin);\n        const initialFlightData = [\n            [\n                \"\",\n                initialTree,\n                null,\n                null\n            ]\n        ];\n        (0, _prefetchcacheutils.createPrefetchCacheEntryForInitialLoad)({\n            url,\n            kind: _routerreducertypes.PrefetchKind.AUTO,\n            data: [\n                initialFlightData,\n                undefined,\n                false,\n                couldBeIntercepted\n            ],\n            tree: initialState.tree,\n            prefetchCache: initialState.prefetchCache,\n            nextUrl: initialState.nextUrl\n        });\n    }\n    return initialState;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js ***!
  \********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRouterCacheKey\", ({\n    enumerable: true,\n    get: function() {\n        return createRouterCacheKey;\n    }\n}));\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nfunction createRouterCacheKey(segment, withoutSearchParameters) {\n    if (withoutSearchParameters === void 0) withoutSearchParameters = false;\n    // if the segment is an array, it means it's a dynamic segment\n    // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n    if (Array.isArray(segment)) {\n        return segment[0] + \"|\" + segment[1] + \"|\" + segment[2];\n    }\n    // Page segments might have search parameters, ie __PAGE__?foo=bar\n    // When `withoutSearchParameters` is true, we only want to return the page segment\n    if (withoutSearchParameters && segment.startsWith(_segment.PAGE_SEGMENT_KEY)) {\n        return _segment.PAGE_SEGMENT_KEY;\n    }\n    return segment;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-router-cache-key.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvY3JlYXRlLXJvdXRlci1jYWNoZS1rZXkuanMiLCJtYXBwaW5ncyI6Ijs7Ozt3REFHZ0JBOzs7ZUFBQUE7OztxQ0FGaUI7QUFFMUIsU0FBU0EscUJBQ2RDLE9BQWdCLEVBQ2hCQyx1QkFBd0M7SUFBeENBLElBQUFBLDRCQUFBQSxLQUFBQSxHQUFBQSwwQkFBbUM7SUFFbkMsOERBQThEO0lBQzlELHVHQUF1RztJQUN2RyxJQUFJQyxNQUFNQyxPQUFPLENBQUNILFVBQVU7UUFDMUIsT0FBT0EsT0FBVSxDQUFDLEVBQUUsR0FBQyxNQUFHQSxPQUFPLENBQUMsRUFBRSxHQUFDLE1BQUdBLE9BQU8sQ0FBQyxFQUFFO0lBQ2xEO0lBRUEsa0VBQWtFO0lBQ2xFLGtGQUFrRjtJQUNsRixJQUFJQywyQkFBMkJELFFBQVFJLFVBQVUsQ0FBQ0MsU0FBQUEsZ0JBQWdCLEdBQUc7UUFDbkUsT0FBT0EsU0FBQUEsZ0JBQWdCO0lBQ3pCO0lBRUEsT0FBT0w7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL2NyZWF0ZS1yb3V0ZXItY2FjaGUta2V5LnRzPzc2YTkiXSwibmFtZXMiOlsiY3JlYXRlUm91dGVyQ2FjaGVLZXkiLCJzZWdtZW50Iiwid2l0aG91dFNlYXJjaFBhcmFtZXRlcnMiLCJBcnJheSIsImlzQXJyYXkiLCJzdGFydHNXaXRoIiwiUEFHRV9TRUdNRU5UX0tFWSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\n"));

/***/ })

}]);