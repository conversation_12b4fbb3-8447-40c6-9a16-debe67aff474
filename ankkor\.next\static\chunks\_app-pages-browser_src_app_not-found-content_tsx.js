"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_app_not-found-content_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-left.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArrowLeft; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst ArrowLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowLeft\", [\n    [\n        \"path\",\n        {\n            d: \"m12 19-7-7 7-7\",\n            key: \"1l729n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 12H5\",\n            key: \"x3x0zl\"\n        }\n    ]\n]);\n //# sourceMappingURL=arrow-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXJyb3ctbGVmdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLE1BQUFBLFlBQVlDLGdFQUFnQkEsQ0FBQyxhQUFhO0lBQzlDO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWtCQyxLQUFLO1FBQUE7S0FBVTtJQUMvQztRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFZQyxLQUFLO1FBQUE7S0FBVTtDQUMxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2ljb25zL2Fycm93LWxlZnQudHM/NGI1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIEFycm93TGVmdFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRJZ01Ua3ROeTAzSURjdE55SWdMejRLSUNBOGNHRjBhQ0JrUFNKTk1Ua2dNVEpJTlNJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Fycm93LWxlZnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBBcnJvd0xlZnQgPSBjcmVhdGVMdWNpZGVJY29uKCdBcnJvd0xlZnQnLCBbXG4gIFsncGF0aCcsIHsgZDogJ20xMiAxOS03LTcgNy03Jywga2V5OiAnMWw3MjluJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE5IDEySDUnLCBrZXk6ICd4M3gwemwnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IEFycm93TGVmdDtcbiJdLCJuYW1lcyI6WyJBcnJvd0xlZnQiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/not-found-content.tsx":
/*!***************************************!*\
  !*** ./src/app/not-found-content.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Client component that safely uses useSearchParams\nconst NotFoundContent = ()=>{\n    _s();\n    // This hook is now safely used in a dynamically imported client component\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const from = (searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"from\")) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-[70vh] px-4 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-9xl font-serif font-bold text-[#2c2c27]\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl md:text-3xl font-serif mt-6 mb-8 text-[#5c5c52]\",\n                children: \"Page Not Found\"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"max-w-md mb-10 text-[#8a8778]\",\n                children: from ? 'The page \"'.concat(from, '\" could not be found.') : \"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.\"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/\",\n                className: \"flex items-center gap-2 px-6 py-3 bg-[#2c2c27] text-white hover:bg-[#3d3d35] transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Return to Homepage\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\not-found-content.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NotFoundContent, \"a+DZx9DY26Zf8FVy1bxe3vp9l1w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = NotFoundContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NotFoundContent);\nvar _c;\n$RefreshReg$(_c, \"NotFoundContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/not-found-content.tsx\n"));

/***/ })

}]);