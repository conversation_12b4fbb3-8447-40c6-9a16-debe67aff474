"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/shirts/page-_",{

/***/ "(app-pages-browser)/./src/app/collection/shirts/page.tsx":
/*!********************************************!*\
  !*** ./src/app/collection/shirts/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShirtsCollectionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_product_ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/product/ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/* harmony import */ var _lib_productUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/productUtils */ \"(app-pages-browser)/./src/lib/productUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ShirtsCollectionPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use the page loading hook\n    (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(isLoading, \"fabric\");\n    // Fetch products from WooCommerce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                var _categoryData_products_nodes, _categoryData_products, _categoryData_products_nodes1, _categoryData_products1, _categoryData_products_nodes2, _categoryData_products2, _categoryData_products_nodes3, _categoryData_products3, _categoryData_products_nodes4, _categoryData_products4, _categoryData_products5, _categoryData_products6;\n                setIsLoading(true);\n                setError(null);\n                console.log(\"\\uD83D\\uDD0D Starting to fetch shirts from WooCommerce...\");\n                // First, let's test the WooCommerce connection\n                let connectionTest = null;\n                try {\n                    console.log(\"\\uD83E\\uDDEA Testing WooCommerce connection...\");\n                    const { testWooCommerceConnection } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                    connectionTest = await testWooCommerceConnection();\n                    console.log(\"\\uD83D\\uDD17 Connection test result:\", connectionTest);\n                } catch (err) {\n                    console.log(\"❌ Failed to test connection:\", err);\n                }\n                // Then, let's test if we can fetch all categories to see what's available\n                let allCategories = null;\n                try {\n                    console.log(\"\\uD83D\\uDCCB Fetching all categories to debug...\");\n                    const { getAllCategories } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                    allCategories = await getAllCategories(50);\n                    console.log(\"\\uD83D\\uDCC2 Available categories:\", allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>({\n                            name: cat.name,\n                            slug: cat.slug,\n                            id: cat.id,\n                            count: cat.count\n                        })));\n                } catch (err) {\n                    console.log(\"❌ Failed to fetch categories:\", err);\n                }\n                // Try multiple approaches to fetch shirts\n                let categoryData = null;\n                let fetchMethod = \"\";\n                // Method 1: Try with category slug 'shirts'\n                try {\n                    var _categoryData_products_nodes5, _categoryData_products7;\n                    console.log('\\uD83D\\uDCCB Attempting to fetch with category slug: \"shirts\"');\n                    categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(\"shirts\", {\n                        first: 100\n                    });\n                    fetchMethod = \"slug: shirts\";\n                    if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products7 = categoryData.products) === null || _categoryData_products7 === void 0 ? void 0 : (_categoryData_products_nodes5 = _categoryData_products7.nodes) === null || _categoryData_products_nodes5 === void 0 ? void 0 : _categoryData_products_nodes5.length) > 0) {\n                        console.log(\"✅ Success with method 1 (slug: shirts)\");\n                    } else {\n                        console.log(\"⚠️ Method 1 returned empty or null:\", categoryData);\n                    }\n                } catch (err) {\n                    console.log(\"❌ Method 1 failed:\", err);\n                }\n                // Method 2: Try with different category variations if method 1 failed\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products = categoryData.products) === null || _categoryData_products === void 0 ? void 0 : (_categoryData_products_nodes = _categoryData_products.nodes) === null || _categoryData_products_nodes === void 0 ? void 0 : _categoryData_products_nodes.length)) {\n                    const alternativeNames = [\n                        \"shirt\",\n                        \"Shirts\",\n                        \"SHIRTS\",\n                        \"men-shirts\",\n                        \"mens-shirts\",\n                        \"clothing\",\n                        \"apparel\"\n                    ];\n                    for (const altName of alternativeNames){\n                        try {\n                            var _categoryData_products_nodes6, _categoryData_products8;\n                            console.log('\\uD83D\\uDCCB Attempting to fetch with category: \"'.concat(altName, '\"'));\n                            categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(altName, {\n                                first: 100\n                            });\n                            fetchMethod = \"slug: \".concat(altName);\n                            if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products8 = categoryData.products) === null || _categoryData_products8 === void 0 ? void 0 : (_categoryData_products_nodes6 = _categoryData_products8.nodes) === null || _categoryData_products_nodes6 === void 0 ? void 0 : _categoryData_products_nodes6.length) > 0) {\n                                console.log(\"✅ Success with alternative name: \".concat(altName));\n                                break;\n                            } else {\n                                console.log(\"⚠️ No products found for category: \".concat(altName));\n                            }\n                        } catch (err) {\n                            console.log(\"❌ Failed with \".concat(altName, \":\"), err);\n                        }\n                    }\n                }\n                // Method 3: Try to find the correct category from the list of all categories\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products1 = categoryData.products) === null || _categoryData_products1 === void 0 ? void 0 : (_categoryData_products_nodes1 = _categoryData_products1.nodes) === null || _categoryData_products_nodes1 === void 0 ? void 0 : _categoryData_products_nodes1.length) && (allCategories === null || allCategories === void 0 ? void 0 : allCategories.length) > 0) {\n                    console.log(\"\\uD83D\\uDCCB Searching for shirt-related categories in available categories...\");\n                    const shirtCategory = allCategories.find((cat)=>{\n                        var _cat_name, _cat_slug;\n                        const name = ((_cat_name = cat.name) === null || _cat_name === void 0 ? void 0 : _cat_name.toLowerCase()) || \"\";\n                        const slug = ((_cat_slug = cat.slug) === null || _cat_slug === void 0 ? void 0 : _cat_slug.toLowerCase()) || \"\";\n                        return name.includes(\"shirt\") || slug.includes(\"shirt\") || name.includes(\"clothing\") || slug.includes(\"clothing\") || name.includes(\"apparel\") || slug.includes(\"apparel\");\n                    });\n                    if (shirtCategory) {\n                        console.log(\"\\uD83D\\uDCCB Found potential shirt category: \".concat(shirtCategory.name, \" (\").concat(shirtCategory.slug, \")\"));\n                        try {\n                            var _categoryData_products_nodes7, _categoryData_products9;\n                            categoryData = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getCategoryProducts)(shirtCategory.slug, {\n                                first: 100\n                            });\n                            fetchMethod = \"found category: \".concat(shirtCategory.slug);\n                            if ((categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products9 = categoryData.products) === null || _categoryData_products9 === void 0 ? void 0 : (_categoryData_products_nodes7 = _categoryData_products9.nodes) === null || _categoryData_products_nodes7 === void 0 ? void 0 : _categoryData_products_nodes7.length) > 0) {\n                                console.log(\"✅ Success with found category: \".concat(shirtCategory.slug));\n                            }\n                        } catch (err) {\n                            console.log(\"❌ Failed with found category \".concat(shirtCategory.slug, \":\"), err);\n                        }\n                    }\n                }\n                // Method 4: If still no results, try fetching all products and filter by keywords\n                if (!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products2 = categoryData.products) === null || _categoryData_products2 === void 0 ? void 0 : (_categoryData_products_nodes2 = _categoryData_products2.nodes) === null || _categoryData_products_nodes2 === void 0 ? void 0 : _categoryData_products_nodes2.length)) {\n                    try {\n                        console.log(\"\\uD83D\\uDCCB Attempting to fetch all products and filter by keywords...\");\n                        const { getAllProducts } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                        const allProducts = await getAllProducts(100);\n                        fetchMethod = \"all products filtered by keywords\";\n                        if ((allProducts === null || allProducts === void 0 ? void 0 : allProducts.length) > 0) {\n                            // Filter products that might be shirts\n                            const filteredProducts = allProducts.filter((product)=>{\n                                var _product_name, _product_title, _product_description, _product_shortDescription, _product_productCategories;\n                                const title = ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase()) || ((_product_title = product.title) === null || _product_title === void 0 ? void 0 : _product_title.toLowerCase()) || \"\";\n                                const description = ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase()) || ((_product_shortDescription = product.shortDescription) === null || _product_shortDescription === void 0 ? void 0 : _product_shortDescription.toLowerCase()) || \"\";\n                                const categories = ((_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : _product_productCategories.nodes) || product.categories || [];\n                                // Check if product title or description contains shirt-related keywords\n                                const shirtKeywords = [\n                                    \"shirt\",\n                                    \"formal\",\n                                    \"casual\",\n                                    \"dress\",\n                                    \"button\",\n                                    \"collar\",\n                                    \"sleeve\"\n                                ];\n                                const hasShirtKeyword = shirtKeywords.some((keyword)=>title.includes(keyword) || description.includes(keyword));\n                                // Check if product belongs to shirts category\n                                const hasShirtCategory = categories.some((cat)=>{\n                                    var _cat_name, _cat_slug;\n                                    const catName = ((_cat_name = cat.name) === null || _cat_name === void 0 ? void 0 : _cat_name.toLowerCase()) || ((_cat_slug = cat.slug) === null || _cat_slug === void 0 ? void 0 : _cat_slug.toLowerCase()) || \"\";\n                                    return catName.includes(\"shirt\") || catName.includes(\"clothing\") || catName.includes(\"apparel\");\n                                });\n                                return hasShirtKeyword || hasShirtCategory;\n                            });\n                            // Create a mock category structure\n                            categoryData = {\n                                products: {\n                                    nodes: filteredProducts\n                                }\n                            };\n                            console.log(\"✅ Filtered \".concat(filteredProducts.length, \" shirt products from all products\"));\n                        }\n                    } catch (err) {\n                        console.log(\"❌ Method 4 failed:\", err);\n                    }\n                }\n                // Set debug information\n                setDebugInfo({\n                    fetchMethod,\n                    totalProducts: (categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products3 = categoryData.products) === null || _categoryData_products3 === void 0 ? void 0 : (_categoryData_products_nodes3 = _categoryData_products3.nodes) === null || _categoryData_products_nodes3 === void 0 ? void 0 : _categoryData_products_nodes3.length) || 0,\n                    connectionTest: connectionTest || \"No connection test performed\",\n                    availableCategories: (allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>({\n                            name: cat.name,\n                            slug: cat.slug,\n                            count: cat.count\n                        }))) || [],\n                    categoryData: categoryData ? JSON.stringify(categoryData, null, 2) : \"No data\",\n                    timestamp: new Date().toISOString()\n                });\n                console.log(\"\\uD83D\\uDCCA Debug Info:\", {\n                    fetchMethod,\n                    totalProducts: (categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products4 = categoryData.products) === null || _categoryData_products4 === void 0 ? void 0 : (_categoryData_products_nodes4 = _categoryData_products4.nodes) === null || _categoryData_products_nodes4 === void 0 ? void 0 : _categoryData_products_nodes4.length) || 0,\n                    hasData: !!categoryData,\n                    hasProducts: !!(categoryData === null || categoryData === void 0 ? void 0 : categoryData.products),\n                    hasNodes: !!(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData_products5 = categoryData.products) === null || _categoryData_products5 === void 0 ? void 0 : _categoryData_products5.nodes),\n                    availableCategories: (allCategories === null || allCategories === void 0 ? void 0 : allCategories.length) || 0\n                });\n                if (!categoryData || !((_categoryData_products6 = categoryData.products) === null || _categoryData_products6 === void 0 ? void 0 : _categoryData_products6.nodes) || categoryData.products.nodes.length === 0) {\n                    console.log(\"❌ No shirt products found in any category\");\n                    setError(\"No shirt products found using method: \".concat(fetchMethod, \". Available categories: \").concat((allCategories === null || allCategories === void 0 ? void 0 : allCategories.map((cat)=>cat.name).join(\", \")) || \"None found\", \". Please check your WooCommerce shirts category setup.\"));\n                    setIsLoading(false);\n                    return;\n                }\n                const allProducts = categoryData.products.nodes;\n                console.log(\"\\uD83D\\uDCE6 Found \".concat(allProducts.length, \" products, normalizing...\"));\n                // Normalize the products\n                const transformedProducts = allProducts.map((product, index)=>{\n                    try {\n                        console.log(\"\\uD83D\\uDD04 Normalizing product \".concat(index + 1, \":\"), product.name || product.title);\n                        const normalizedProduct = (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.normalizeProduct)(product);\n                        if (normalizedProduct) {\n                            // Ensure currencyCode is included\n                            normalizedProduct.currencyCode = \"INR\";\n                            console.log(\"✅ Successfully normalized: \".concat(normalizedProduct.title));\n                            return normalizedProduct;\n                        } else {\n                            console.log(\"⚠️ Failed to normalize product: \".concat(product.name || product.title));\n                            return null;\n                        }\n                    } catch (err) {\n                        console.error(\"❌ Error normalizing product \".concat(index + 1, \":\"), err);\n                        return null;\n                    }\n                }).filter(Boolean);\n                console.log(\"\\uD83C\\uDF89 Successfully processed \".concat(transformedProducts.length, \" shirt products\"));\n                console.log(\"\\uD83D\\uDCE6 Setting products:\", transformedProducts.map((p)=>{\n                    var _p_priceRange_minVariantPrice, _p_priceRange;\n                    return {\n                        title: p.title,\n                        price: (_p_priceRange = p.priceRange) === null || _p_priceRange === void 0 ? void 0 : (_p_priceRange_minVariantPrice = _p_priceRange.minVariantPrice) === null || _p_priceRange_minVariantPrice === void 0 ? void 0 : _p_priceRange_minVariantPrice.amount,\n                        id: p.id\n                    };\n                }));\n                setProducts(transformedProducts);\n            } catch (err) {\n                console.error(\"\\uD83D\\uDCA5 Critical error fetching products:\", err);\n                setError(\"Failed to load products from WooCommerce: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchProducts();\n    }, []);\n    // Toggle filter drawer\n    const toggleFilter = ()=>{\n        setIsFilterOpen(!isFilterOpen);\n    };\n    // No filtering - show all products\n    const sortedProducts = products;\n    // Animation variants\n    const fadeIn = {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: 20,\n            transition: {\n                duration: 0.3\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#f8f8f5] pt-8 pb-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 mb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-serif font-bold mb-4 text-[#2c2c27]\",\n                            children: \"Shirts Collection\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-[#5c5c52] mb-8\",\n                            children: \"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-[300px] mb-16 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80\",\n                        alt: \"Ankkor Shirts Collection\",\n                        fill: true,\n                        sizes: \"(max-width: 768px) 100vw, 50vw\",\n                        className: \"object-cover image-animate\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-4\",\n                                    children: \"Signature Shirts\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg max-w-xl mx-auto\",\n                                    children: \"Impeccably tailored for the perfect fit\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-semibold\",\n                                children: \"Error loading shirts:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: \"Please check your WooCommerce configuration and ensure you have products in the 'shirts' category.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            debugInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer text-sm font-semibold\",\n                                        children: \"Debug Information\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40\",\n                                        children: JSON.stringify(debugInfo, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#2c2c27]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-[#5c5c52]\",\n                                children: \"Loading shirts...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end items-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-[#5c5c52] text-sm\",\n                            children: [\n                                sortedProducts.length,\n                                \" products\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex justify-between items-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-[#2c2c27] font-serif text-xl\",\n                                        children: \"Shirts Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-[#5c5c52]\",\n                                        children: [\n                                            sortedProducts.length,\n                                            \" products\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            !isLoading && sortedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: sortedProducts.map((product)=>{\n                                    var _product__originalWooProduct, _product__originalWooProduct1, _product_priceRange_minVariantPrice, _product_priceRange, _product_images_, _product__originalWooProduct2, _product__originalWooProduct3, _product__originalWooProduct4, _product__originalWooProduct5, _product__originalWooProduct6, _product__originalWooProduct7;\n                                    // Extract and validate the variant ID for the product\n                                    let variantId = \"\";\n                                    let isValidVariant = false;\n                                    try {\n                                        // Check if variants exist and extract the first variant ID\n                                        if (product.variants && product.variants.length > 0) {\n                                            const variant = product.variants[0];\n                                            if (variant && variant.id) {\n                                                variantId = variant.id;\n                                                isValidVariant = true;\n                                                // Ensure the variant ID is properly formatted\n                                                if (!variantId.startsWith(\"gid://shopify/ProductVariant/\")) {\n                                                    // Extract numeric ID if possible and reformat\n                                                    const numericId = variantId.replace(/\\D/g, \"\");\n                                                    if (numericId) {\n                                                        variantId = \"gid://shopify/ProductVariant/\".concat(numericId);\n                                                    } else {\n                                                        console.warn(\"Cannot parse variant ID for product \".concat(product.title, \": \").concat(variantId));\n                                                        isValidVariant = false;\n                                                    }\n                                                }\n                                                console.log(\"Product \".concat(product.title, \" using variant ID: \").concat(variantId));\n                                            }\n                                        }\n                                        // If no valid variant ID found, try to create a fallback from product ID\n                                        if (!isValidVariant && product.id) {\n                                            // Only attempt fallback if product ID has a numeric component\n                                            if (product.id.includes(\"/\")) {\n                                                const parts = product.id.split(\"/\");\n                                                const numericId = parts[parts.length - 1];\n                                                if (numericId && /^\\d+$/.test(numericId)) {\n                                                    // Create a fallback ID - note this might not work if variants aren't 1:1 with products\n                                                    variantId = \"gid://shopify/ProductVariant/\".concat(numericId);\n                                                    console.warn(\"Using fallback variant ID for \".concat(product.title, \": \").concat(variantId));\n                                                    isValidVariant = true;\n                                                }\n                                            }\n                                        }\n                                    } catch (error) {\n                                        console.error(\"Error processing variant for product \".concat(product.title, \":\"), error);\n                                        isValidVariant = false;\n                                    }\n                                    // If we couldn't find a valid variant ID, log an error\n                                    if (!isValidVariant) {\n                                        console.error(\"No valid variant ID found for product: \".concat(product.title));\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        variants: fadeIn,\n                                        initial: \"initial\",\n                                        animate: \"animate\",\n                                        exit: \"exit\",\n                                        layout: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            id: product.id,\n                                            name: product.title,\n                                            slug: product.handle,\n                                            price: ((_product__originalWooProduct = product._originalWooProduct) === null || _product__originalWooProduct === void 0 ? void 0 : _product__originalWooProduct.salePrice) || ((_product__originalWooProduct1 = product._originalWooProduct) === null || _product__originalWooProduct1 === void 0 ? void 0 : _product__originalWooProduct1.price) || ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : (_product_priceRange_minVariantPrice = _product_priceRange.minVariantPrice) === null || _product_priceRange_minVariantPrice === void 0 ? void 0 : _product_priceRange_minVariantPrice.amount) || \"0\",\n                                            image: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"\",\n                                            material: (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_5__.getMetafield)(product, \"custom_material\", undefined, \"Premium Fabric\"),\n                                            isNew: true,\n                                            stockStatus: ((_product__originalWooProduct2 = product._originalWooProduct) === null || _product__originalWooProduct2 === void 0 ? void 0 : _product__originalWooProduct2.stockStatus) || \"IN_STOCK\",\n                                            compareAtPrice: product.compareAtPrice,\n                                            regularPrice: (_product__originalWooProduct3 = product._originalWooProduct) === null || _product__originalWooProduct3 === void 0 ? void 0 : _product__originalWooProduct3.regularPrice,\n                                            salePrice: (_product__originalWooProduct4 = product._originalWooProduct) === null || _product__originalWooProduct4 === void 0 ? void 0 : _product__originalWooProduct4.salePrice,\n                                            onSale: ((_product__originalWooProduct5 = product._originalWooProduct) === null || _product__originalWooProduct5 === void 0 ? void 0 : _product__originalWooProduct5.onSale) || false,\n                                            currencySymbol: (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrencySymbol)(product.currencyCode),\n                                            currencyCode: product.currencyCode || \"INR\",\n                                            shortDescription: (_product__originalWooProduct6 = product._originalWooProduct) === null || _product__originalWooProduct6 === void 0 ? void 0 : _product__originalWooProduct6.shortDescription,\n                                            type: (_product__originalWooProduct7 = product._originalWooProduct) === null || _product__originalWooProduct7 === void 0 ? void 0 : _product__originalWooProduct7.type\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, this),\n                            !isLoading && sortedProducts.length === 0 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-[#5c5c52] mb-4\",\n                                        children: \"No products found with the selected filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setPriceRange([\n                                                0,\n                                                25000\n                                            ]);\n                                        },\n                                        className: \"text-[#2c2c27] underline\",\n                                        children: \"Reset filters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\collection\\\\shirts\\\\page.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_s(ShirtsCollectionPage, \"7RXDNUkQ5k2yNnIUb2S2Hzwi6f4=\", false, function() {\n    return [\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = ShirtsCollectionPage;\nvar _c;\n$RefreshReg$(_c, \"ShirtsCollectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/shirts/page.tsx\n"));

/***/ })

});