"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_p"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/promise-queue.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PromiseQueue\", ({\n    enumerable: true,\n    get: function() {\n        return PromiseQueue;\n    }\n}));\nconst _class_private_field_loose_base = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_base */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_base.js\");\nconst _class_private_field_loose_key = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_key */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_key.js\");\nvar _maxConcurrency = /*#__PURE__*/ _class_private_field_loose_key._(\"_maxConcurrency\"), _runningCount = /*#__PURE__*/ _class_private_field_loose_key._(\"_runningCount\"), _queue = /*#__PURE__*/ _class_private_field_loose_key._(\"_queue\"), _processNext = /*#__PURE__*/ _class_private_field_loose_key._(\"_processNext\");\nclass PromiseQueue {\n    enqueue(promiseFn) {\n        let taskResolve;\n        let taskReject;\n        const taskPromise = new Promise((resolve, reject)=>{\n            taskResolve = resolve;\n            taskReject = reject;\n        });\n        const task = async ()=>{\n            try {\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]++;\n                const result = await promiseFn();\n                taskResolve(result);\n            } catch (error) {\n                taskReject(error);\n            } finally{\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]--;\n                _class_private_field_loose_base._(this, _processNext)[_processNext]();\n            }\n        };\n        const enqueueResult = {\n            promiseFn: taskPromise,\n            task\n        };\n        // wonder if we should take a LIFO approach here\n        _class_private_field_loose_base._(this, _queue)[_queue].push(enqueueResult);\n        _class_private_field_loose_base._(this, _processNext)[_processNext]();\n        return taskPromise;\n    }\n    bump(promiseFn) {\n        const index = _class_private_field_loose_base._(this, _queue)[_queue].findIndex((item)=>item.promiseFn === promiseFn);\n        if (index > -1) {\n            const bumpedItem = _class_private_field_loose_base._(this, _queue)[_queue].splice(index, 1)[0];\n            _class_private_field_loose_base._(this, _queue)[_queue].unshift(bumpedItem);\n            _class_private_field_loose_base._(this, _processNext)[_processNext](true);\n        }\n    }\n    constructor(maxConcurrency = 5){\n        Object.defineProperty(this, _processNext, {\n            value: processNext\n        });\n        Object.defineProperty(this, _maxConcurrency, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _runningCount, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _queue, {\n            writable: true,\n            value: void 0\n        });\n        _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] = maxConcurrency;\n        _class_private_field_loose_base._(this, _runningCount)[_runningCount] = 0;\n        _class_private_field_loose_base._(this, _queue)[_queue] = [];\n    }\n}\nfunction processNext(forced) {\n    if (forced === void 0) forced = false;\n    if ((_class_private_field_loose_base._(this, _runningCount)[_runningCount] < _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] || forced) && _class_private_field_loose_base._(this, _queue)[_queue].length > 0) {\n        var _class_private_field_loose_base__queue_shift;\n        (_class_private_field_loose_base__queue_shift = _class_private_field_loose_base._(this, _queue)[_queue].shift()) == null ? void 0 : _class_private_field_loose_base__queue_shift.task();\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=promise-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js ***!
  \*******************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return ReactDevOverlay;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _ShadowPortal = __webpack_require__(/*! ../internal/components/ShadowPortal */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/ShadowPortal.js\");\nconst _BuildError = __webpack_require__(/*! ../internal/container/BuildError */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/BuildError.js\");\nconst _Errors = __webpack_require__(/*! ../internal/container/Errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/Errors.js\");\nconst _parseStack = __webpack_require__(/*! ../internal/helpers/parseStack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js\");\nconst _Base = __webpack_require__(/*! ../internal/styles/Base */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/Base.js\");\nconst _ComponentStyles = __webpack_require__(/*! ../internal/styles/ComponentStyles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/ComponentStyles.js\");\nconst _CssReset = __webpack_require__(/*! ../internal/styles/CssReset */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/styles/CssReset.js\");\nconst _rootlayoutmissingtagserror = __webpack_require__(/*! ../internal/container/root-layout-missing-tags-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/container/root-layout-missing-tags-error.js\");\nclass ReactDevOverlay extends _react.PureComponent {\n    static getDerivedStateFromError(error) {\n        if (!error.stack) return {\n            reactError: null\n        };\n        return {\n            reactError: {\n                id: 0,\n                event: {\n                    type: _shared.ACTION_UNHANDLED_ERROR,\n                    reason: error,\n                    frames: (0, _parseStack.parseStack)(error.stack)\n                }\n            }\n        };\n    }\n    componentDidCatch(componentErr) {\n        this.props.onReactError(componentErr);\n    }\n    render() {\n        var _state_rootLayoutMissingTags, _state_rootLayoutMissingTags1;\n        const { state, children } = this.props;\n        const { reactError } = this.state;\n        const hasBuildError = state.buildError != null;\n        const hasRuntimeErrors = Boolean(state.errors.length);\n        const hasMissingTags = Boolean((_state_rootLayoutMissingTags = state.rootLayoutMissingTags) == null ? void 0 : _state_rootLayoutMissingTags.length);\n        const isMounted = hasBuildError || hasRuntimeErrors || reactError || hasMissingTags;\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                reactError ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"body\", {})\n                    ]\n                }) : children,\n                isMounted ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_ShadowPortal.ShadowPortal, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_CssReset.CssReset, {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Base.Base, {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_ComponentStyles.ComponentStyles, {}),\n                        ((_state_rootLayoutMissingTags1 = state.rootLayoutMissingTags) == null ? void 0 : _state_rootLayoutMissingTags1.length) ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_rootlayoutmissingtagserror.RootLayoutMissingTagsError, {\n                            missingTags: state.rootLayoutMissingTags\n                        }) : hasBuildError ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_BuildError.BuildError, {\n                            message: state.buildError,\n                            versionInfo: state.versionInfo\n                        }) : reactError ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_Errors.Errors, {\n                            isAppDir: true,\n                            versionInfo: state.versionInfo,\n                            initialDisplayState: \"fullscreen\",\n                            errors: [\n                                reactError\n                            ]\n                        }) : hasRuntimeErrors ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_Errors.Errors, {\n                            isAppDir: true,\n                            initialDisplayState: \"minimized\",\n                            errors: state.errors,\n                            versionInfo: state.versionInfo\n                        }) : undefined\n                    ]\n                }) : undefined\n            ]\n        });\n    }\n    constructor(...args){\n        super(...args);\n        this.state = {\n            reactError: null\n        };\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ReactDevOverlay.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js ***!
  \***********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return HotReload;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _formatwebpackmessages = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../internal/helpers/format-webpack-messages */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.js\"));\nconst _navigation = __webpack_require__(/*! ../../navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _parseStack = __webpack_require__(/*! ../internal/helpers/parseStack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parseStack.js\");\nconst _ReactDevOverlay = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./ReactDevOverlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js\"));\nconst _useerrorhandler = __webpack_require__(/*! ../internal/helpers/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-error-handler.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../internal/helpers/runtime-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.js\");\nconst _usewebsocket = __webpack_require__(/*! ../internal/helpers/use-websocket */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-websocket.js\");\nconst _parsecomponentstack = __webpack_require__(/*! ../internal/helpers/parse-component-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/parse-component-stack.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _extractmodulesfromturbopackmessage = __webpack_require__(/*! ../../../../server/dev/extract-modules-from-turbopack-message */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/extract-modules-from-turbopack-message.js\");\nlet mostRecentCompilationHash = null;\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now());\nlet reloading = false;\nlet startLatency = null;\nfunction onBeforeFastRefresh(dispatcher, hasUpdates) {\n    if (hasUpdates) {\n        dispatcher.onBeforeRefresh();\n    }\n}\nfunction onFastRefresh(dispatcher, sendMessage, updatedModules) {\n    dispatcher.onBuildOk();\n    reportHmrLatency(sendMessage, updatedModules);\n    dispatcher.onRefresh();\n}\nfunction reportHmrLatency(sendMessage, updatedModules) {\n    if (!startLatency) return;\n    let endLatency = Date.now();\n    const latency = endLatency - startLatency;\n    console.log(\"[Fast Refresh] done in \" + latency + \"ms\");\n    sendMessage(JSON.stringify({\n        event: \"client-hmr-latency\",\n        id: window.__nextDevClientId,\n        startTime: startLatency,\n        endTime: endLatency,\n        page: window.location.pathname,\n        updatedModules,\n        // Whether the page (tab) was hidden at the time the event occurred.\n        // This can impact the accuracy of the event's timing.\n        isPageHidden: document.visibilityState === \"hidden\"\n    }));\n}\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n    // Update last known compilation hash.\n    mostRecentCompilationHash = hash;\n}\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */ function isUpdateAvailable() {\n    if (false) {}\n    /* globals __webpack_hash__ */ // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    return mostRecentCompilationHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error module.hot exists\n    return module.hot.status() === \"idle\";\n}\nfunction afterApplyUpdates(fn) {\n    if (canApplyUpdates()) {\n        fn();\n    } else {\n        function handler(status) {\n            if (status === \"idle\") {\n                // @ts-expect-error module.hot exists\n                module.hot.removeStatusHandler(handler);\n                fn();\n            }\n        }\n        // @ts-expect-error module.hot exists\n        module.hot.addStatusHandler(handler);\n    }\n}\nfunction performFullReload(err, sendMessage) {\n    const stackTrace = err && (err.stack && err.stack.split(\"\\n\").slice(0, 5).join(\"\\n\") || err.message || err + \"\");\n    sendMessage(JSON.stringify({\n        event: \"client-full-reload\",\n        stackTrace,\n        hadRuntimeError: !!_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError,\n        dependencyChain: err ? err.dependencyChain : undefined\n    }));\n    if (reloading) return;\n    reloading = true;\n    window.location.reload();\n}\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onBeforeUpdate, onHotUpdateSuccess, sendMessage, dispatcher) {\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        dispatcher.onBuildOk();\n        return;\n    }\n    function handleApplyUpdates(err, updatedModules) {\n        if (err || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n            if (err) {\n                console.warn(\"[Fast Refresh] performing full reload\\n\\n\" + \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" + \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" + \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" + \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" + \"Fast Refresh requires at least one parent function component in your React tree.\");\n            } else if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n            }\n            performFullReload(err, sendMessage);\n            return;\n        }\n        const hasUpdates = Boolean(updatedModules.length);\n        if (typeof onHotUpdateSuccess === \"function\") {\n            // Maybe we want to do something.\n            onHotUpdateSuccess(updatedModules);\n        }\n        if (isUpdateAvailable()) {\n            // While we were updating, there was a new update! Do it again.\n            tryApplyUpdates(hasUpdates ? ()=>{} : onBeforeUpdate, hasUpdates ? ()=>dispatcher.onBuildOk() : onHotUpdateSuccess, sendMessage, dispatcher);\n        } else {\n            dispatcher.onBuildOk();\n            if (false) {}\n        }\n    }\n    // https://webpack.js.org/api/hot-module-replacement/#check\n    // @ts-expect-error module.hot exists\n    module.hot.check(/* autoApply */ false).then((updatedModules)=>{\n        if (!updatedModules) {\n            return null;\n        }\n        if (typeof onBeforeUpdate === \"function\") {\n            const hasUpdates = Boolean(updatedModules.length);\n            onBeforeUpdate(hasUpdates);\n        }\n        // https://webpack.js.org/api/hot-module-replacement/#apply\n        // @ts-expect-error module.hot exists\n        return module.hot.apply();\n    }).then((updatedModules)=>{\n        handleApplyUpdates(null, updatedModules);\n    }, (err)=>{\n        handleApplyUpdates(err, null);\n    });\n}\n/** Handles messages from the sevrer for the App Router. */ function processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher) {\n    if (!(\"action\" in obj)) {\n        return;\n    }\n    function handleErrors(errors) {\n        // \"Massage\" webpack messages.\n        const formatted = (0, _formatwebpackmessages.default)({\n            errors: errors,\n            warnings: []\n        });\n        // Only show the first error.\n        dispatcher.onBuildError(formatted.errors[0]);\n        // Also log them to the console.\n        for(let i = 0; i < formatted.errors.length; i++){\n            console.error((0, _stripansi.default)(formatted.errors[i]));\n        }\n        // Do not attempt to reload now.\n        // We will reload on next success instead.\n        if (false) {}\n    }\n    function handleHotUpdate() {\n        if (false) {} else {\n            tryApplyUpdates(function onBeforeHotUpdate(hasUpdates) {\n                onBeforeFastRefresh(dispatcher, hasUpdates);\n            }, function onSuccessfulHotUpdate(webpackUpdatedModules) {\n                // Only dismiss it when we're sure it's a hot update.\n                // Otherwise it would flicker right before the reload.\n                onFastRefresh(dispatcher, sendMessage, webpackUpdatedModules);\n            }, sendMessage, dispatcher);\n        }\n    }\n    switch(obj.action){\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n            {\n                startLatency = Date.now();\n                console.log(\"[Fast Refresh] rebuilding\");\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n            {\n                if (obj.hash) {\n                    handleAvailableHash(obj.hash);\n                }\n                const { errors, warnings } = obj;\n                // Is undefined when it's a 'built' event\n                if (\"versionInfo\" in obj) dispatcher.onVersionInfo(obj.versionInfo);\n                const hasErrors = Boolean(errors && errors.length);\n                // Compilation with errors (e.g. syntax error or missing modules).\n                if (hasErrors) {\n                    sendMessage(JSON.stringify({\n                        event: \"client-error\",\n                        errorCount: errors.length,\n                        clientId: __nextDevClientId\n                    }));\n                    handleErrors(errors);\n                    return;\n                }\n                const hasWarnings = Boolean(warnings && warnings.length);\n                if (hasWarnings) {\n                    sendMessage(JSON.stringify({\n                        event: \"client-warning\",\n                        warningCount: warnings.length,\n                        clientId: __nextDevClientId\n                    }));\n                    // Print warnings to the console.\n                    const formattedMessages = (0, _formatwebpackmessages.default)({\n                        warnings: warnings,\n                        errors: []\n                    });\n                    for(let i = 0; i < formattedMessages.warnings.length; i++){\n                        if (i === 5) {\n                            console.warn(\"There were more warnings in other files.\\n\" + \"You can find a complete log in the terminal.\");\n                            break;\n                        }\n                        console.warn((0, _stripansi.default)(formattedMessages.warnings[i]));\n                    }\n                // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n                }\n                sendMessage(JSON.stringify({\n                    event: \"client-success\",\n                    clientId: __nextDevClientId\n                }));\n                if (obj.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n                    // Handle hot updates\n                    handleHotUpdate();\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n            {\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n                });\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n            {\n                const updatedModules = (0, _extractmodulesfromturbopackmessage.extractModulesFromTurbopackMessage)(obj.data);\n                dispatcher.onBeforeRefresh();\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n                    data: obj.data\n                });\n                dispatcher.onRefresh();\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n                    performFullReload(null, sendMessage);\n                }\n                reportHmrLatency(sendMessage, updatedModules);\n                break;\n            }\n        // TODO-APP: make server component change more granular\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n            {\n                sendMessage(JSON.stringify({\n                    event: \"server-component-reload-page\",\n                    clientId: __nextDevClientId\n                }));\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    if (reloading) return;\n                    reloading = true;\n                    return window.location.reload();\n                }\n                (0, _react.startTransition)(()=>{\n                    router.fastRefresh();\n                    dispatcher.onRefresh();\n                });\n                if (false) {}\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE:\n            {\n                sendMessage(JSON.stringify({\n                    event: \"client-reload-page\",\n                    clientId: __nextDevClientId\n                }));\n                if (reloading) return;\n                reloading = true;\n                return window.location.reload();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n            {\n                // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n                return router.fastRefresh();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR:\n            {\n                const { errorJSON } = obj;\n                if (errorJSON) {\n                    const { message, stack } = JSON.parse(errorJSON);\n                    const error = new Error(message);\n                    error.stack = stack;\n                    handleErrors([\n                        error\n                    ]);\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE:\n            {\n                return;\n            }\n        default:\n            {}\n    }\n}\nfunction HotReload(param) {\n    let { assetPrefix, children } = param;\n    const [state, dispatch] = (0, _shared.useErrorOverlayReducer)();\n    const dispatcher = (0, _react.useMemo)(()=>{\n        return {\n            onBuildOk () {\n                dispatch({\n                    type: _shared.ACTION_BUILD_OK\n                });\n            },\n            onBuildError (message) {\n                dispatch({\n                    type: _shared.ACTION_BUILD_ERROR,\n                    message\n                });\n            },\n            onBeforeRefresh () {\n                dispatch({\n                    type: _shared.ACTION_BEFORE_REFRESH\n                });\n            },\n            onRefresh () {\n                dispatch({\n                    type: _shared.ACTION_REFRESH\n                });\n            },\n            onVersionInfo (versionInfo) {\n                dispatch({\n                    type: _shared.ACTION_VERSION_INFO,\n                    versionInfo\n                });\n            }\n        };\n    }, [\n        dispatch\n    ]);\n    const handleOnUnhandledError = (0, _react.useCallback)((error)=>{\n        const errorDetails = error.details;\n        // Component stack is added to the error in use-error-handler in case there was a hydration errror\n        const componentStack = errorDetails == null ? void 0 : errorDetails.componentStack;\n        const warning = errorDetails == null ? void 0 : errorDetails.warning;\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_ERROR,\n            reason: error,\n            frames: (0, _parseStack.parseStack)(error.stack),\n            componentStackFrames: componentStack ? (0, _parsecomponentstack.parseComponentStack)(componentStack) : undefined,\n            warning\n        });\n    }, [\n        dispatch\n    ]);\n    const handleOnUnhandledRejection = (0, _react.useCallback)((reason)=>{\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_REJECTION,\n            reason: reason,\n            frames: (0, _parseStack.parseStack)(reason.stack)\n        });\n    }, [\n        dispatch\n    ]);\n    const handleOnReactError = (0, _react.useCallback)(()=>{\n        _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError = true;\n    }, []);\n    (0, _useerrorhandler.useErrorHandler)(handleOnUnhandledError, handleOnUnhandledRejection);\n    const webSocketRef = (0, _usewebsocket.useWebsocket)(assetPrefix);\n    (0, _usewebsocket.useWebsocketPing)(webSocketRef);\n    const sendMessage = (0, _usewebsocket.useSendMessage)(webSocketRef);\n    const processTurbopackMessage = (0, _usewebsocket.useTurbopack)(sendMessage, (err)=>performFullReload(err, sendMessage));\n    const router = (0, _navigation.useRouter)();\n    (0, _react.useEffect)(()=>{\n        const websocket = webSocketRef.current;\n        if (!websocket) return;\n        const handler = (event)=>{\n            try {\n                const obj = JSON.parse(event.data);\n                processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher);\n            } catch (err) {\n                var _err_stack;\n                console.warn(\"[HMR] Invalid message: \" + event.data + \"\\n\" + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : \"\"));\n            }\n        };\n        websocket.addEventListener(\"message\", handler);\n        return ()=>websocket.removeEventListener(\"message\", handler);\n    }, [\n        sendMessage,\n        router,\n        webSocketRef,\n        dispatcher,\n        processTurbopackMessage\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_ReactDevOverlay.default, {\n        onReactError: handleOnReactError,\n        state: state,\n        children: children\n    });\n}\n_c = HotReload;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-reloader-client.js.map\nvar _c;\n$RefreshReg$(_c, \"HotReload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\n"));

/***/ })

}]);