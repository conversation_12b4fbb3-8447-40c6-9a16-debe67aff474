"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_a"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/inertia.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/inertia.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inertia: function() { return /* binding */ inertia; }\n/* harmony export */ });\n/* harmony import */ var _spring_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./spring/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs\");\n/* harmony import */ var _utils_velocity_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/velocity.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs\");\n\n\n\nfunction inertia({ keyframes, velocity = 0.0, power = 0.8, timeConstant = 325, bounceDamping = 10, bounceStiffness = 500, modifyTarget, min, max, restDelta = 0.5, restSpeed, }) {\n    const origin = keyframes[0];\n    const state = {\n        done: false,\n        value: origin,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / timeConstant);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDelta;\n        state.value = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.value))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = (0,_spring_index_mjs__WEBPACK_IMPORTED_MODULE_0__.spring)({\n            keyframes: [state.value, nearestBoundary(state.value)],\n            velocity: (0,_utils_velocity_mjs__WEBPACK_IMPORTED_MODULE_1__.calcGeneratorVelocity)(calcLatest, t, state.value),\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDelta,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return {\n        calculatedDuration: null,\n        next: (t) => {\n            /**\n             * We need to resolve the friction to figure out if we need a\n             * spring but we don't want to do this twice per frame. So here\n             * we flag if we updated for this frame and later if we did\n             * we can skip doing it again.\n             */\n            let hasUpdatedFrame = false;\n            if (!spring$1 && timeReachedBoundary === undefined) {\n                hasUpdatedFrame = true;\n                applyFriction(t);\n                checkCatchBoundary(t);\n            }\n            /**\n             * If we have a spring and the provided t is beyond the moment the friction\n             * animation crossed the min/max boundary, use the spring.\n             */\n            if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n                return spring$1.next(t - timeReachedBoundary);\n            }\n            else {\n                !hasUpdatedFrame && applyFriction(t);\n                return state;\n            }\n        },\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/inertia.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultEasing: function() { return /* binding */ defaultEasing; },\n/* harmony export */   keyframes: function() { return /* binding */ keyframes; }\n/* harmony export */ });\n/* harmony import */ var _easing_ease_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../easing/ease.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/ease.mjs\");\n/* harmony import */ var _easing_utils_is_easing_array_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../easing/utils/is-easing-array.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-easing-array.mjs\");\n/* harmony import */ var _easing_utils_map_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../easing/utils/map.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/map.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/offsets/default.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\");\n/* harmony import */ var _utils_offsets_time_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/offsets/time.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/time.mjs\");\n\n\n\n\n\n\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || _easing_ease_mjs__WEBPACK_IMPORTED_MODULE_0__.easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({ duration = 300, keyframes: keyframeValues, times, ease = \"easeInOut\", }) {\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = (0,_easing_utils_is_easing_array_mjs__WEBPACK_IMPORTED_MODULE_1__.isEasingArray)(ease)\n        ? ease.map(_easing_utils_map_mjs__WEBPACK_IMPORTED_MODULE_2__.easingDefinitionToFunction)\n        : (0,_easing_utils_map_mjs__WEBPACK_IMPORTED_MODULE_2__.easingDefinitionToFunction)(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = {\n        done: false,\n        value: keyframeValues[0],\n    };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = (0,_utils_offsets_time_mjs__WEBPACK_IMPORTED_MODULE_3__.convertOffsetToTimes)(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframeValues.length\n        ? times\n        : (0,_utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(keyframeValues), duration);\n    const mapTimeToKeyframe = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__.interpolate)(absoluteTimes, keyframeValues, {\n        ease: Array.isArray(easingFunctions)\n            ? easingFunctions\n            : defaultEasing(keyframeValues, easingFunctions),\n    });\n    return {\n        calculatedDuration: duration,\n        next: (t) => {\n            state.value = mapTimeToKeyframe(t);\n            state.done = t >= duration;\n            return state;\n        },\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMva2V5ZnJhbWVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFrRDtBQUNxQjtBQUNDO0FBQ2Q7QUFDTTtBQUNJOztBQUVwRTtBQUNBLHNDQUFzQyx1REFBUztBQUMvQztBQUNBLHFCQUFxQix1RUFBdUU7QUFDNUY7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZ0ZBQWE7QUFDekMsbUJBQW1CLDZFQUEwQjtBQUM3QyxVQUFVLGlGQUEwQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZFQUFvQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUseUVBQWE7QUFDdkIsOEJBQThCLG1FQUFXO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTs7QUFFb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9hbmltYXRpb24vZ2VuZXJhdG9ycy9rZXlmcmFtZXMubWpzP2RkYzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZWFzZUluT3V0IH0gZnJvbSAnLi4vLi4vZWFzaW5nL2Vhc2UubWpzJztcbmltcG9ydCB7IGlzRWFzaW5nQXJyYXkgfSBmcm9tICcuLi8uLi9lYXNpbmcvdXRpbHMvaXMtZWFzaW5nLWFycmF5Lm1qcyc7XG5pbXBvcnQgeyBlYXNpbmdEZWZpbml0aW9uVG9GdW5jdGlvbiB9IGZyb20gJy4uLy4uL2Vhc2luZy91dGlscy9tYXAubWpzJztcbmltcG9ydCB7IGludGVycG9sYXRlIH0gZnJvbSAnLi4vLi4vdXRpbHMvaW50ZXJwb2xhdGUubWpzJztcbmltcG9ydCB7IGRlZmF1bHRPZmZzZXQgfSBmcm9tICcuLi8uLi91dGlscy9vZmZzZXRzL2RlZmF1bHQubWpzJztcbmltcG9ydCB7IGNvbnZlcnRPZmZzZXRUb1RpbWVzIH0gZnJvbSAnLi4vLi4vdXRpbHMvb2Zmc2V0cy90aW1lLm1qcyc7XG5cbmZ1bmN0aW9uIGRlZmF1bHRFYXNpbmcodmFsdWVzLCBlYXNpbmcpIHtcbiAgICByZXR1cm4gdmFsdWVzLm1hcCgoKSA9PiBlYXNpbmcgfHwgZWFzZUluT3V0KS5zcGxpY2UoMCwgdmFsdWVzLmxlbmd0aCAtIDEpO1xufVxuZnVuY3Rpb24ga2V5ZnJhbWVzKHsgZHVyYXRpb24gPSAzMDAsIGtleWZyYW1lczoga2V5ZnJhbWVWYWx1ZXMsIHRpbWVzLCBlYXNlID0gXCJlYXNlSW5PdXRcIiwgfSkge1xuICAgIC8qKlxuICAgICAqIEVhc2luZyBmdW5jdGlvbnMgY2FuIGJlIGV4dGVybmFsbHkgZGVmaW5lZCBhcyBzdHJpbmdzLiBIZXJlIHdlIGNvbnZlcnQgdGhlbVxuICAgICAqIGludG8gYWN0dWFsIGZ1bmN0aW9ucy5cbiAgICAgKi9cbiAgICBjb25zdCBlYXNpbmdGdW5jdGlvbnMgPSBpc0Vhc2luZ0FycmF5KGVhc2UpXG4gICAgICAgID8gZWFzZS5tYXAoZWFzaW5nRGVmaW5pdGlvblRvRnVuY3Rpb24pXG4gICAgICAgIDogZWFzaW5nRGVmaW5pdGlvblRvRnVuY3Rpb24oZWFzZSk7XG4gICAgLyoqXG4gICAgICogVGhpcyBpcyB0aGUgSXRlcmF0b3Itc3BlYyByZXR1cm4gdmFsdWUuIFdlIGVuc3VyZSBpdCdzIG11dGFibGUgcmF0aGVyIHRoYW4gdXNpbmcgYSBnZW5lcmF0b3JcbiAgICAgKiB0byByZWR1Y2UgR0MgZHVyaW5nIGFuaW1hdGlvbi5cbiAgICAgKi9cbiAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgZG9uZTogZmFsc2UsXG4gICAgICAgIHZhbHVlOiBrZXlmcmFtZVZhbHVlc1swXSxcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIHRpbWVzIGFycmF5IGJhc2VkIG9uIHRoZSBwcm92aWRlZCAwLTEgb2Zmc2V0c1xuICAgICAqL1xuICAgIGNvbnN0IGFic29sdXRlVGltZXMgPSBjb252ZXJ0T2Zmc2V0VG9UaW1lcyhcbiAgICAvLyBPbmx5IHVzZSB0aGUgcHJvdmlkZWQgb2Zmc2V0cyBpZiB0aGV5J3JlIHRoZSBjb3JyZWN0IGxlbmd0aFxuICAgIC8vIFRPRE8gTWF5YmUgd2Ugc2hvdWxkIHdhcm4gaGVyZSBpZiB0aGVyZSdzIGEgbGVuZ3RoIG1pc21hdGNoXG4gICAgdGltZXMgJiYgdGltZXMubGVuZ3RoID09PSBrZXlmcmFtZVZhbHVlcy5sZW5ndGhcbiAgICAgICAgPyB0aW1lc1xuICAgICAgICA6IGRlZmF1bHRPZmZzZXQoa2V5ZnJhbWVWYWx1ZXMpLCBkdXJhdGlvbik7XG4gICAgY29uc3QgbWFwVGltZVRvS2V5ZnJhbWUgPSBpbnRlcnBvbGF0ZShhYnNvbHV0ZVRpbWVzLCBrZXlmcmFtZVZhbHVlcywge1xuICAgICAgICBlYXNlOiBBcnJheS5pc0FycmF5KGVhc2luZ0Z1bmN0aW9ucylcbiAgICAgICAgICAgID8gZWFzaW5nRnVuY3Rpb25zXG4gICAgICAgICAgICA6IGRlZmF1bHRFYXNpbmcoa2V5ZnJhbWVWYWx1ZXMsIGVhc2luZ0Z1bmN0aW9ucyksXG4gICAgfSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY2FsY3VsYXRlZER1cmF0aW9uOiBkdXJhdGlvbixcbiAgICAgICAgbmV4dDogKHQpID0+IHtcbiAgICAgICAgICAgIHN0YXRlLnZhbHVlID0gbWFwVGltZVRvS2V5ZnJhbWUodCk7XG4gICAgICAgICAgICBzdGF0ZS5kb25lID0gdCA+PSBkdXJhdGlvbjtcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuXG5leHBvcnQgeyBkZWZhdWx0RWFzaW5nLCBrZXlmcmFtZXMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/find.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/spring/find.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcAngularFreq: function() { return /* binding */ calcAngularFreq; },\n/* harmony export */   findSpring: function() { return /* binding */ findSpring; },\n/* harmony export */   maxDamping: function() { return /* binding */ maxDamping; },\n/* harmony export */   maxDuration: function() { return /* binding */ maxDuration; },\n/* harmony export */   minDamping: function() { return /* binding */ minDamping; },\n/* harmony export */   minDuration: function() { return /* binding */ minDuration; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n\n\n\n\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring({ duration = 800, bounce = 0.25, velocity = 0, mass = 1, }) {\n    let envelope;\n    let derivative;\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.warning)(duration <= (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.secondsToMilliseconds)(maxDuration), \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_2__.clamp)(minDamping, maxDamping, dampingRatio);\n    duration = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_2__.clamp)(minDuration, maxDuration, (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.millisecondsToSeconds)(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.secondsToMilliseconds)(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: 100,\n            damping: 10,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/find.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   spring: function() { return /* binding */ spring; }\n/* harmony export */ });\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _utils_velocity_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/velocity.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs\");\n/* harmony import */ var _find_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./find.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/find.mjs\");\n\n\n\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: 0.0,\n        stiffness: 100,\n        damping: 10,\n        mass: 1.0,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        const derived = (0,_find_mjs__WEBPACK_IMPORTED_MODULE_0__.findSpring)(options);\n        springOptions = {\n            ...springOptions,\n            ...derived,\n            mass: 1.0,\n        };\n        springOptions.isResolvedFromDuration = true;\n    }\n    return springOptions;\n}\nfunction spring({ keyframes, restDelta, restSpeed, ...options }) {\n    const origin = keyframes[0];\n    const target = keyframes[keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -(0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.millisecondsToSeconds)(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.millisecondsToSeconds)(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n    restDelta || (restDelta = isGranularScale ? 0.005 : 0.5);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = (0,_find_mjs__WEBPACK_IMPORTED_MODULE_0__.calcAngularFreq)(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    return {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = initialVelocity;\n                if (t !== 0) {\n                    /**\n                     * We only need to calculate velocity for under-damped springs\n                     * as over- and critically-damped springs can't overshoot, so\n                     * checking only for displacement is enough.\n                     */\n                    if (dampingRatio < 1) {\n                        currentVelocity = (0,_utils_velocity_mjs__WEBPACK_IMPORTED_MODULE_2__.calcGeneratorVelocity)(resolveSpring, t, current);\n                    }\n                    else {\n                        currentVelocity = 0;\n                    }\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorDuration: function() { return /* binding */ calcGeneratorDuration; },\n/* harmony export */   maxGeneratorDuration: function() { return /* binding */ maxGeneratorDuration; }\n/* harmony export */ });\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY2FsYy1kdXJhdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY2FsYy1kdXJhdGlvbi5tanM/NzJkOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEltcGxlbWVudCBhIHByYWN0aWNhbCBtYXggZHVyYXRpb24gZm9yIGtleWZyYW1lIGdlbmVyYXRpb25cbiAqIHRvIHByZXZlbnQgaW5maW5pdGUgbG9vcHNcbiAqL1xuY29uc3QgbWF4R2VuZXJhdG9yRHVyYXRpb24gPSAyMDAwMDtcbmZ1bmN0aW9uIGNhbGNHZW5lcmF0b3JEdXJhdGlvbihnZW5lcmF0b3IpIHtcbiAgICBsZXQgZHVyYXRpb24gPSAwO1xuICAgIGNvbnN0IHRpbWVTdGVwID0gNTA7XG4gICAgbGV0IHN0YXRlID0gZ2VuZXJhdG9yLm5leHQoZHVyYXRpb24pO1xuICAgIHdoaWxlICghc3RhdGUuZG9uZSAmJiBkdXJhdGlvbiA8IG1heEdlbmVyYXRvckR1cmF0aW9uKSB7XG4gICAgICAgIGR1cmF0aW9uICs9IHRpbWVTdGVwO1xuICAgICAgICBzdGF0ZSA9IGdlbmVyYXRvci5uZXh0KGR1cmF0aW9uKTtcbiAgICB9XG4gICAgcmV0dXJuIGR1cmF0aW9uID49IG1heEdlbmVyYXRvckR1cmF0aW9uID8gSW5maW5pdHkgOiBkdXJhdGlvbjtcbn1cblxuZXhwb3J0IHsgY2FsY0dlbmVyYXRvckR1cmF0aW9uLCBtYXhHZW5lcmF0b3JEdXJhdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorVelocity: function() { return /* binding */ calcGeneratorVelocity; }\n/* harmony export */ });\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n\n\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - velocitySampleDuration, 0);\n    return (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_0__.velocityPerSecond)(current - resolveValue(prevT), t - prevT);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvdmVsb2NpdHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJFOztBQUUzRSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBLFdBQVcsaUZBQWlCO0FBQzVCOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL3ZlbG9jaXR5Lm1qcz83MDk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlbG9jaXR5UGVyU2Vjb25kIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvdmVsb2NpdHktcGVyLXNlY29uZC5tanMnO1xuXG5jb25zdCB2ZWxvY2l0eVNhbXBsZUR1cmF0aW9uID0gNTsgLy8gbXNcbmZ1bmN0aW9uIGNhbGNHZW5lcmF0b3JWZWxvY2l0eShyZXNvbHZlVmFsdWUsIHQsIGN1cnJlbnQpIHtcbiAgICBjb25zdCBwcmV2VCA9IE1hdGgubWF4KHQgLSB2ZWxvY2l0eVNhbXBsZUR1cmF0aW9uLCAwKTtcbiAgICByZXR1cm4gdmVsb2NpdHlQZXJTZWNvbmQoY3VycmVudCAtIHJlc29sdmVWYWx1ZShwcmV2VCksIHQgLSBwcmV2VCk7XG59XG5cbmV4cG9ydCB7IGNhbGNHZW5lcmF0b3JWZWxvY2l0eSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateMotionValue: function() { return /* binding */ animateMotionValue; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _utils_use_instant_transition_state_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/use-instant-transition-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-instant-transition-state.mjs\");\n/* harmony import */ var _animators_waapi_create_accelerated_animation_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../animators/waapi/create-accelerated-animation.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs\");\n/* harmony import */ var _animators_instant_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../animators/instant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/instant.mjs\");\n/* harmony import */ var _utils_default_transitions_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/default-transitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs\");\n/* harmony import */ var _utils_is_animatable_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/is-animatable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs\");\n/* harmony import */ var _utils_keyframes_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/keyframes.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs\");\n/* harmony import */ var _utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/transitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs\");\n/* harmony import */ var _animators_js_index_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../animators/js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/GlobalConfig.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst animateMotionValue = (valueName, value, target, transition = {}) => {\n    return (onComplete) => {\n        const valueTransition = (0,_utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_0__.getValueTransition)(transition, valueName) || {};\n        /**\n         * Most transition values are currently completely overwritten by value-specific\n         * transitions. In the future it'd be nicer to blend these transitions. But for now\n         * delay actually does inherit from the root transition if not value-specific.\n         */\n        const delay = valueTransition.delay || transition.delay || 0;\n        /**\n         * Elapsed isn't a public transition option but can be passed through from\n         * optimized appear effects in milliseconds.\n         */\n        let { elapsed = 0 } = transition;\n        elapsed = elapsed - (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.secondsToMilliseconds)(delay);\n        const keyframes = (0,_utils_keyframes_mjs__WEBPACK_IMPORTED_MODULE_2__.getKeyframes)(value, valueName, target, valueTransition);\n        /**\n         * Check if we're able to animate between the start and end keyframes,\n         * and throw a warning if we're attempting to animate between one that's\n         * animatable and another that isn't.\n         */\n        const originKeyframe = keyframes[0];\n        const targetKeyframe = keyframes[keyframes.length - 1];\n        const isOriginAnimatable = (0,_utils_is_animatable_mjs__WEBPACK_IMPORTED_MODULE_3__.isAnimatable)(valueName, originKeyframe);\n        const isTargetAnimatable = (0,_utils_is_animatable_mjs__WEBPACK_IMPORTED_MODULE_3__.isAnimatable)(valueName, targetKeyframe);\n        (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__.warning)(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n        let options = {\n            keyframes,\n            velocity: value.getVelocity(),\n            ease: \"easeOut\",\n            ...valueTransition,\n            delay: -elapsed,\n            onUpdate: (v) => {\n                value.set(v);\n                valueTransition.onUpdate && valueTransition.onUpdate(v);\n            },\n            onComplete: () => {\n                onComplete();\n                valueTransition.onComplete && valueTransition.onComplete();\n            },\n        };\n        /**\n         * If there's no transition defined for this value, we can generate\n         * unqiue transition settings for this value.\n         */\n        if (!(0,_utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_0__.isTransitionDefined)(valueTransition)) {\n            options = {\n                ...options,\n                ...(0,_utils_default_transitions_mjs__WEBPACK_IMPORTED_MODULE_5__.getDefaultTransition)(valueName, options),\n            };\n        }\n        /**\n         * Both WAAPI and our internal animation functions use durations\n         * as defined by milliseconds, while our external API defines them\n         * as seconds.\n         */\n        if (options.duration) {\n            options.duration = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.secondsToMilliseconds)(options.duration);\n        }\n        if (options.repeatDelay) {\n            options.repeatDelay = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.secondsToMilliseconds)(options.repeatDelay);\n        }\n        if (!isOriginAnimatable ||\n            !isTargetAnimatable ||\n            _utils_use_instant_transition_state_mjs__WEBPACK_IMPORTED_MODULE_6__.instantAnimationState.current ||\n            valueTransition.type === false ||\n            _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionGlobalConfig.skipAnimations) {\n            /**\n             * If we can't animate this value, or the global instant animation flag is set,\n             * or this is simply defined as an instant transition, return an instant transition.\n             */\n            return (0,_animators_instant_mjs__WEBPACK_IMPORTED_MODULE_8__.createInstantAnimation)(_utils_use_instant_transition_state_mjs__WEBPACK_IMPORTED_MODULE_6__.instantAnimationState.current\n                ? { ...options, delay: 0 }\n                : options);\n        }\n        /**\n         * Animate via WAAPI if possible.\n         */\n        if (\n        /**\n         * If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        !transition.isHandoff &&\n            value.owner &&\n            value.owner.current instanceof HTMLElement &&\n            /**\n             * If we're outputting values to onUpdate then we can't use WAAPI as there's\n             * no way to read the value from WAAPI every frame.\n             */\n            !value.owner.getProps().onUpdate) {\n            const acceleratedAnimation = (0,_animators_waapi_create_accelerated_animation_mjs__WEBPACK_IMPORTED_MODULE_9__.createAcceleratedAnimation)(value, valueName, options);\n            if (acceleratedAnimation)\n                return acceleratedAnimation;\n        }\n        /**\n         * If we didn't create an accelerated animation, create a JS animation\n         */\n        return (0,_animators_js_index_mjs__WEBPACK_IMPORTED_MODULE_10__.animateValue)(options);\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/single-value.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/interfaces/single-value.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateSingleValue: function() { return /* binding */ animateSingleValue; }\n/* harmony export */ });\n/* harmony import */ var _motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\");\n/* harmony import */ var _value_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../value/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = (0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value) ? value : (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_1__.motionValue)(value);\n    motionValue$1.start((0,_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__.animateMotionValue)(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2ludGVyZmFjZXMvc2luZ2xlLXZhbHVlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ0o7QUFDa0I7O0FBRXRFO0FBQ0EsMEJBQTBCLCtFQUFhLGtCQUFrQiw2REFBVztBQUNwRSx3QkFBd0IscUVBQWtCO0FBQzFDO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2ludGVyZmFjZXMvc2luZ2xlLXZhbHVlLm1qcz9iZThlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFuaW1hdGVNb3Rpb25WYWx1ZSB9IGZyb20gJy4vbW90aW9uLXZhbHVlLm1qcyc7XG5pbXBvcnQgeyBtb3Rpb25WYWx1ZSB9IGZyb20gJy4uLy4uL3ZhbHVlL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBpc01vdGlvblZhbHVlIH0gZnJvbSAnLi4vLi4vdmFsdWUvdXRpbHMvaXMtbW90aW9uLXZhbHVlLm1qcyc7XG5cbmZ1bmN0aW9uIGFuaW1hdGVTaW5nbGVWYWx1ZSh2YWx1ZSwga2V5ZnJhbWVzLCBvcHRpb25zKSB7XG4gICAgY29uc3QgbW90aW9uVmFsdWUkMSA9IGlzTW90aW9uVmFsdWUodmFsdWUpID8gdmFsdWUgOiBtb3Rpb25WYWx1ZSh2YWx1ZSk7XG4gICAgbW90aW9uVmFsdWUkMS5zdGFydChhbmltYXRlTW90aW9uVmFsdWUoXCJcIiwgbW90aW9uVmFsdWUkMSwga2V5ZnJhbWVzLCBvcHRpb25zKSk7XG4gICAgcmV0dXJuIG1vdGlvblZhbHVlJDEuYW5pbWF0aW9uO1xufVxuXG5leHBvcnQgeyBhbmltYXRlU2luZ2xlVmFsdWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/single-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs":
/*!*******************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateTarget: function() { return /* binding */ animateTarget; }\n/* harmony export */ });\n/* harmony import */ var _render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../render/html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../optimized-appear/data-id.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs\");\n/* harmony import */ var _motion_value_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\");\n/* harmony import */ var _value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../value/use-will-change/is.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs\");\n/* harmony import */ var _render_utils_setters_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../render/utils/setters.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/setters.mjs\");\n/* harmony import */ var _utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/transitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n    const current = value.get();\n    if (Array.isArray(target)) {\n        for (let i = 0; i < target.length; i++) {\n            if (target[i] !== current)\n                return true;\n        }\n    }\n    else {\n        return current !== target;\n    }\n}\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            elapsed: 0,\n            ...(0,_utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_0__.getValueTransition)(transition || {}, key),\n        };\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (window.HandoffAppearAnimations) {\n            const appearId = visualElement.getProps()[_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_1__.optimizedAppearDataAttribute];\n            if (appearId) {\n                const elapsed = window.HandoffAppearAnimations(appearId, key, value, _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame);\n                if (elapsed !== null) {\n                    valueTransition.elapsed = elapsed;\n                    valueTransition.isHandoff = true;\n                }\n            }\n        }\n        let canSkip = !valueTransition.isHandoff &&\n            !hasKeyframesChanged(value, valueTarget);\n        if (valueTransition.type === \"spring\" &&\n            (value.getVelocity() || valueTransition.velocity)) {\n            canSkip = false;\n        }\n        /**\n         * Temporarily disable skipping animations if there's an animation in\n         * progress. Better would be to track the current target of a value\n         * and compare that against valueTarget.\n         */\n        if (value.animation) {\n            canSkip = false;\n        }\n        if (canSkip)\n            continue;\n        value.start((0,_motion_value_mjs__WEBPACK_IMPORTED_MODULE_3__.animateMotionValue)(key, value, valueTarget, visualElement.shouldReduceMotion && _render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_4__.transformProps.has(key)\n            ? { type: false }\n            : valueTransition));\n        const animation = value.animation;\n        if ((0,_value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_5__.isWillChangeMotionValue)(willChange)) {\n            willChange.add(key);\n            animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            transitionEnd && (0,_render_utils_setters_mjs__WEBPACK_IMPORTED_MODULE_6__.setTarget)(visualElement, transitionEnd);\n        });\n    }\n    return animations;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateVariant: function() { return /* binding */ animateVariant; },\n/* harmony export */   sortByTreeOrder: function() { return /* binding */ sortByTreeOrder; }\n/* harmony export */ });\n/* harmony import */ var _render_utils_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/utils/resolve-dynamic-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n/* harmony import */ var _visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./visual-element-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs\");\n\n\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = (0,_render_utils_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveVariant)(visualElement, variant, options.custom);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all((0,_visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_1__.animateTarget)(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n    const generateStaggerDuration = staggerDirection === 1\n        ? (i = 0) => i * staggerChildren\n        : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delayChildren + generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateVisualElement: function() { return /* binding */ animateVisualElement; }\n/* harmony export */ });\n/* harmony import */ var _render_utils_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../render/utils/resolve-dynamic-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n/* harmony import */ var _visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./visual-element-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs\");\n/* harmony import */ var _visual_element_variant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./visual-element-variant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs\");\n\n\n\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => (0,_visual_element_variant_mjs__WEBPACK_IMPORTED_MODULE_0__.animateVariant)(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = (0,_visual_element_variant_mjs__WEBPACK_IMPORTED_MODULE_0__.animateVariant)(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? (0,_render_utils_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_1__.resolveVariant)(visualElement, definition, options.custom)\n            : definition;\n        animation = Promise.all((0,_visual_element_target_mjs__WEBPACK_IMPORTED_MODULE_2__.animateTarget)(visualElement, resolvedDefinition, options));\n    }\n    return animation.then(() => visualElement.notify(\"AnimationComplete\", definition));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   optimizedAppearDataAttribute: function() { return /* binding */ optimizedAppearDataAttribute; },\n/* harmony export */   optimizedAppearDataId: function() { return /* binding */ optimizedAppearDataId; }\n/* harmony export */ });\n/* harmony import */ var _render_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/dom/utils/camel-to-dash.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\");\n\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + (0,_render_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_0__.camelToDash)(optimizedAppearDataId);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL29wdGltaXplZC1hcHBlYXIvZGF0YS1pZC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVFOztBQUV2RTtBQUNBLCtDQUErQyxnRkFBVzs7QUFFSyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi9vcHRpbWl6ZWQtYXBwZWFyL2RhdGEtaWQubWpzP2UxZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FtZWxUb0Rhc2ggfSBmcm9tICcuLi8uLi9yZW5kZXIvZG9tL3V0aWxzL2NhbWVsLXRvLWRhc2gubWpzJztcblxuY29uc3Qgb3B0aW1pemVkQXBwZWFyRGF0YUlkID0gXCJmcmFtZXJBcHBlYXJJZFwiO1xuY29uc3Qgb3B0aW1pemVkQXBwZWFyRGF0YUF0dHJpYnV0ZSA9IFwiZGF0YS1cIiArIGNhbWVsVG9EYXNoKG9wdGltaXplZEFwcGVhckRhdGFJZCk7XG5cbmV4cG9ydCB7IG9wdGltaXplZEFwcGVhckRhdGFBdHRyaWJ1dGUsIG9wdGltaXplZEFwcGVhckRhdGFJZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultTransition: function() { return /* binding */ getDefaultTransition; }\n/* harmony export */ });\n/* harmony import */ var _render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (_render_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAnimatable: function() { return /* binding */ isAnimatable; }\n/* harmony export */ });\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (key, value) => {\n    // If the list of keys tat might be non-animatable grows, replace with Set\n    if (key === \"zIndex\")\n        return false;\n    // If it's a number or a keyframes array, we can animate it. We might at some point\n    // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n    // but for now lets leave it like this for performance reasons\n    if (typeof value === \"number\" || Array.isArray(value))\n        return true;\n    if (typeof value === \"string\" && // It's animatable if we have a string\n        (_value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_0__.complex.test(value) || value === \"0\") && // And it contains numbers and/or colors\n        !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n    ) {\n        return true;\n    }\n    return false;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAnimationControls: function() { return /* binding */ isAnimationControls; }\n/* harmony export */ });\nfunction isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLWFuaW1hdGlvbi1jb250cm9scy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRStCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLWFuaW1hdGlvbi1jb250cm9scy5tanM/NTFjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc0FuaW1hdGlvbkNvbnRyb2xzKHYpIHtcbiAgICByZXR1cm4gKHYgIT09IG51bGwgJiZcbiAgICAgICAgdHlwZW9mIHYgPT09IFwib2JqZWN0XCIgJiZcbiAgICAgICAgdHlwZW9mIHYuc3RhcnQgPT09IFwiZnVuY3Rpb25cIik7XG59XG5cbmV4cG9ydCB7IGlzQW5pbWF0aW9uQ29udHJvbHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isKeyframesTarget: function() { return /* binding */ isKeyframesTarget; }\n/* harmony export */ });\nconst isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLWtleWZyYW1lcy10YXJnZXQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRTZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLWtleWZyYW1lcy10YXJnZXQubWpzPzk0MWYiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNLZXlmcmFtZXNUYXJnZXQgPSAodikgPT4ge1xuICAgIHJldHVybiBBcnJheS5pc0FycmF5KHYpO1xufTtcblxuZXhwb3J0IHsgaXNLZXlmcmFtZXNUYXJnZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-none.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/is-none.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNone: function() { return /* binding */ isNone; }\n/* harmony export */ });\n/* harmony import */ var _utils_is_zero_value_string_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/is-zero-value-string.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs\");\n\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || (0,_utils_is_zero_value_string_mjs__WEBPACK_IMPORTED_MODULE_0__.isZeroValueString)(value);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2lzLW5vbmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlFOztBQUV6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGtGQUFpQjtBQUNyRTtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi91dGlscy9pcy1ub25lLm1qcz83Mjc4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzWmVyb1ZhbHVlU3RyaW5nIH0gZnJvbSAnLi4vLi4vdXRpbHMvaXMtemVyby12YWx1ZS1zdHJpbmcubWpzJztcblxuZnVuY3Rpb24gaXNOb25lKHZhbHVlKSB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICByZXR1cm4gdmFsdWUgPT09IDA7XG4gICAgfVxuICAgIGVsc2UgaWYgKHZhbHVlICE9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZSA9PT0gXCJub25lXCIgfHwgdmFsdWUgPT09IFwiMFwiIHx8IGlzWmVyb1ZhbHVlU3RyaW5nKHZhbHVlKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGlzTm9uZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-none.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getKeyframes: function() { return /* binding */ getKeyframes; }\n/* harmony export */ });\n/* harmony import */ var _render_dom_value_types_animatable_none_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../render/dom/value-types/animatable-none.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs\");\n/* harmony import */ var _is_animatable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-animatable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animatable.mjs\");\n/* harmony import */ var _is_none_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is-none.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-none.mjs\");\n\n\n\n\nfunction getKeyframes(value, valueName, target, transition) {\n    const isTargetAnimatable = (0,_is_animatable_mjs__WEBPACK_IMPORTED_MODULE_0__.isAnimatable)(valueName, target);\n    let keyframes;\n    if (Array.isArray(target)) {\n        keyframes = [...target];\n    }\n    else {\n        keyframes = [null, target];\n    }\n    const defaultOrigin = transition.from !== undefined ? transition.from : value.get();\n    let animatableTemplateValue = undefined;\n    const noneKeyframeIndexes = [];\n    for (let i = 0; i < keyframes.length; i++) {\n        /**\n         * Fill null/wildcard keyframes\n         */\n        if (keyframes[i] === null) {\n            keyframes[i] = i === 0 ? defaultOrigin : keyframes[i - 1];\n        }\n        if ((0,_is_none_mjs__WEBPACK_IMPORTED_MODULE_1__.isNone)(keyframes[i])) {\n            noneKeyframeIndexes.push(i);\n        }\n        // TODO: Clean this conditional, it works for now\n        if (typeof keyframes[i] === \"string\" &&\n            keyframes[i] !== \"none\" &&\n            keyframes[i] !== \"0\") {\n            animatableTemplateValue = keyframes[i];\n        }\n    }\n    if (isTargetAnimatable &&\n        noneKeyframeIndexes.length &&\n        animatableTemplateValue) {\n        for (let i = 0; i < noneKeyframeIndexes.length; i++) {\n            const index = noneKeyframeIndexes[i];\n            keyframes[index] = (0,_render_dom_value_types_animatable_none_mjs__WEBPACK_IMPORTED_MODULE_2__.getAnimatableNone)(valueName, animatableTemplateValue);\n        }\n    }\n    return keyframes;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueTransition: function() { return /* binding */ getValueTransition; },\n/* harmony export */   isTransitionDefined: function() { return /* binding */ isTransitionDefined; }\n/* harmony export */ });\n/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n    return transition[key] || transition[\"default\"] || transition;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL3RyYW5zaXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isc0lBQXNJO0FBQ3JLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL3RyYW5zaXRpb25zLm1qcz85YTI5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGVjaWRlIHdoZXRoZXIgYSB0cmFuc2l0aW9uIGlzIGRlZmluZWQgb24gYSBnaXZlbiBUcmFuc2l0aW9uLlxuICogVGhpcyBmaWx0ZXJzIG91dCBvcmNoZXN0cmF0aW9uIG9wdGlvbnMgYW5kIHJldHVybnMgdHJ1ZVxuICogaWYgYW55IG9wdGlvbnMgYXJlIGxlZnQuXG4gKi9cbmZ1bmN0aW9uIGlzVHJhbnNpdGlvbkRlZmluZWQoeyB3aGVuLCBkZWxheTogX2RlbGF5LCBkZWxheUNoaWxkcmVuLCBzdGFnZ2VyQ2hpbGRyZW4sIHN0YWdnZXJEaXJlY3Rpb24sIHJlcGVhdCwgcmVwZWF0VHlwZSwgcmVwZWF0RGVsYXksIGZyb20sIGVsYXBzZWQsIC4uLnRyYW5zaXRpb24gfSkge1xuICAgIHJldHVybiAhIU9iamVjdC5rZXlzKHRyYW5zaXRpb24pLmxlbmd0aDtcbn1cbmZ1bmN0aW9uIGdldFZhbHVlVHJhbnNpdGlvbih0cmFuc2l0aW9uLCBrZXkpIHtcbiAgICByZXR1cm4gdHJhbnNpdGlvbltrZXldIHx8IHRyYW5zaXRpb25bXCJkZWZhdWx0XCJdIHx8IHRyYW5zaXRpb247XG59XG5cbmV4cG9ydCB7IGdldFZhbHVlVHJhbnNpdGlvbiwgaXNUcmFuc2l0aW9uRGVmaW5lZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: function() { return /* binding */ PopChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, { ref })));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: function() { return /* binding */ PresenceChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n\n\n\n\n\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__.PopChild, { isPresent: isPresent }, children);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: function() { return /* binding */ AnimatePresence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-force-update.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs\");\n/* harmony import */ var _utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-is-mounted.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/use-unmount-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child) => {\n        if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__.invariant)(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LayoutGroupContext).forceRender || (0,_utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__.useForceUpdate)()[0];\n    const isMounted = (0,_utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsMounted)();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    (0,_utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__.useUnmountEffect)(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, childrenToRender.map((child) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if ( true &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child))));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPresent: function() { return /* binding */ isPresent; },\n/* harmony export */   useIsPresent: function() { return /* binding */ useIsPresent; },\n/* harmony export */   usePresence: function() { return /* binding */ usePresence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n\n\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_1__.PresenceContext);\n    if (context === null)\n        return [true, null];\n    const { isPresent, onExitComplete, register } = context;\n    // It's safe to call the following hooks conditionally (after an early return) because the context will always\n    // either be null or non-null for the lifespan of the component.\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => register(id), []);\n    const safeToRemove = () => onExitComplete && onExitComplete(id);\n    return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n    return isPresent((0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_1__.PresenceContext));\n}\nfunction isPresent(context) {\n    return context === null ? true : context.isPresent;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutGroupContext: function() { return /* binding */ LayoutGroupContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst LayoutGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9MYXlvdXRHcm91cENvbnRleHQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDOztBQUV0QywyQkFBMkIsb0RBQWEsR0FBRzs7QUFFYiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2NvbnRleHQvTGF5b3V0R3JvdXBDb250ZXh0Lm1qcz8xNzhlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IExheW91dEdyb3VwQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoe30pO1xuXG5leHBvcnQgeyBMYXlvdXRHcm91cENvbnRleHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/LazyContext.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LazyContext: function() { return /* binding */ LazyContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst LazyContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ strict: false });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9MYXp5Q29udGV4dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7O0FBRXRDLG9CQUFvQixvREFBYSxHQUFHLGVBQWU7O0FBRTVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9MYXp5Q29udGV4dC5tanM/OTk1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBMYXp5Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQoeyBzdHJpY3Q6IGZhbHNlIH0pO1xuXG5leHBvcnQgeyBMYXp5Q29udGV4dCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionConfigContext: function() { return /* binding */ MotionConfigContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n/**\n * @public\n */\nconst MotionConfigContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    transformPagePoint: (p) => p,\n    isStatic: false,\n    reducedMotion: \"never\",\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db25maWdDb250ZXh0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQzs7QUFFdEM7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLG9EQUFhO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db25maWdDb250ZXh0Lm1qcz8zMGQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQHB1YmxpY1xuICovXG5jb25zdCBNb3Rpb25Db25maWdDb250ZXh0ID0gY3JlYXRlQ29udGV4dCh7XG4gICAgdHJhbnNmb3JtUGFnZVBvaW50OiAocCkgPT4gcCxcbiAgICBpc1N0YXRpYzogZmFsc2UsXG4gICAgcmVkdWNlZE1vdGlvbjogXCJuZXZlclwiLFxufSk7XG5cbmV4cG9ydCB7IE1vdGlvbkNvbmZpZ0NvbnRleHQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateMotionContext: function() { return /* binding */ useCreateMotionContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs\");\n\n\n\n\nfunction useCreateMotionContext(props) {\n    const { initial, animate } = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getCurrentTreeVariants)(props, (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_index_mjs__WEBPACK_IMPORTED_MODULE_2__.MotionContext));\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({ initial, animate }), [variantLabelsAsDependency(initial), variantLabelsAsDependency(animate)]);\n}\nfunction variantLabelsAsDependency(prop) {\n    return Array.isArray(prop) ? prop.join(\" \") : prop;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db250ZXh0L2NyZWF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNBO0FBQ1M7O0FBRXJEO0FBQ0EsWUFBWSxtQkFBbUIsRUFBRSxrRUFBc0IsUUFBUSxpREFBVSxDQUFDLHFEQUFhO0FBQ3ZGLFdBQVcsOENBQU8sVUFBVSxrQkFBa0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7O0FBRWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db250ZXh0L2NyZWF0ZS5tanM/Y2U5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0LCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTW90aW9uQ29udGV4dCB9IGZyb20gJy4vaW5kZXgubWpzJztcbmltcG9ydCB7IGdldEN1cnJlbnRUcmVlVmFyaWFudHMgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNyZWF0ZU1vdGlvbkNvbnRleHQocHJvcHMpIHtcbiAgICBjb25zdCB7IGluaXRpYWwsIGFuaW1hdGUgfSA9IGdldEN1cnJlbnRUcmVlVmFyaWFudHMocHJvcHMsIHVzZUNvbnRleHQoTW90aW9uQ29udGV4dCkpO1xuICAgIHJldHVybiB1c2VNZW1vKCgpID0+ICh7IGluaXRpYWwsIGFuaW1hdGUgfSksIFt2YXJpYW50TGFiZWxzQXNEZXBlbmRlbmN5KGluaXRpYWwpLCB2YXJpYW50TGFiZWxzQXNEZXBlbmRlbmN5KGFuaW1hdGUpXSk7XG59XG5mdW5jdGlvbiB2YXJpYW50TGFiZWxzQXNEZXBlbmRlbmN5KHByb3ApIHtcbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwcm9wKSA/IHByb3Auam9pbihcIiBcIikgOiBwcm9wO1xufVxuXG5leHBvcnQgeyB1c2VDcmVhdGVNb3Rpb25Db250ZXh0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionContext: function() { return /* binding */ MotionContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst MotionContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db250ZXh0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQzs7QUFFdEMsc0JBQXNCLG9EQUFhLEdBQUc7O0FBRWIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9jb250ZXh0L01vdGlvbkNvbnRleHQvaW5kZXgubWpzP2RlODQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcblxuY29uc3QgTW90aW9uQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoe30pO1xuXG5leHBvcnQgeyBNb3Rpb25Db250ZXh0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentTreeVariants: function() { return /* binding */ getCurrentTreeVariants; }\n/* harmony export */ });\n/* harmony import */ var _render_utils_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../render/utils/is-variant-label.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../render/utils/is-controlling-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n\n\n\nfunction getCurrentTreeVariants(props, context) {\n    if ((0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_0__.isControllingVariants)(props)) {\n        const { initial, animate } = props;\n        return {\n            initial: initial === false || (0,_render_utils_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_1__.isVariantLabel)(initial)\n                ? initial\n                : undefined,\n            animate: (0,_render_utils_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_1__.isVariantLabel)(animate) ? animate : undefined,\n        };\n    }\n    return props.inherit !== false ? context : {};\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db250ZXh0L3V0aWxzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUU7QUFDYzs7QUFFdkY7QUFDQSxRQUFRLGdHQUFxQjtBQUM3QixnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0EsMENBQTBDLGtGQUFjO0FBQ3hEO0FBQ0E7QUFDQSxxQkFBcUIsa0ZBQWM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Nb3Rpb25Db250ZXh0L3V0aWxzLm1qcz8xZTUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzVmFyaWFudExhYmVsIH0gZnJvbSAnLi4vLi4vcmVuZGVyL3V0aWxzL2lzLXZhcmlhbnQtbGFiZWwubWpzJztcbmltcG9ydCB7IGlzQ29udHJvbGxpbmdWYXJpYW50cyB9IGZyb20gJy4uLy4uL3JlbmRlci91dGlscy9pcy1jb250cm9sbGluZy12YXJpYW50cy5tanMnO1xuXG5mdW5jdGlvbiBnZXRDdXJyZW50VHJlZVZhcmlhbnRzKHByb3BzLCBjb250ZXh0KSB7XG4gICAgaWYgKGlzQ29udHJvbGxpbmdWYXJpYW50cyhwcm9wcykpIHtcbiAgICAgICAgY29uc3QgeyBpbml0aWFsLCBhbmltYXRlIH0gPSBwcm9wcztcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGluaXRpYWw6IGluaXRpYWwgPT09IGZhbHNlIHx8IGlzVmFyaWFudExhYmVsKGluaXRpYWwpXG4gICAgICAgICAgICAgICAgPyBpbml0aWFsXG4gICAgICAgICAgICAgICAgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBhbmltYXRlOiBpc1ZhcmlhbnRMYWJlbChhbmltYXRlKSA/IGFuaW1hdGUgOiB1bmRlZmluZWQsXG4gICAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiBwcm9wcy5pbmhlcml0ICE9PSBmYWxzZSA/IGNvbnRleHQgOiB7fTtcbn1cblxuZXhwb3J0IHsgZ2V0Q3VycmVudFRyZWVWYXJpYW50cyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/PresenceContext.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceContext: function() { return /* binding */ PresenceContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n/**\n * @public\n */\nconst PresenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9QcmVzZW5jZUNvbnRleHQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDOztBQUV0QztBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isb0RBQWE7O0FBRVYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9jb250ZXh0L1ByZXNlbmNlQ29udGV4dC5tanM/ZmY3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuXG4vKipcbiAqIEBwdWJsaWNcbiAqL1xuY29uc3QgUHJlc2VuY2VDb250ZXh0ID0gY3JlYXRlQ29udGV4dChudWxsKTtcblxuZXhwb3J0IHsgUHJlc2VuY2VDb250ZXh0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwitchLayoutGroupContext: function() { return /* binding */ SwitchLayoutGroupContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n/**\n * Internal, exported only for usage in Framer\n */\nconst SwitchLayoutGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29udGV4dC9Td2l0Y2hMYXlvdXRHcm91cENvbnRleHQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDOztBQUV0QztBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsb0RBQWEsR0FBRzs7QUFFYiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2NvbnRleHQvU3dpdGNoTGF5b3V0R3JvdXBDb250ZXh0Lm1qcz80NzA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogSW50ZXJuYWwsIGV4cG9ydGVkIG9ubHkgZm9yIHVzYWdlIGluIEZyYW1lclxuICovXG5jb25zdCBTd2l0Y2hMYXlvdXRHcm91cENvbnRleHQgPSBjcmVhdGVDb250ZXh0KHt9KTtcblxuZXhwb3J0IHsgU3dpdGNoTGF5b3V0R3JvdXBDb250ZXh0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\n"));

/***/ })

}]);