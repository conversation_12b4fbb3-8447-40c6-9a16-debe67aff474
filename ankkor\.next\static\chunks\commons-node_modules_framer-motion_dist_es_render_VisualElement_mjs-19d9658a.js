"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_render_VisualElement_mjs-19d9658a"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/VisualElement.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/VisualElement.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualElement: function() { return /* binding */ VisualElement; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../projection/geometry/models.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../utils/is-ref-object.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _utils_reduced_motion_index_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/reduced-motion/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs\");\n/* harmony import */ var _utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/reduced-motion/state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs\");\n/* harmony import */ var _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../utils/subscription-manager.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\");\n/* harmony import */ var _value_index_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../value/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../value/use-will-change/is.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/is-controlling-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n/* harmony import */ var _utils_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/is-variant-label.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _utils_motion_values_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/motion-values.mjs\");\n/* harmony import */ var _utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./utils/resolve-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../motion/features/definitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _utils_variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/variant-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n/* harmony import */ var _store_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./store.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/store.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst featureNames = Object.keys(_motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\nconst numVariantProps = _utils_variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__.variantProps.length;\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    constructor({ parent, props, presenceContext, reducedMotionConfig, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.scheduleRender = () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.render(this.render, false, true);\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.isControllingVariants = (0,_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_3__.isControllingVariants)(props);\n        this.isVariantNode = (0,_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_3__.isVariantNode)(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't neccessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {});\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && (0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.isMotionValue)(value)) {\n                value.set(latestValues[key], false);\n                if ((0,_value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_5__.isWillChangeMotionValue)(willChange)) {\n                    willChange.add(key);\n                }\n            }\n        }\n    }\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps) {\n        return {};\n    }\n    mount(instance) {\n        this.current = instance;\n        _store_mjs__WEBPACK_IMPORTED_MODULE_6__.visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!_utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_7__.hasReducedMotionListener.current) {\n            (0,_utils_reduced_motion_index_mjs__WEBPACK_IMPORTED_MODULE_8__.initPrefersReducedMotion)();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : _utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_7__.prefersReducedMotion.current;\n        if (true) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_9__.warnOnce)(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        _store_mjs__WEBPACK_IMPORTED_MODULE_6__.visualElementStore.delete(this.current);\n        this.projection && this.projection.unmount();\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(this.notifyUpdate);\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            this.features[key].unmount();\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        const valueIsTransform = _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_10__.transformProps.has(key);\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate &&\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(this.notifyUpdate, false, true);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    loadFeatures({ children, ...renderedProps }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n        let ProjectionNodeConstructor;\n        let MeasureLayout;\n        /**\n         * If we're in development mode, check to make sure we're not rendering a motion component\n         * as a child of LazyMotion, as this will break the file-size benefits of using it.\n         */\n        if ( true &&\n            preloadedFeatures &&\n            isStrict) {\n            const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n            renderedProps.ignoreStrict\n                ? (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_11__.warning)(false, strictMessage)\n                : (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_11__.invariant)(false, strictMessage);\n        }\n        for (let i = 0; i < numFeatures; i++) {\n            const name = featureNames[i];\n            const { isEnabled, Feature: FeatureConstructor, ProjectionNode, MeasureLayout: MeasureLayoutComponent, } = _motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[name];\n            if (ProjectionNode)\n                ProjectionNodeConstructor = ProjectionNode;\n            if (isEnabled(renderedProps)) {\n                if (!this.features[name] && FeatureConstructor) {\n                    this.features[name] = new FeatureConstructor(this);\n                }\n                if (MeasureLayoutComponent) {\n                    MeasureLayout = MeasureLayoutComponent;\n                }\n            }\n        }\n        if ((this.type === \"html\" || this.type === \"svg\") &&\n            !this.projection &&\n            ProjectionNodeConstructor) {\n            this.projection = new ProjectionNodeConstructor(this.latestValues, this.parent && this.parent.projection);\n            const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, } = renderedProps;\n            this.projection.setOptions({\n                layoutId,\n                layout,\n                alwaysMeasureLayout: Boolean(drag) ||\n                    (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_12__.isRefObject)(dragConstraints)),\n                visualElement: this,\n                scheduleRender: () => this.scheduleRender(),\n                /**\n                 * TODO: Update options in an effect. This could be tricky as it'll be too late\n                 * to update by the time layout animations run.\n                 * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n                 * ensuring it gets called if there's no potential layout animations.\n                 *\n                 */\n                animationType: typeof layout === \"string\" ? layout : \"both\",\n                initialPromotionConfig: initialLayoutGroupConfig,\n                layoutScroll,\n                layoutRoot,\n            });\n        }\n        return MeasureLayout;\n    }\n    updateFeatures() {\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature.isMounted) {\n                feature.update();\n            }\n            else {\n                feature.mount();\n                feature.isMounted = true;\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.options, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_13__.createBox)();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Make a target animatable by Popmotion. For instance, if we're\n     * trying to animate width from 100px to 100vw we need to measure 100vw\n     * in pixels to determine what we really need to animate to. This is also\n     * pluggable to support Framer's custom value types like Color,\n     * and CSS variables.\n     */\n    makeTargetAnimatable(target, canMutate = true) {\n        return this.makeTargetAnimatableFromInstance(target, this.props, canMutate);\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listener = props[\"on\" + key];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = (0,_utils_motion_values_mjs__WEBPACK_IMPORTED_MODULE_14__.updateMotionValuesFromProps)(this, this.scrapeMotionValuesFromProps(props, this.prevProps), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    getVariantContext(startAtParent = false) {\n        if (startAtParent) {\n            return this.parent ? this.parent.getVariantContext() : undefined;\n        }\n        if (!this.isControllingVariants) {\n            const context = this.parent\n                ? this.parent.getVariantContext() || {}\n                : {};\n            if (this.props.initial !== undefined) {\n                context.initial = this.props.initial;\n            }\n            return context;\n        }\n        const context = {};\n        for (let i = 0; i < numVariantProps; i++) {\n            const name = _utils_variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__.variantProps[i];\n            const prop = this.props[name];\n            if ((0,_utils_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_15__.isVariantLabel)(prop) || prop === false) {\n                context[name] = prop;\n            }\n        }\n        return context;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        if (value !== this.values.get(key)) {\n            this.removeValue(key);\n            this.bindToMotionValue(key, value);\n        }\n        this.values.set(key, value);\n        this.latestValues[key] = value.get();\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_16__.motionValue)(defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key) {\n        var _a;\n        return this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        var _a;\n        const { initial } = this.props;\n        const valueFromInitial = typeof initial === \"string\" || typeof initial === \"object\"\n            ? (_a = (0,_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_17__.resolveVariantFromProps)(this.props, initial)) === null || _a === void 0 ? void 0 : _a[key]\n            : undefined;\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !(0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.isMotionValue)(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_18__.SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/VisualElement.mjs\n"));

/***/ })

}]);