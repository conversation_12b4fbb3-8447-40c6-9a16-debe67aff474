"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_value_i"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/index.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionValue: function() { return /* binding */ MotionValue; },\n/* harmony export */   collectMotionValues: function() { return /* binding */ collectMotionValues; },\n/* harmony export */   motionValue: function() { return /* binding */ motionValue; }\n/* harmony export */ });\n/* harmony import */ var _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/subscription-manager.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     *\n     * @internal\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"10.18.0\";\n        /**\n         * Duration, in milliseconds, since last updating frame.\n         *\n         * @internal\n         */\n        this.timeDelta = 0;\n        /**\n         * Timestamp of the last time this `MotionValue` was updated.\n         *\n         * @internal\n         */\n        this.lastUpdated = 0;\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = false;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            this.prev = this.current;\n            this.current = v;\n            // Update timestamp\n            const { delta, timestamp } = _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData;\n            if (this.lastUpdated !== timestamp) {\n                this.timeDelta = delta;\n                this.lastUpdated = timestamp;\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(this.scheduleVelocityCheck);\n            }\n            // Update update subscribers\n            if (this.prev !== this.current && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update velocity subscribers\n            if (this.events.velocityChange) {\n                this.events.velocityChange.notify(this.getVelocity());\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        /**\n         * Schedule a velocity check for the next frame.\n         *\n         * This is an instanced and bound function to prevent generating a new\n         * function once per frame.\n         *\n         * @internal\n         */\n        this.scheduleVelocityCheck = () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(this.velocityCheck);\n        /**\n         * Updates `prev` with `current` if the value hasn't been updated this frame.\n         * This ensures velocity calculations return `0`.\n         *\n         * This is an instanced and bound function to prevent generating a new\n         * function once per frame.\n         *\n         * @internal\n         */\n        this.velocityCheck = ({ timestamp }) => {\n            if (timestamp !== this.lastUpdated) {\n                this.prev = this.current;\n                if (this.events.velocityChange) {\n                    this.events.velocityChange.notify(this.getVelocity());\n                }\n            }\n        };\n        this.hasAnimated = false;\n        this.prev = this.current = init;\n        this.canTrackVelocity = isFloat(this.current);\n        this.owner = options.owner;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (true) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_1__.warnOnce)(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__.SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     *\n     * @internal\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = prev;\n        this.timeDelta = delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        // This could be isFloat(this.prev) && isFloat(this.current), but that would be wasteful\n        return this.canTrackVelocity\n            ? // These casts could be avoided if parseFloat would be typed better\n                (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_3__.velocityPerSecond)(parseFloat(this.current) -\n                    parseFloat(this.prev), this.timeDelta)\n            : 0;\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     *\n     * @internal\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/hex.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hex: function() { return /* binding */ hex; }\n/* harmony export */ });\n/* harmony import */ var _rgba_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isColorString)(\"#\"),\n    parse: parseHex,\n    transform: _rgba_mjs__WEBPACK_IMPORTED_MODULE_1__.rgba.transform,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7QUFDVTs7QUFFNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHlEQUFhO0FBQ3ZCO0FBQ0EsZUFBZSwyQ0FBSTtBQUNuQjs7QUFFZSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL2hleC5tanM/M2U5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZ2JhIH0gZnJvbSAnLi9yZ2JhLm1qcyc7XG5pbXBvcnQgeyBpc0NvbG9yU3RyaW5nIH0gZnJvbSAnLi91dGlscy5tanMnO1xuXG5mdW5jdGlvbiBwYXJzZUhleCh2KSB7XG4gICAgbGV0IHIgPSBcIlwiO1xuICAgIGxldCBnID0gXCJcIjtcbiAgICBsZXQgYiA9IFwiXCI7XG4gICAgbGV0IGEgPSBcIlwiO1xuICAgIC8vIElmIHdlIGhhdmUgNiBjaGFyYWN0ZXJzLCBpZSAjRkYwMDAwXG4gICAgaWYgKHYubGVuZ3RoID4gNSkge1xuICAgICAgICByID0gdi5zdWJzdHJpbmcoMSwgMyk7XG4gICAgICAgIGcgPSB2LnN1YnN0cmluZygzLCA1KTtcbiAgICAgICAgYiA9IHYuc3Vic3RyaW5nKDUsIDcpO1xuICAgICAgICBhID0gdi5zdWJzdHJpbmcoNywgOSk7XG4gICAgICAgIC8vIE9yIHdlIGhhdmUgMyBjaGFyYWN0ZXJzLCBpZSAjRjAwXG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByID0gdi5zdWJzdHJpbmcoMSwgMik7XG4gICAgICAgIGcgPSB2LnN1YnN0cmluZygyLCAzKTtcbiAgICAgICAgYiA9IHYuc3Vic3RyaW5nKDMsIDQpO1xuICAgICAgICBhID0gdi5zdWJzdHJpbmcoNCwgNSk7XG4gICAgICAgIHIgKz0gcjtcbiAgICAgICAgZyArPSBnO1xuICAgICAgICBiICs9IGI7XG4gICAgICAgIGEgKz0gYTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcmVkOiBwYXJzZUludChyLCAxNiksXG4gICAgICAgIGdyZWVuOiBwYXJzZUludChnLCAxNiksXG4gICAgICAgIGJsdWU6IHBhcnNlSW50KGIsIDE2KSxcbiAgICAgICAgYWxwaGE6IGEgPyBwYXJzZUludChhLCAxNikgLyAyNTUgOiAxLFxuICAgIH07XG59XG5jb25zdCBoZXggPSB7XG4gICAgdGVzdDogaXNDb2xvclN0cmluZyhcIiNcIiksXG4gICAgcGFyc2U6IHBhcnNlSGV4LFxuICAgIHRyYW5zZm9ybTogcmdiYS50cmFuc2Zvcm0sXG59O1xuXG5leHBvcnQgeyBoZXggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hsla: function() { return /* binding */ hsla; }\n/* harmony export */ });\n/* harmony import */ var _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\n\n\nconst hsla = {\n    test: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isColorString)(\"hsl\", \"hue\"),\n    parse: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.splitColor)(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent.transform((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(saturation)) +\n            \", \" +\n            _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent.transform((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(lightness)) +\n            \", \" +\n            (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_3__.alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaHNsYS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkM7QUFDRTtBQUNQO0FBQ2dCOztBQUV4RDtBQUNBLFVBQVUseURBQWE7QUFDdkIsV0FBVyxzREFBVTtBQUNyQixrQkFBa0IsZ0RBQWdEO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBLFlBQVksdURBQU8sV0FBVyxvREFBUTtBQUN0QztBQUNBLFlBQVksdURBQU8sV0FBVyxvREFBUTtBQUN0QztBQUNBLFlBQVksb0RBQVEsQ0FBQyxxREFBSztBQUMxQjtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9jb2xvci9oc2xhLm1qcz81NjcwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFscGhhIH0gZnJvbSAnLi4vbnVtYmVycy9pbmRleC5tanMnO1xuaW1wb3J0IHsgcGVyY2VudCB9IGZyb20gJy4uL251bWJlcnMvdW5pdHMubWpzJztcbmltcG9ydCB7IHNhbml0aXplIH0gZnJvbSAnLi4vdXRpbHMubWpzJztcbmltcG9ydCB7IGlzQ29sb3JTdHJpbmcsIHNwbGl0Q29sb3IgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmNvbnN0IGhzbGEgPSB7XG4gICAgdGVzdDogaXNDb2xvclN0cmluZyhcImhzbFwiLCBcImh1ZVwiKSxcbiAgICBwYXJzZTogc3BsaXRDb2xvcihcImh1ZVwiLCBcInNhdHVyYXRpb25cIiwgXCJsaWdodG5lc3NcIiksXG4gICAgdHJhbnNmb3JtOiAoeyBodWUsIHNhdHVyYXRpb24sIGxpZ2h0bmVzcywgYWxwaGE6IGFscGhhJDEgPSAxIH0pID0+IHtcbiAgICAgICAgcmV0dXJuIChcImhzbGEoXCIgK1xuICAgICAgICAgICAgTWF0aC5yb3VuZChodWUpICtcbiAgICAgICAgICAgIFwiLCBcIiArXG4gICAgICAgICAgICBwZXJjZW50LnRyYW5zZm9ybShzYW5pdGl6ZShzYXR1cmF0aW9uKSkgK1xuICAgICAgICAgICAgXCIsIFwiICtcbiAgICAgICAgICAgIHBlcmNlbnQudHJhbnNmb3JtKHNhbml0aXplKGxpZ2h0bmVzcykpICtcbiAgICAgICAgICAgIFwiLCBcIiArXG4gICAgICAgICAgICBzYW5pdGl6ZShhbHBoYS50cmFuc2Zvcm0oYWxwaGEkMSkpICtcbiAgICAgICAgICAgIFwiKVwiKTtcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgaHNsYSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: function() { return /* binding */ color; }\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n/* harmony import */ var _hex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\");\n/* harmony import */ var _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hsla.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\");\n/* harmony import */ var _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n\n\n\n\n\nconst color = {\n    test: (v) => _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.test(v) || _hex_mjs__WEBPACK_IMPORTED_MODULE_1__.hex.test(v) || _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.test(v),\n    parse: (v) => {\n        if (_rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.test(v)) {\n            return _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.parse(v);\n        }\n        else if (_hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.test(v)) {\n            return _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.parse(v);\n        }\n        else {\n            return _hex_mjs__WEBPACK_IMPORTED_MODULE_1__.hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.isString)(v)\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.transform(v)\n                : _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.transform(v);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdDO0FBQ1I7QUFDRTtBQUNBOztBQUVsQztBQUNBLGlCQUFpQiwyQ0FBSSxZQUFZLHlDQUFHLFlBQVksMkNBQUk7QUFDcEQ7QUFDQSxZQUFZLDJDQUFJO0FBQ2hCLG1CQUFtQiwyQ0FBSTtBQUN2QjtBQUNBLGlCQUFpQiwyQ0FBSTtBQUNyQixtQkFBbUIsMkNBQUk7QUFDdkI7QUFDQTtBQUNBLG1CQUFtQix5Q0FBRztBQUN0QjtBQUNBLEtBQUs7QUFDTDtBQUNBLGVBQWUsb0RBQVE7QUFDdkI7QUFDQTtBQUNBLGtCQUFrQiwyQ0FBSTtBQUN0QixrQkFBa0IsMkNBQUk7QUFDdEIsS0FBSztBQUNMOztBQUVpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL2luZGV4Lm1qcz83YTUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzU3RyaW5nIH0gZnJvbSAnLi4vdXRpbHMubWpzJztcbmltcG9ydCB7IGhleCB9IGZyb20gJy4vaGV4Lm1qcyc7XG5pbXBvcnQgeyBoc2xhIH0gZnJvbSAnLi9oc2xhLm1qcyc7XG5pbXBvcnQgeyByZ2JhIH0gZnJvbSAnLi9yZ2JhLm1qcyc7XG5cbmNvbnN0IGNvbG9yID0ge1xuICAgIHRlc3Q6ICh2KSA9PiByZ2JhLnRlc3QodikgfHwgaGV4LnRlc3QodikgfHwgaHNsYS50ZXN0KHYpLFxuICAgIHBhcnNlOiAodikgPT4ge1xuICAgICAgICBpZiAocmdiYS50ZXN0KHYpKSB7XG4gICAgICAgICAgICByZXR1cm4gcmdiYS5wYXJzZSh2KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChoc2xhLnRlc3QodikpIHtcbiAgICAgICAgICAgIHJldHVybiBoc2xhLnBhcnNlKHYpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGhleC5wYXJzZSh2KTtcbiAgICAgICAgfVxuICAgIH0sXG4gICAgdHJhbnNmb3JtOiAodikgPT4ge1xuICAgICAgICByZXR1cm4gaXNTdHJpbmcodilcbiAgICAgICAgICAgID8gdlxuICAgICAgICAgICAgOiB2Lmhhc093blByb3BlcnR5KFwicmVkXCIpXG4gICAgICAgICAgICAgICAgPyByZ2JhLnRyYW5zZm9ybSh2KVxuICAgICAgICAgICAgICAgIDogaHNsYS50cmFuc2Zvcm0odik7XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGNvbG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rgbUnit: function() { return /* binding */ rgbUnit; },\n/* harmony export */   rgba: function() { return /* binding */ rgba; }\n/* harmony export */ });\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\n\n\nconst clampRgbUnit = (v) => (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, 255, v);\nconst rgbUnit = {\n    ..._numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.isColorString)(\"rgb\", \"red\"),\n    parse: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.splitColor)(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitize)(_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.alpha.transform(alpha$1)) +\n        \")\",\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvcmdiYS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlEO0FBQ0k7QUFDYjtBQUNnQjs7QUFFeEQsNEJBQTRCLHVEQUFLO0FBQ2pDO0FBQ0EsT0FBTyxzREFBTTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFVBQVUseURBQWE7QUFDdkIsV0FBVyxzREFBVTtBQUNyQixrQkFBa0Isc0NBQXNDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsb0RBQVEsQ0FBQyxxREFBSztBQUN0QjtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL3JnYmEubWpzPzI0YmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xhbXAgfSBmcm9tICcuLi8uLi8uLi91dGlscy9jbGFtcC5tanMnO1xuaW1wb3J0IHsgbnVtYmVyLCBhbHBoYSB9IGZyb20gJy4uL251bWJlcnMvaW5kZXgubWpzJztcbmltcG9ydCB7IHNhbml0aXplIH0gZnJvbSAnLi4vdXRpbHMubWpzJztcbmltcG9ydCB7IGlzQ29sb3JTdHJpbmcsIHNwbGl0Q29sb3IgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmNvbnN0IGNsYW1wUmdiVW5pdCA9ICh2KSA9PiBjbGFtcCgwLCAyNTUsIHYpO1xuY29uc3QgcmdiVW5pdCA9IHtcbiAgICAuLi5udW1iZXIsXG4gICAgdHJhbnNmb3JtOiAodikgPT4gTWF0aC5yb3VuZChjbGFtcFJnYlVuaXQodikpLFxufTtcbmNvbnN0IHJnYmEgPSB7XG4gICAgdGVzdDogaXNDb2xvclN0cmluZyhcInJnYlwiLCBcInJlZFwiKSxcbiAgICBwYXJzZTogc3BsaXRDb2xvcihcInJlZFwiLCBcImdyZWVuXCIsIFwiYmx1ZVwiKSxcbiAgICB0cmFuc2Zvcm06ICh7IHJlZCwgZ3JlZW4sIGJsdWUsIGFscGhhOiBhbHBoYSQxID0gMSB9KSA9PiBcInJnYmEoXCIgK1xuICAgICAgICByZ2JVbml0LnRyYW5zZm9ybShyZWQpICtcbiAgICAgICAgXCIsIFwiICtcbiAgICAgICAgcmdiVW5pdC50cmFuc2Zvcm0oZ3JlZW4pICtcbiAgICAgICAgXCIsIFwiICtcbiAgICAgICAgcmdiVW5pdC50cmFuc2Zvcm0oYmx1ZSkgK1xuICAgICAgICBcIiwgXCIgK1xuICAgICAgICBzYW5pdGl6ZShhbHBoYS50cmFuc2Zvcm0oYWxwaGEkMSkpICtcbiAgICAgICAgXCIpXCIsXG59O1xuXG5leHBvcnQgeyByZ2JVbml0LCByZ2JhIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/utils.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isColorString: function() { return /* binding */ isColorString; },\n/* harmony export */   splitColor: function() { return /* binding */ splitColor; }\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean(((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isString)(v) && _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.singleColorRegex.test(v) && v.startsWith(type)) ||\n        (testProp && Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isString)(v))\n        return v;\n    const [a, b, c, alpha] = v.match(_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRTs7QUFFdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixvREFBUSxPQUFPLHdEQUFnQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQSxTQUFTLG9EQUFRO0FBQ2pCO0FBQ0EscUNBQXFDLGtEQUFVO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL3V0aWxzLm1qcz85ZDAxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzU3RyaW5nLCBzaW5nbGVDb2xvclJlZ2V4LCBmbG9hdFJlZ2V4IH0gZnJvbSAnLi4vdXRpbHMubWpzJztcblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHByb3ZpZGVkIHN0cmluZyBpcyBhIGNvbG9yLCBpZSByZ2JhKDAsMCwwLDApIG9yICMwMDAsXG4gKiBidXQgZmFsc2UgaWYgYSBudW1iZXIgb3IgbXVsdGlwbGUgY29sb3JzXG4gKi9cbmNvbnN0IGlzQ29sb3JTdHJpbmcgPSAodHlwZSwgdGVzdFByb3ApID0+ICh2KSA9PiB7XG4gICAgcmV0dXJuIEJvb2xlYW4oKGlzU3RyaW5nKHYpICYmIHNpbmdsZUNvbG9yUmVnZXgudGVzdCh2KSAmJiB2LnN0YXJ0c1dpdGgodHlwZSkpIHx8XG4gICAgICAgICh0ZXN0UHJvcCAmJiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwodiwgdGVzdFByb3ApKSk7XG59O1xuY29uc3Qgc3BsaXRDb2xvciA9IChhTmFtZSwgYk5hbWUsIGNOYW1lKSA9PiAodikgPT4ge1xuICAgIGlmICghaXNTdHJpbmcodikpXG4gICAgICAgIHJldHVybiB2O1xuICAgIGNvbnN0IFthLCBiLCBjLCBhbHBoYV0gPSB2Lm1hdGNoKGZsb2F0UmVnZXgpO1xuICAgIHJldHVybiB7XG4gICAgICAgIFthTmFtZV06IHBhcnNlRmxvYXQoYSksXG4gICAgICAgIFtiTmFtZV06IHBhcnNlRmxvYXQoYiksXG4gICAgICAgIFtjTmFtZV06IHBhcnNlRmxvYXQoYyksXG4gICAgICAgIGFscGhhOiBhbHBoYSAhPT0gdW5kZWZpbmVkID8gcGFyc2VGbG9hdChhbHBoYSkgOiAxLFxuICAgIH07XG59O1xuXG5leHBvcnQgeyBpc0NvbG9yU3RyaW5nLCBzcGxpdENvbG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/filter.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/complex/filter.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filter: function() { return /* binding */ filter; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n\n\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n    const [name, value] = v.slice(0, -1).split(\"(\");\n    if (name === \"drop-shadow\")\n        return v;\n    const [number] = value.match(_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, \"\");\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /([a-z-]*)\\(.*?\\)/g;\nconst filter = {\n    ..._index_mjs__WEBPACK_IMPORTED_MODULE_1__.complex,\n    getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29tcGxleC9maWx0ZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUNJOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGtEQUFVO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTywrQ0FBTztBQUNkO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9jb21wbGV4L2ZpbHRlci5tanM/NzQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb21wbGV4IH0gZnJvbSAnLi9pbmRleC5tanMnO1xuaW1wb3J0IHsgZmxvYXRSZWdleCB9IGZyb20gJy4uL3V0aWxzLm1qcyc7XG5cbi8qKlxuICogUHJvcGVydGllcyB0aGF0IHNob3VsZCBkZWZhdWx0IHRvIDEgb3IgMTAwJVxuICovXG5jb25zdCBtYXhEZWZhdWx0cyA9IG5ldyBTZXQoW1wiYnJpZ2h0bmVzc1wiLCBcImNvbnRyYXN0XCIsIFwic2F0dXJhdGVcIiwgXCJvcGFjaXR5XCJdKTtcbmZ1bmN0aW9uIGFwcGx5RGVmYXVsdEZpbHRlcih2KSB7XG4gICAgY29uc3QgW25hbWUsIHZhbHVlXSA9IHYuc2xpY2UoMCwgLTEpLnNwbGl0KFwiKFwiKTtcbiAgICBpZiAobmFtZSA9PT0gXCJkcm9wLXNoYWRvd1wiKVxuICAgICAgICByZXR1cm4gdjtcbiAgICBjb25zdCBbbnVtYmVyXSA9IHZhbHVlLm1hdGNoKGZsb2F0UmVnZXgpIHx8IFtdO1xuICAgIGlmICghbnVtYmVyKVxuICAgICAgICByZXR1cm4gdjtcbiAgICBjb25zdCB1bml0ID0gdmFsdWUucmVwbGFjZShudW1iZXIsIFwiXCIpO1xuICAgIGxldCBkZWZhdWx0VmFsdWUgPSBtYXhEZWZhdWx0cy5oYXMobmFtZSkgPyAxIDogMDtcbiAgICBpZiAobnVtYmVyICE9PSB2YWx1ZSlcbiAgICAgICAgZGVmYXVsdFZhbHVlICo9IDEwMDtcbiAgICByZXR1cm4gbmFtZSArIFwiKFwiICsgZGVmYXVsdFZhbHVlICsgdW5pdCArIFwiKVwiO1xufVxuY29uc3QgZnVuY3Rpb25SZWdleCA9IC8oW2Etei1dKilcXCguKj9cXCkvZztcbmNvbnN0IGZpbHRlciA9IHtcbiAgICAuLi5jb21wbGV4LFxuICAgIGdldEFuaW1hdGFibGVOb25lOiAodikgPT4ge1xuICAgICAgICBjb25zdCBmdW5jdGlvbnMgPSB2Lm1hdGNoKGZ1bmN0aW9uUmVnZXgpO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb25zID8gZnVuY3Rpb25zLm1hcChhcHBseURlZmF1bHRGaWx0ZXIpLmpvaW4oXCIgXCIpIDogdjtcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgZmlsdGVyIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/filter.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/complex/index.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyseComplexValue: function() { return /* binding */ analyseComplexValue; },\n/* harmony export */   complex: function() { return /* binding */ complex; }\n/* harmony export */ });\n/* harmony import */ var _render_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../render/dom/utils/is-css-variable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _color_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n\n\n\n\n\n\nfunction test(v) {\n    var _a, _b;\n    return (isNaN(v) &&\n        (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isString)(v) &&\n        (((_a = v.match(_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) +\n            (((_b = v.match(_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) >\n            0);\n}\nconst cssVarTokeniser = {\n    regex: _render_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_1__.cssVariableRegex,\n    countKey: \"Vars\",\n    token: \"${v}\",\n    parse: _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop,\n};\nconst colorTokeniser = {\n    regex: _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.colorRegex,\n    countKey: \"Colors\",\n    token: \"${c}\",\n    parse: _color_index_mjs__WEBPACK_IMPORTED_MODULE_3__.color.parse,\n};\nconst numberTokeniser = {\n    regex: _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.floatRegex,\n    countKey: \"Numbers\",\n    token: \"${n}\",\n    parse: _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_4__.number.parse,\n};\nfunction tokenise(info, { regex, countKey, token, parse }) {\n    const matches = info.tokenised.match(regex);\n    if (!matches)\n        return;\n    info[\"num\" + countKey] = matches.length;\n    info.tokenised = info.tokenised.replace(regex, token);\n    info.values.push(...matches.map(parse));\n}\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const info = {\n        value: originalValue,\n        tokenised: originalValue,\n        values: [],\n        numVars: 0,\n        numColors: 0,\n        numNumbers: 0,\n    };\n    if (info.value.includes(\"var(--\"))\n        tokenise(info, cssVarTokeniser);\n    tokenise(info, colorTokeniser);\n    tokenise(info, numberTokeniser);\n    return info;\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { values, numColors, numVars, tokenised } = analyseComplexValue(source);\n    const numValues = values.length;\n    return (v) => {\n        let output = tokenised;\n        for (let i = 0; i < numValues; i++) {\n            if (i < numVars) {\n                output = output.replace(cssVarTokeniser.token, v[i]);\n            }\n            else if (i < numVars + numColors) {\n                output = output.replace(colorTokeniser.token, _color_index_mjs__WEBPACK_IMPORTED_MODULE_3__.color.transform(v[i]));\n            }\n            else {\n                output = output.replace(numberTokeniser.token, (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.sanitize)(v[i]));\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alpha: function() { return /* binding */ alpha; },\n/* harmony export */   number: function() { return /* binding */ number; },\n/* harmony export */   scale: function() { return /* binding */ scale; }\n/* harmony export */ });\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvbnVtYmVycy9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsdURBQUs7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9udW1iZXJzL2luZGV4Lm1qcz8xN2U1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsYW1wIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvY2xhbXAubWpzJztcblxuY29uc3QgbnVtYmVyID0ge1xuICAgIHRlc3Q6ICh2KSA9PiB0eXBlb2YgdiA9PT0gXCJudW1iZXJcIixcbiAgICBwYXJzZTogcGFyc2VGbG9hdCxcbiAgICB0cmFuc2Zvcm06ICh2KSA9PiB2LFxufTtcbmNvbnN0IGFscGhhID0ge1xuICAgIC4uLm51bWJlcixcbiAgICB0cmFuc2Zvcm06ICh2KSA9PiBjbGFtcCgwLCAxLCB2KSxcbn07XG5jb25zdCBzY2FsZSA9IHtcbiAgICAuLi5udW1iZXIsXG4gICAgZGVmYXVsdDogMSxcbn07XG5cbmV4cG9ydCB7IGFscGhhLCBudW1iZXIsIHNjYWxlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   degrees: function() { return /* binding */ degrees; },\n/* harmony export */   percent: function() { return /* binding */ percent; },\n/* harmony export */   progressPercentage: function() { return /* binding */ progressPercentage; },\n/* harmony export */   px: function() { return /* binding */ px; },\n/* harmony export */   vh: function() { return /* binding */ vh; },\n/* harmony export */   vw: function() { return /* binding */ vw; }\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\");\n\n\nconst createUnitType = (unit) => ({\n    test: (v) => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isString)(v) && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = createUnitType(\"deg\");\nconst percent = createUnitType(\"%\");\nconst px = createUnitType(\"px\");\nconst vh = createUnitType(\"vh\");\nconst vw = createUnitType(\"vw\");\nconst progressPercentage = {\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvbnVtYmVycy91bml0cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3Qzs7QUFFeEM7QUFDQSxpQkFBaUIsb0RBQVE7QUFDekI7QUFDQSx5QkFBeUIsRUFBRSxFQUFFLEtBQUs7QUFDbEMsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL251bWJlcnMvdW5pdHMubWpzPzZhNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNTdHJpbmcgfSBmcm9tICcuLi91dGlscy5tanMnO1xuXG5jb25zdCBjcmVhdGVVbml0VHlwZSA9ICh1bml0KSA9PiAoe1xuICAgIHRlc3Q6ICh2KSA9PiBpc1N0cmluZyh2KSAmJiB2LmVuZHNXaXRoKHVuaXQpICYmIHYuc3BsaXQoXCIgXCIpLmxlbmd0aCA9PT0gMSxcbiAgICBwYXJzZTogcGFyc2VGbG9hdCxcbiAgICB0cmFuc2Zvcm06ICh2KSA9PiBgJHt2fSR7dW5pdH1gLFxufSk7XG5jb25zdCBkZWdyZWVzID0gY3JlYXRlVW5pdFR5cGUoXCJkZWdcIik7XG5jb25zdCBwZXJjZW50ID0gY3JlYXRlVW5pdFR5cGUoXCIlXCIpO1xuY29uc3QgcHggPSBjcmVhdGVVbml0VHlwZShcInB4XCIpO1xuY29uc3QgdmggPSBjcmVhdGVVbml0VHlwZShcInZoXCIpO1xuY29uc3QgdncgPSBjcmVhdGVVbml0VHlwZShcInZ3XCIpO1xuY29uc3QgcHJvZ3Jlc3NQZXJjZW50YWdlID0ge1xuICAgIC4uLnBlcmNlbnQsXG4gICAgcGFyc2U6ICh2KSA9PiBwZXJjZW50LnBhcnNlKHYpIC8gMTAwLFxuICAgIHRyYW5zZm9ybTogKHYpID0+IHBlcmNlbnQudHJhbnNmb3JtKHYgKiAxMDApLFxufTtcblxuZXhwb3J0IHsgZGVncmVlcywgcGVyY2VudCwgcHJvZ3Jlc3NQZXJjZW50YWdlLCBweCwgdmgsIHZ3IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colorRegex: function() { return /* binding */ colorRegex; },\n/* harmony export */   floatRegex: function() { return /* binding */ floatRegex; },\n/* harmony export */   isString: function() { return /* binding */ isString; },\n/* harmony export */   sanitize: function() { return /* binding */ sanitize; },\n/* harmony export */   singleColorRegex: function() { return /* binding */ singleColorRegex; }\n/* harmony export */ });\n/**\n * TODO: When we move from string as a source of truth to data models\n * everything in this folder should probably be referred to as models vs types\n */\n// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\nconst floatRegex = /(-)?([\\d]*\\.?[\\d])+/g;\nconst colorRegex = /(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))/gi;\nconst singleColorRegex = /^(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))$/i;\nfunction isString(v) {\n    return typeof v === \"string\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixJQUFJLGtDQUFrQyxFQUFFO0FBQ3ZFLHNDQUFzQyxJQUFJLGtDQUFrQyxFQUFFO0FBQzlFO0FBQ0E7QUFDQTs7QUFFd0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy91dGlscy5tanM/MGM4OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRPRE86IFdoZW4gd2UgbW92ZSBmcm9tIHN0cmluZyBhcyBhIHNvdXJjZSBvZiB0cnV0aCB0byBkYXRhIG1vZGVsc1xuICogZXZlcnl0aGluZyBpbiB0aGlzIGZvbGRlciBzaG91bGQgcHJvYmFibHkgYmUgcmVmZXJyZWQgdG8gYXMgbW9kZWxzIHZzIHR5cGVzXG4gKi9cbi8vIElmIHRoaXMgbnVtYmVyIGlzIGEgZGVjaW1hbCwgbWFrZSBpdCBqdXN0IGZpdmUgZGVjaW1hbCBwbGFjZXNcbi8vIHRvIGF2b2lkIGV4cG9uZW50c1xuY29uc3Qgc2FuaXRpemUgPSAodikgPT4gTWF0aC5yb3VuZCh2ICogMTAwMDAwKSAvIDEwMDAwMDtcbmNvbnN0IGZsb2F0UmVnZXggPSAvKC0pPyhbXFxkXSpcXC4/W1xcZF0pKy9nO1xuY29uc3QgY29sb3JSZWdleCA9IC8oI1swLTlhLWZdezMsOH18KHJnYnxoc2wpYT9cXCgoLT9bXFxkXFwuXSslP1ssXFxzXSspezJ9KC0/W1xcZFxcLl0rJT8pXFxzKltcXCxcXC9dP1xccypbXFxkXFwuXSolP1xcKSkvZ2k7XG5jb25zdCBzaW5nbGVDb2xvclJlZ2V4ID0gL14oI1swLTlhLWZdezMsOH18KHJnYnxoc2wpYT9cXCgoLT9bXFxkXFwuXSslP1ssXFxzXSspezJ9KC0/W1xcZFxcLl0rJT8pXFxzKltcXCxcXC9dP1xccypbXFxkXFwuXSolP1xcKSkkL2k7XG5mdW5jdGlvbiBpc1N0cmluZyh2KSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2ID09PSBcInN0cmluZ1wiO1xufVxuXG5leHBvcnQgeyBjb2xvclJlZ2V4LCBmbG9hdFJlZ2V4LCBpc1N0cmluZywgc2FuaXRpemUsIHNpbmdsZUNvbG9yUmVnZXggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWillChangeMotionValue: function() { return /* binding */ isWillChangeMotionValue; }\n/* harmony export */ });\n/* harmony import */ var _utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value) && value.add);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXdpbGwtY2hhbmdlL2lzLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2RDs7QUFFN0Q7QUFDQSxtQkFBbUIseUVBQWE7QUFDaEM7O0FBRW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXdpbGwtY2hhbmdlL2lzLm1qcz9kMWMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzTW90aW9uVmFsdWUgfSBmcm9tICcuLi91dGlscy9pcy1tb3Rpb24tdmFsdWUubWpzJztcblxuZnVuY3Rpb24gaXNXaWxsQ2hhbmdlTW90aW9uVmFsdWUodmFsdWUpIHtcbiAgICByZXR1cm4gQm9vbGVhbihpc01vdGlvblZhbHVlKHZhbHVlKSAmJiB2YWx1ZS5hZGQpO1xufVxuXG5leHBvcnQgeyBpc1dpbGxDaGFuZ2VNb3Rpb25WYWx1ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMotionValue: function() { return /* binding */ isMotionValue; }\n/* harmony export */ });\nconst isMotionValue = (value) => Boolean(value && value.getVelocity);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXRpbHMvaXMtbW90aW9uLXZhbHVlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXRpbHMvaXMtbW90aW9uLXZhbHVlLm1qcz84YzhmIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzTW90aW9uVmFsdWUgPSAodmFsdWUpID0+IEJvb2xlYW4odmFsdWUgJiYgdmFsdWUuZ2V0VmVsb2NpdHkpO1xuXG5leHBvcnQgeyBpc01vdGlvblZhbHVlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveMotionValue: function() { return /* binding */ resolveMotionValue; }\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_value_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/resolve-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/resolve-value.mjs\");\n/* harmony import */ var _is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n    const unwrappedValue = (0,_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value) ? value.get() : value;\n    return (0,_utils_resolve_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isCustomValue)(unwrappedValue)\n        ? unwrappedValue.toValue()\n        : unwrappedValue;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXRpbHMvcmVzb2x2ZS1tb3Rpb24tdmFsdWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4RDtBQUNSOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsbUVBQWE7QUFDeEMsV0FBVyx1RUFBYTtBQUN4QjtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXRpbHMvcmVzb2x2ZS1tb3Rpb24tdmFsdWUubWpzP2ZlZjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNDdXN0b21WYWx1ZSB9IGZyb20gJy4uLy4uL3V0aWxzL3Jlc29sdmUtdmFsdWUubWpzJztcbmltcG9ydCB7IGlzTW90aW9uVmFsdWUgfSBmcm9tICcuL2lzLW1vdGlvbi12YWx1ZS5tanMnO1xuXG4vKipcbiAqIElmIHRoZSBwcm92aWRlZCB2YWx1ZSBpcyBhIE1vdGlvblZhbHVlLCB0aGlzIHJldHVybnMgdGhlIGFjdHVhbCB2YWx1ZSwgb3RoZXJ3aXNlIGp1c3QgdGhlIHZhbHVlIGl0c2VsZlxuICpcbiAqIFRPRE86IFJlbW92ZSBhbmQgbW92ZSB0byBsaWJyYXJ5XG4gKi9cbmZ1bmN0aW9uIHJlc29sdmVNb3Rpb25WYWx1ZSh2YWx1ZSkge1xuICAgIGNvbnN0IHVud3JhcHBlZFZhbHVlID0gaXNNb3Rpb25WYWx1ZSh2YWx1ZSkgPyB2YWx1ZS5nZXQoKSA6IHZhbHVlO1xuICAgIHJldHVybiBpc0N1c3RvbVZhbHVlKHVud3JhcHBlZFZhbHVlKVxuICAgICAgICA/IHVud3JhcHBlZFZhbHVlLnRvVmFsdWUoKVxuICAgICAgICA6IHVud3JhcHBlZFZhbHVlO1xufVxuXG5leHBvcnQgeyByZXNvbHZlTW90aW9uVmFsdWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs\n"));

/***/ })

}]);