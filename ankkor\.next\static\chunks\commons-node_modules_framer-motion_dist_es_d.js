"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_d"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/debug/record.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/debug/record.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   record: function() { return /* binding */ record; }\n/* harmony export */ });\nfunction record(data) {\n    if (window.MotionDebug) {\n        window.MotionDebug.record(data);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZGVidWcvcmVjb3JkLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9kZWJ1Zy9yZWNvcmQubWpzP2RhMmMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVjb3JkKGRhdGEpIHtcbiAgICBpZiAod2luZG93Lk1vdGlvbkRlYnVnKSB7XG4gICAgICAgIHdpbmRvdy5Nb3Rpb25EZWJ1Zy5yZWNvcmQoZGF0YSk7XG4gICAgfVxufVxuXG5leHBvcnQgeyByZWNvcmQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/debug/record.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/anticipate.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/anticipate.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   anticipate: function() { return /* binding */ anticipate; }\n/* harmony export */ });\n/* harmony import */ var _back_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./back.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/back.mjs\");\n\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * (0,_back_mjs__WEBPACK_IMPORTED_MODULE_0__.backIn)(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL2FudGljaXBhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DOztBQUVwQywrQ0FBK0MsaURBQU07O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL2FudGljaXBhdGUubWpzP2E2NWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYmFja0luIH0gZnJvbSAnLi9iYWNrLm1qcyc7XG5cbmNvbnN0IGFudGljaXBhdGUgPSAocCkgPT4gKHAgKj0gMikgPCAxID8gMC41ICogYmFja0luKHApIDogMC41ICogKDIgLSBNYXRoLnBvdygyLCAtMTAgKiAocCAtIDEpKSk7XG5cbmV4cG9ydCB7IGFudGljaXBhdGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/anticipate.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/back.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/back.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backIn: function() { return /* binding */ backIn; },\n/* harmony export */   backInOut: function() { return /* binding */ backInOut; },\n/* harmony export */   backOut: function() { return /* binding */ backOut; }\n/* harmony export */ });\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs\");\n/* harmony import */ var _modifiers_mirror_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modifiers/mirror.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs\");\n/* harmony import */ var _modifiers_reverse_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/reverse.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/reverse.mjs\");\n\n\n\n\nconst backOut = (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.33, 1.53, 0.69, 0.99);\nconst backIn = (0,_modifiers_reverse_mjs__WEBPACK_IMPORTED_MODULE_1__.reverseEasing)(backOut);\nconst backInOut = (0,_modifiers_mirror_mjs__WEBPACK_IMPORTED_MODULE_2__.mirrorEasing)(backIn);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL2JhY2subWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFpRDtBQUNLO0FBQ0U7O0FBRXhELGdCQUFnQiw4REFBVztBQUMzQixlQUFlLHFFQUFhO0FBQzVCLGtCQUFrQixtRUFBWTs7QUFFUSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2Vhc2luZy9iYWNrLm1qcz80NjFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGN1YmljQmV6aWVyIH0gZnJvbSAnLi9jdWJpYy1iZXppZXIubWpzJztcbmltcG9ydCB7IG1pcnJvckVhc2luZyB9IGZyb20gJy4vbW9kaWZpZXJzL21pcnJvci5tanMnO1xuaW1wb3J0IHsgcmV2ZXJzZUVhc2luZyB9IGZyb20gJy4vbW9kaWZpZXJzL3JldmVyc2UubWpzJztcblxuY29uc3QgYmFja091dCA9IGN1YmljQmV6aWVyKDAuMzMsIDEuNTMsIDAuNjksIDAuOTkpO1xuY29uc3QgYmFja0luID0gcmV2ZXJzZUVhc2luZyhiYWNrT3V0KTtcbmNvbnN0IGJhY2tJbk91dCA9IG1pcnJvckVhc2luZyhiYWNrSW4pO1xuXG5leHBvcnQgeyBiYWNrSW4sIGJhY2tJbk91dCwgYmFja091dCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/back.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/circ.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/circ.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circIn: function() { return /* binding */ circIn; },\n/* harmony export */   circInOut: function() { return /* binding */ circInOut; },\n/* harmony export */   circOut: function() { return /* binding */ circOut; }\n/* harmony export */ });\n/* harmony import */ var _modifiers_mirror_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/mirror.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs\");\n/* harmony import */ var _modifiers_reverse_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modifiers/reverse.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/reverse.mjs\");\n\n\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = (0,_modifiers_reverse_mjs__WEBPACK_IMPORTED_MODULE_0__.reverseEasing)(circIn);\nconst circInOut = (0,_modifiers_mirror_mjs__WEBPACK_IMPORTED_MODULE_1__.mirrorEasing)(circIn);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL2NpcmMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNEO0FBQ0U7O0FBRXhEO0FBQ0EsZ0JBQWdCLHFFQUFhO0FBQzdCLGtCQUFrQixtRUFBWTs7QUFFUSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2Vhc2luZy9jaXJjLm1qcz82YWM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1pcnJvckVhc2luZyB9IGZyb20gJy4vbW9kaWZpZXJzL21pcnJvci5tanMnO1xuaW1wb3J0IHsgcmV2ZXJzZUVhc2luZyB9IGZyb20gJy4vbW9kaWZpZXJzL3JldmVyc2UubWpzJztcblxuY29uc3QgY2lyY0luID0gKHApID0+IDEgLSBNYXRoLnNpbihNYXRoLmFjb3MocCkpO1xuY29uc3QgY2lyY091dCA9IHJldmVyc2VFYXNpbmcoY2lyY0luKTtcbmNvbnN0IGNpcmNJbk91dCA9IG1pcnJvckVhc2luZyhjaXJjSW4pO1xuXG5leHBvcnQgeyBjaXJjSW4sIGNpcmNJbk91dCwgY2lyY091dCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/circ.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezier: function() { return /* binding */ cubicBezier; }\n/* harmony export */ });\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\n/*\n  Bezier function generator\n  This has been modified from Gaëtan Renaudeau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/ease.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/ease.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   easeIn: function() { return /* binding */ easeIn; },\n/* harmony export */   easeInOut: function() { return /* binding */ easeInOut; },\n/* harmony export */   easeOut: function() { return /* binding */ easeOut; }\n/* harmony export */ });\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cubic-bezier.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs\");\n\n\nconst easeIn = (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.42, 0, 1, 1);\nconst easeOut = (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0, 0, 0.58, 1);\nconst easeInOut = (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_0__.cubicBezier)(0.42, 0, 0.58, 1);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL2Vhc2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7O0FBRWpELGVBQWUsOERBQVc7QUFDMUIsZ0JBQWdCLDhEQUFXO0FBQzNCLGtCQUFrQiw4REFBVzs7QUFFUyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2Vhc2luZy9lYXNlLm1qcz83NzdjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGN1YmljQmV6aWVyIH0gZnJvbSAnLi9jdWJpYy1iZXppZXIubWpzJztcblxuY29uc3QgZWFzZUluID0gY3ViaWNCZXppZXIoMC40MiwgMCwgMSwgMSk7XG5jb25zdCBlYXNlT3V0ID0gY3ViaWNCZXppZXIoMCwgMCwgMC41OCwgMSk7XG5jb25zdCBlYXNlSW5PdXQgPSBjdWJpY0JlemllcigwLjQyLCAwLCAwLjU4LCAxKTtcblxuZXhwb3J0IHsgZWFzZUluLCBlYXNlSW5PdXQsIGVhc2VPdXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/ease.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mirrorEasing: function() { return /* binding */ mirrorEasing; }\n/* harmony export */ });\n// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL21vZGlmaWVycy9taXJyb3IubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL21vZGlmaWVycy9taXJyb3IubWpzPzQyYTUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQWNjZXB0cyBhbiBlYXNpbmcgZnVuY3Rpb24gYW5kIHJldHVybnMgYSBuZXcgb25lIHRoYXQgb3V0cHV0cyBtaXJyb3JlZCB2YWx1ZXMgZm9yXG4vLyB0aGUgc2Vjb25kIGhhbGYgb2YgdGhlIGFuaW1hdGlvbi4gVHVybnMgZWFzZUluIGludG8gZWFzZUluT3V0LlxuY29uc3QgbWlycm9yRWFzaW5nID0gKGVhc2luZykgPT4gKHApID0+IHAgPD0gMC41ID8gZWFzaW5nKDIgKiBwKSAvIDIgOiAoMiAtIGVhc2luZygyICogKDEgLSBwKSkpIC8gMjtcblxuZXhwb3J0IHsgbWlycm9yRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/reverse.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/modifiers/reverse.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reverseEasing: function() { return /* binding */ reverseEasing; }\n/* harmony export */ });\n// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL21vZGlmaWVycy9yZXZlcnNlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2Vhc2luZy9tb2RpZmllcnMvcmV2ZXJzZS5tanM/ODlhMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBY2NlcHRzIGFuIGVhc2luZyBmdW5jdGlvbiBhbmQgcmV0dXJucyBhIG5ldyBvbmUgdGhhdCBvdXRwdXRzIHJldmVyc2VkIHZhbHVlcy5cbi8vIFR1cm5zIGVhc2VJbiBpbnRvIGVhc2VPdXQuXG5jb25zdCByZXZlcnNlRWFzaW5nID0gKGVhc2luZykgPT4gKHApID0+IDEgLSBlYXNpbmcoMSAtIHApO1xuXG5leHBvcnQgeyByZXZlcnNlRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/modifiers/reverse.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-bezier-definition.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/utils/is-bezier-definition.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBezierDefinition: function() { return /* binding */ isBezierDefinition; }\n/* harmony export */ });\nconst isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcz9kNzY1Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzQmV6aWVyRGVmaW5pdGlvbiA9IChlYXNpbmcpID0+IEFycmF5LmlzQXJyYXkoZWFzaW5nKSAmJiB0eXBlb2YgZWFzaW5nWzBdID09PSBcIm51bWJlclwiO1xuXG5leHBvcnQgeyBpc0JlemllckRlZmluaXRpb24gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-bezier-definition.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-easing-array.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/utils/is-easing-array.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEasingArray: function() { return /* binding */ isEasingArray; }\n/* harmony export */ });\nconst isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZWFzaW5nL3V0aWxzL2lzLWVhc2luZy1hcnJheS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9lYXNpbmcvdXRpbHMvaXMtZWFzaW5nLWFycmF5Lm1qcz82ZDliIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzRWFzaW5nQXJyYXkgPSAoZWFzZSkgPT4ge1xuICAgIHJldHVybiBBcnJheS5pc0FycmF5KGVhc2UpICYmIHR5cGVvZiBlYXNlWzBdICE9PSBcIm51bWJlclwiO1xufTtcblxuZXhwb3J0IHsgaXNFYXNpbmdBcnJheSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-easing-array.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/map.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/easing/utils/map.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   easingDefinitionToFunction: function() { return /* binding */ easingDefinitionToFunction; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../cubic-bezier.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/cubic-bezier.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _ease_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ease.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/ease.mjs\");\n/* harmony import */ var _circ_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../circ.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/circ.mjs\");\n/* harmony import */ var _back_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../back.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/back.mjs\");\n/* harmony import */ var _anticipate_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../anticipate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/anticipate.mjs\");\n\n\n\n\n\n\n\n\nconst easingLookup = {\n    linear: _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop,\n    easeIn: _ease_mjs__WEBPACK_IMPORTED_MODULE_1__.easeIn,\n    easeInOut: _ease_mjs__WEBPACK_IMPORTED_MODULE_1__.easeInOut,\n    easeOut: _ease_mjs__WEBPACK_IMPORTED_MODULE_1__.easeOut,\n    circIn: _circ_mjs__WEBPACK_IMPORTED_MODULE_2__.circIn,\n    circInOut: _circ_mjs__WEBPACK_IMPORTED_MODULE_2__.circInOut,\n    circOut: _circ_mjs__WEBPACK_IMPORTED_MODULE_2__.circOut,\n    backIn: _back_mjs__WEBPACK_IMPORTED_MODULE_3__.backIn,\n    backInOut: _back_mjs__WEBPACK_IMPORTED_MODULE_3__.backInOut,\n    backOut: _back_mjs__WEBPACK_IMPORTED_MODULE_3__.backOut,\n    anticipate: _anticipate_mjs__WEBPACK_IMPORTED_MODULE_4__.anticipate,\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (Array.isArray(definition)) {\n        // If cubic bezier definition, create bezier curve\n        (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__.invariant)(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n        const [x1, y1, x2, y2] = definition;\n        return (0,_cubic_bezier_mjs__WEBPACK_IMPORTED_MODULE_6__.cubicBezier)(x1, y1, x2, y2);\n    }\n    else if (typeof definition === \"string\") {\n        // Else lookup from table\n        (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__.invariant)(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/map.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/events/add-dom-event.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addDomEvent: function() { return /* binding */ addDomEvent; }\n/* harmony export */ });\nfunction addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2FkZC1kb20tZXZlbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2REFBNkQsZUFBZTtBQUM1RTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2FkZC1kb20tZXZlbnQubWpzPzUxZDgiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYWRkRG9tRXZlbnQodGFyZ2V0LCBldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMgPSB7IHBhc3NpdmU6IHRydWUgfSkge1xuICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKGV2ZW50TmFtZSwgaGFuZGxlciwgb3B0aW9ucyk7XG4gICAgcmV0dXJuICgpID0+IHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKGV2ZW50TmFtZSwgaGFuZGxlcik7XG59XG5cbmV4cG9ydCB7IGFkZERvbUV2ZW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerEvent: function() { return /* binding */ addPointerEvent; }\n/* harmony export */ });\n/* harmony import */ var _add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-dom-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./event-info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs\");\n\n\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return (0,_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__.addDomEvent)(target, eventName, (0,_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.addPointerInfo)(handler), options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2FkZC1wb2ludGVyLWV2ZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDQTs7QUFFbEQ7QUFDQSxXQUFXLCtEQUFXLG9CQUFvQiwrREFBYztBQUN4RDs7QUFFMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9ldmVudHMvYWRkLXBvaW50ZXItZXZlbnQubWpzPzY5ZDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkRG9tRXZlbnQgfSBmcm9tICcuL2FkZC1kb20tZXZlbnQubWpzJztcbmltcG9ydCB7IGFkZFBvaW50ZXJJbmZvIH0gZnJvbSAnLi9ldmVudC1pbmZvLm1qcyc7XG5cbmZ1bmN0aW9uIGFkZFBvaW50ZXJFdmVudCh0YXJnZXQsIGV2ZW50TmFtZSwgaGFuZGxlciwgb3B0aW9ucykge1xuICAgIHJldHVybiBhZGREb21FdmVudCh0YXJnZXQsIGV2ZW50TmFtZSwgYWRkUG9pbnRlckluZm8oaGFuZGxlciksIG9wdGlvbnMpO1xufVxuXG5leHBvcnQgeyBhZGRQb2ludGVyRXZlbnQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/events/event-info.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerInfo: function() { return /* binding */ addPointerInfo; },\n/* harmony export */   extractEventInfo: function() { return /* binding */ extractEventInfo; }\n/* harmony export */ });\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is-primary-pointer.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs\");\n\n\nfunction extractEventInfo(event, pointType = \"page\") {\n    return {\n        point: {\n            x: event[pointType + \"X\"],\n            y: event[pointType + \"Y\"],\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && handler(event, extractEventInfo(event));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2V2ZW50LWluZm8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRTs7QUFFbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsK0VBQWdCO0FBQ3RDOztBQUU0QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2V2ZW50cy9ldmVudC1pbmZvLm1qcz9lMzg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfSBmcm9tICcuL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMnO1xuXG5mdW5jdGlvbiBleHRyYWN0RXZlbnRJbmZvKGV2ZW50LCBwb2ludFR5cGUgPSBcInBhZ2VcIikge1xuICAgIHJldHVybiB7XG4gICAgICAgIHBvaW50OiB7XG4gICAgICAgICAgICB4OiBldmVudFtwb2ludFR5cGUgKyBcIlhcIl0sXG4gICAgICAgICAgICB5OiBldmVudFtwb2ludFR5cGUgKyBcIllcIl0sXG4gICAgICAgIH0sXG4gICAgfTtcbn1cbmNvbnN0IGFkZFBvaW50ZXJJbmZvID0gKGhhbmRsZXIpID0+IHtcbiAgICByZXR1cm4gKGV2ZW50KSA9PiBpc1ByaW1hcnlQb2ludGVyKGV2ZW50KSAmJiBoYW5kbGVyKGV2ZW50LCBleHRyYWN0RXZlbnRJbmZvKGV2ZW50KSk7XG59O1xuXG5leHBvcnQgeyBhZGRQb2ludGVySW5mbywgZXh0cmFjdEV2ZW50SW5mbyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: function() { return /* binding */ isPrimaryPointer; }\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2V2ZW50cy91dGlscy9pcy1wcmltYXJ5LXBvaW50ZXIubWpzP2NmMTkiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQcmltYXJ5UG9pbnRlciA9IChldmVudCkgPT4ge1xuICAgIGlmIChldmVudC5wb2ludGVyVHlwZSA9PT0gXCJtb3VzZVwiKSB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgZXZlbnQuYnV0dG9uICE9PSBcIm51bWJlclwiIHx8IGV2ZW50LmJ1dHRvbiA8PSAwO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIGlzUHJpbWFyeSBpcyB0cnVlIGZvciBhbGwgbWljZSBidXR0b25zLCB3aGVyZWFzIGV2ZXJ5IHRvdWNoIHBvaW50XG4gICAgICAgICAqIGlzIHJlZ2FyZGVkIGFzIGl0cyBvd24gaW5wdXQuIFNvIHN1YnNlcXVlbnQgY29uY3VycmVudCB0b3VjaCBwb2ludHNcbiAgICAgICAgICogd2lsbCBiZSBmYWxzZS5cbiAgICAgICAgICpcbiAgICAgICAgICogU3BlY2lmaWNhbGx5IG1hdGNoIGFnYWluc3QgZmFsc2UgaGVyZSBhcyBpbmNvbXBsZXRlIHZlcnNpb25zIG9mXG4gICAgICAgICAqIFBvaW50ZXJFdmVudHMgaW4gdmVyeSBvbGQgYnJvd3NlciBtaWdodCBoYXZlIGl0IHNldCBhcyB1bmRlZmluZWQuXG4gICAgICAgICAqL1xuICAgICAgICByZXR1cm4gZXZlbnQuaXNQcmltYXJ5ICE9PSBmYWxzZTtcbiAgICB9XG59O1xuXG5leHBvcnQgeyBpc1ByaW1hcnlQb2ludGVyIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/batcher.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: function() { return /* binding */ createRenderBatcher; },\n/* harmony export */   stepsOrder: function() { return /* binding */ stepsOrder; }\n/* harmony export */ });\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./render-step.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs\");\n\n\nconst stepsOrder = [\n    \"prepare\",\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0,\n        timestamp: 0,\n        isProcessing: false,\n    };\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderStep)(() => (runNextFrame = true));\n        return acc;\n    }, {});\n    const processStep = (stepId) => steps[stepId].process(state);\n    const processBatch = () => {\n        const timestamp = performance.now();\n        runNextFrame = false;\n        state.delta = useDefaultElapsed\n            ? 1000 / 60\n            : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        stepsOrder.forEach(processStep);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => stepsOrder.forEach((key) => steps[key].cancel(process));\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/frame.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: function() { return /* binding */ cancelFrame; },\n/* harmony export */   frame: function() { return /* binding */ frame; },\n/* harmony export */   frameData: function() { return /* binding */ frameData; },\n/* harmony export */   steps: function() { return /* binding */ steps; }\n/* harmony export */ });\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps, } = (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZnJhbWVsb29wL2ZyYW1lLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUM7QUFDVzs7QUFFcEQsUUFBUSxpRUFBaUUsRUFBRSxpRUFBbUIsd0VBQXdFLGlEQUFJOztBQUUxSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2ZyYW1lbG9vcC9mcmFtZS5tanM/OGNiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub29wIH0gZnJvbSAnLi4vdXRpbHMvbm9vcC5tanMnO1xuaW1wb3J0IHsgY3JlYXRlUmVuZGVyQmF0Y2hlciB9IGZyb20gJy4vYmF0Y2hlci5tanMnO1xuXG5jb25zdCB7IHNjaGVkdWxlOiBmcmFtZSwgY2FuY2VsOiBjYW5jZWxGcmFtZSwgc3RhdGU6IGZyYW1lRGF0YSwgc3RlcHMsIH0gPSBjcmVhdGVSZW5kZXJCYXRjaGVyKHR5cGVvZiByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgIT09IFwidW5kZWZpbmVkXCIgPyByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgOiBub29wLCB0cnVlKTtcblxuZXhwb3J0IHsgY2FuY2VsRnJhbWUsIGZyYW1lLCBmcmFtZURhdGEsIHN0ZXBzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/render-step.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: function() { return /* binding */ createRenderStep; }\n/* harmony export */ });\nclass Queue {\n    constructor() {\n        this.order = [];\n        this.scheduled = new Set();\n    }\n    add(process) {\n        if (!this.scheduled.has(process)) {\n            this.scheduled.add(process);\n            this.order.push(process);\n            return true;\n        }\n    }\n    remove(process) {\n        const index = this.order.indexOf(process);\n        if (index !== -1) {\n            this.order.splice(index, 1);\n            this.scheduled.delete(process);\n        }\n    }\n    clear() {\n        this.order.length = 0;\n        this.scheduled.clear();\n    }\n}\nfunction createRenderStep(runNextFrame) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Queue();\n    let nextFrame = new Queue();\n    let numToRun = 0;\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (queue.add(callback) && addToCurrentFrame && isProcessing) {\n                // If we're adding it to the currently running queue, update its measured size\n                numToRun = thisFrame.order.length;\n            }\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.remove(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Clear the next frame queue\n            nextFrame.clear();\n            // Execute this frame\n            numToRun = thisFrame.order.length;\n            if (numToRun) {\n                for (let i = 0; i < numToRun; i++) {\n                    const callback = thisFrame.order[i];\n                    callback(frameData);\n                    if (toKeepAlive.has(callback)) {\n                        step.schedule(callback);\n                        runNextFrame();\n                    }\n                }\n            }\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualElementDragControls: function() { return /* binding */ VisualElementDragControls; },\n/* harmony export */   elementDragControls: function() { return /* binding */ elementDragControls; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../pan/PanSession.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n/* harmony import */ var _utils_lock_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/lock.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/constraints.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../projection/utils/each-axis.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs\");\n/* harmony import */ var _projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../projection/utils/measure.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/measure.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../projection/geometry/conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../events/add-dom-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../projection/geometry/delta-calc.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../animation/interfaces/motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        // This is a reference to the global drag gesture lock, ensuring only one component\n        // can \"capture\" the drag of one or both axes.\n        // TODO: Look into moving this into pansession?\n        this.openGlobalLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__.createBox)();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor((0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event, \"page\").point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openGlobalLock)\n                    this.openGlobalLock();\n                this.openGlobalLock = (0,_utils_lock_mjs__WEBPACK_IMPORTED_MODULE_2__.getGlobalLock)(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openGlobalLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (_value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_4__.percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_5__.calcLength)(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_6__.frame.update(() => onDragStart(event, info), false, true);\n            }\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openGlobalLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        const resumeAnimation = () => (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            var _a;\n            return this.getAnimationState(axis) === \"paused\" &&\n                ((_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.play());\n        });\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_7__.PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_8__.getContextWindow)(this.visualElement),\n        });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_6__.frame.update(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openGlobalLock) {\n            this.openGlobalLock();\n            this.openGlobalLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.applyConstraints)(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        var _a;\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_10__.isRefObject)(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.calcRelativeConstraints)(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.resolveDragElastic)(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                if (this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.rebaseAxisConstraints)(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_10__.isRefObject)(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_11__.invariant)(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = (0,_projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_12__.measurePageBox)(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.calcViewportConstraints)(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints((0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_13__.convertBoxToBoundingBox)(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = (0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_13__.convertBoundingBoxToBox)(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        return axisValue.start((0,_animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_14__.animateMotionValue)(axis, axisValue, 0, transition));\n    }\n    stopAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => { var _a; return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.pause(); });\n    }\n    getAnimationState(axis) {\n        var _a;\n        return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = \"_drag\" + axis.toUpperCase();\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n    }\n    snapToCursor(point) {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_15__.mix)(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_10__.isRefObject)(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue) {\n                const latest = axisValue.get();\n                boxProgress[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.calcOrigin)({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set((0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_15__.mix)(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_16__.addPointerEvent)(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_10__.isRefObject)(dragConstraints)) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        measureDragConstraints();\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_17__.addDomEvent)(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_9__.defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/index.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/drag/index.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragGesture: function() { return /* binding */ DragGesture; }\n/* harmony export */ });\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VisualElementDragControls.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\");\n\n\n\n\nclass DragGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.removeListeners = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.controls = new _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__.VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_1__.noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RDtBQUNoQjtBQUNnQzs7QUFFNUUsMEJBQTBCLGlFQUFPO0FBQ2pDO0FBQ0E7QUFDQSxtQ0FBbUMsaURBQUk7QUFDdkMsK0JBQStCLGlEQUFJO0FBQ25DLDRCQUE0QixxRkFBeUI7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZUFBZTtBQUMvQjtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsaURBQUk7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvaW5kZXgubWpzP2Q5ODEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRmVhdHVyZSB9IGZyb20gJy4uLy4uL21vdGlvbi9mZWF0dXJlcy9GZWF0dXJlLm1qcyc7XG5pbXBvcnQgeyBub29wIH0gZnJvbSAnLi4vLi4vdXRpbHMvbm9vcC5tanMnO1xuaW1wb3J0IHsgVmlzdWFsRWxlbWVudERyYWdDb250cm9scyB9IGZyb20gJy4vVmlzdWFsRWxlbWVudERyYWdDb250cm9scy5tanMnO1xuXG5jbGFzcyBEcmFnR2VzdHVyZSBleHRlbmRzIEZlYXR1cmUge1xuICAgIGNvbnN0cnVjdG9yKG5vZGUpIHtcbiAgICAgICAgc3VwZXIobm9kZSk7XG4gICAgICAgIHRoaXMucmVtb3ZlR3JvdXBDb250cm9scyA9IG5vb3A7XG4gICAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXJzID0gbm9vcDtcbiAgICAgICAgdGhpcy5jb250cm9scyA9IG5ldyBWaXN1YWxFbGVtZW50RHJhZ0NvbnRyb2xzKG5vZGUpO1xuICAgIH1cbiAgICBtb3VudCgpIHtcbiAgICAgICAgLy8gSWYgd2UndmUgYmVlbiBwcm92aWRlZCBhIERyYWdDb250cm9scyBmb3IgbWFudWFsIGNvbnRyb2wgb3ZlciB0aGUgZHJhZyBnZXN0dXJlLFxuICAgICAgICAvLyBzdWJzY3JpYmUgdGhpcyBjb21wb25lbnQgdG8gaXQgb24gbW91bnQuXG4gICAgICAgIGNvbnN0IHsgZHJhZ0NvbnRyb2xzIH0gPSB0aGlzLm5vZGUuZ2V0UHJvcHMoKTtcbiAgICAgICAgaWYgKGRyYWdDb250cm9scykge1xuICAgICAgICAgICAgdGhpcy5yZW1vdmVHcm91cENvbnRyb2xzID0gZHJhZ0NvbnRyb2xzLnN1YnNjcmliZSh0aGlzLmNvbnRyb2xzKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnJlbW92ZUxpc3RlbmVycyA9IHRoaXMuY29udHJvbHMuYWRkTGlzdGVuZXJzKCkgfHwgbm9vcDtcbiAgICB9XG4gICAgdW5tb3VudCgpIHtcbiAgICAgICAgdGhpcy5yZW1vdmVHcm91cENvbnRyb2xzKCk7XG4gICAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXJzKCk7XG4gICAgfVxufVxuXG5leHBvcnQgeyBEcmFnR2VzdHVyZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyConstraints: function() { return /* binding */ applyConstraints; },\n/* harmony export */   calcOrigin: function() { return /* binding */ calcOrigin; },\n/* harmony export */   calcRelativeAxisConstraints: function() { return /* binding */ calcRelativeAxisConstraints; },\n/* harmony export */   calcRelativeConstraints: function() { return /* binding */ calcRelativeConstraints; },\n/* harmony export */   calcViewportAxisConstraints: function() { return /* binding */ calcViewportAxisConstraints; },\n/* harmony export */   calcViewportConstraints: function() { return /* binding */ calcViewportConstraints; },\n/* harmony export */   defaultElastic: function() { return /* binding */ defaultElastic; },\n/* harmony export */   rebaseAxisConstraints: function() { return /* binding */ rebaseAxisConstraints; },\n/* harmony export */   resolveAxisElastic: function() { return /* binding */ resolveAxisElastic; },\n/* harmony export */   resolveDragElastic: function() { return /* binding */ resolveDragElastic; },\n/* harmony export */   resolvePointElastic: function() { return /* binding */ resolvePointElastic; }\n/* harmony export */ });\n/* harmony import */ var _utils_progress_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../projection/geometry/delta-calc.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n\n\n\n\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic ? (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(min, point, elastic.min) : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic ? (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(max, point, elastic.max) : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(source);\n    const targetLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(target);\n    if (targetLength > sourceLength) {\n        origin = (0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_2__.progress)(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = (0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_2__.progress)(source.min, source.max - targetLength, target.min);\n    }\n    return (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_3__.clamp)(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLock: function() { return /* binding */ createLock; },\n/* harmony export */   getGlobalLock: function() { return /* binding */ getGlobalLock; },\n/* harmony export */   isDragActive: function() { return /* binding */ isDragActive; }\n/* harmony export */ });\nfunction createLock(name) {\n    let lock = null;\n    return () => {\n        const openLock = () => {\n            lock = null;\n        };\n        if (lock === null) {\n            lock = name;\n            return openLock;\n        }\n        return false;\n    };\n}\nconst globalHorizontalLock = createLock(\"dragHorizontal\");\nconst globalVerticalLock = createLock(\"dragVertical\");\nfunction getGlobalLock(drag) {\n    let lock = false;\n    if (drag === \"y\") {\n        lock = globalVerticalLock();\n    }\n    else if (drag === \"x\") {\n        lock = globalHorizontalLock();\n    }\n    else {\n        const openHorizontal = globalHorizontalLock();\n        const openVertical = globalVerticalLock();\n        if (openHorizontal && openVertical) {\n            lock = () => {\n                openHorizontal();\n                openVertical();\n            };\n        }\n        else {\n            // Release the locks because we don't use them\n            if (openHorizontal)\n                openHorizontal();\n            if (openVertical)\n                openVertical();\n        }\n    }\n    return lock;\n}\nfunction isDragActive() {\n    // Check the gesture lock - if we get it, it means no drag gesture is active\n    // and we can safely fire the tap gesture.\n    const openGestureLock = getGlobalLock(true);\n    if (!openGestureLock)\n        return true;\n    openGestureLock();\n    return false;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/focus.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/focus.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGesture: function() { return /* binding */ FocusGesture; }\n/* harmony export */ });\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../events/add-dom-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n\n\n\n\nclass FocusGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_1__.pipe)((0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"focus\", () => this.onFocus()), (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvZm9jdXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEQ7QUFDRDtBQUNoQjs7QUFFekMsMkJBQTJCLGlFQUFPO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixxREFBSSxDQUFDLHNFQUFXLG9EQUFvRCxzRUFBVztBQUN0RztBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvZm9jdXMubWpzP2YwNDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkRG9tRXZlbnQgfSBmcm9tICcuLi9ldmVudHMvYWRkLWRvbS1ldmVudC5tanMnO1xuaW1wb3J0IHsgRmVhdHVyZSB9IGZyb20gJy4uL21vdGlvbi9mZWF0dXJlcy9GZWF0dXJlLm1qcyc7XG5pbXBvcnQgeyBwaXBlIH0gZnJvbSAnLi4vdXRpbHMvcGlwZS5tanMnO1xuXG5jbGFzcyBGb2N1c0dlc3R1cmUgZXh0ZW5kcyBGZWF0dXJlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5pc0FjdGl2ZSA9IGZhbHNlO1xuICAgIH1cbiAgICBvbkZvY3VzKCkge1xuICAgICAgICBsZXQgaXNGb2N1c1Zpc2libGUgPSBmYWxzZTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIElmIHRoaXMgZWxlbWVudCBkb2Vzbid0IG1hdGNoIGZvY3VzLXZpc2libGUgdGhlbiBkb24ndFxuICAgICAgICAgKiBhcHBseSB3aGlsZUhvdmVyLiBCdXQsIGlmIG1hdGNoZXMgdGhyb3dzIHRoYXQgZm9jdXMtdmlzaWJsZVxuICAgICAgICAgKiBpcyBub3QgYSB2YWxpZCBzZWxlY3RvciB0aGVuIGluIHRoYXQgYnJvd3NlciBvdXRsaW5lIHN0eWxlcyB3aWxsIGJlIGFwcGxpZWRcbiAgICAgICAgICogdG8gdGhlIGVsZW1lbnQgYnkgZGVmYXVsdCBhbmQgd2Ugd2FudCB0byBtYXRjaCB0aGF0IGJlaGF2aW91ciB3aXRoIHdoaWxlRm9jdXMuXG4gICAgICAgICAqL1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgaXNGb2N1c1Zpc2libGUgPSB0aGlzLm5vZGUuY3VycmVudC5tYXRjaGVzKFwiOmZvY3VzLXZpc2libGVcIik7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIGlzRm9jdXNWaXNpYmxlID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlzRm9jdXNWaXNpYmxlIHx8ICF0aGlzLm5vZGUuYW5pbWF0aW9uU3RhdGUpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIHRoaXMubm9kZS5hbmltYXRpb25TdGF0ZS5zZXRBY3RpdmUoXCJ3aGlsZUZvY3VzXCIsIHRydWUpO1xuICAgICAgICB0aGlzLmlzQWN0aXZlID0gdHJ1ZTtcbiAgICB9XG4gICAgb25CbHVyKCkge1xuICAgICAgICBpZiAoIXRoaXMuaXNBY3RpdmUgfHwgIXRoaXMubm9kZS5hbmltYXRpb25TdGF0ZSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgdGhpcy5ub2RlLmFuaW1hdGlvblN0YXRlLnNldEFjdGl2ZShcIndoaWxlRm9jdXNcIiwgZmFsc2UpO1xuICAgICAgICB0aGlzLmlzQWN0aXZlID0gZmFsc2U7XG4gICAgfVxuICAgIG1vdW50KCkge1xuICAgICAgICB0aGlzLnVubW91bnQgPSBwaXBlKGFkZERvbUV2ZW50KHRoaXMubm9kZS5jdXJyZW50LCBcImZvY3VzXCIsICgpID0+IHRoaXMub25Gb2N1cygpKSwgYWRkRG9tRXZlbnQodGhpcy5ub2RlLmN1cnJlbnQsIFwiYmx1clwiLCAoKSA9PiB0aGlzLm9uQmx1cigpKSk7XG4gICAgfVxuICAgIHVubW91bnQoKSB7IH1cbn1cblxuZXhwb3J0IHsgRm9jdXNHZXN0dXJlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/focus.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/hover.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/hover.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverGesture: function() { return /* binding */ HoverGesture; }\n/* harmony export */ });\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../events/add-pointer-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var _drag_utils_lock_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/utils/lock.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\nfunction addHoverEvent(node, isActive) {\n    const eventName = \"pointer\" + (isActive ? \"enter\" : \"leave\");\n    const callbackName = \"onHover\" + (isActive ? \"Start\" : \"End\");\n    const handleEvent = (event, info) => {\n        if (event.pointerType === \"touch\" || (0,_drag_utils_lock_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)())\n            return;\n        const props = node.getProps();\n        if (node.animationState && props.whileHover) {\n            node.animationState.setActive(\"whileHover\", isActive);\n        }\n        if (props[callbackName]) {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frame.update(() => props[callbackName](event, info));\n        }\n    };\n    return (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addPointerEvent)(node.current, eventName, handleEvent, {\n        passive: !node.getProps()[callbackName],\n    });\n}\nclass HoverGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_3__.Feature {\n    mount() {\n        this.unmount = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_4__.pipe)(addHoverEvent(this.node, true), addHoverEvent(this.node, false));\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/hover.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanSession: function() { return /* binding */ PanSession; }\n/* harmony export */ });\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var _utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/distance.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/distance.mjs\");\n/* harmony import */ var _events_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../events/utils/is-primary-pointer.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow, dragSnapToOrigin = false } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursoe.\n            const isDistancePastThreshold = (0,_utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__.distance2D)(info.offset, { x: 0, y: 0 }) >= 3;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!(0,_events_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPrimaryPointer)(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.contextWindow = contextWindow || window;\n        const info = (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_3__.extractEventInfo)(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_4__.pipe)((0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.contextWindow, \"pointermove\", this.handlePointerMove), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.contextWindow, \"pointerup\", this.handlePointerUp), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_1__.cancelFrame)(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_6__.secondsToMilliseconds)(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_6__.millisecondsToSeconds)(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/index.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/pan/index.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanGesture: function() { return /* binding */ PanGesture; }\n/* harmony export */ });\n/* harmony import */ var _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PanSession.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(() => handler(event, info));\n    }\n};\nclass PanGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__.Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__.PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__.getContextWindow)(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/pan/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/press.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/press.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PressGesture: function() { return /* binding */ PressGesture; }\n/* harmony export */ });\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../events/event-info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../events/add-dom-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../events/add-pointer-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var _drag_utils_lock_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./drag/utils/lock.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/is-node-or-child.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\n\nfunction fireSyntheticPointerEvent(name, handler) {\n    if (!handler)\n        return;\n    const syntheticPointerEvent = new PointerEvent(\"pointer\" + name);\n    handler(syntheticPointerEvent, (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_0__.extractEventInfo)(syntheticPointerEvent));\n}\nclass PressGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__.Feature {\n    constructor() {\n        super(...arguments);\n        this.removeStartListeners = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop;\n        this.removeEndListeners = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop;\n        this.removeAccessibleListeners = _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop;\n        this.startPointerPress = (startEvent, startInfo) => {\n            if (this.isPressing)\n                return;\n            this.removeEndListeners();\n            const props = this.node.getProps();\n            const endPointerPress = (endEvent, endInfo) => {\n                if (!this.checkPressEnd())\n                    return;\n                const { onTap, onTapCancel, globalTapTarget } = this.node.getProps();\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.update(() => {\n                    /**\n                     * We only count this as a tap gesture if the event.target is the same\n                     * as, or a child of, this component's element\n                     */\n                    !globalTapTarget &&\n                        !(0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__.isNodeOrChild)(this.node.current, endEvent.target)\n                        ? onTapCancel && onTapCancel(endEvent, endInfo)\n                        : onTap && onTap(endEvent, endInfo);\n                });\n            };\n            const removePointerUpListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(window, \"pointerup\", endPointerPress, { passive: !(props.onTap || props[\"onPointerUp\"]) });\n            const removePointerCancelListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(window, \"pointercancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo), { passive: !(props.onTapCancel || props[\"onPointerCancel\"]) });\n            this.removeEndListeners = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_6__.pipe)(removePointerUpListener, removePointerCancelListener);\n            this.startPress(startEvent, startInfo);\n        };\n        this.startAccessiblePress = () => {\n            const handleKeydown = (keydownEvent) => {\n                if (keydownEvent.key !== \"Enter\" || this.isPressing)\n                    return;\n                const handleKeyup = (keyupEvent) => {\n                    if (keyupEvent.key !== \"Enter\" || !this.checkPressEnd())\n                        return;\n                    fireSyntheticPointerEvent(\"up\", (event, info) => {\n                        const { onTap } = this.node.getProps();\n                        if (onTap) {\n                            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.update(() => onTap(event, info));\n                        }\n                    });\n                };\n                this.removeEndListeners();\n                this.removeEndListeners = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_7__.addDomEvent)(this.node.current, \"keyup\", handleKeyup);\n                fireSyntheticPointerEvent(\"down\", (event, info) => {\n                    this.startPress(event, info);\n                });\n            };\n            const removeKeydownListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_7__.addDomEvent)(this.node.current, \"keydown\", handleKeydown);\n            const handleBlur = () => {\n                if (!this.isPressing)\n                    return;\n                fireSyntheticPointerEvent(\"cancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo));\n            };\n            const removeBlurListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_7__.addDomEvent)(this.node.current, \"blur\", handleBlur);\n            this.removeAccessibleListeners = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_6__.pipe)(removeKeydownListener, removeBlurListener);\n        };\n    }\n    startPress(event, info) {\n        this.isPressing = true;\n        const { onTapStart, whileTap } = this.node.getProps();\n        /**\n         * Ensure we trigger animations before firing event callback\n         */\n        if (whileTap && this.node.animationState) {\n            this.node.animationState.setActive(\"whileTap\", true);\n        }\n        if (onTapStart) {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.update(() => onTapStart(event, info));\n        }\n    }\n    checkPressEnd() {\n        this.removeEndListeners();\n        this.isPressing = false;\n        const props = this.node.getProps();\n        if (props.whileTap && this.node.animationState) {\n            this.node.animationState.setActive(\"whileTap\", false);\n        }\n        return !(0,_drag_utils_lock_mjs__WEBPACK_IMPORTED_MODULE_8__.isDragActive)();\n    }\n    cancelPress(event, info) {\n        if (!this.checkPressEnd())\n            return;\n        const { onTapCancel } = this.node.getProps();\n        if (onTapCancel) {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.update(() => onTapCancel(event, info));\n        }\n    }\n    mount() {\n        const props = this.node.getProps();\n        const removePointerListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(props.globalTapTarget ? window : this.node.current, \"pointerdown\", this.startPointerPress, { passive: !(props.onTapStart || props[\"onPointerStart\"]) });\n        const removeFocusListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_7__.addDomEvent)(this.node.current, \"focus\", this.startAccessiblePress);\n        this.removeStartListeners = (0,_utils_pipe_mjs__WEBPACK_IMPORTED_MODULE_6__.pipe)(removePointerListener, removeFocusListener);\n    }\n    unmount() {\n        this.removeStartListeners();\n        this.removeEndListeners();\n        this.removeAccessibleListeners();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNEQ7QUFDRjtBQUNRO0FBQ1Q7QUFDaEI7QUFDWTtBQUNRO0FBQ3BCO0FBQ007O0FBRS9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLHdFQUFnQjtBQUNuRDtBQUNBLDJCQUEyQixpRUFBTztBQUNsQztBQUNBO0FBQ0Esb0NBQW9DLGlEQUFJO0FBQ3hDLGtDQUFrQyxpREFBSTtBQUN0Qyx5Q0FBeUMsaURBQUk7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixzQ0FBc0M7QUFDOUQsZ0JBQWdCLHVEQUFLO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsMEVBQWE7QUFDdEM7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLDRDQUE0Qyw4RUFBZSx5Q0FBeUMsaURBQWlEO0FBQ3JKLGdEQUFnRCw4RUFBZSxvR0FBb0csMkRBQTJEO0FBQzlOLHNDQUFzQyxxREFBSTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxRQUFRO0FBQ3hDO0FBQ0EsNEJBQTRCLHVEQUFLO0FBQ2pDO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSwwQ0FBMEMsc0VBQVc7QUFDckQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLDBDQUEwQyxzRUFBVztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLHNFQUFXO0FBQ2xELDZDQUE2QyxxREFBSTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix1QkFBdUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVEQUFLO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixrRUFBWTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixjQUFjO0FBQzlCO0FBQ0EsWUFBWSx1REFBSztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw4RUFBZSw4RkFBOEYseURBQXlEO0FBQzVNLG9DQUFvQyxzRUFBVztBQUMvQyxvQ0FBb0MscURBQUk7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MubWpzPzQzYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZXh0cmFjdEV2ZW50SW5mbyB9IGZyb20gJy4uL2V2ZW50cy9ldmVudC1pbmZvLm1qcyc7XG5pbXBvcnQgeyBhZGREb21FdmVudCB9IGZyb20gJy4uL2V2ZW50cy9hZGQtZG9tLWV2ZW50Lm1qcyc7XG5pbXBvcnQgeyBhZGRQb2ludGVyRXZlbnQgfSBmcm9tICcuLi9ldmVudHMvYWRkLXBvaW50ZXItZXZlbnQubWpzJztcbmltcG9ydCB7IEZlYXR1cmUgfSBmcm9tICcuLi9tb3Rpb24vZmVhdHVyZXMvRmVhdHVyZS5tanMnO1xuaW1wb3J0IHsgcGlwZSB9IGZyb20gJy4uL3V0aWxzL3BpcGUubWpzJztcbmltcG9ydCB7IGlzRHJhZ0FjdGl2ZSB9IGZyb20gJy4vZHJhZy91dGlscy9sb2NrLm1qcyc7XG5pbXBvcnQgeyBpc05vZGVPckNoaWxkIH0gZnJvbSAnLi91dGlscy9pcy1ub2RlLW9yLWNoaWxkLm1qcyc7XG5pbXBvcnQgeyBub29wIH0gZnJvbSAnLi4vdXRpbHMvbm9vcC5tanMnO1xuaW1wb3J0IHsgZnJhbWUgfSBmcm9tICcuLi9mcmFtZWxvb3AvZnJhbWUubWpzJztcblxuZnVuY3Rpb24gZmlyZVN5bnRoZXRpY1BvaW50ZXJFdmVudChuYW1lLCBoYW5kbGVyKSB7XG4gICAgaWYgKCFoYW5kbGVyKVxuICAgICAgICByZXR1cm47XG4gICAgY29uc3Qgc3ludGhldGljUG9pbnRlckV2ZW50ID0gbmV3IFBvaW50ZXJFdmVudChcInBvaW50ZXJcIiArIG5hbWUpO1xuICAgIGhhbmRsZXIoc3ludGhldGljUG9pbnRlckV2ZW50LCBleHRyYWN0RXZlbnRJbmZvKHN5bnRoZXRpY1BvaW50ZXJFdmVudCkpO1xufVxuY2xhc3MgUHJlc3NHZXN0dXJlIGV4dGVuZHMgRmVhdHVyZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgICAgIHRoaXMucmVtb3ZlU3RhcnRMaXN0ZW5lcnMgPSBub29wO1xuICAgICAgICB0aGlzLnJlbW92ZUVuZExpc3RlbmVycyA9IG5vb3A7XG4gICAgICAgIHRoaXMucmVtb3ZlQWNjZXNzaWJsZUxpc3RlbmVycyA9IG5vb3A7XG4gICAgICAgIHRoaXMuc3RhcnRQb2ludGVyUHJlc3MgPSAoc3RhcnRFdmVudCwgc3RhcnRJbmZvKSA9PiB7XG4gICAgICAgICAgICBpZiAodGhpcy5pc1ByZXNzaW5nKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIHRoaXMucmVtb3ZlRW5kTGlzdGVuZXJzKCk7XG4gICAgICAgICAgICBjb25zdCBwcm9wcyA9IHRoaXMubm9kZS5nZXRQcm9wcygpO1xuICAgICAgICAgICAgY29uc3QgZW5kUG9pbnRlclByZXNzID0gKGVuZEV2ZW50LCBlbmRJbmZvKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCF0aGlzLmNoZWNrUHJlc3NFbmQoKSlcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgb25UYXAsIG9uVGFwQ2FuY2VsLCBnbG9iYWxUYXBUYXJnZXQgfSA9IHRoaXMubm9kZS5nZXRQcm9wcygpO1xuICAgICAgICAgICAgICAgIGZyYW1lLnVwZGF0ZSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAgICAgKiBXZSBvbmx5IGNvdW50IHRoaXMgYXMgYSB0YXAgZ2VzdHVyZSBpZiB0aGUgZXZlbnQudGFyZ2V0IGlzIHRoZSBzYW1lXG4gICAgICAgICAgICAgICAgICAgICAqIGFzLCBvciBhIGNoaWxkIG9mLCB0aGlzIGNvbXBvbmVudCdzIGVsZW1lbnRcbiAgICAgICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgICAgICFnbG9iYWxUYXBUYXJnZXQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICFpc05vZGVPckNoaWxkKHRoaXMubm9kZS5jdXJyZW50LCBlbmRFdmVudC50YXJnZXQpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG9uVGFwQ2FuY2VsICYmIG9uVGFwQ2FuY2VsKGVuZEV2ZW50LCBlbmRJbmZvKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBvblRhcCAmJiBvblRhcChlbmRFdmVudCwgZW5kSW5mbyk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgcmVtb3ZlUG9pbnRlclVwTGlzdGVuZXIgPSBhZGRQb2ludGVyRXZlbnQod2luZG93LCBcInBvaW50ZXJ1cFwiLCBlbmRQb2ludGVyUHJlc3MsIHsgcGFzc2l2ZTogIShwcm9wcy5vblRhcCB8fCBwcm9wc1tcIm9uUG9pbnRlclVwXCJdKSB9KTtcbiAgICAgICAgICAgIGNvbnN0IHJlbW92ZVBvaW50ZXJDYW5jZWxMaXN0ZW5lciA9IGFkZFBvaW50ZXJFdmVudCh3aW5kb3csIFwicG9pbnRlcmNhbmNlbFwiLCAoY2FuY2VsRXZlbnQsIGNhbmNlbEluZm8pID0+IHRoaXMuY2FuY2VsUHJlc3MoY2FuY2VsRXZlbnQsIGNhbmNlbEluZm8pLCB7IHBhc3NpdmU6ICEocHJvcHMub25UYXBDYW5jZWwgfHwgcHJvcHNbXCJvblBvaW50ZXJDYW5jZWxcIl0pIH0pO1xuICAgICAgICAgICAgdGhpcy5yZW1vdmVFbmRMaXN0ZW5lcnMgPSBwaXBlKHJlbW92ZVBvaW50ZXJVcExpc3RlbmVyLCByZW1vdmVQb2ludGVyQ2FuY2VsTGlzdGVuZXIpO1xuICAgICAgICAgICAgdGhpcy5zdGFydFByZXNzKHN0YXJ0RXZlbnQsIHN0YXJ0SW5mbyk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuc3RhcnRBY2Nlc3NpYmxlUHJlc3MgPSAoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBoYW5kbGVLZXlkb3duID0gKGtleWRvd25FdmVudCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChrZXlkb3duRXZlbnQua2V5ICE9PSBcIkVudGVyXCIgfHwgdGhpcy5pc1ByZXNzaW5nKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgY29uc3QgaGFuZGxlS2V5dXAgPSAoa2V5dXBFdmVudCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoa2V5dXBFdmVudC5rZXkgIT09IFwiRW50ZXJcIiB8fCAhdGhpcy5jaGVja1ByZXNzRW5kKCkpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIGZpcmVTeW50aGV0aWNQb2ludGVyRXZlbnQoXCJ1cFwiLCAoZXZlbnQsIGluZm8pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgb25UYXAgfSA9IHRoaXMubm9kZS5nZXRQcm9wcygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9uVGFwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZnJhbWUudXBkYXRlKCgpID0+IG9uVGFwKGV2ZW50LCBpbmZvKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgdGhpcy5yZW1vdmVFbmRMaXN0ZW5lcnMoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZUVuZExpc3RlbmVycyA9IGFkZERvbUV2ZW50KHRoaXMubm9kZS5jdXJyZW50LCBcImtleXVwXCIsIGhhbmRsZUtleXVwKTtcbiAgICAgICAgICAgICAgICBmaXJlU3ludGhldGljUG9pbnRlckV2ZW50KFwiZG93blwiLCAoZXZlbnQsIGluZm8pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGFydFByZXNzKGV2ZW50LCBpbmZvKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCByZW1vdmVLZXlkb3duTGlzdGVuZXIgPSBhZGREb21FdmVudCh0aGlzLm5vZGUuY3VycmVudCwgXCJrZXlkb3duXCIsIGhhbmRsZUtleWRvd24pO1xuICAgICAgICAgICAgY29uc3QgaGFuZGxlQmx1ciA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuaXNQcmVzc2luZylcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGZpcmVTeW50aGV0aWNQb2ludGVyRXZlbnQoXCJjYW5jZWxcIiwgKGNhbmNlbEV2ZW50LCBjYW5jZWxJbmZvKSA9PiB0aGlzLmNhbmNlbFByZXNzKGNhbmNlbEV2ZW50LCBjYW5jZWxJbmZvKSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgcmVtb3ZlQmx1ckxpc3RlbmVyID0gYWRkRG9tRXZlbnQodGhpcy5ub2RlLmN1cnJlbnQsIFwiYmx1clwiLCBoYW5kbGVCbHVyKTtcbiAgICAgICAgICAgIHRoaXMucmVtb3ZlQWNjZXNzaWJsZUxpc3RlbmVycyA9IHBpcGUocmVtb3ZlS2V5ZG93bkxpc3RlbmVyLCByZW1vdmVCbHVyTGlzdGVuZXIpO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBzdGFydFByZXNzKGV2ZW50LCBpbmZvKSB7XG4gICAgICAgIHRoaXMuaXNQcmVzc2luZyA9IHRydWU7XG4gICAgICAgIGNvbnN0IHsgb25UYXBTdGFydCwgd2hpbGVUYXAgfSA9IHRoaXMubm9kZS5nZXRQcm9wcygpO1xuICAgICAgICAvKipcbiAgICAgICAgICogRW5zdXJlIHdlIHRyaWdnZXIgYW5pbWF0aW9ucyBiZWZvcmUgZmlyaW5nIGV2ZW50IGNhbGxiYWNrXG4gICAgICAgICAqL1xuICAgICAgICBpZiAod2hpbGVUYXAgJiYgdGhpcy5ub2RlLmFuaW1hdGlvblN0YXRlKSB7XG4gICAgICAgICAgICB0aGlzLm5vZGUuYW5pbWF0aW9uU3RhdGUuc2V0QWN0aXZlKFwid2hpbGVUYXBcIiwgdHJ1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9uVGFwU3RhcnQpIHtcbiAgICAgICAgICAgIGZyYW1lLnVwZGF0ZSgoKSA9PiBvblRhcFN0YXJ0KGV2ZW50LCBpbmZvKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2hlY2tQcmVzc0VuZCgpIHtcbiAgICAgICAgdGhpcy5yZW1vdmVFbmRMaXN0ZW5lcnMoKTtcbiAgICAgICAgdGhpcy5pc1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgIGNvbnN0IHByb3BzID0gdGhpcy5ub2RlLmdldFByb3BzKCk7XG4gICAgICAgIGlmIChwcm9wcy53aGlsZVRhcCAmJiB0aGlzLm5vZGUuYW5pbWF0aW9uU3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMubm9kZS5hbmltYXRpb25TdGF0ZS5zZXRBY3RpdmUoXCJ3aGlsZVRhcFwiLCBmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICFpc0RyYWdBY3RpdmUoKTtcbiAgICB9XG4gICAgY2FuY2VsUHJlc3MoZXZlbnQsIGluZm8pIHtcbiAgICAgICAgaWYgKCF0aGlzLmNoZWNrUHJlc3NFbmQoKSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3QgeyBvblRhcENhbmNlbCB9ID0gdGhpcy5ub2RlLmdldFByb3BzKCk7XG4gICAgICAgIGlmIChvblRhcENhbmNlbCkge1xuICAgICAgICAgICAgZnJhbWUudXBkYXRlKCgpID0+IG9uVGFwQ2FuY2VsKGV2ZW50LCBpbmZvKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgbW91bnQoKSB7XG4gICAgICAgIGNvbnN0IHByb3BzID0gdGhpcy5ub2RlLmdldFByb3BzKCk7XG4gICAgICAgIGNvbnN0IHJlbW92ZVBvaW50ZXJMaXN0ZW5lciA9IGFkZFBvaW50ZXJFdmVudChwcm9wcy5nbG9iYWxUYXBUYXJnZXQgPyB3aW5kb3cgOiB0aGlzLm5vZGUuY3VycmVudCwgXCJwb2ludGVyZG93blwiLCB0aGlzLnN0YXJ0UG9pbnRlclByZXNzLCB7IHBhc3NpdmU6ICEocHJvcHMub25UYXBTdGFydCB8fCBwcm9wc1tcIm9uUG9pbnRlclN0YXJ0XCJdKSB9KTtcbiAgICAgICAgY29uc3QgcmVtb3ZlRm9jdXNMaXN0ZW5lciA9IGFkZERvbUV2ZW50KHRoaXMubm9kZS5jdXJyZW50LCBcImZvY3VzXCIsIHRoaXMuc3RhcnRBY2Nlc3NpYmxlUHJlc3MpO1xuICAgICAgICB0aGlzLnJlbW92ZVN0YXJ0TGlzdGVuZXJzID0gcGlwZShyZW1vdmVQb2ludGVyTGlzdGVuZXIsIHJlbW92ZUZvY3VzTGlzdGVuZXIpO1xuICAgIH1cbiAgICB1bm1vdW50KCkge1xuICAgICAgICB0aGlzLnJlbW92ZVN0YXJ0TGlzdGVuZXJzKCk7XG4gICAgICAgIHRoaXMucmVtb3ZlRW5kTGlzdGVuZXJzKCk7XG4gICAgICAgIHRoaXMucmVtb3ZlQWNjZXNzaWJsZUxpc3RlbmVycygpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgUHJlc3NHZXN0dXJlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/press.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/utils/is-node-or-child.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: function() { return /* binding */ isNodeOrChild; }\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtbm9kZS1vci1jaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9nZXN0dXJlcy91dGlscy9pcy1ub2RlLW9yLWNoaWxkLm1qcz9kZmIxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVjdXJzaXZlbHkgdHJhdmVyc2UgdXAgdGhlIHRyZWUgdG8gY2hlY2sgd2hldGhlciB0aGUgcHJvdmlkZWQgY2hpbGQgbm9kZVxuICogaXMgdGhlIHBhcmVudCBvciBhIGRlc2NlbmRhbnQgb2YgaXQuXG4gKlxuICogQHBhcmFtIHBhcmVudCAtIEVsZW1lbnQgdG8gZmluZFxuICogQHBhcmFtIGNoaWxkIC0gRWxlbWVudCB0byB0ZXN0IGFnYWluc3QgcGFyZW50XG4gKi9cbmNvbnN0IGlzTm9kZU9yQ2hpbGQgPSAocGFyZW50LCBjaGlsZCkgPT4ge1xuICAgIGlmICghY2hpbGQpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBlbHNlIGlmIChwYXJlbnQgPT09IGNoaWxkKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGlzTm9kZU9yQ2hpbGQocGFyZW50LCBjaGlsZC5wYXJlbnRFbGVtZW50KTtcbiAgICB9XG59O1xuXG5leHBvcnQgeyBpc05vZGVPckNoaWxkIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/gestures/utils/is-node-or-child.mjs\n"));

/***/ })

}]);