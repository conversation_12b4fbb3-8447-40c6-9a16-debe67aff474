"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_wooInventoryMapping_ts-292aad95"],{

/***/ "(app-pages-browser)/./src/lib/wooInventoryMapping.ts":
/*!****************************************!*\
  !*** ./src/lib/wooInventoryMapping.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addInventoryMapping: function() { return /* binding */ addInventoryMapping; },\n/* harmony export */   clearInventoryMappings: function() { return /* binding */ clearInventoryMappings; },\n/* harmony export */   getAllInventoryMappings: function() { return /* binding */ getAllInventoryMappings; },\n/* harmony export */   getAllShopifyToWooMappings: function() { return /* binding */ getAllShopifyToWooMappings; },\n/* harmony export */   getInventoryMapping: function() { return /* binding */ getInventoryMapping; },\n/* harmony export */   getProductSlugFromInventory: function() { return /* binding */ getProductSlugFromInventory; },\n/* harmony export */   getWooIdFromShopifyId: function() { return /* binding */ getWooIdFromShopifyId; },\n/* harmony export */   initializeFromProducts: function() { return /* binding */ initializeFromProducts; },\n/* harmony export */   loadInventoryMap: function() { return /* binding */ loadInventoryMap; },\n/* harmony export */   mapShopifyToWooId: function() { return /* binding */ mapShopifyToWooId; },\n/* harmony export */   saveInventoryMap: function() { return /* binding */ saveInventoryMap; },\n/* harmony export */   updateInventoryMapping: function() { return /* binding */ updateInventoryMapping; },\n/* harmony export */   updateInventoryMappings: function() { return /* binding */ updateInventoryMappings; },\n/* harmony export */   validateProductId: function() { return /* binding */ validateProductId; }\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(app-pages-browser)/./node_modules/@upstash/redis/nodejs.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Redis key prefix for inventory mappings\nconst KEY_PREFIX = \"woo:inventory:mapping:\";\n// Redis key for the mapping between Shopify and WooCommerce IDs\nconst SHOPIFY_TO_WOO_KEY = \"shopify:to:woo:mapping\";\n// Initialize Redis client with support for both Upstash Redis and Vercel KV variables\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL || process.env.NEXT_PUBLIC_KV_REST_API_URL || \"\",\n    token: process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_TOKEN || \"\"\n});\n// In-memory fallback for local development or when Redis is unavailable\nconst memoryStorage = {};\nconst shopifyToWooMemoryStorage = {};\n// Key for storing inventory mapping in KV store\nconst INVENTORY_MAPPING_KEY = \"woo-inventory-mapping\";\n/**\n * Check if Redis is available\n */ function isRedisAvailable() {\n    return Boolean(process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_URL && process.env.NEXT_PUBLIC_KV_REST_API_TOKEN);\n}\n/**\n * Load inventory mapping from storage\n * Maps WooCommerce product IDs to product slugs\n * \n * @returns A record mapping product IDs to product slugs\n */ async function loadInventoryMap() {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Get all keys with our prefix\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length === 0) {\n                console.log(\"No existing WooCommerce inventory mappings found in Redis\");\n                return {};\n            }\n            // Create a mapping object\n            const map = {};\n            // Get all values in a single batch operation\n            const values = await redis.mget(...keys);\n            // Populate the mapping object\n            keys.forEach((key, index)=>{\n                const productId = key.replace(KEY_PREFIX, \"\");\n                const productSlug = values[index];\n                map[productId] = productSlug;\n            });\n            console.log(\"Loaded WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries from Redis\"));\n            return map;\n        } catch (error) {\n            console.error(\"Error loading WooCommerce inventory mapping from Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            return {\n                ...memoryStorage\n            };\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        return {\n            ...memoryStorage\n        };\n    }\n}\n/**\n * Save inventory mapping to storage\n * \n * @param map The inventory mapping to save\n */ async function saveInventoryMap(map) {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Convert map to array of Redis commands\n            const pipeline = redis.pipeline();\n            // First clear existing keys with this prefix\n            const existingKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (existingKeys.length > 0) {\n                pipeline.del(...existingKeys);\n            }\n            // Set new key-value pairs\n            Object.entries(map).forEach((param)=>{\n                let [productId, productSlug] = param;\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            });\n            // Execute all commands in a single transaction\n            await pipeline.exec();\n            console.log(\"Saved WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries to Redis\"));\n        } catch (error) {\n            console.error(\"Error saving WooCommerce inventory mapping to Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            // Update in-memory storage as fallback\n            Object.assign(memoryStorage, map);\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        Object.assign(memoryStorage, map);\n        console.log(\"Saved WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries to memory\"));\n    }\n}\n/**\n * Add a mapping between a WooCommerce product ID and a product slug\n * \n * @param productId The WooCommerce product ID\n * @param productSlug The product slug\n * @returns True if the mapping was added or updated, false if there was an error\n */ async function addInventoryMapping(productId, productSlug) {\n    try {\n        if (isRedisAvailable()) {\n            await redis.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            console.log(\"Added WooCommerce mapping to Redis: \".concat(productId, \" -> \").concat(productSlug));\n        } else {\n            memoryStorage[productId] = productSlug;\n            console.log(\"Added WooCommerce mapping to memory: \".concat(productId, \" -> \").concat(productSlug));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error adding WooCommerce inventory mapping:\", error);\n        // Try memory as fallback\n        try {\n            memoryStorage[productId] = productSlug;\n            console.log(\"Added WooCommerce mapping to memory fallback: \".concat(productId, \" -> \").concat(productSlug));\n            return true;\n        } catch (memError) {\n            console.error(\"Error adding to memory fallback:\", memError);\n            return false;\n        }\n    }\n}\n/**\n * Get the product slug associated with a WooCommerce product ID\n * \n * @param productId The WooCommerce product ID\n * @returns The product slug, or null if not found\n */ async function getProductSlugFromInventory(productId) {\n    try {\n        if (isRedisAvailable()) {\n            const slug = await redis.get(\"\".concat(KEY_PREFIX).concat(productId));\n            return slug || null;\n        } else {\n            return memoryStorage[productId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting product slug from Redis:\", error);\n        // Try memory as fallback\n        try {\n            return memoryStorage[productId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Batch update multiple WooCommerce inventory mappings\n * \n * @param mappings An array of product ID to product slug mappings\n * @returns True if all mappings were successfully updated, false otherwise\n */ async function updateInventoryMappings(mappings) {\n    try {\n        if (isRedisAvailable()) {\n            const pipeline = redis.pipeline();\n            for (const { productId, productSlug } of mappings){\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            }\n            await pipeline.exec();\n            console.log(\"Updated \".concat(mappings.length, \" WooCommerce inventory mappings in Redis\"));\n        } else {\n            for (const { productId, productSlug } of mappings){\n                memoryStorage[productId] = productSlug;\n            }\n            console.log(\"Updated \".concat(mappings.length, \" WooCommerce inventory mappings in memory\"));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error batch updating WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Get all WooCommerce inventory mappings\n * \n * @returns The complete inventory map\n */ async function getAllInventoryMappings() {\n    return await loadInventoryMap();\n}\n/**\n * Clear all WooCommerce inventory mappings\n * \n * @returns True if successfully cleared, false otherwise\n */ async function clearInventoryMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length > 0) {\n                await redis.del(...keys);\n            }\n            console.log(\"Cleared all WooCommerce inventory mappings from Redis\");\n        }\n        // Clear memory storage regardless of Redis availability\n        Object.keys(memoryStorage).forEach((key)=>{\n            delete memoryStorage[key];\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error clearing WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Map a Shopify product ID to a WooCommerce product ID\n * \n * @param shopifyId The Shopify product ID\n * @param wooId The WooCommerce product ID\n * @returns True if the mapping was added successfully, false otherwise\n */ async function mapShopifyToWooId(shopifyId, wooId) {\n    try {\n        if (isRedisAvailable()) {\n            // Get existing mappings\n            const existingMap = await redis.hgetall(SHOPIFY_TO_WOO_KEY) || {};\n            // Add new mapping\n            existingMap[shopifyId] = wooId;\n            // Save updated mappings\n            await redis.hset(SHOPIFY_TO_WOO_KEY, existingMap);\n            console.log(\"Mapped Shopify ID \".concat(shopifyId, \" to WooCommerce ID \").concat(wooId, \" in Redis\"));\n            return true;\n        } else {\n            // Fallback to in-memory\n            shopifyToWooMemoryStorage[shopifyId] = wooId;\n            console.log(\"Mapped Shopify ID \".concat(shopifyId, \" to WooCommerce ID \").concat(wooId, \" in memory\"));\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error mapping Shopify ID to WooCommerce ID:\", error);\n        return false;\n    }\n}\n/**\n * Get the WooCommerce ID corresponding to a Shopify ID\n * \n * @param shopifyId The original Shopify product ID or inventory item ID\n * @returns The corresponding WooCommerce ID, or null if not found\n */ async function getWooIdFromShopifyId(shopifyId) {\n    try {\n        if (isRedisAvailable()) {\n            const wooId = await redis.hget(SHOPIFY_TO_WOO_KEY, shopifyId);\n            return wooId || null;\n        } else {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting WooCommerce ID for Shopify ID \".concat(shopifyId, \":\"), error);\n        // Try memory as fallback\n        try {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Get all Shopify to WooCommerce ID mappings\n * \n * @returns Record of Shopify IDs to WooCommerce IDs\n */ async function getAllShopifyToWooMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const mappings = await redis.hgetall(SHOPIFY_TO_WOO_KEY);\n            return mappings || {};\n        } else {\n            return {\n                ...shopifyToWooMemoryStorage\n            };\n        }\n    } catch (error) {\n        console.error(\"Error getting all Shopify to WooCommerce mappings:\", error);\n        return {\n            ...shopifyToWooMemoryStorage\n        };\n    }\n}\n/**\n * Initialize inventory mappings from WooCommerce products\n * This function should be called after initial product import or periodically to refresh the mappings\n * \n * @param products Array of WooCommerce products with id and slug properties\n * @returns True if successfully initialized, false otherwise\n */ async function initializeFromProducts(products) {\n    try {\n        const inventoryMappings = [];\n        const idMappings = [];\n        for (const product of products){\n            // Add to inventory mappings\n            inventoryMappings.push({\n                productId: product.id,\n                productSlug: product.slug\n            });\n            // If this product has a Shopify ID, add to ID mappings\n            if (product.shopifyId) {\n                idMappings.push({\n                    shopifyId: product.shopifyId,\n                    wooId: product.id\n                });\n            }\n        }\n        // Update inventory mappings\n        await updateInventoryMappings(inventoryMappings);\n        // Update ID mappings\n        for (const { shopifyId, wooId } of idMappings){\n            await mapShopifyToWooId(shopifyId, wooId);\n        }\n        console.log(\"Initialized \".concat(inventoryMappings.length, \" inventory mappings and \").concat(idMappings.length, \" ID mappings\"));\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing inventory mappings from products:\", error);\n        return false;\n    }\n}\n/**\n * Get the current inventory mapping\n * \n * @returns The inventory mapping\n */ async function getInventoryMapping() {\n    try {\n        // Try to get the mapping from Redis\n        if (isRedisAvailable()) {\n            const allKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (allKeys.length > 0) {\n                const mapping = {};\n                const values = await redis.mget(...allKeys);\n                allKeys.forEach((key, index)=>{\n                    const productId = key.replace(KEY_PREFIX, \"\");\n                    const slug = values[index];\n                    mapping[productId] = {\n                        wooId: productId,\n                        inventory: 0,\n                        sku: \"\",\n                        title: slug,\n                        lastUpdated: new Date().toISOString()\n                    };\n                });\n                return mapping;\n            }\n        }\n        // Default empty mapping\n        return {};\n    } catch (error) {\n        console.error(\"Error getting inventory mapping:\", error);\n        return {};\n    }\n}\n/**\n * Update the inventory mapping\n * \n * @param mapping The inventory mapping to save\n * @returns True if successful, false otherwise\n */ async function updateInventoryMapping(mapping) {\n    try {\n        if (isRedisAvailable()) {\n            // First clear existing keys\n            const existingKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (existingKeys.length > 0) {\n                await redis.del(...existingKeys);\n            }\n            // Add each product mapping\n            const pipeline = redis.pipeline();\n            for (const [productId, details] of Object.entries(mapping)){\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), details.title || productId);\n            }\n            await pipeline.exec();\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Error updating inventory mapping:\", error);\n        return false;\n    }\n}\n/**\n * Validate and transform product ID\n * \n * This function helps with the migration from Shopify to WooCommerce by:\n * 1. Checking if the ID is a valid WooCommerce ID\n * 2. If not, attempting to map from Shopify ID to WooCommerce ID\n * 3. Returning a normalized ID or the original ID if no mapping found\n * \n * @param productId The product ID to validate (could be Shopify or WooCommerce ID)\n * @returns A valid WooCommerce product ID or the original ID if no mapping found\n */ async function validateProductId(productId) {\n    if (!productId || productId === \"undefined\" || productId === \"null\") {\n        console.warn(\"Invalid product ID received:\", productId);\n        return productId; // Return the original ID even if invalid\n    }\n    // Check if this looks like a Shopify ID (gid://shopify/Product/123456789)\n    if (productId.includes(\"gid://shopify/Product/\")) {\n        console.log(\"Detected Shopify ID: \".concat(productId, \", attempting to map to WooCommerce ID\"));\n        // Try to get the WooCommerce ID from our mapping\n        const wooId = await getWooIdFromShopifyId(productId);\n        if (wooId) {\n            console.log(\"Mapped Shopify ID \".concat(productId, \" to WooCommerce ID \").concat(wooId));\n            return wooId;\n        } else {\n            console.warn(\"No mapping found for Shopify ID: \".concat(productId, \", using original ID\"));\n            return productId; // Return the original ID if no mapping found\n        }\n    }\n    // If it's a base64 encoded ID like \"cG9zdDo2MA==\", it might be a WooCommerce ID\n    // but we should check if it actually exists in our inventory mapping\n    if (productId.includes(\"=\")) {\n        const slug = await getProductSlugFromInventory(productId);\n        if (slug) {\n            // We have a mapping for this ID, so it's likely valid\n            return productId;\n        } else {\n            console.warn(\"Product ID \".concat(productId, \" not found in inventory mapping, using as is\"));\n            // We'll still return the ID and let the GraphQL API handle the validation\n            return productId;\n        }\n    }\n    // If it's a numeric ID, it's likely a valid WooCommerce product ID\n    if (/^\\d+$/.test(productId)) {\n        return productId;\n    }\n    // For any other format, return as is and let the GraphQL API validate\n    return productId;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/wooInventoryMapping.ts\n"));

/***/ })

}]);