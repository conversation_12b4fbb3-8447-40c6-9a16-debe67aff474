"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_graphql_language_a"],{

/***/ "(app-pages-browser)/./node_modules/graphql/language/ast.mjs":
/*!***********************************************!*\
  !*** ./node_modules/graphql/language/ast.mjs ***!
  \***********************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Location: function() { return /* binding */ Location; },\n/* harmony export */   OperationTypeNode: function() { return /* binding */ OperationTypeNode; },\n/* harmony export */   QueryDocumentKeys: function() { return /* binding */ QueryDocumentKeys; },\n/* harmony export */   Token: function() { return /* binding */ Token; },\n/* harmony export */   isNode: function() { return /* binding */ isNode; }\n/* harmony export */ });\n/**\n * Contains a range of UTF-8 character offsets and token references that\n * identify the region of the source from which the AST derived.\n */\nclass Location {\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The Token at which this Node begins.\n   */\n\n  /**\n   * The Token at which this Node ends.\n   */\n\n  /**\n   * The Source document the AST represents.\n   */\n  constructor(startToken, endToken, source) {\n    this.start = startToken.start;\n    this.end = endToken.end;\n    this.startToken = startToken;\n    this.endToken = endToken;\n    this.source = source;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Location';\n  }\n\n  toJSON() {\n    return {\n      start: this.start,\n      end: this.end,\n    };\n  }\n}\n/**\n * Represents a range of characters represented by a lexical token\n * within a Source.\n */\n\nclass Token {\n  /**\n   * The kind of Token.\n   */\n\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The 1-indexed line number on which this Token appears.\n   */\n\n  /**\n   * The 1-indexed column number at which this Token begins.\n   */\n\n  /**\n   * For non-punctuation tokens, represents the interpreted value of the token.\n   *\n   * Note: is undefined for punctuation tokens, but typed as string for\n   * convenience in the parser.\n   */\n\n  /**\n   * Tokens exist as nodes in a double-linked-list amongst all tokens\n   * including ignored tokens. <SOF> is always the first node and <EOF>\n   * the last.\n   */\n  constructor(kind, start, end, line, column, value) {\n    this.kind = kind;\n    this.start = start;\n    this.end = end;\n    this.line = line;\n    this.column = column; // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    this.value = value;\n    this.prev = null;\n    this.next = null;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Token';\n  }\n\n  toJSON() {\n    return {\n      kind: this.kind,\n      value: this.value,\n      line: this.line,\n      column: this.column,\n    };\n  }\n}\n/**\n * The list of all possible AST node types.\n */\n\n/**\n * @internal\n */\nconst QueryDocumentKeys = {\n  Name: [],\n  Document: ['definitions'],\n  OperationDefinition: [\n    'name',\n    'variableDefinitions',\n    'directives',\n    'selectionSet',\n  ],\n  VariableDefinition: ['variable', 'type', 'defaultValue', 'directives'],\n  Variable: ['name'],\n  SelectionSet: ['selections'],\n  Field: ['alias', 'name', 'arguments', 'directives', 'selectionSet'],\n  Argument: ['name', 'value'],\n  FragmentSpread: ['name', 'directives'],\n  InlineFragment: ['typeCondition', 'directives', 'selectionSet'],\n  FragmentDefinition: [\n    'name', // Note: fragment variable definitions are deprecated and will removed in v17.0.0\n    'variableDefinitions',\n    'typeCondition',\n    'directives',\n    'selectionSet',\n  ],\n  IntValue: [],\n  FloatValue: [],\n  StringValue: [],\n  BooleanValue: [],\n  NullValue: [],\n  EnumValue: [],\n  ListValue: ['values'],\n  ObjectValue: ['fields'],\n  ObjectField: ['name', 'value'],\n  Directive: ['name', 'arguments'],\n  NamedType: ['name'],\n  ListType: ['type'],\n  NonNullType: ['type'],\n  SchemaDefinition: ['description', 'directives', 'operationTypes'],\n  OperationTypeDefinition: ['type'],\n  ScalarTypeDefinition: ['description', 'name', 'directives'],\n  ObjectTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  FieldDefinition: ['description', 'name', 'arguments', 'type', 'directives'],\n  InputValueDefinition: [\n    'description',\n    'name',\n    'type',\n    'defaultValue',\n    'directives',\n  ],\n  InterfaceTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  UnionTypeDefinition: ['description', 'name', 'directives', 'types'],\n  EnumTypeDefinition: ['description', 'name', 'directives', 'values'],\n  EnumValueDefinition: ['description', 'name', 'directives'],\n  InputObjectTypeDefinition: ['description', 'name', 'directives', 'fields'],\n  DirectiveDefinition: ['description', 'name', 'arguments', 'locations'],\n  SchemaExtension: ['directives', 'operationTypes'],\n  ScalarTypeExtension: ['name', 'directives'],\n  ObjectTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  InterfaceTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  UnionTypeExtension: ['name', 'directives', 'types'],\n  EnumTypeExtension: ['name', 'directives', 'values'],\n  InputObjectTypeExtension: ['name', 'directives', 'fields'],\n};\nconst kindValues = new Set(Object.keys(QueryDocumentKeys));\n/**\n * @internal\n */\n\nfunction isNode(maybeNode) {\n  const maybeKind =\n    maybeNode === null || maybeNode === void 0 ? void 0 : maybeNode.kind;\n  return typeof maybeKind === 'string' && kindValues.has(maybeKind);\n}\n/** Name */\n\nvar OperationTypeNode;\n\n(function (OperationTypeNode) {\n  OperationTypeNode['QUERY'] = 'query';\n  OperationTypeNode['MUTATION'] = 'mutation';\n  OperationTypeNode['SUBSCRIPTION'] = 'subscription';\n})(OperationTypeNode || (OperationTypeNode = {}));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/ast.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/blockString.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/language/blockString.mjs ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedentBlockStringLines: function() { return /* binding */ dedentBlockStringLines; },\n/* harmony export */   isPrintableAsBlockString: function() { return /* binding */ isPrintableAsBlockString; },\n/* harmony export */   printBlockString: function() { return /* binding */ printBlockString; }\n/* harmony export */ });\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/characterClasses.mjs\");\n\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nfunction dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine =\n      (_firstNonEmptyLine = firstNonEmptyLine) !== null &&\n      _firstNonEmptyLine !== void 0\n        ? _firstNonEmptyLine\n        : i;\n    lastNonEmptyLine = i;\n\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n\n  return lines // Remove common indentation from all lines but first.\n    .map((line, i) => (i === 0 ? line : line.slice(commonIndent))) // Remove leading and trailing blank lines.\n    .slice(\n      (_firstNonEmptyLine2 = firstNonEmptyLine) !== null &&\n        _firstNonEmptyLine2 !== void 0\n        ? _firstNonEmptyLine2\n        : 0,\n      lastNonEmptyLine + 1,\n    );\n}\n\nfunction leadingWhitespace(str) {\n  let i = 0;\n\n  while (i < str.length && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(str.charCodeAt(i))) {\n    ++i;\n  }\n\n  return i;\n}\n/**\n * @internal\n */\n\nfunction isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nfunction printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine =\n    lines.length > 1 &&\n    lines\n      .slice(1)\n      .every((line) => line.length === 0 || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines =\n    !(options !== null && options !== void 0 && options.minimize) && // add leading and trailing new lines only if it improves readability\n    (!isSingleLine ||\n      value.length > 70 ||\n      forceTrailingNewline ||\n      forceLeadingNewLine ||\n      hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(value.charCodeAt(0));\n\n  if ((printAsMultipleLines && !skipLeadingNewLine) || forceLeadingNewLine) {\n    result += '\\n';\n  }\n\n  result += escapedValue;\n\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n\n  return '\"\"\"' + result + '\"\"\"';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/blockString.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/characterClasses.mjs":
/*!************************************************************!*\
  !*** ./node_modules/graphql/language/characterClasses.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDigit: function() { return /* binding */ isDigit; },\n/* harmony export */   isLetter: function() { return /* binding */ isLetter; },\n/* harmony export */   isNameContinue: function() { return /* binding */ isNameContinue; },\n/* harmony export */   isNameStart: function() { return /* binding */ isNameStart; },\n/* harmony export */   isWhiteSpace: function() { return /* binding */ isWhiteSpace; }\n/* harmony export */ });\n/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nfunction isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nfunction isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nfunction isLetter(code) {\n  return (\n    (code >= 0x0061 && code <= 0x007a) || // A-Z\n    (code >= 0x0041 && code <= 0x005a) // a-z\n  );\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/characterClasses.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/directiveLocation.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/graphql/language/directiveLocation.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectiveLocation: function() { return /* binding */ DirectiveLocation; }\n/* harmony export */ });\n/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\n\n\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2RpcmVjdGl2ZUxvY2F0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDOztBQUVsQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2RpcmVjdGl2ZUxvY2F0aW9uLm1qcz8xMTQ0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhlIHNldCBvZiBhbGxvd2VkIGRpcmVjdGl2ZSBsb2NhdGlvbiB2YWx1ZXMuXG4gKi9cbnZhciBEaXJlY3RpdmVMb2NhdGlvbjtcblxuKGZ1bmN0aW9uIChEaXJlY3RpdmVMb2NhdGlvbikge1xuICBEaXJlY3RpdmVMb2NhdGlvblsnUVVFUlknXSA9ICdRVUVSWSc7XG4gIERpcmVjdGl2ZUxvY2F0aW9uWydNVVRBVElPTiddID0gJ01VVEFUSU9OJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ1NVQlNDUklQVElPTiddID0gJ1NVQlNDUklQVElPTic7XG4gIERpcmVjdGl2ZUxvY2F0aW9uWydGSUVMRCddID0gJ0ZJRUxEJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ0ZSQUdNRU5UX0RFRklOSVRJT04nXSA9ICdGUkFHTUVOVF9ERUZJTklUSU9OJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ0ZSQUdNRU5UX1NQUkVBRCddID0gJ0ZSQUdNRU5UX1NQUkVBRCc7XG4gIERpcmVjdGl2ZUxvY2F0aW9uWydJTkxJTkVfRlJBR01FTlQnXSA9ICdJTkxJTkVfRlJBR01FTlQnO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnVkFSSUFCTEVfREVGSU5JVElPTiddID0gJ1ZBUklBQkxFX0RFRklOSVRJT04nO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnU0NIRU1BJ10gPSAnU0NIRU1BJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ1NDQUxBUiddID0gJ1NDQUxBUic7XG4gIERpcmVjdGl2ZUxvY2F0aW9uWydPQkpFQ1QnXSA9ICdPQkpFQ1QnO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnRklFTERfREVGSU5JVElPTiddID0gJ0ZJRUxEX0RFRklOSVRJT04nO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnQVJHVU1FTlRfREVGSU5JVElPTiddID0gJ0FSR1VNRU5UX0RFRklOSVRJT04nO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnSU5URVJGQUNFJ10gPSAnSU5URVJGQUNFJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ1VOSU9OJ10gPSAnVU5JT04nO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnRU5VTSddID0gJ0VOVU0nO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnRU5VTV9WQUxVRSddID0gJ0VOVU1fVkFMVUUnO1xuICBEaXJlY3RpdmVMb2NhdGlvblsnSU5QVVRfT0JKRUNUJ10gPSAnSU5QVVRfT0JKRUNUJztcbiAgRGlyZWN0aXZlTG9jYXRpb25bJ0lOUFVUX0ZJRUxEX0RFRklOSVRJT04nXSA9ICdJTlBVVF9GSUVMRF9ERUZJTklUSU9OJztcbn0pKERpcmVjdGl2ZUxvY2F0aW9uIHx8IChEaXJlY3RpdmVMb2NhdGlvbiA9IHt9KSk7XG5cbmV4cG9ydCB7IERpcmVjdGl2ZUxvY2F0aW9uIH07XG4vKipcbiAqIFRoZSBlbnVtIHR5cGUgcmVwcmVzZW50aW5nIHRoZSBkaXJlY3RpdmUgbG9jYXRpb24gdmFsdWVzLlxuICpcbiAqIEBkZXByZWNhdGVkIFBsZWFzZSB1c2UgYERpcmVjdGl2ZUxvY2F0aW9uYC4gV2lsbCBiZSByZW1vdmUgaW4gdjE3LlxuICovXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/directiveLocation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/kinds.mjs":
/*!*************************************************!*\
  !*** ./node_modules/graphql/language/kinds.mjs ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Kind: function() { return /* binding */ Kind; }\n/* harmony export */ });\n/**\n * The set of allowed kind values for AST nodes.\n */\nvar Kind;\n\n(function (Kind) {\n  Kind['NAME'] = 'Name';\n  Kind['DOCUMENT'] = 'Document';\n  Kind['OPERATION_DEFINITION'] = 'OperationDefinition';\n  Kind['VARIABLE_DEFINITION'] = 'VariableDefinition';\n  Kind['SELECTION_SET'] = 'SelectionSet';\n  Kind['FIELD'] = 'Field';\n  Kind['ARGUMENT'] = 'Argument';\n  Kind['FRAGMENT_SPREAD'] = 'FragmentSpread';\n  Kind['INLINE_FRAGMENT'] = 'InlineFragment';\n  Kind['FRAGMENT_DEFINITION'] = 'FragmentDefinition';\n  Kind['VARIABLE'] = 'Variable';\n  Kind['INT'] = 'IntValue';\n  Kind['FLOAT'] = 'FloatValue';\n  Kind['STRING'] = 'StringValue';\n  Kind['BOOLEAN'] = 'BooleanValue';\n  Kind['NULL'] = 'NullValue';\n  Kind['ENUM'] = 'EnumValue';\n  Kind['LIST'] = 'ListValue';\n  Kind['OBJECT'] = 'ObjectValue';\n  Kind['OBJECT_FIELD'] = 'ObjectField';\n  Kind['DIRECTIVE'] = 'Directive';\n  Kind['NAMED_TYPE'] = 'NamedType';\n  Kind['LIST_TYPE'] = 'ListType';\n  Kind['NON_NULL_TYPE'] = 'NonNullType';\n  Kind['SCHEMA_DEFINITION'] = 'SchemaDefinition';\n  Kind['OPERATION_TYPE_DEFINITION'] = 'OperationTypeDefinition';\n  Kind['SCALAR_TYPE_DEFINITION'] = 'ScalarTypeDefinition';\n  Kind['OBJECT_TYPE_DEFINITION'] = 'ObjectTypeDefinition';\n  Kind['FIELD_DEFINITION'] = 'FieldDefinition';\n  Kind['INPUT_VALUE_DEFINITION'] = 'InputValueDefinition';\n  Kind['INTERFACE_TYPE_DEFINITION'] = 'InterfaceTypeDefinition';\n  Kind['UNION_TYPE_DEFINITION'] = 'UnionTypeDefinition';\n  Kind['ENUM_TYPE_DEFINITION'] = 'EnumTypeDefinition';\n  Kind['ENUM_VALUE_DEFINITION'] = 'EnumValueDefinition';\n  Kind['INPUT_OBJECT_TYPE_DEFINITION'] = 'InputObjectTypeDefinition';\n  Kind['DIRECTIVE_DEFINITION'] = 'DirectiveDefinition';\n  Kind['SCHEMA_EXTENSION'] = 'SchemaExtension';\n  Kind['SCALAR_TYPE_EXTENSION'] = 'ScalarTypeExtension';\n  Kind['OBJECT_TYPE_EXTENSION'] = 'ObjectTypeExtension';\n  Kind['INTERFACE_TYPE_EXTENSION'] = 'InterfaceTypeExtension';\n  Kind['UNION_TYPE_EXTENSION'] = 'UnionTypeExtension';\n  Kind['ENUM_TYPE_EXTENSION'] = 'EnumTypeExtension';\n  Kind['INPUT_OBJECT_TYPE_EXTENSION'] = 'InputObjectTypeExtension';\n})(Kind || (Kind = {}));\n\n\n/**\n * The enum type representing the possible kind values of AST nodes.\n *\n * @deprecated Please use `Kind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/kinds.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/lexer.mjs":
/*!*************************************************!*\
  !*** ./node_modules/graphql/language/lexer.mjs ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lexer: function() { return /* binding */ Lexer; },\n/* harmony export */   isPunctuatorTokenKind: function() { return /* binding */ isPunctuatorTokenKind; }\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(app-pages-browser)/./node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blockString.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/characterClasses.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nclass Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = (this.token = this.lookahead());\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT);\n    }\n\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nfunction isPunctuatorTokenKind(kind) {\n  return (\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R\n  );\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return (\n    (code >= 0x0000 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0x10ffff)\n  );\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return (\n    isLeadingSurrogate(body.charCodeAt(location)) &&\n    isTrailingSurrogate(body.charCodeAt(location + 1))\n  );\n}\n\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\n\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n\n  if (code === undefined) {\n    return _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG, position, position + 1);\n\n      case 0x0024:\n        // $\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR, position, position + 1);\n\n      case 0x0026:\n        // &\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP, position, position + 1);\n\n      case 0x0028:\n        // (\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L, position, position + 1);\n\n      case 0x0029:\n        // )\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R, position, position + 1);\n\n      case 0x002e:\n        // .\n        if (\n          body.charCodeAt(position + 1) === 0x002e &&\n          body.charCodeAt(position + 2) === 0x002e\n        ) {\n          return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD, position, position + 3);\n        }\n\n        break;\n\n      case 0x003a:\n        // :\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON, position, position + 1);\n\n      case 0x003d:\n        // =\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS, position, position + 1);\n\n      case 0x0040:\n        // @\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT, position, position + 1);\n\n      case 0x005b:\n        // [\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L, position, position + 1);\n\n      case 0x005d:\n        // ]\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R, position, position + 1);\n\n      case 0x007b:\n        // {\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L, position, position + 1);\n\n      case 0x007c:\n        // |\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE, position, position + 1);\n\n      case 0x007d:\n        // }\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (\n          body.charCodeAt(position + 1) === 0x0022 &&\n          body.charCodeAt(position + 2) === 0x0022\n        ) {\n          return readBlockString(lexer, position);\n        }\n\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n      return readName(lexer, position);\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      code === 0x0027\n        ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?'\n        : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position)\n        ? `Unexpected character: ${printCodePointAt(lexer, position)}.`\n        : `Invalid character: ${printCodePointAt(lexer, position)}.`,\n    );\n  }\n\n  return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT,\n    start,\n    position,\n    body.slice(start + 1, position),\n  );\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code)) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid number, unexpected digit after 0: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        position,\n      )}.`,\n    );\n  }\n\n  return createToken(\n    lexer,\n    isFloat ? _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.FLOAT : _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.INT,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!(0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(firstCode)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      start,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        start,\n      )}.`,\n    );\n  }\n\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(body.charCodeAt(position))) {\n    ++position;\n  }\n\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape =\n        body.charCodeAt(position + 1) === 0x0075 // u\n          ? body.charCodeAt(position + 2) === 0x007b // {\n            ? readEscapedUnicodeVariableWidth(lexer, position)\n            : readEscapedUnicodeFixedWidth(lexer, position)\n          : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n\n      return {\n        value: String.fromCodePoint(point),\n        size,\n      };\n    } // Append this hex digit to the code point.\n\n    point = (point << 4) | readHexDigit(code);\n\n    if (point < 0) {\n      break;\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(\n      position,\n      position + size,\n    )}\".`,\n  );\n}\n\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6,\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (\n      body.charCodeAt(position + 6) === 0x005c &&\n      body.charCodeAt(position + 7) === 0x0075\n    ) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12,\n        };\n      }\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`,\n  );\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return (\n    (readHexDigit(body.charCodeAt(position)) << 12) |\n    (readHexDigit(body.charCodeAt(position + 1)) << 8) |\n    (readHexDigit(body.charCodeAt(position + 2)) << 4) |\n    readHexDigit(body.charCodeAt(position + 3))\n  );\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n    ? code - 0x0030\n    : code >= 0x0041 && code <= 0x0046 // A-F\n    ? code - 0x0037\n    : code >= 0x0061 && code <= 0x0066 // a-f\n    ? code - 0x0057\n    : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2,\n      };\n\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2,\n      };\n\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2,\n      };\n\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2,\n      };\n\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2,\n      };\n\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2,\n      };\n\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2,\n      };\n\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2,\n      };\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid character escape sequence: \"${body.slice(\n      position,\n      position + 2,\n    )}\".`,\n  );\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (\n      code === 0x0022 &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(\n        lexer,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BLOCK_STRING,\n        start,\n        position + 3, // Return a string of the lines joined with U+000A.\n        (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_4__.dedentBlockStringLines)(blockLines).join('\\n'),\n      );\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (\n      code === 0x005c &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022 &&\n      body.charCodeAt(position + 3) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameContinue)(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.NAME,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/lexer.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/language/location.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/language/location.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: function() { return /* binding */ getLocation; }\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(app-pages-browser)/./node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2xvY2F0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxpRUFBUzs7QUFFaEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2xvY2F0aW9uLm1qcz82MTk2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJy4uL2pzdXRpbHMvaW52YXJpYW50Lm1qcyc7XG5jb25zdCBMaW5lUmVnRXhwID0gL1xcclxcbnxbXFxuXFxyXS9nO1xuLyoqXG4gKiBSZXByZXNlbnRzIGEgbG9jYXRpb24gaW4gYSBTb3VyY2UuXG4gKi9cblxuLyoqXG4gKiBUYWtlcyBhIFNvdXJjZSBhbmQgYSBVVEYtOCBjaGFyYWN0ZXIgb2Zmc2V0LCBhbmQgcmV0dXJucyB0aGUgY29ycmVzcG9uZGluZ1xuICogbGluZSBhbmQgY29sdW1uIGFzIGEgU291cmNlTG9jYXRpb24uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2NhdGlvbihzb3VyY2UsIHBvc2l0aW9uKSB7XG4gIGxldCBsYXN0TGluZVN0YXJ0ID0gMDtcbiAgbGV0IGxpbmUgPSAxO1xuXG4gIGZvciAoY29uc3QgbWF0Y2ggb2Ygc291cmNlLmJvZHkubWF0Y2hBbGwoTGluZVJlZ0V4cCkpIHtcbiAgICB0eXBlb2YgbWF0Y2guaW5kZXggPT09ICdudW1iZXInIHx8IGludmFyaWFudChmYWxzZSk7XG5cbiAgICBpZiAobWF0Y2guaW5kZXggPj0gcG9zaXRpb24pIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGxhc3RMaW5lU3RhcnQgPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aDtcbiAgICBsaW5lICs9IDE7XG4gIH1cblxuICByZXR1cm4ge1xuICAgIGxpbmUsXG4gICAgY29sdW1uOiBwb3NpdGlvbiArIDEgLSBsYXN0TGluZVN0YXJ0LFxuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/location.mjs\n"));

/***/ })

}]);