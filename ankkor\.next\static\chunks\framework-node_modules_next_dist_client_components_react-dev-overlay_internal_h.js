"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useOnClickOutside\", ({\n    enumerable: true,\n    get: function() {\n        return useOnClickOutside;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nfunction useOnClickOutside(el, handler) {\n    _s();\n    _react.useEffect(()=>{\n        if (el == null || handler == null) {\n            return;\n        }\n        const listener = (e)=>{\n            // Do nothing if clicking ref's element or descendent elements\n            if (!el || el.contains(e.target)) {\n                return;\n            }\n            handler(e);\n        };\n        const root = el.getRootNode();\n        root.addEventListener(\"mousedown\", listener);\n        root.addEventListener(\"touchstart\", listener);\n        return function() {\n            root.removeEventListener(\"mousedown\", listener);\n            root.removeEventListener(\"touchstart\", listener);\n        };\n    }, [\n        handler,\n        el\n    ]);\n}\n_s(useOnClickOutside, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-on-click-outside.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaG9va3MvdXNlLW9uLWNsaWNrLW91dHNpZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3FEQUVnQkE7OztlQUFBQTs7Ozs2RUFGTztBQUVoQixTQUFTQSxrQkFDZEMsRUFBZSxFQUNmQyxPQUEyRDs7SUFFM0RDLE9BQU1DLFNBQVMsQ0FBQztRQUNkLElBQUlILE1BQU0sUUFBUUMsV0FBVyxNQUFNO1lBQ2pDO1FBQ0Y7UUFFQSxNQUFNRyxXQUFXLENBQUNDO1lBQ2hCLDhEQUE4RDtZQUM5RCxJQUFJLENBQUNMLE1BQU1BLEdBQUdNLFFBQVEsQ0FBQ0QsRUFBRUUsTUFBTSxHQUFjO2dCQUMzQztZQUNGO1lBRUFOLFFBQVFJO1FBQ1Y7UUFFQSxNQUFNRyxPQUFPUixHQUFHUyxXQUFXO1FBQzNCRCxLQUFLRSxnQkFBZ0IsQ0FBQyxhQUFhTjtRQUNuQ0ksS0FBS0UsZ0JBQWdCLENBQUMsY0FBY047UUFDcEMsT0FBTztZQUNMSSxLQUFLRyxtQkFBbUIsQ0FBQyxhQUFhUDtZQUN0Q0ksS0FBS0csbUJBQW1CLENBQUMsY0FBY1A7UUFDekM7SUFDRixHQUFHO1FBQUNIO1FBQVNEO0tBQUc7QUFDbEI7R0ExQmdCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2hvb2tzL3VzZS1vbi1jbGljay1vdXRzaWRlLnRzPzBhZGIiXSwibmFtZXMiOlsidXNlT25DbGlja091dHNpZGUiLCJlbCIsImhhbmRsZXIiLCJSZWFjdCIsInVzZUVmZmVjdCIsImxpc3RlbmVyIiwiZSIsImNvbnRhaW5zIiwidGFyZ2V0Iiwicm9vdCIsImdldFJvb3ROb2RlIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js ***!
  \************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CloseIcon\", ({\n    enumerable: true,\n    get: function() {\n        return CloseIcon;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst CloseIcon = ()=>{\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                d: \"M18 6L6 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                d: \"M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            })\n        ]\n    });\n};\n_c = CloseIcon;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CloseIcon.js.map\nvar _c;\n$RefreshReg$(_c, \"CloseIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaWNvbnMvQ2xvc2VJY29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBNkJTQTs7O2VBQUFBOzs7Ozs2RUE3QmM7QUFFdkIsTUFBTUEsWUFBWTtJQUNoQixPQUNFLFdBREYsR0FDRSxJQUFBQyxZQUFBQyxJQUFBLEVBQUNDLE9BQUFBO1FBQ0NDLE9BQU07UUFDTkMsUUFBTztRQUNQQyxTQUFRO1FBQ1JDLE1BQUs7UUFDTEMsT0FBTTs7MEJBRU4sSUFBQVAsWUFBQVEsR0FBQSxFQUFDQyxRQUFBQTtnQkFDQ0MsR0FBRTtnQkFDRkMsUUFBTztnQkFDUEMsYUFBWTtnQkFDWkMsZUFBYztnQkFDZEMsZ0JBQWU7OzBCQUVqQixJQUFBZCxZQUFBUSxHQUFBLEVBQUNDLFFBQUFBO2dCQUNDQyxHQUFFO2dCQUNGQyxRQUFPO2dCQUNQQyxhQUFZO2dCQUNaQyxlQUFjO2dCQUNkQyxnQkFBZTs7OztBQUl2QjtLQXpCTWYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9pY29ucy9DbG9zZUljb24udHN4P2UzODQiXSwibmFtZXMiOlsiQ2xvc2VJY29uIiwiX2pzeHJ1bnRpbWUiLCJqc3hzIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInhtbG5zIiwianN4IiwicGF0aCIsImQiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js ***!
  \***************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CollapseIcon\", ({\n    enumerable: true,\n    get: function() {\n        return CollapseIcon;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nfunction CollapseIcon(param) {\n    let { collapsed } = param === void 0 ? {} : param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        \"data-nextjs-call-stack-chevron-icon\": true,\n        \"data-collapsed\": collapsed,\n        fill: \"none\",\n        height: \"20\",\n        width: \"20\",\n        shapeRendering: \"geometricPrecision\",\n        stroke: \"currentColor\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        viewBox: \"0 0 24 24\",\n        ...typeof collapsed === \"boolean\" ? {\n            style: {\n                transform: collapsed ? undefined : \"rotate(90deg)\"\n            }\n        } : {},\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            d: \"M9 18l6-6-6-6\"\n        })\n    });\n}\n_c = CollapseIcon;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CollapseIcon.js.map\nvar _c;\n$RefreshReg$(_c, \"CollapseIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvaWNvbnMvQ29sbGFwc2VJY29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBQWdCQTs7O2VBQUFBOzs7O0FBQVQsU0FBU0EsYUFBYUMsS0FBQTtJQUFBLE1BQUVDLFNBQVMsRUFBMkIsR0FBdENELFVBQUEsU0FBeUMsQ0FBQyxJQUExQ0E7SUFDM0IsT0FDRSxXQURGLEdBQ0UsSUFBQUUsWUFBQUMsR0FBQSxFQUFDQyxPQUFBQTtRQUNDQyx1Q0FBbUM7UUFDbkNDLGtCQUFnQkw7UUFDaEJNLE1BQUs7UUFDTEMsUUFBTztRQUNQQyxPQUFNO1FBQ05DLGdCQUFlO1FBQ2ZDLFFBQU87UUFDUEMsZUFBYztRQUNkQyxnQkFBZTtRQUNmQyxhQUFZO1FBQ1pDLFNBQVE7UUFHUCxHQUFJLE9BQU9kLGNBQWMsWUFDdEI7WUFBRWUsT0FBTztnQkFBRUMsV0FBV2hCLFlBQVlpQixZQUFZO1lBQWdCO1FBQUUsSUFDaEUsQ0FBQyxDQUFDO2tCQUVOLGtCQUFBaEIsWUFBQUMsR0FBQSxFQUFDZ0IsUUFBQUE7WUFBS0MsR0FBRTs7O0FBR2Q7S0F2QmdCckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9pY29ucy9Db2xsYXBzZUljb24udHN4PzZlOTciXSwibmFtZXMiOlsiQ29sbGFwc2VJY29uIiwicGFyYW0iLCJjb2xsYXBzZWQiLCJfanN4cnVudGltZSIsImpzeCIsInN2ZyIsImRhdGEtbmV4dGpzLWNhbGwtc3RhY2stY2hldnJvbi1pY29uIiwiZGF0YS1jb2xsYXBzZWQiLCJmaWxsIiwiaGVpZ2h0Iiwid2lkdGgiLCJzaGFwZVJlbmRlcmluZyIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwidmlld0JveCIsInN0eWxlIiwidHJhbnNmb3JtIiwidW5kZWZpbmVkIiwicGF0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/CollapseIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/FrameworkIcon.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/FrameworkIcon.js ***!
  \****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"FrameworkIcon\", ({\n    enumerable: true,\n    get: function() {\n        return FrameworkIcon;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nfunction FrameworkIcon(param) {\n    let { framework } = param;\n    if (framework === \"react\") {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n            \"data-nextjs-call-stack-framework-icon\": \"react\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 410 369\",\n            fill: \"none\",\n            shapeRendering: \"geometricPrecision\",\n            stroke: \"currentColor\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"5\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M204.995 224.552C226.56 224.552 244.042 207.07 244.042 185.506C244.042 163.941 226.56 146.459 204.995 146.459C183.43 146.459 165.948 163.941 165.948 185.506C165.948 207.07 183.43 224.552 204.995 224.552Z\",\n                    fill: \"currentColor\"\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M409.99 184.505C409.99 153.707 381.437 126.667 335.996 108.925C343.342 60.6535 334.19 22.3878 307.492 6.98883C283.649 -6.77511 250.631 -0.0395641 214.512 25.9753C211.316 28.2692 208.143 30.7097 204.97 33.2477C201.822 30.7097 198.65 28.2692 195.477 25.9753C159.359 -0.0395641 126.34 -6.79951 102.497 6.98883C75.8237 22.3878 66.6721 60.6291 74.0422 108.852C28.5529 126.618 0 153.682 0 184.505C0 215.303 28.5528 242.342 73.9934 260.084C66.6477 308.356 75.7993 346.621 102.497 362.02C110.575 366.682 119.727 369 129.684 369C149.085 369 171.61 360.215 195.477 343.034C198.674 340.74 201.847 338.3 205.019 335.762C208.167 338.3 211.34 340.74 214.512 343.034C238.38 360.239 260.905 369 280.306 369C290.263 369 299.415 366.682 307.492 362.02C331.335 348.256 342 316.287 337.534 271.993C337.143 268.089 336.631 264.135 335.996 260.109C381.461 242.367 409.99 215.327 409.99 184.505ZM225.934 41.8136C246.238 27.1955 265.127 19.5814 280.306 19.5814C286.871 19.5814 292.728 20.9968 297.731 23.8765C315.204 33.9798 322.672 62.9475 317.327 102.433C299.756 97.0401 280.306 92.9158 259.392 90.2802C246.872 73.8074 233.597 58.9453 220.003 46.2551C221.98 44.7421 223.957 43.229 225.934 41.8136ZM112.259 23.8765C117.262 20.9968 123.119 19.5814 129.684 19.5814C144.863 19.5814 163.752 27.1711 184.056 41.8136C186.033 43.229 188.01 44.7176 189.986 46.2551C176.393 58.9453 163.142 73.783 150.622 90.2558C129.732 92.8914 110.258 97.0401 92.687 102.409C87.3424 62.9475 94.7857 33.9798 112.259 23.8765ZM19.5233 184.505C19.5233 164.322 40.9014 143.359 77.776 128.253C81.9003 146.141 88.0502 165.054 96.1768 184.456C88.0014 203.881 81.8515 222.819 77.7272 240.732C40.9014 225.626 19.5233 204.687 19.5233 184.505ZM184.056 327.196C154.966 348.134 128.805 354.675 112.259 345.133C94.7857 335.029 87.3181 306.062 92.6626 266.576C110.234 271.969 129.684 276.093 150.598 278.729C163.117 295.202 176.393 310.064 189.986 322.754C188.01 324.292 186.033 325.78 184.056 327.196ZM204.995 310.04C180.591 287.685 157.138 257.815 137.347 223.551C132.051 214.4 121.344 191.396 117 182.489C113.535 190.786 110.112 198.398 107.427 206.5C109.623 210.575 118.092 229.213 120.434 233.288C125.071 241.317 129.928 249.127 134.931 256.692C120.898 254.227 107.915 251.055 96.1035 247.321C102.815 217.011 116.213 182.064 137.347 145.458C142.545 136.453 153.838 116.346 159.5 108C150.568 109.147 143.395 108.767 135 110.5C132.56 114.453 122.777 131.645 120.434 135.721C115.749 143.823 111.454 151.925 107.427 159.978C102.546 146.581 98.8124 133.744 96.1524 121.64C125.755 112.293 162.727 106.411 204.995 106.411C215.562 106.411 237.63 106.197 247.49 106.905C242.048 99.7544 237.38 93.2819 231.694 86.888C227.082 86.7416 209.705 86.888 204.995 86.888C195.672 86.888 186.545 87.2053 177.589 87.7422C186.472 77.1752 195.672 67.5111 204.995 58.9697C229.375 81.3239 252.851 111.195 272.643 145.458C277.841 154.463 289.073 175.426 293.49 184.505C296.98 176.207 300.281 168.64 302.99 160.489C300.793 156.389 291.898 139.747 289.555 135.696C284.918 127.667 280.062 119.858 275.059 112.317C289.092 114.782 302.075 117.954 313.886 121.688C307.175 151.998 293.777 186.945 272.643 223.551C267.445 232.556 252.651 253.178 246.99 261.524C255.922 260.377 265.595 258.663 273.99 256.93C276.43 252.976 287.212 237.364 289.555 233.288C294.216 225.235 298.512 217.182 302.489 209.153C307.224 222.185 310.982 234.997 313.715 247.394C284.138 256.741 247.214 262.598 204.995 262.598C194.428 262.598 169.859 261.208 160 260.5C165.442 267.65 171.304 275.095 176.99 281.489C181.602 281.635 200.285 282.121 204.995 282.121C214.317 282.121 223.444 281.804 232.401 281.267C223.493 291.834 214.317 301.498 204.995 310.04ZM297.731 345.133C281.185 354.699 254.999 348.159 225.934 327.196C223.957 325.78 221.98 324.292 220.003 322.754C233.597 310.064 246.848 295.226 259.367 278.753C280.233 276.118 299.659 271.993 317.205 266.625C317.547 269.089 317.888 271.554 318.132 273.97C321.72 309.649 314.277 335.566 297.731 345.133ZM332.262 240.756C328.065 222.599 321.842 203.686 313.813 184.578C321.988 165.152 328.138 146.215 332.262 128.302C369.088 143.408 390.466 164.322 390.466 184.505C390.466 204.687 369.113 225.626 332.262 240.756Z\",\n                    fill: \"currentColor\"\n                })\n            ]\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        \"data-nextjs-call-stack-framework-icon\": \"next\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 180 180\",\n        fill: \"none\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"mask0_408_139\",\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"0\",\n                width: \"180\",\n                height: \"180\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"90\",\n                    fill: \"black\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                mask: \"url(#mask0_408_139)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n                        cx: \"90\",\n                        cy: \"90\",\n                        r: \"87\",\n                        fill: \"black\",\n                        stroke: \"white\",\n                        strokeWidth: \"6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M149.508 157.52L69.142 54H54V125.97H66.1136V69.3836L139.999 164.845C143.333 162.614 146.509 160.165 149.508 157.52Z\",\n                        fill: \"url(#paint0_linear_408_139)\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                        x: \"115\",\n                        y: \"54\",\n                        width: \"12\",\n                        height: \"72\",\n                        fill: \"url(#paint1_linear_408_139)\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"paint0_linear_408_139\",\n                        x1: \"109\",\n                        y1: \"116.5\",\n                        x2: \"144.5\",\n                        y2: \"160.5\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: \"white\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"white\",\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"paint1_linear_408_139\",\n                        x1: \"121\",\n                        y1: \"54\",\n                        x2: \"120.799\",\n                        y2: \"106.875\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: \"white\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"white\",\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = FrameworkIcon;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=FrameworkIcon.js.map\nvar _c;\n$RefreshReg$(_c, \"FrameworkIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/icons/FrameworkIcon.js\n"));

/***/ })

}]);