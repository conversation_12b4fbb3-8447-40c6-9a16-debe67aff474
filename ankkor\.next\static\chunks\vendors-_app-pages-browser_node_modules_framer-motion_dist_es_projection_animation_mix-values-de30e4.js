"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-_app-pages-browser_node_modules_framer-motion_dist_es_projection_animation_mix-values-de30e4"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixValues: function() { return /* binding */ mixValues; }\n/* harmony export */ });\n/* harmony import */ var _easing_circ_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../easing/circ.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/circ.mjs\");\n/* harmony import */ var _utils_progress_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n\n\n\n\n\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(0, \n        // TODO Reinstate this if only child\n        lead.opacity !== undefined ? lead.opacity : 1, easeCrossfadeIn(progress));\n        target.opacityExit = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(follow.opacity !== undefined ? follow.opacity : 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(follow.opacity !== undefined ? follow.opacity : 1, lead.opacity !== undefined ? lead.opacity : 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max((0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (_value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.percent.test(leadRadius) || _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = compress(0, 0.5, _easing_circ_mjs__WEBPACK_IMPORTED_MODULE_2__.circOut);\nconst easeCrossfadeOut = compress(0.5, 0.95, _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_3__.noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing((0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_4__.progress)(min, max, p));\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertBoundingBoxToBox: function() { return /* binding */ convertBoundingBoxToBox; },\n/* harmony export */   convertBoxToBoundingBox: function() { return /* binding */ convertBoxToBoundingBox; },\n/* harmony export */   transformBoxPoints: function() { return /* binding */ transformBoxPoints; }\n/* harmony export */ });\n/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/copy.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/copy.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyAxisInto: function() { return /* binding */ copyAxisInto; },\n/* harmony export */   copyBoxInto: function() { return /* binding */ copyBoxInto; }\n/* harmony export */ });\n/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS9jb3B5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9wcm9qZWN0aW9uL2dlb21ldHJ5L2NvcHkubWpzP2Y0NzIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXNldCBhbiBheGlzIHRvIHRoZSBwcm92aWRlZCBvcmlnaW4gYm94LlxuICpcbiAqIFRoaXMgaXMgYSBtdXRhdGl2ZSBvcGVyYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGNvcHlBeGlzSW50byhheGlzLCBvcmlnaW5BeGlzKSB7XG4gICAgYXhpcy5taW4gPSBvcmlnaW5BeGlzLm1pbjtcbiAgICBheGlzLm1heCA9IG9yaWdpbkF4aXMubWF4O1xufVxuLyoqXG4gKiBSZXNldCBhIGJveCB0byB0aGUgcHJvdmlkZWQgb3JpZ2luIGJveC5cbiAqXG4gKiBUaGlzIGlzIGEgbXV0YXRpdmUgb3BlcmF0aW9uLlxuICovXG5mdW5jdGlvbiBjb3B5Qm94SW50byhib3gsIG9yaWdpbkJveCkge1xuICAgIGNvcHlBeGlzSW50byhib3gueCwgb3JpZ2luQm94LngpO1xuICAgIGNvcHlBeGlzSW50byhib3gueSwgb3JpZ2luQm94LnkpO1xufVxuXG5leHBvcnQgeyBjb3B5QXhpc0ludG8sIGNvcHlCb3hJbnRvIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/copy.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyAxisDelta: function() { return /* binding */ applyAxisDelta; },\n/* harmony export */   applyBoxDelta: function() { return /* binding */ applyBoxDelta; },\n/* harmony export */   applyPointDelta: function() { return /* binding */ applyPointDelta; },\n/* harmony export */   applyTreeDeltas: function() { return /* binding */ applyTreeDeltas; },\n/* harmony export */   scalePoint: function() { return /* binding */ scalePoint; },\n/* harmony export */   transformAxis: function() { return /* binding */ transformAxis; },\n/* harmony export */   transformBox: function() { return /* binding */ transformBox; },\n/* harmony export */   translateAxis: function() { return /* binding */ translateAxis; }\n/* harmony export */ });\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/has-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs\");\n\n\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const instance = node.instance;\n        if (instance &&\n            instance.style &&\n            instance.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__.hasTransform)(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    treeScale.x = snapToDefault(treeScale.x);\n    treeScale.y = snapToDefault(treeScale.y);\n}\nfunction snapToDefault(scale) {\n    if (Number.isInteger(scale))\n        return scale;\n    return scale > 1.0000000000001 || scale < 0.999999999999 ? scale : 1;\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, transforms, [key, scaleKey, originKey]) {\n    const axisOrigin = transforms[originKey] !== undefined ? transforms[originKey] : 0.5;\n    const originPoint = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, transforms[key], transforms[scaleKey], originPoint, transforms.scale);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform, xKeys);\n    transformAxis(box.y, transform, yKeys);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcAxisDelta: function() { return /* binding */ calcAxisDelta; },\n/* harmony export */   calcBoxDelta: function() { return /* binding */ calcBoxDelta; },\n/* harmony export */   calcLength: function() { return /* binding */ calcLength; },\n/* harmony export */   calcRelativeAxis: function() { return /* binding */ calcRelativeAxis; },\n/* harmony export */   calcRelativeAxisPosition: function() { return /* binding */ calcRelativeAxisPosition; },\n/* harmony export */   calcRelativeBox: function() { return /* binding */ calcRelativeBox; },\n/* harmony export */   calcRelativePosition: function() { return /* binding */ calcRelativePosition; },\n/* harmony export */   isNear: function() { return /* binding */ isNear; }\n/* harmony export */ });\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n\n\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target = 0, maxDistance = 0.01) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    if (isNear(delta.scale, 1, 0.0001) || isNaN(delta.scale))\n        delta.scale = 1;\n    delta.translate =\n        (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(target.min, target.max, delta.origin) - delta.originPoint;\n    if (isNear(delta.translate) || isNaN(delta.translate))\n        delta.translate = 0;\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeAxisDelta: function() { return /* binding */ removeAxisDelta; },\n/* harmony export */   removeAxisTransforms: function() { return /* binding */ removeAxisTransforms; },\n/* harmony export */   removeBoxTransforms: function() { return /* binding */ removeBoxTransforms; },\n/* harmony export */   removePointDelta: function() { return /* binding */ removePointDelta; }\n/* harmony export */ });\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-apply.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\");\n\n\n\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (_value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_2__.mix)(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_2__.mix)(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/models.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAxis: function() { return /* binding */ createAxis; },\n/* harmony export */   createAxisDelta: function() { return /* binding */ createAxisDelta; },\n/* harmony export */   createBox: function() { return /* binding */ createBox; },\n/* harmony export */   createDelta: function() { return /* binding */ createDelta; }\n/* harmony export */ });\nconst createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS9tb2RlbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCw0QkFBNEIsZ0JBQWdCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRThEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS9tb2RlbHMubWpzPzQzYWUiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY3JlYXRlQXhpc0RlbHRhID0gKCkgPT4gKHtcbiAgICB0cmFuc2xhdGU6IDAsXG4gICAgc2NhbGU6IDEsXG4gICAgb3JpZ2luOiAwLFxuICAgIG9yaWdpblBvaW50OiAwLFxufSk7XG5jb25zdCBjcmVhdGVEZWx0YSA9ICgpID0+ICh7XG4gICAgeDogY3JlYXRlQXhpc0RlbHRhKCksXG4gICAgeTogY3JlYXRlQXhpc0RlbHRhKCksXG59KTtcbmNvbnN0IGNyZWF0ZUF4aXMgPSAoKSA9PiAoeyBtaW46IDAsIG1heDogMCB9KTtcbmNvbnN0IGNyZWF0ZUJveCA9ICgpID0+ICh7XG4gICAgeDogY3JlYXRlQXhpcygpLFxuICAgIHk6IGNyZWF0ZUF4aXMoKSxcbn0pO1xuXG5leHBvcnQgeyBjcmVhdGVBeGlzLCBjcmVhdGVBeGlzRGVsdGEsIGNyZWF0ZUJveCwgY3JlYXRlRGVsdGEgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/utils.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/geometry/utils.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aspectRatio: function() { return /* binding */ aspectRatio; },\n/* harmony export */   boxEquals: function() { return /* binding */ boxEquals; },\n/* harmony export */   boxEqualsRounded: function() { return /* binding */ boxEqualsRounded; },\n/* harmony export */   isDeltaZero: function() { return /* binding */ isDeltaZero; }\n/* harmony export */ });\n/* harmony import */ var _delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-calc.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction boxEquals(a, b) {\n    return (a.x.min === b.x.min &&\n        a.x.max === b.x.max &&\n        a.y.min === b.y.min &&\n        a.y.max === b.y.max);\n}\nfunction boxEqualsRounded(a, b) {\n    return (Math.round(a.x.min) === Math.round(b.x.min) &&\n        Math.round(a.x.max) === Math.round(b.x.max) &&\n        Math.round(a.y.min) === Math.round(b.y.min) &&\n        Math.round(a.y.max) === Math.round(b.y.max));\n}\nfunction aspectRatio(box) {\n    return (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.x) / (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.y);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS91dGlscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEM7O0FBRTlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywyREFBVSxVQUFVLDJEQUFVO0FBQ3pDOztBQUVpRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3Byb2plY3Rpb24vZ2VvbWV0cnkvdXRpbHMubWpzP2JkNGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FsY0xlbmd0aCB9IGZyb20gJy4vZGVsdGEtY2FsYy5tanMnO1xuXG5mdW5jdGlvbiBpc0F4aXNEZWx0YVplcm8oZGVsdGEpIHtcbiAgICByZXR1cm4gZGVsdGEudHJhbnNsYXRlID09PSAwICYmIGRlbHRhLnNjYWxlID09PSAxO1xufVxuZnVuY3Rpb24gaXNEZWx0YVplcm8oZGVsdGEpIHtcbiAgICByZXR1cm4gaXNBeGlzRGVsdGFaZXJvKGRlbHRhLngpICYmIGlzQXhpc0RlbHRhWmVybyhkZWx0YS55KTtcbn1cbmZ1bmN0aW9uIGJveEVxdWFscyhhLCBiKSB7XG4gICAgcmV0dXJuIChhLngubWluID09PSBiLngubWluICYmXG4gICAgICAgIGEueC5tYXggPT09IGIueC5tYXggJiZcbiAgICAgICAgYS55Lm1pbiA9PT0gYi55Lm1pbiAmJlxuICAgICAgICBhLnkubWF4ID09PSBiLnkubWF4KTtcbn1cbmZ1bmN0aW9uIGJveEVxdWFsc1JvdW5kZWQoYSwgYikge1xuICAgIHJldHVybiAoTWF0aC5yb3VuZChhLngubWluKSA9PT0gTWF0aC5yb3VuZChiLngubWluKSAmJlxuICAgICAgICBNYXRoLnJvdW5kKGEueC5tYXgpID09PSBNYXRoLnJvdW5kKGIueC5tYXgpICYmXG4gICAgICAgIE1hdGgucm91bmQoYS55Lm1pbikgPT09IE1hdGgucm91bmQoYi55Lm1pbikgJiZcbiAgICAgICAgTWF0aC5yb3VuZChhLnkubWF4KSA9PT0gTWF0aC5yb3VuZChiLnkubWF4KSk7XG59XG5mdW5jdGlvbiBhc3BlY3RSYXRpbyhib3gpIHtcbiAgICByZXR1cm4gY2FsY0xlbmd0aChib3gueCkgLyBjYWxjTGVuZ3RoKGJveC55KTtcbn1cblxuZXhwb3J0IHsgYXNwZWN0UmF0aW8sIGJveEVxdWFscywgYm94RXF1YWxzUm91bmRlZCwgaXNEZWx0YVplcm8gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentProjectionNode: function() { return /* binding */ DocumentProjectionNode; }\n/* harmony export */ });\n/* harmony import */ var _create_projection_node_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-projection-node.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../events/add-dom-event.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n\n\n\nconst DocumentProjectionNode = (0,_create_projection_node_mjs__WEBPACK_IMPORTED_MODULE_0__.createProjectionNode)({\n    attachResizeListener: (ref, notify) => (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__.addDomEvent)(ref, \"resize\", notify),\n    measureScroll: () => ({\n        x: document.documentElement.scrollLeft || document.body.scrollLeft,\n        y: document.documentElement.scrollTop || document.body.scrollTop,\n    }),\n    checkIsScrollRoot: () => true,\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9ub2RlL0RvY3VtZW50UHJvamVjdGlvbk5vZGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRTtBQUNQOztBQUU3RCwrQkFBK0IsaUZBQW9CO0FBQ25ELDJDQUEyQyxzRUFBVztBQUN0RDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxDQUFDOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3Byb2plY3Rpb24vbm9kZS9Eb2N1bWVudFByb2plY3Rpb25Ob2RlLm1qcz9jYjEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVByb2plY3Rpb25Ob2RlIH0gZnJvbSAnLi9jcmVhdGUtcHJvamVjdGlvbi1ub2RlLm1qcyc7XG5pbXBvcnQgeyBhZGREb21FdmVudCB9IGZyb20gJy4uLy4uL2V2ZW50cy9hZGQtZG9tLWV2ZW50Lm1qcyc7XG5cbmNvbnN0IERvY3VtZW50UHJvamVjdGlvbk5vZGUgPSBjcmVhdGVQcm9qZWN0aW9uTm9kZSh7XG4gICAgYXR0YWNoUmVzaXplTGlzdGVuZXI6IChyZWYsIG5vdGlmeSkgPT4gYWRkRG9tRXZlbnQocmVmLCBcInJlc2l6ZVwiLCBub3RpZnkpLFxuICAgIG1lYXN1cmVTY3JvbGw6ICgpID0+ICh7XG4gICAgICAgIHg6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxMZWZ0IHx8IGRvY3VtZW50LmJvZHkuc2Nyb2xsTGVmdCxcbiAgICAgICAgeTogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbFRvcCB8fCBkb2N1bWVudC5ib2R5LnNjcm9sbFRvcCxcbiAgICB9KSxcbiAgICBjaGVja0lzU2Nyb2xsUm9vdDogKCkgPT4gdHJ1ZSxcbn0pO1xuXG5leHBvcnQgeyBEb2N1bWVudFByb2plY3Rpb25Ob2RlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTMLProjectionNode: function() { return /* binding */ HTMLProjectionNode; },\n/* harmony export */   rootProjectionNode: function() { return /* binding */ rootProjectionNode; }\n/* harmony export */ });\n/* harmony import */ var _create_projection_node_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-projection-node.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs\");\n/* harmony import */ var _DocumentProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DocumentProjectionNode.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs\");\n\n\n\nconst rootProjectionNode = {\n    current: undefined,\n};\nconst HTMLProjectionNode = (0,_create_projection_node_mjs__WEBPACK_IMPORTED_MODULE_0__.createProjectionNode)({\n    measureScroll: (instance) => ({\n        x: instance.scrollLeft,\n        y: instance.scrollTop,\n    }),\n    defaultParent: () => {\n        if (!rootProjectionNode.current) {\n            const documentNode = new _DocumentProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_1__.DocumentProjectionNode({});\n            documentNode.mount(window);\n            documentNode.setOptions({ layoutScroll: true });\n            rootProjectionNode.current = documentNode;\n        }\n        return rootProjectionNode.current;\n    },\n    resetTransform: (instance, value) => {\n        instance.style.transform = value !== undefined ? value : \"none\";\n    },\n    checkIsScrollRoot: (instance) => Boolean(window.getComputedStyle(instance).position === \"fixed\"),\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/state.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/node/state.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   globalProjectionState: function() { return /* binding */ globalProjectionState; }\n/* harmony export */ });\n/**\n * This should only ever be modified on the client otherwise it'll\n * persist through server requests. If we need instanced states we\n * could lazy-init via root.\n */\nconst globalProjectionState = {\n    /**\n     * Global flag as to whether the tree has animated since the last time\n     * we resized the window\n     */\n    hasAnimatedSinceResize: true,\n    /**\n     * We set this to true once, on the first update. Any nodes added to the tree beyond that\n     * update will be given a `data-projection-id` attribute.\n     */\n    hasEverUpdated: false,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9ub2RlL3N0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9wcm9qZWN0aW9uL25vZGUvc3RhdGUubWpzPzk5NDEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGlzIHNob3VsZCBvbmx5IGV2ZXIgYmUgbW9kaWZpZWQgb24gdGhlIGNsaWVudCBvdGhlcndpc2UgaXQnbGxcbiAqIHBlcnNpc3QgdGhyb3VnaCBzZXJ2ZXIgcmVxdWVzdHMuIElmIHdlIG5lZWQgaW5zdGFuY2VkIHN0YXRlcyB3ZVxuICogY291bGQgbGF6eS1pbml0IHZpYSByb290LlxuICovXG5jb25zdCBnbG9iYWxQcm9qZWN0aW9uU3RhdGUgPSB7XG4gICAgLyoqXG4gICAgICogR2xvYmFsIGZsYWcgYXMgdG8gd2hldGhlciB0aGUgdHJlZSBoYXMgYW5pbWF0ZWQgc2luY2UgdGhlIGxhc3QgdGltZVxuICAgICAqIHdlIHJlc2l6ZWQgdGhlIHdpbmRvd1xuICAgICAqL1xuICAgIGhhc0FuaW1hdGVkU2luY2VSZXNpemU6IHRydWUsXG4gICAgLyoqXG4gICAgICogV2Ugc2V0IHRoaXMgdG8gdHJ1ZSBvbmNlLCBvbiB0aGUgZmlyc3QgdXBkYXRlLiBBbnkgbm9kZXMgYWRkZWQgdG8gdGhlIHRyZWUgYmV5b25kIHRoYXRcbiAgICAgKiB1cGRhdGUgd2lsbCBiZSBnaXZlbiBhIGBkYXRhLXByb2plY3Rpb24taWRgIGF0dHJpYnV0ZS5cbiAgICAgKi9cbiAgICBoYXNFdmVyVXBkYXRlZDogZmFsc2UsXG59O1xuXG5leHBvcnQgeyBnbG9iYWxQcm9qZWN0aW9uU3RhdGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/shared/stack.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/shared/stack.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeStack: function() { return /* binding */ NodeStack; }\n/* harmony export */ });\n/* harmony import */ var _utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/array.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\");\n\n\nclass NodeStack {\n    constructor() {\n        this.members = [];\n    }\n    add(node) {\n        (0,_utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.members, node);\n        node.scheduleRender();\n    }\n    remove(node) {\n        (0,_utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.members, node);\n        if (node === this.prevLead) {\n            this.prevLead = undefined;\n        }\n        if (node === this.lead) {\n            const prevLead = this.members[this.members.length - 1];\n            if (prevLead) {\n                this.promote(prevLead);\n            }\n        }\n    }\n    relegate(node) {\n        const indexOfNode = this.members.findIndex((member) => node === member);\n        if (indexOfNode === 0)\n            return false;\n        /**\n         * Find the next projection node that is present\n         */\n        let prevLead;\n        for (let i = indexOfNode; i >= 0; i--) {\n            const member = this.members[i];\n            if (member.isPresent !== false) {\n                prevLead = member;\n                break;\n            }\n        }\n        if (prevLead) {\n            this.promote(prevLead);\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    promote(node, preserveFollowOpacity) {\n        const prevLead = this.lead;\n        if (node === prevLead)\n            return;\n        this.prevLead = prevLead;\n        this.lead = node;\n        node.show();\n        if (prevLead) {\n            prevLead.instance && prevLead.scheduleRender();\n            node.scheduleRender();\n            node.resumeFrom = prevLead;\n            if (preserveFollowOpacity) {\n                node.resumeFrom.preserveOpacity = true;\n            }\n            if (prevLead.snapshot) {\n                node.snapshot = prevLead.snapshot;\n                node.snapshot.latestValues =\n                    prevLead.animationValues || prevLead.latestValues;\n            }\n            if (node.root && node.root.isUpdating) {\n                node.isLayoutDirty = true;\n            }\n            const { crossfade } = node.options;\n            if (crossfade === false) {\n                prevLead.hide();\n            }\n            /**\n             * TODO:\n             *   - Test border radius when previous node was deleted\n             *   - boxShadow mixing\n             *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n             *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n             *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n             * ---\n             *   - Crossfade opacity of root nodes\n             *   - layoutId changes after animation\n             *   - layoutId changes mid animation\n             */\n        }\n    }\n    exitAnimationComplete() {\n        this.members.forEach((node) => {\n            const { options, resumingFrom } = node;\n            options.onExitComplete && options.onExitComplete();\n            if (resumingFrom) {\n                resumingFrom.options.onExitComplete &&\n                    resumingFrom.options.onExitComplete();\n            }\n        });\n    }\n    scheduleRender() {\n        this.members.forEach((node) => {\n            node.instance && node.scheduleRender(false);\n        });\n    }\n    /**\n     * Clear any leads that have been removed this render to prevent them from being\n     * used in future animations and to prevent memory leaks\n     */\n    removeLeadSnapshot() {\n        if (this.lead && this.lead.snapshot) {\n            this.lead.snapshot = undefined;\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/shared/stack.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   correctBorderRadius: function() { return /* binding */ correctBorderRadius; },\n/* harmony export */   pixelsToPercent: function() { return /* binding */ pixelsToPercent; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n\n\nfunction pixelsToPercent(pixels, axis) {\n    if (axis.max === axis.min)\n        return 0;\n    return (pixels / (axis.max - axis.min)) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n    correct: (latest, node) => {\n        if (!node.target)\n            return latest;\n        /**\n         * If latest is a string, if it's a percentage we can return immediately as it's\n         * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n         */\n        if (typeof latest === \"string\") {\n            if (_value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.test(latest)) {\n                latest = parseFloat(latest);\n            }\n            else {\n                return latest;\n            }\n        }\n        /**\n         * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n         * pixel value as a percentage of each axis\n         */\n        const x = pixelsToPercent(latest, node.target.x);\n        const y = pixelsToPercent(latest, node.target.y);\n        return `${x}% ${y}%`;\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   correctBoxShadow: function() { return /* binding */ correctBoxShadow; }\n/* harmony export */ });\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n\n\n\nconst correctBoxShadow = {\n    correct: (latest, { treeScale, projectionDelta }) => {\n        const original = latest;\n        const shadow = _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_0__.complex.parse(latest);\n        // TODO: Doesn't support multiple shadows\n        if (shadow.length > 5)\n            return original;\n        const template = _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_0__.complex.createTransformer(latest);\n        const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n        // Calculate the overall context scale\n        const xScale = projectionDelta.x.scale * treeScale.x;\n        const yScale = projectionDelta.y.scale * treeScale.y;\n        shadow[0 + offset] /= xScale;\n        shadow[1 + offset] /= yScale;\n        /**\n         * Ideally we'd correct x and y scales individually, but because blur and\n         * spread apply to both we have to take a scale average and apply that instead.\n         * We could potentially improve the outcome of this by incorporating the ratio between\n         * the two scales.\n         */\n        const averageScale = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(xScale, yScale, 0.5);\n        // Blur\n        if (typeof shadow[2 + offset] === \"number\")\n            shadow[2 + offset] /= averageScale;\n        // Spread\n        if (typeof shadow[3 + offset] === \"number\")\n            shadow[3 + offset] /= averageScale;\n        return template(shadow);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addScaleCorrector: function() { return /* binding */ addScaleCorrector; },\n/* harmony export */   scaleCorrectors: function() { return /* binding */ scaleCorrectors; }\n/* harmony export */ });\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    Object.assign(scaleCorrectors, correctors);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9zdHlsZXMvc2NhbGUtY29ycmVjdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9wcm9qZWN0aW9uL3N0eWxlcy9zY2FsZS1jb3JyZWN0aW9uLm1qcz9lNTM2Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNjYWxlQ29ycmVjdG9ycyA9IHt9O1xuZnVuY3Rpb24gYWRkU2NhbGVDb3JyZWN0b3IoY29ycmVjdG9ycykge1xuICAgIE9iamVjdC5hc3NpZ24oc2NhbGVDb3JyZWN0b3JzLCBjb3JyZWN0b3JzKTtcbn1cblxuZXhwb3J0IHsgYWRkU2NhbGVDb3JyZWN0b3IsIHNjYWxlQ29ycmVjdG9ycyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/transform.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/styles/transform.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildProjectionTransform: function() { return /* binding */ buildProjectionTransform; }\n/* harmony export */ });\nfunction buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    if (xTranslate || yTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, 0) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { rotate, rotateX, rotateY } = latestTransform;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eachAxis: function() { return /* binding */ eachAxis; }\n/* harmony export */ });\nfunction eachAxis(callback) {\n    return [callback(\"x\"), callback(\"y\")];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi91dGlscy9lYWNoLWF4aXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi91dGlscy9lYWNoLWF4aXMubWpzPzIzNDAiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZWFjaEF4aXMoY2FsbGJhY2spIHtcbiAgICByZXR1cm4gW2NhbGxiYWNrKFwieFwiKSwgY2FsbGJhY2soXCJ5XCIpXTtcbn1cblxuZXhwb3J0IHsgZWFjaEF4aXMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   has2DTranslate: function() { return /* binding */ has2DTranslate; },\n/* harmony export */   hasScale: function() { return /* binding */ hasScale; },\n/* harmony export */   hasTransform: function() { return /* binding */ hasTransform; }\n/* harmony export */ });\nfunction isIdentityScale(scale) {\n    return scale === undefined || scale === 1;\n}\nfunction hasScale({ scale, scaleX, scaleY }) {\n    return (!isIdentityScale(scale) ||\n        !isIdentityScale(scaleX) ||\n        !isIdentityScale(scaleY));\n}\nfunction hasTransform(values) {\n    return (hasScale(values) ||\n        has2DTranslate(values) ||\n        values.z ||\n        values.rotate ||\n        values.rotateX ||\n        values.rotateY);\n}\nfunction has2DTranslate(values) {\n    return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n    return value && value !== \"0%\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi91dGlscy9oYXMtdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9wcm9qZWN0aW9uL3V0aWxzL2hhcy10cmFuc2Zvcm0ubWpzP2QzMmIiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNJZGVudGl0eVNjYWxlKHNjYWxlKSB7XG4gICAgcmV0dXJuIHNjYWxlID09PSB1bmRlZmluZWQgfHwgc2NhbGUgPT09IDE7XG59XG5mdW5jdGlvbiBoYXNTY2FsZSh7IHNjYWxlLCBzY2FsZVgsIHNjYWxlWSB9KSB7XG4gICAgcmV0dXJuICghaXNJZGVudGl0eVNjYWxlKHNjYWxlKSB8fFxuICAgICAgICAhaXNJZGVudGl0eVNjYWxlKHNjYWxlWCkgfHxcbiAgICAgICAgIWlzSWRlbnRpdHlTY2FsZShzY2FsZVkpKTtcbn1cbmZ1bmN0aW9uIGhhc1RyYW5zZm9ybSh2YWx1ZXMpIHtcbiAgICByZXR1cm4gKGhhc1NjYWxlKHZhbHVlcykgfHxcbiAgICAgICAgaGFzMkRUcmFuc2xhdGUodmFsdWVzKSB8fFxuICAgICAgICB2YWx1ZXMueiB8fFxuICAgICAgICB2YWx1ZXMucm90YXRlIHx8XG4gICAgICAgIHZhbHVlcy5yb3RhdGVYIHx8XG4gICAgICAgIHZhbHVlcy5yb3RhdGVZKTtcbn1cbmZ1bmN0aW9uIGhhczJEVHJhbnNsYXRlKHZhbHVlcykge1xuICAgIHJldHVybiBpczJEVHJhbnNsYXRlKHZhbHVlcy54KSB8fCBpczJEVHJhbnNsYXRlKHZhbHVlcy55KTtcbn1cbmZ1bmN0aW9uIGlzMkRUcmFuc2xhdGUodmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgJiYgdmFsdWUgIT09IFwiMCVcIjtcbn1cblxuZXhwb3J0IHsgaGFzMkRUcmFuc2xhdGUsIGhhc1NjYWxlLCBoYXNUcmFuc2Zvcm0gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/measure.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/utils/measure.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   measurePageBox: function() { return /* binding */ measurePageBox; },\n/* harmony export */   measureViewportBox: function() { return /* binding */ measureViewportBox; }\n/* harmony export */ });\n/* harmony import */ var _geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../geometry/conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\");\n/* harmony import */ var _geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../geometry/delta-apply.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\");\n\n\n\nfunction measureViewportBox(instance, transformPoint) {\n    return (0,_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_0__.convertBoundingBoxToBox)((0,_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_0__.transformBoxPoints)(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_1__.translateAxis)(viewportBox.x, scroll.offset.x);\n        (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_1__.translateAxis)(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi91dGlscy9tZWFzdXJlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlGO0FBQzdCOztBQUU1RDtBQUNBLFdBQVcsaUZBQXVCLENBQUMsNEVBQWtCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLFlBQVksU0FBUztBQUNyQjtBQUNBLFFBQVEsd0VBQWE7QUFDckIsUUFBUSx3RUFBYTtBQUNyQjtBQUNBO0FBQ0E7O0FBRThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi91dGlscy9tZWFzdXJlLm1qcz83MmJkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbnZlcnRCb3VuZGluZ0JveFRvQm94LCB0cmFuc2Zvcm1Cb3hQb2ludHMgfSBmcm9tICcuLi9nZW9tZXRyeS9jb252ZXJzaW9uLm1qcyc7XG5pbXBvcnQgeyB0cmFuc2xhdGVBeGlzIH0gZnJvbSAnLi4vZ2VvbWV0cnkvZGVsdGEtYXBwbHkubWpzJztcblxuZnVuY3Rpb24gbWVhc3VyZVZpZXdwb3J0Qm94KGluc3RhbmNlLCB0cmFuc2Zvcm1Qb2ludCkge1xuICAgIHJldHVybiBjb252ZXJ0Qm91bmRpbmdCb3hUb0JveCh0cmFuc2Zvcm1Cb3hQb2ludHMoaW5zdGFuY2UuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCksIHRyYW5zZm9ybVBvaW50KSk7XG59XG5mdW5jdGlvbiBtZWFzdXJlUGFnZUJveChlbGVtZW50LCByb290UHJvamVjdGlvbk5vZGUsIHRyYW5zZm9ybVBhZ2VQb2ludCkge1xuICAgIGNvbnN0IHZpZXdwb3J0Qm94ID0gbWVhc3VyZVZpZXdwb3J0Qm94KGVsZW1lbnQsIHRyYW5zZm9ybVBhZ2VQb2ludCk7XG4gICAgY29uc3QgeyBzY3JvbGwgfSA9IHJvb3RQcm9qZWN0aW9uTm9kZTtcbiAgICBpZiAoc2Nyb2xsKSB7XG4gICAgICAgIHRyYW5zbGF0ZUF4aXModmlld3BvcnRCb3gueCwgc2Nyb2xsLm9mZnNldC54KTtcbiAgICAgICAgdHJhbnNsYXRlQXhpcyh2aWV3cG9ydEJveC55LCBzY3JvbGwub2Zmc2V0LnkpO1xuICAgIH1cbiAgICByZXR1cm4gdmlld3BvcnRCb3g7XG59XG5cbmV4cG9ydCB7IG1lYXN1cmVQYWdlQm94LCBtZWFzdXJlVmlld3BvcnRCb3ggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/measure.mjs\n"));

/***/ })

}]);