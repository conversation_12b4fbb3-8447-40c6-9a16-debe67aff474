"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_render_d"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOMVisualElement: function() { return /* binding */ DOMVisualElement; }\n/* harmony export */ });\n/* harmony import */ var _utils_setters_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/setters.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/setters.mjs\");\n/* harmony import */ var _utils_parse_dom_variant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/parse-dom-variant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/parse-dom-variant.mjs\");\n/* harmony import */ var _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../VisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/VisualElement.mjs\");\n\n\n\n\nclass DOMVisualElement extends _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.VisualElement {\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style ? props.style[key] : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    makeTargetAnimatableFromInstance({ transition, transitionEnd, ...target }, { transformValues }, isMounted) {\n        let origin = (0,_utils_setters_mjs__WEBPACK_IMPORTED_MODULE_1__.getOrigin)(target, transition || {}, this);\n        /**\n         * If Framer has provided a function to convert `Color` etc value types, convert them\n         */\n        if (transformValues) {\n            if (transitionEnd)\n                transitionEnd = transformValues(transitionEnd);\n            if (target)\n                target = transformValues(target);\n            if (origin)\n                origin = transformValues(origin);\n        }\n        if (isMounted) {\n            (0,_utils_setters_mjs__WEBPACK_IMPORTED_MODULE_1__.checkTargetForNewValues)(this, target, origin);\n            const parsed = (0,_utils_parse_dom_variant_mjs__WEBPACK_IMPORTED_MODULE_2__.parseDomVariant)(this, target, origin, transitionEnd);\n            transitionEnd = parsed.transitionEnd;\n            target = parsed.target;\n        }\n        return {\n            transition,\n            transitionEnd,\n            ...target,\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDomVisualElement: function() { return /* binding */ createDomVisualElement; }\n/* harmony export */ });\n/* harmony import */ var _html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../html/HTMLVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs\");\n/* harmony import */ var _svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../svg/SVGVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs\");\n/* harmony import */ var _utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is-svg-component.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n\n\n\n\nconst createDomVisualElement = (Component, options) => {\n    return (0,_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__.isSVGComponent)(Component)\n        ? new _svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_1__.SVGVisualElement(options, { enableHardwareAcceleration: false })\n        : new _html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__.HTMLVisualElement(options, { enableHardwareAcceleration: true });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9jcmVhdGUtdmlzdWFsLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0U7QUFDSDtBQUNEOztBQUU5RDtBQUNBLFdBQVcsMkVBQWM7QUFDekIsY0FBYyx1RUFBZ0IsWUFBWSxtQ0FBbUM7QUFDN0UsY0FBYywwRUFBaUIsWUFBWSxrQ0FBa0M7QUFDN0U7O0FBRWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9jcmVhdGUtdmlzdWFsLWVsZW1lbnQubWpzPzJkNWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSFRNTFZpc3VhbEVsZW1lbnQgfSBmcm9tICcuLi9odG1sL0hUTUxWaXN1YWxFbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyBTVkdWaXN1YWxFbGVtZW50IH0gZnJvbSAnLi4vc3ZnL1NWR1Zpc3VhbEVsZW1lbnQubWpzJztcbmltcG9ydCB7IGlzU1ZHQ29tcG9uZW50IH0gZnJvbSAnLi91dGlscy9pcy1zdmctY29tcG9uZW50Lm1qcyc7XG5cbmNvbnN0IGNyZWF0ZURvbVZpc3VhbEVsZW1lbnQgPSAoQ29tcG9uZW50LCBvcHRpb25zKSA9PiB7XG4gICAgcmV0dXJuIGlzU1ZHQ29tcG9uZW50KENvbXBvbmVudClcbiAgICAgICAgPyBuZXcgU1ZHVmlzdWFsRWxlbWVudChvcHRpb25zLCB7IGVuYWJsZUhhcmR3YXJlQWNjZWxlcmF0aW9uOiBmYWxzZSB9KVxuICAgICAgICA6IG5ldyBIVE1MVmlzdWFsRWxlbWVudChvcHRpb25zLCB7IGVuYWJsZUhhcmR3YXJlQWNjZWxlcmF0aW9uOiB0cnVlIH0pO1xufTtcblxuZXhwb3J0IHsgY3JlYXRlRG9tVmlzdWFsRWxlbWVudCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion-proxy.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/motion-proxy.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMotionProxy: function() { return /* binding */ createMotionProxy; }\n/* harmony export */ });\n/* harmony import */ var _motion_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\");\n\n\n/**\n * Convert any React component into a `motion` component. The provided component\n * **must** use `React.forwardRef` to the underlying DOM component you want to animate.\n *\n * ```jsx\n * const Component = React.forwardRef((props, ref) => {\n *   return <div ref={ref} />\n * })\n *\n * const MotionComponent = motion(Component)\n * ```\n *\n * @public\n */\nfunction createMotionProxy(createConfig) {\n    function custom(Component, customMotionComponentConfig = {}) {\n        return (0,_motion_index_mjs__WEBPACK_IMPORTED_MODULE_0__.createMotionComponent)(createConfig(Component, customMotionComponentConfig));\n    }\n    if (typeof Proxy === \"undefined\") {\n        return custom;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    return new Proxy(custom, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, custom(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion-proxy.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/motion.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDomMotionComponent: function() { return /* binding */ createDomMotionComponent; },\n/* harmony export */   motion: function() { return /* binding */ motion; }\n/* harmony export */ });\n/* harmony import */ var _motion_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../motion/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\");\n/* harmony import */ var _motion_proxy_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./motion-proxy.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion-proxy.mjs\");\n/* harmony import */ var _utils_create_config_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/create-config.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs\");\n/* harmony import */ var _motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/gestures.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/gestures.mjs\");\n/* harmony import */ var _motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/features/animations.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/animations.mjs\");\n/* harmony import */ var _motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../motion/features/drag.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/drag.mjs\");\n/* harmony import */ var _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\");\n/* harmony import */ var _motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../motion/features/layout.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/layout.mjs\");\n\n\n\n\n\n\n\n\n\nconst preloadedFeatures = {\n    ..._motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_0__.animations,\n    ..._motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_1__.gestureAnimations,\n    ..._motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_2__.drag,\n    ..._motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_3__.layout,\n};\n/**\n * HTML & SVG components, optimised for use with gestures and animation. These can be used as\n * drop-in replacements for any HTML & SVG component, all CSS & SVG properties are supported.\n *\n * @public\n */\nconst motion = /*@__PURE__*/ (0,_motion_proxy_mjs__WEBPACK_IMPORTED_MODULE_4__.createMotionProxy)((Component, config) => (0,_utils_create_config_mjs__WEBPACK_IMPORTED_MODULE_5__.createDomMotionConfig)(Component, config, preloadedFeatures, _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.createDomVisualElement));\n/**\n * Create a DOM `motion` component with the provided string. This is primarily intended\n * as a full alternative to `motion` for consumers who have to support environments that don't\n * support `Proxy`.\n *\n * ```javascript\n * import { createDomMotionComponent } from \"framer-motion\"\n *\n * const motion = {\n *   div: createDomMotionComponent('div')\n * }\n * ```\n *\n * @public\n */\nfunction createDomMotionComponent(key) {\n    return (0,_motion_index_mjs__WEBPACK_IMPORTED_MODULE_7__.createMotionComponent)((0,_utils_create_config_mjs__WEBPACK_IMPORTED_MODULE_5__.createDomMotionConfig)(key, { forwardMotionProps: false }, preloadedFeatures, _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.createDomVisualElement));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/use-render.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/use-render.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUseRender: function() { return /* binding */ createUseRender; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../html/use-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/use-props.mjs\");\n/* harmony import */ var _utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/filter-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\");\n/* harmony import */ var _utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is-svg-component.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n/* harmony import */ var _svg_use_props_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../svg/use-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/use-props.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\n\n\n\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = (0,_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__.isSVGComponent)(Component)\n            ? _svg_use_props_mjs__WEBPACK_IMPORTED_MODULE_2__.useSVGProps\n            : _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_3__.useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = (0,_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_4__.filterProps)(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = {\n            ...filteredProps,\n            ...visualProps,\n            ref,\n        };\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_5__.isMotionValue)(children) ? children.get() : children), [children]);\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/use-render.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelToDash: function() { return /* binding */ camelToDash; }\n/* harmony export */ });\n/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9jYW1lbC10by1kYXNoLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9jYW1lbC10by1kYXNoLm1qcz8wYTA3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29udmVydCBjYW1lbENhc2UgdG8gZGFzaC1jYXNlIHByb3BlcnRpZXMuXG4gKi9cbmNvbnN0IGNhbWVsVG9EYXNoID0gKHN0cikgPT4gc3RyLnJlcGxhY2UoLyhbYS16XSkoW0EtWl0pL2csIFwiJDEtJDJcIikudG9Mb3dlckNhc2UoKTtcblxuZXhwb3J0IHsgY2FtZWxUb0Rhc2ggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDomMotionConfig: function() { return /* binding */ createDomMotionConfig; }\n/* harmony export */ });\n/* harmony import */ var _is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-svg-component.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n/* harmony import */ var _use_render_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../use-render.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/use-render.mjs\");\n/* harmony import */ var _svg_config_motion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../svg/config-motion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/config-motion.mjs\");\n/* harmony import */ var _html_config_motion_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../html/config-motion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/config-motion.mjs\");\n\n\n\n\n\nfunction createDomMotionConfig(Component, { forwardMotionProps = false }, preloadedFeatures, createVisualElement) {\n    const baseConfig = (0,_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__.isSVGComponent)(Component)\n        ? _svg_config_motion_mjs__WEBPACK_IMPORTED_MODULE_1__.svgMotionConfig\n        : _html_config_motion_mjs__WEBPACK_IMPORTED_MODULE_2__.htmlMotionConfig;\n    return {\n        ...baseConfig,\n        preloadedFeatures,\n        useRender: (0,_use_render_mjs__WEBPACK_IMPORTED_MODULE_3__.createUseRender)(forwardMotionProps),\n        createVisualElement,\n        Component,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9jcmVhdGUtY29uZmlnLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNKO0FBQ1U7QUFDRTs7QUFFaEUsNENBQTRDLDRCQUE0QjtBQUN4RSx1QkFBdUIscUVBQWM7QUFDckMsVUFBVSxtRUFBZTtBQUN6QixVQUFVLHFFQUFnQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsZ0VBQWU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7O0FBRWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9jcmVhdGUtY29uZmlnLm1qcz83NzhhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzU1ZHQ29tcG9uZW50IH0gZnJvbSAnLi9pcy1zdmctY29tcG9uZW50Lm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVVc2VSZW5kZXIgfSBmcm9tICcuLi91c2UtcmVuZGVyLm1qcyc7XG5pbXBvcnQgeyBzdmdNb3Rpb25Db25maWcgfSBmcm9tICcuLi8uLi9zdmcvY29uZmlnLW1vdGlvbi5tanMnO1xuaW1wb3J0IHsgaHRtbE1vdGlvbkNvbmZpZyB9IGZyb20gJy4uLy4uL2h0bWwvY29uZmlnLW1vdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBjcmVhdGVEb21Nb3Rpb25Db25maWcoQ29tcG9uZW50LCB7IGZvcndhcmRNb3Rpb25Qcm9wcyA9IGZhbHNlIH0sIHByZWxvYWRlZEZlYXR1cmVzLCBjcmVhdGVWaXN1YWxFbGVtZW50KSB7XG4gICAgY29uc3QgYmFzZUNvbmZpZyA9IGlzU1ZHQ29tcG9uZW50KENvbXBvbmVudClcbiAgICAgICAgPyBzdmdNb3Rpb25Db25maWdcbiAgICAgICAgOiBodG1sTW90aW9uQ29uZmlnO1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmJhc2VDb25maWcsXG4gICAgICAgIHByZWxvYWRlZEZlYXR1cmVzLFxuICAgICAgICB1c2VSZW5kZXI6IGNyZWF0ZVVzZVJlbmRlcihmb3J3YXJkTW90aW9uUHJvcHMpLFxuICAgICAgICBjcmVhdGVWaXN1YWxFbGVtZW50LFxuICAgICAgICBDb21wb25lbnQsXG4gICAgfTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlRG9tTW90aW9uQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/css-variables-conversion.mjs":
/*!******************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/css-variables-conversion.mjs ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseCSSVariable: function() { return /* binding */ parseCSSVariable; },\n/* harmony export */   resolveCSSVariables: function() { return /* binding */ resolveCSSVariables; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _utils_is_numerical_string_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/is-numerical-string.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs\");\n/* harmony import */ var _is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-css-variable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\");\n\n\n\n\n/**\n * Parse Framer's special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token, fallback] = match;\n    return [token, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`);\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return (0,_utils_is_numerical_string_mjs__WEBPACK_IMPORTED_MODULE_1__.isNumericalString)(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    else if ((0,_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__.isCSSVariableToken)(fallback)) {\n        // The fallback might itself be a CSS variable, in which case we attempt to resolve it too.\n        return getVariableValue(fallback, element, depth + 1);\n    }\n    else {\n        return fallback;\n    }\n}\n/**\n * Resolve CSS variables from\n *\n * @internal\n */\nfunction resolveCSSVariables(visualElement, { ...target }, transitionEnd) {\n    const element = visualElement.current;\n    if (!(element instanceof Element))\n        return { target, transitionEnd };\n    // If `transitionEnd` isn't `undefined`, clone it. We could clone `target` and `transitionEnd`\n    // only if they change but I think this reads clearer and this isn't a performance-critical path.\n    if (transitionEnd) {\n        transitionEnd = { ...transitionEnd };\n    }\n    // Go through existing `MotionValue`s and ensure any existing CSS variables are resolved\n    visualElement.values.forEach((value) => {\n        const current = value.get();\n        if (!(0,_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__.isCSSVariableToken)(current))\n            return;\n        const resolved = getVariableValue(current, element);\n        if (resolved)\n            value.set(resolved);\n    });\n    // Cycle through every target property and resolve CSS variables. Currently\n    // we only read single-var properties like `var(--foo)`, not `calc(var(--foo) + 20px)`\n    for (const key in target) {\n        const current = target[key];\n        if (!(0,_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__.isCSSVariableToken)(current))\n            continue;\n        const resolved = getVariableValue(current, element);\n        if (!resolved)\n            continue;\n        // Clone target if it hasn't already been\n        target[key] = resolved;\n        if (!transitionEnd)\n            transitionEnd = {};\n        // If the user hasn't already set this key on `transitionEnd`, set it to the unresolved\n        // CSS variable. This will ensure that after the animation the component will reflect\n        // changes in the value of the CSS variable.\n        if (transitionEnd[key] === undefined) {\n            transitionEnd[key] = current;\n        }\n    }\n    return { target, transitionEnd };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/css-variables-conversion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterProps: function() { return /* binding */ filterProps; },\n/* harmony export */   loadExternalIsValidProp: function() { return /* binding */ loadExternalIsValidProp; }\n/* harmony export */ });\n/* harmony import */ var _motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../motion/utils/valid-prop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\");\n\n\nlet shouldForward = (key) => !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (!isValidProp)\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch (_a) {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && (0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            (!isDom && !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] && key.startsWith(\"onDrag\"))) {\n            filteredProps[key] = props[key];\n        }\n    }\n    return filteredProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cssVariableRegex: function() { return /* binding */ cssVariableRegex; },\n/* harmony export */   isCSSVariableName: function() { return /* binding */ isCSSVariableName; },\n/* harmony export */   isCSSVariableToken: function() { return /* binding */ isCSSVariableToken; }\n/* harmony export */ });\nconst checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = checkStringStartsWith(\"--\");\nconst isCSSVariableToken = checkStringStartsWith(\"var(--\");\nconst cssVariableRegex = /var\\s*\\(\\s*--[\\w-]+(\\s*,\\s*(?:(?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)+)?\\s*\\)/g;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1jc3MtdmFyaWFibGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVtRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vdXRpbHMvaXMtY3NzLXZhcmlhYmxlLm1qcz9lM2IwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNoZWNrU3RyaW5nU3RhcnRzV2l0aCA9ICh0b2tlbikgPT4gKGtleSkgPT4gdHlwZW9mIGtleSA9PT0gXCJzdHJpbmdcIiAmJiBrZXkuc3RhcnRzV2l0aCh0b2tlbik7XG5jb25zdCBpc0NTU1ZhcmlhYmxlTmFtZSA9IGNoZWNrU3RyaW5nU3RhcnRzV2l0aChcIi0tXCIpO1xuY29uc3QgaXNDU1NWYXJpYWJsZVRva2VuID0gY2hlY2tTdHJpbmdTdGFydHNXaXRoKFwidmFyKC0tXCIpO1xuY29uc3QgY3NzVmFyaWFibGVSZWdleCA9IC92YXJcXHMqXFwoXFxzKi0tW1xcdy1dKyhcXHMqLFxccyooPzooPzpbXikoXXxcXCgoPzpbXikoXSt8XFwoW14pKF0qXFwpKSpcXCkpKikrKT9cXHMqXFwpL2c7XG5cbmV4cG9ydCB7IGNzc1ZhcmlhYmxlUmVnZXgsIGlzQ1NTVmFyaWFibGVOYW1lLCBpc0NTU1ZhcmlhYmxlVG9rZW4gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSVGComponent: function() { return /* binding */ isSVGComponent; }\n/* harmony export */ });\n/* harmony import */ var _svg_lowercase_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../svg/lowercase-elements.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs\");\n\n\nfunction isSVGComponent(Component) {\n    if (\n    /**\n     * If it's not a string, it's a custom React component. Currently we only support\n     * HTML custom React components.\n     */\n    typeof Component !== \"string\" ||\n        /**\n         * If it contains a dash, the element is a custom HTML webcomponent.\n         */\n        Component.includes(\"-\")) {\n        return false;\n    }\n    else if (\n    /**\n     * If it's in our list of lowercase SVG tags, it's an SVG component\n     */\n    _svg_lowercase_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.lowercaseSVGElements.indexOf(Component) > -1 ||\n        /**\n         * If it contains a capital letter, it's an SVG component\n         */\n        /[A-Z]/.test(Component)) {\n        return true;\n    }\n    return false;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1zdmctY29tcG9uZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3RTs7QUFFeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksNkVBQW9CO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1zdmctY29tcG9uZW50Lm1qcz9hNzViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGxvd2VyY2FzZVNWR0VsZW1lbnRzIH0gZnJvbSAnLi4vLi4vc3ZnL2xvd2VyY2FzZS1lbGVtZW50cy5tanMnO1xuXG5mdW5jdGlvbiBpc1NWR0NvbXBvbmVudChDb21wb25lbnQpIHtcbiAgICBpZiAoXG4gICAgLyoqXG4gICAgICogSWYgaXQncyBub3QgYSBzdHJpbmcsIGl0J3MgYSBjdXN0b20gUmVhY3QgY29tcG9uZW50LiBDdXJyZW50bHkgd2Ugb25seSBzdXBwb3J0XG4gICAgICogSFRNTCBjdXN0b20gUmVhY3QgY29tcG9uZW50cy5cbiAgICAgKi9cbiAgICB0eXBlb2YgQ29tcG9uZW50ICE9PSBcInN0cmluZ1wiIHx8XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBJZiBpdCBjb250YWlucyBhIGRhc2gsIHRoZSBlbGVtZW50IGlzIGEgY3VzdG9tIEhUTUwgd2ViY29tcG9uZW50LlxuICAgICAgICAgKi9cbiAgICAgICAgQ29tcG9uZW50LmluY2x1ZGVzKFwiLVwiKSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKFxuICAgIC8qKlxuICAgICAqIElmIGl0J3MgaW4gb3VyIGxpc3Qgb2YgbG93ZXJjYXNlIFNWRyB0YWdzLCBpdCdzIGFuIFNWRyBjb21wb25lbnRcbiAgICAgKi9cbiAgICBsb3dlcmNhc2VTVkdFbGVtZW50cy5pbmRleE9mKENvbXBvbmVudCkgPiAtMSB8fFxuICAgICAgICAvKipcbiAgICAgICAgICogSWYgaXQgY29udGFpbnMgYSBjYXBpdGFsIGxldHRlciwgaXQncyBhbiBTVkcgY29tcG9uZW50XG4gICAgICAgICAqL1xuICAgICAgICAvW0EtWl0vLnRlc3QoQ29tcG9uZW50KSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuXG5leHBvcnQgeyBpc1NWR0NvbXBvbmVudCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-element.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-element.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSVGElement: function() { return /* binding */ isSVGElement; }\n/* harmony export */ });\nfunction isSVGElement(element) {\n    return element instanceof SVGElement && element.tagName !== \"svg\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1zdmctZWxlbWVudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3V0aWxzL2lzLXN2Zy1lbGVtZW50Lm1qcz9kOGE1Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzU1ZHRWxlbWVudChlbGVtZW50KSB7XG4gICAgcmV0dXJuIGVsZW1lbnQgaW5zdGFuY2VvZiBTVkdFbGVtZW50ICYmIGVsZW1lbnQudGFnTmFtZSAhPT0gXCJzdmdcIjtcbn1cblxuZXhwb3J0IHsgaXNTVkdFbGVtZW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/parse-dom-variant.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/parse-dom-variant.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDomVariant: function() { return /* binding */ parseDomVariant; }\n/* harmony export */ });\n/* harmony import */ var _css_variables_conversion_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./css-variables-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/css-variables-conversion.mjs\");\n/* harmony import */ var _unit_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./unit-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs\");\n\n\n\n/**\n * Parse a DOM variant to make it animatable. This involves resolving CSS variables\n * and ensuring animations like \"20%\" => \"calc(50vw)\" are performed in pixels.\n */\nconst parseDomVariant = (visualElement, target, origin, transitionEnd) => {\n    const resolved = (0,_css_variables_conversion_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveCSSVariables)(visualElement, target, transitionEnd);\n    target = resolved.target;\n    transitionEnd = resolved.transitionEnd;\n    return (0,_unit_conversion_mjs__WEBPACK_IMPORTED_MODULE_1__.unitConversion)(visualElement, target, origin, transitionEnd);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9wYXJzZS1kb20tdmFyaWFudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFFO0FBQ2Q7O0FBRXZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsa0ZBQW1CO0FBQ3hDO0FBQ0E7QUFDQSxXQUFXLG9FQUFjO0FBQ3pCOztBQUUyQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vdXRpbHMvcGFyc2UtZG9tLXZhcmlhbnQubWpzPzgxYWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZUNTU1ZhcmlhYmxlcyB9IGZyb20gJy4vY3NzLXZhcmlhYmxlcy1jb252ZXJzaW9uLm1qcyc7XG5pbXBvcnQgeyB1bml0Q29udmVyc2lvbiB9IGZyb20gJy4vdW5pdC1jb252ZXJzaW9uLm1qcyc7XG5cbi8qKlxuICogUGFyc2UgYSBET00gdmFyaWFudCB0byBtYWtlIGl0IGFuaW1hdGFibGUuIFRoaXMgaW52b2x2ZXMgcmVzb2x2aW5nIENTUyB2YXJpYWJsZXNcbiAqIGFuZCBlbnN1cmluZyBhbmltYXRpb25zIGxpa2UgXCIyMCVcIiA9PiBcImNhbGMoNTB2dylcIiBhcmUgcGVyZm9ybWVkIGluIHBpeGVscy5cbiAqL1xuY29uc3QgcGFyc2VEb21WYXJpYW50ID0gKHZpc3VhbEVsZW1lbnQsIHRhcmdldCwgb3JpZ2luLCB0cmFuc2l0aW9uRW5kKSA9PiB7XG4gICAgY29uc3QgcmVzb2x2ZWQgPSByZXNvbHZlQ1NTVmFyaWFibGVzKHZpc3VhbEVsZW1lbnQsIHRhcmdldCwgdHJhbnNpdGlvbkVuZCk7XG4gICAgdGFyZ2V0ID0gcmVzb2x2ZWQudGFyZ2V0O1xuICAgIHRyYW5zaXRpb25FbmQgPSByZXNvbHZlZC50cmFuc2l0aW9uRW5kO1xuICAgIHJldHVybiB1bml0Q29udmVyc2lvbih2aXN1YWxFbGVtZW50LCB0YXJnZXQsIG9yaWdpbiwgdHJhbnNpdGlvbkVuZCk7XG59O1xuXG5leHBvcnQgeyBwYXJzZURvbVZhcmlhbnQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/parse-dom-variant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   positionalValues: function() { return /* binding */ positionalValues; },\n/* harmony export */   unitConversion: function() { return /* binding */ unitConversion; }\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../animation/utils/is-keyframes-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../value-types/dimensions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n\n\n\n\n\n\n\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    \"x\",\n    \"y\",\n    \"translateX\",\n    \"translateY\",\n]);\nconst isPositionalKey = (key) => positionalKeys.has(key);\nconst hasPositionalKey = (target) => {\n    return Object.keys(target).some(isPositionalKey);\n};\nconst isNumOrPxType = (v) => v === _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__.number || v === _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.px;\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, { transform }) => {\n    if (transform === \"none\" || !transform)\n        return 0;\n    const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (matrix3d) {\n        return getPosFromMatrix(matrix3d[1], pos3);\n    }\n    else {\n        const matrix = transform.match(/^matrix\\((.+)\\)$/);\n        if (matrix) {\n            return getPosFromMatrix(matrix[1], pos2);\n        }\n        else {\n            return 0;\n        }\n    }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_2__.transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    // Apply changes to element before measurement\n    if (removedTransforms.length)\n        visualElement.render();\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: getTranslateFromMatrix(4, 13),\n    y: getTranslateFromMatrix(5, 14),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n    const originBbox = visualElement.measureViewportBox();\n    const element = visualElement.current;\n    const elementComputedStyle = getComputedStyle(element);\n    const { display } = elementComputedStyle;\n    const origin = {};\n    // If the element is currently set to display: \"none\", make it visible before\n    // measuring the target bounding box\n    if (display === \"none\") {\n        visualElement.setStaticValue(\"display\", target.display || \"block\");\n    }\n    /**\n     * Record origins before we render and update styles\n     */\n    changedKeys.forEach((key) => {\n        origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n    });\n    // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n    visualElement.render();\n    const targetBbox = visualElement.measureViewportBox();\n    changedKeys.forEach((key) => {\n        // Restore styles to their **calculated computed style**, not their actual\n        // originally set style. This allows us to animate between equivalent pixel units.\n        const value = visualElement.getValue(key);\n        value && value.jump(origin[key]);\n        target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n    });\n    return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n    target = { ...target };\n    transitionEnd = { ...transitionEnd };\n    const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n    // We want to remove any transform values that could affect the element's bounding box before\n    // it's measured. We'll reapply these later.\n    let removedTransformValues = [];\n    let hasAttemptedToRemoveTransformValues = false;\n    const changedValueTypeKeys = [];\n    targetPositionalKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (!visualElement.hasValue(key))\n            return;\n        let from = origin[key];\n        let fromType = (0,_value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__.findDimensionValueType)(from);\n        const to = target[key];\n        let toType;\n        // TODO: The current implementation of this basically throws an error\n        // if you try and do value conversion via keyframes. There's probably\n        // a way of doing this but the performance implications would need greater scrutiny,\n        // as it'd be doing multiple resize-remeasure operations.\n        if ((0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_4__.isKeyframesTarget)(to)) {\n            const numKeyframes = to.length;\n            const fromIndex = to[0] === null ? 1 : 0;\n            from = to[fromIndex];\n            fromType = (0,_value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__.findDimensionValueType)(from);\n            for (let i = fromIndex; i < numKeyframes; i++) {\n                /**\n                 * Don't allow wildcard keyframes to be used to detect\n                 * a difference in value types.\n                 */\n                if (to[i] === null)\n                    break;\n                if (!toType) {\n                    toType = (0,_value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__.findDimensionValueType)(to[i]);\n                    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__.invariant)(toType === fromType ||\n                        (isNumOrPxType(fromType) && isNumOrPxType(toType)), \"Keyframes must be of the same dimension as the current value\");\n                }\n                else {\n                    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_5__.invariant)((0,_value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__.findDimensionValueType)(to[i]) === toType, \"All keyframes must be of the same type\");\n                }\n            }\n        }\n        else {\n            toType = (0,_value_types_dimensions_mjs__WEBPACK_IMPORTED_MODULE_3__.findDimensionValueType)(to);\n        }\n        if (fromType !== toType) {\n            // If they're both just number or px, convert them both to numbers rather than\n            // relying on resize/remeasure to convert (which is wasteful in this situation)\n            if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n                const current = value.get();\n                if (typeof current === \"string\") {\n                    value.set(parseFloat(current));\n                }\n                if (typeof to === \"string\") {\n                    target[key] = parseFloat(to);\n                }\n                else if (Array.isArray(to) && toType === _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.px) {\n                    target[key] = to.map(parseFloat);\n                }\n            }\n            else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) &&\n                (toType === null || toType === void 0 ? void 0 : toType.transform) &&\n                (from === 0 || to === 0)) {\n                // If one or the other value is 0, it's safe to coerce it to the\n                // type of the other without measurement\n                if (from === 0) {\n                    value.set(toType.transform(from));\n                }\n                else {\n                    target[key] = fromType.transform(to);\n                }\n            }\n            else {\n                // If we're going to do value conversion via DOM measurements, we first\n                // need to remove non-positional transform values that could affect the bbox measurements.\n                if (!hasAttemptedToRemoveTransformValues) {\n                    removedTransformValues =\n                        removeNonTranslationalTransform(visualElement);\n                    hasAttemptedToRemoveTransformValues = true;\n                }\n                changedValueTypeKeys.push(key);\n                transitionEnd[key] =\n                    transitionEnd[key] !== undefined\n                        ? transitionEnd[key]\n                        : target[key];\n                value.jump(to);\n            }\n        }\n    });\n    if (changedValueTypeKeys.length) {\n        const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0\n            ? window.pageYOffset\n            : null;\n        const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n        // If we removed transform values, reapply them before the next render\n        if (removedTransformValues.length) {\n            removedTransformValues.forEach(([key, value]) => {\n                visualElement.getValue(key).set(value);\n            });\n        }\n        // Reapply original values\n        visualElement.render();\n        // Restore scroll position\n        if (_utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_6__.isBrowser && scrollY !== null) {\n            window.scrollTo({ top: scrollY });\n        }\n        return { target: convertedTarget, transitionEnd };\n    }\n    else {\n        return { target, transitionEnd };\n    }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n    return hasPositionalKey(target)\n        ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd)\n        : { target, transitionEnd };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnimatableNone: function() { return /* binding */ getAnimatableNone; }\n/* harmony export */ });\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n/* harmony import */ var _value_types_complex_filter_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/complex/filter.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/filter.mjs\");\n/* harmony import */ var _defaults_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs\");\n\n\n\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = (0,_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultValueType)(key);\n    if (defaultValueType !== _value_types_complex_filter_mjs__WEBPACK_IMPORTED_MODULE_1__.filter)\n        defaultValueType = _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_2__.complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9hbmltYXRhYmxlLW5vbmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUU7QUFDQTtBQUNaOztBQUVyRDtBQUNBLDJCQUEyQixrRUFBbUI7QUFDOUMsNkJBQTZCLG1FQUFNO0FBQ25DLDJCQUEyQixtRUFBTztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vdmFsdWUtdHlwZXMvYW5pbWF0YWJsZS1ub25lLm1qcz9hMmEzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbXBsZXggfSBmcm9tICcuLi8uLi8uLi92YWx1ZS90eXBlcy9jb21wbGV4L2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBmaWx0ZXIgfSBmcm9tICcuLi8uLi8uLi92YWx1ZS90eXBlcy9jb21wbGV4L2ZpbHRlci5tanMnO1xuaW1wb3J0IHsgZ2V0RGVmYXVsdFZhbHVlVHlwZSB9IGZyb20gJy4vZGVmYXVsdHMubWpzJztcblxuZnVuY3Rpb24gZ2V0QW5pbWF0YWJsZU5vbmUoa2V5LCB2YWx1ZSkge1xuICAgIGxldCBkZWZhdWx0VmFsdWVUeXBlID0gZ2V0RGVmYXVsdFZhbHVlVHlwZShrZXkpO1xuICAgIGlmIChkZWZhdWx0VmFsdWVUeXBlICE9PSBmaWx0ZXIpXG4gICAgICAgIGRlZmF1bHRWYWx1ZVR5cGUgPSBjb21wbGV4O1xuICAgIC8vIElmIHZhbHVlIGlzIG5vdCByZWNvZ25pc2VkIGFzIGFuaW1hdGFibGUsIGllIFwibm9uZVwiLCBjcmVhdGUgYW4gYW5pbWF0YWJsZSB2ZXJzaW9uIG9yaWdpbiBiYXNlZCBvbiB0aGUgdGFyZ2V0XG4gICAgcmV0dXJuIGRlZmF1bHRWYWx1ZVR5cGUuZ2V0QW5pbWF0YWJsZU5vbmVcbiAgICAgICAgPyBkZWZhdWx0VmFsdWVUeXBlLmdldEFuaW1hdGFibGVOb25lKHZhbHVlKVxuICAgICAgICA6IHVuZGVmaW5lZDtcbn1cblxuZXhwb3J0IHsgZ2V0QW5pbWF0YWJsZU5vbmUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultValueTypes: function() { return /* binding */ defaultValueTypes; },\n/* harmony export */   getDefaultValueType: function() { return /* binding */ getDefaultValueType; }\n/* harmony export */ });\n/* harmony import */ var _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _value_types_complex_filter_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../value/types/complex/filter.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/filter.mjs\");\n/* harmony import */ var _number_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs\");\n\n\n\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ..._number_mjs__WEBPACK_IMPORTED_MODULE_0__.numberValueTypes,\n    // Color props\n    color: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    backgroundColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    outlineColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    fill: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    stroke: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    // Border props\n    borderColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    borderTopColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    borderRightColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    borderBottomColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    borderLeftColor: _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color,\n    filter: _value_types_complex_filter_mjs__WEBPACK_IMPORTED_MODULE_2__.filter,\n    WebkitFilter: _value_types_complex_filter_mjs__WEBPACK_IMPORTED_MODULE_2__.filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9kZWZhdWx0cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkQ7QUFDSTtBQUNqQjs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLHlEQUFnQjtBQUN2QjtBQUNBLFNBQVM7QUFDVCxxQkFBcUIsK0RBQUs7QUFDMUIsa0JBQWtCLCtEQUFLO0FBQ3ZCLFVBQVUsK0RBQUs7QUFDZixZQUFZLCtEQUFLO0FBQ2pCO0FBQ0EsaUJBQWlCLCtEQUFLO0FBQ3RCLG9CQUFvQiwrREFBSztBQUN6QixzQkFBc0IsK0RBQUs7QUFDM0IsdUJBQXVCLCtEQUFLO0FBQzVCLHFCQUFxQiwrREFBSztBQUMxQixVQUFVO0FBQ1Ysa0JBQWtCLG1FQUFNO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWtEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9kZWZhdWx0cy5tanM/ZDViYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2xvciB9IGZyb20gJy4uLy4uLy4uL3ZhbHVlL3R5cGVzL2NvbG9yL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBmaWx0ZXIgfSBmcm9tICcuLi8uLi8uLi92YWx1ZS90eXBlcy9jb21wbGV4L2ZpbHRlci5tanMnO1xuaW1wb3J0IHsgbnVtYmVyVmFsdWVUeXBlcyB9IGZyb20gJy4vbnVtYmVyLm1qcyc7XG5cbi8qKlxuICogQSBtYXAgb2YgZGVmYXVsdCB2YWx1ZSB0eXBlcyBmb3IgY29tbW9uIHZhbHVlc1xuICovXG5jb25zdCBkZWZhdWx0VmFsdWVUeXBlcyA9IHtcbiAgICAuLi5udW1iZXJWYWx1ZVR5cGVzLFxuICAgIC8vIENvbG9yIHByb3BzXG4gICAgY29sb3IsXG4gICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcixcbiAgICBvdXRsaW5lQ29sb3I6IGNvbG9yLFxuICAgIGZpbGw6IGNvbG9yLFxuICAgIHN0cm9rZTogY29sb3IsXG4gICAgLy8gQm9yZGVyIHByb3BzXG4gICAgYm9yZGVyQ29sb3I6IGNvbG9yLFxuICAgIGJvcmRlclRvcENvbG9yOiBjb2xvcixcbiAgICBib3JkZXJSaWdodENvbG9yOiBjb2xvcixcbiAgICBib3JkZXJCb3R0b21Db2xvcjogY29sb3IsXG4gICAgYm9yZGVyTGVmdENvbG9yOiBjb2xvcixcbiAgICBmaWx0ZXIsXG4gICAgV2Via2l0RmlsdGVyOiBmaWx0ZXIsXG59O1xuLyoqXG4gKiBHZXRzIHRoZSBkZWZhdWx0IFZhbHVlVHlwZSBmb3IgdGhlIHByb3ZpZGVkIHZhbHVlIGtleVxuICovXG5jb25zdCBnZXREZWZhdWx0VmFsdWVUeXBlID0gKGtleSkgPT4gZGVmYXVsdFZhbHVlVHlwZXNba2V5XTtcblxuZXhwb3J0IHsgZGVmYXVsdFZhbHVlVHlwZXMsIGdldERlZmF1bHRWYWx1ZVR5cGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dimensionValueTypes: function() { return /* binding */ dimensionValueTypes; },\n/* harmony export */   findDimensionValueType: function() { return /* binding */ findDimensionValueType; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _test_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./test.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs\");\n/* harmony import */ var _type_auto_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./type-auto.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-auto.mjs\");\n\n\n\n\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [_value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__.number, _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.px, _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent, _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.degrees, _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.vw, _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.vh, _type_auto_mjs__WEBPACK_IMPORTED_MODULE_2__.auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find((0,_test_mjs__WEBPACK_IMPORTED_MODULE_3__.testValueType)(v));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9kaW1lbnNpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0U7QUFDc0I7QUFDM0M7QUFDSjs7QUFFdkM7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGtFQUFNLEVBQUUsOERBQUUsRUFBRSxtRUFBTyxFQUFFLG1FQUFPLEVBQUUsOERBQUUsRUFBRSw4REFBRSxFQUFFLGdEQUFJO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBLCtEQUErRCx3REFBYTs7QUFFckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3ZhbHVlLXR5cGVzL2RpbWVuc2lvbnMubWpzPzliMTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbnVtYmVyIH0gZnJvbSAnLi4vLi4vLi4vdmFsdWUvdHlwZXMvbnVtYmVycy9pbmRleC5tanMnO1xuaW1wb3J0IHsgcHgsIHBlcmNlbnQsIGRlZ3JlZXMsIHZ3LCB2aCB9IGZyb20gJy4uLy4uLy4uL3ZhbHVlL3R5cGVzL251bWJlcnMvdW5pdHMubWpzJztcbmltcG9ydCB7IHRlc3RWYWx1ZVR5cGUgfSBmcm9tICcuL3Rlc3QubWpzJztcbmltcG9ydCB7IGF1dG8gfSBmcm9tICcuL3R5cGUtYXV0by5tanMnO1xuXG4vKipcbiAqIEEgbGlzdCBvZiB2YWx1ZSB0eXBlcyBjb21tb25seSB1c2VkIGZvciBkaW1lbnNpb25zXG4gKi9cbmNvbnN0IGRpbWVuc2lvblZhbHVlVHlwZXMgPSBbbnVtYmVyLCBweCwgcGVyY2VudCwgZGVncmVlcywgdncsIHZoLCBhdXRvXTtcbi8qKlxuICogVGVzdHMgYSBkaW1lbnNpb25hbCB2YWx1ZSBhZ2FpbnN0IHRoZSBsaXN0IG9mIGRpbWVuc2lvbiBWYWx1ZVR5cGVzXG4gKi9cbmNvbnN0IGZpbmREaW1lbnNpb25WYWx1ZVR5cGUgPSAodikgPT4gZGltZW5zaW9uVmFsdWVUeXBlcy5maW5kKHRlc3RWYWx1ZVR5cGUodikpO1xuXG5leHBvcnQgeyBkaW1lbnNpb25WYWx1ZVR5cGVzLCBmaW5kRGltZW5zaW9uVmFsdWVUeXBlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/find.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/find.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findValueType: function() { return /* binding */ findValueType; }\n/* harmony export */ });\n/* harmony import */ var _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n/* harmony import */ var _dimensions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dimensions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/dimensions.mjs\");\n/* harmony import */ var _test_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./test.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs\");\n\n\n\n\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [..._dimensions_mjs__WEBPACK_IMPORTED_MODULE_0__.dimensionValueTypes, _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color, _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_2__.complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = (v) => valueTypes.find((0,_test_mjs__WEBPACK_IMPORTED_MODULE_3__.testValueType)(v));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9maW5kLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE2RDtBQUNJO0FBQ1Y7QUFDWjs7QUFFM0M7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdFQUFtQixFQUFFLCtEQUFLLEVBQUUsbUVBQU87QUFDMUQ7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLHdEQUFhOztBQUVqQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vdmFsdWUtdHlwZXMvZmluZC5tanM/N2FiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2xvciB9IGZyb20gJy4uLy4uLy4uL3ZhbHVlL3R5cGVzL2NvbG9yL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBjb21wbGV4IH0gZnJvbSAnLi4vLi4vLi4vdmFsdWUvdHlwZXMvY29tcGxleC9pbmRleC5tanMnO1xuaW1wb3J0IHsgZGltZW5zaW9uVmFsdWVUeXBlcyB9IGZyb20gJy4vZGltZW5zaW9ucy5tanMnO1xuaW1wb3J0IHsgdGVzdFZhbHVlVHlwZSB9IGZyb20gJy4vdGVzdC5tanMnO1xuXG4vKipcbiAqIEEgbGlzdCBvZiBhbGwgVmFsdWVUeXBlc1xuICovXG5jb25zdCB2YWx1ZVR5cGVzID0gWy4uLmRpbWVuc2lvblZhbHVlVHlwZXMsIGNvbG9yLCBjb21wbGV4XTtcbi8qKlxuICogVGVzdHMgYSB2YWx1ZSBhZ2FpbnN0IHRoZSBsaXN0IG9mIFZhbHVlVHlwZXNcbiAqL1xuY29uc3QgZmluZFZhbHVlVHlwZSA9ICh2KSA9PiB2YWx1ZVR5cGVzLmZpbmQodGVzdFZhbHVlVHlwZSh2KSk7XG5cbmV4cG9ydCB7IGZpbmRWYWx1ZVR5cGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/find.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/get-as-type.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/get-as-type.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueAsType: function() { return /* binding */ getValueAsType; }\n/* harmony export */ });\n/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9nZXQtYXMtdHlwZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy9nZXQtYXMtdHlwZS5tanM/MzQ4OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFByb3ZpZGVkIGEgdmFsdWUgYW5kIGEgVmFsdWVUeXBlLCByZXR1cm5zIHRoZSB2YWx1ZSBhcyB0aGF0IHZhbHVlIHR5cGUuXG4gKi9cbmNvbnN0IGdldFZhbHVlQXNUeXBlID0gKHZhbHVlLCB0eXBlKSA9PiB7XG4gICAgcmV0dXJuIHR5cGUgJiYgdHlwZW9mIHZhbHVlID09PSBcIm51bWJlclwiXG4gICAgICAgID8gdHlwZS50cmFuc2Zvcm0odmFsdWUpXG4gICAgICAgIDogdmFsdWU7XG59O1xuXG5leHBvcnQgeyBnZXRWYWx1ZUFzVHlwZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/get-as-type.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   numberValueTypes: function() { return /* binding */ numberValueTypes; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/types/numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _type_int_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./type-int.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs\");\n\n\n\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderTopWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderRightWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderBottomWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderLeftWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderRadius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    radius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderTopLeftRadius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderTopRightRadius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderBottomRightRadius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    borderBottomLeftRadius: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    // Positioning props\n    width: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    maxWidth: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    height: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    maxHeight: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    size: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    top: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    right: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    bottom: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    left: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    // Spacing props\n    padding: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    paddingTop: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    paddingRight: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    paddingBottom: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    paddingLeft: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    margin: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    marginTop: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    marginRight: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    marginBottom: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    marginLeft: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    // Transform props\n    rotate: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    rotateX: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    rotateY: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    rotateZ: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    scale: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.scale,\n    scaleX: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.scale,\n    scaleY: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.scale,\n    scaleZ: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.scale,\n    skew: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    skewX: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    skewY: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.degrees,\n    distance: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    translateX: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    translateY: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    translateZ: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    x: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    y: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    z: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    perspective: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    transformPerspective: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    opacity: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.alpha,\n    originX: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.progressPercentage,\n    originY: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.progressPercentage,\n    originZ: _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px,\n    // Misc\n    zIndex: _type_int_mjs__WEBPACK_IMPORTED_MODULE_2__.int,\n    // SVG\n    fillOpacity: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.alpha,\n    strokeOpacity: _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.alpha,\n    numOctaves: _type_int_mjs__WEBPACK_IMPORTED_MODULE_2__.int,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   testValueType: function() { return /* binding */ testValueType; }\n/* harmony export */ });\n/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = (v) => (type) => type.test(v);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy90ZXN0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy90ZXN0Lm1qcz9mYmUyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGVzdHMgYSBwcm92aWRlZCB2YWx1ZSBhZ2FpbnN0IGEgVmFsdWVUeXBlXG4gKi9cbmNvbnN0IHRlc3RWYWx1ZVR5cGUgPSAodikgPT4gKHR5cGUpID0+IHR5cGUudGVzdCh2KTtcblxuZXhwb3J0IHsgdGVzdFZhbHVlVHlwZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-auto.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/type-auto.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auto: function() { return /* binding */ auto; }\n/* harmony export */ });\n/**\n * ValueType for \"auto\"\n */\nconst auto = {\n    test: (v) => v === \"auto\",\n    parse: (v) => v,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy90eXBlLWF1dG8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3ZhbHVlLXR5cGVzL3R5cGUtYXV0by5tanM/MzZiMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFZhbHVlVHlwZSBmb3IgXCJhdXRvXCJcbiAqL1xuY29uc3QgYXV0byA9IHtcbiAgICB0ZXN0OiAodikgPT4gdiA9PT0gXCJhdXRvXCIsXG4gICAgcGFyc2U6ICh2KSA9PiB2LFxufTtcblxuZXhwb3J0IHsgYXV0byB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-auto.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int: function() { return /* binding */ int; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n\n\nconst int = {\n    ..._value_types_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_0__.number,\n    transform: Math.round,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92YWx1ZS10eXBlcy90eXBlLWludC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0U7O0FBRWhFO0FBQ0EsT0FBTyxrRUFBTTtBQUNiO0FBQ0E7O0FBRWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3ZhbHVlLXR5cGVzL3R5cGUtaW50Lm1qcz8yNWVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG51bWJlciB9IGZyb20gJy4uLy4uLy4uL3ZhbHVlL3R5cGVzL251bWJlcnMvaW5kZXgubWpzJztcblxuY29uc3QgaW50ID0ge1xuICAgIC4uLm51bWJlcixcbiAgICB0cmFuc2Zvcm06IE1hdGgucm91bmQsXG59O1xuXG5leHBvcnQgeyBpbnQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTMLVisualElement: function() { return /* binding */ HTMLVisualElement; },\n/* harmony export */   getComputedStyle: function() { return /* binding */ getComputedStyle; }\n/* harmony export */ });\n/* harmony import */ var _utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/build-styles.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom/utils/is-css-variable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\");\n/* harmony import */ var _utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _utils_render_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/render.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/render.mjs\");\n/* harmony import */ var _dom_value_types_defaults_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dom/value-types/defaults.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs\");\n/* harmony import */ var _projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../projection/utils/measure.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/measure.mjs\");\n/* harmony import */ var _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom/DOMVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\n\n\n\n\n\n\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n    }\n    readValueFromInstance(instance, key) {\n        if (_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.transformProps.has(key)) {\n            const defaultType = (0,_dom_value_types_defaults_mjs__WEBPACK_IMPORTED_MODULE_2__.getDefaultValueType)(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = ((0,_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_3__.isCSSVariableName)(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return (0,_projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_4__.measureViewportBox)(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, options, props) {\n        (0,_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_5__.buildHTMLStyles)(renderState, latestValues, options, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps) {\n        return (0,_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_6__.scrapeMotionValuesFromProps)(props, prevProps);\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_7__.isMotionValue)(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current)\n                    this.current.textContent = `${latest}`;\n            });\n        }\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        (0,_utils_render_mjs__WEBPACK_IMPORTED_MODULE_8__.renderHTML)(instance, renderState, styleProp, projection);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/config-motion.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/config-motion.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlMotionConfig: function() { return /* binding */ htmlMotionConfig; }\n/* harmony export */ });\n/* harmony import */ var _motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/utils/use-visual-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\n\n\nconst htmlMotionConfig = {\n    useVisualState: (0,_motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__.makeUseVisualState)({\n        scrapeMotionValuesFromProps: _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__.scrapeMotionValuesFromProps,\n        createRenderState: _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__.createHtmlRenderState,\n    }),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvY29uZmlnLW1vdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2RTtBQUNFO0FBQ1A7O0FBRXhFO0FBQ0Esb0JBQW9CLHNGQUFrQjtBQUN0QyxtQ0FBbUM7QUFDbkMsMkJBQTJCLGlGQUFxQjtBQUNoRCxLQUFLO0FBQ0w7O0FBRTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvY29uZmlnLW1vdGlvbi5tanM/MjAyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtYWtlVXNlVmlzdWFsU3RhdGUgfSBmcm9tICcuLi8uLi9tb3Rpb24vdXRpbHMvdXNlLXZpc3VhbC1zdGF0ZS5tanMnO1xuaW1wb3J0IHsgc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzIH0gZnJvbSAnLi91dGlscy9zY3JhcGUtbW90aW9uLXZhbHVlcy5tanMnO1xuaW1wb3J0IHsgY3JlYXRlSHRtbFJlbmRlclN0YXRlIH0gZnJvbSAnLi91dGlscy9jcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyc7XG5cbmNvbnN0IGh0bWxNb3Rpb25Db25maWcgPSB7XG4gICAgdXNlVmlzdWFsU3RhdGU6IG1ha2VVc2VWaXN1YWxTdGF0ZSh7XG4gICAgICAgIHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyxcbiAgICAgICAgY3JlYXRlUmVuZGVyU3RhdGU6IGNyZWF0ZUh0bWxSZW5kZXJTdGF0ZSxcbiAgICB9KSxcbn07XG5cbmV4cG9ydCB7IGh0bWxNb3Rpb25Db25maWcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/config-motion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/use-props.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/use-props.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyRawValuesOnly: function() { return /* binding */ copyRawValuesOnly; },\n/* harmony export */   useHTMLProps: function() { return /* binding */ useHTMLProps; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../motion/utils/is-forced-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/build-styles.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\n\n\n\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!(0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(source[key]) && !(0,_motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__.isForcedMotionValue)(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState, isStatic) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        const state = (0,_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_3__.createHtmlRenderState)();\n        (0,_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__.buildHTMLStyles)(state, visualState, { enableHardwareAcceleration: !isStatic }, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState, isStatic) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState, isStatic));\n    return props.transformValues ? props.transformValues(style) : style;\n}\nfunction useHTMLProps(props, visualState, isStatic) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState, isStatic);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/use-props.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildHTMLStyles: function() { return /* binding */ buildHTMLStyles; }\n/* harmony export */ });\n/* harmony import */ var _build_transform_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./build-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs\");\n/* harmony import */ var _dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../dom/utils/is-css-variable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\");\n/* harmony import */ var _transform_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _dom_value_types_get_as_type_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../dom/value-types/get-as-type.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/get-as-type.mjs\");\n/* harmony import */ var _dom_value_types_number_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../dom/value-types/number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs\");\n\n\n\n\n\n\nfunction buildHTMLStyles(state, latestValues, options, transformTemplate) {\n    const { style, vars, transform, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    // Does the calculated transform essentially equal \"none\"?\n    let transformIsNone = true;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept seperately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        /**\n         * If this is a CSS variable we don't do any further processing.\n         */\n        if ((0,_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_0__.isCSSVariableName)(key)) {\n            vars[key] = value;\n            continue;\n        }\n        // Convert the value to its default value type, ie 0 -> \"0px\"\n        const valueType = _dom_value_types_number_mjs__WEBPACK_IMPORTED_MODULE_1__.numberValueTypes[key];\n        const valueAsType = (0,_dom_value_types_get_as_type_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueAsType)(value, valueType);\n        if (_transform_mjs__WEBPACK_IMPORTED_MODULE_3__.transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            transform[key] = valueAsType;\n            // If we already know we have a non-default transform, early return\n            if (!transformIsNone)\n                continue;\n            // Otherwise check to see if this is a default transform\n            if (value !== (valueType.default || 0))\n                transformIsNone = false;\n        }\n        else if (key.startsWith(\"origin\")) {\n            // If this is a transform origin, flag and enable further transform-origin processing\n            hasTransformOrigin = true;\n            transformOrigin[key] = valueAsType;\n        }\n        else {\n            style[key] = valueAsType;\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = (0,_build_transform_mjs__WEBPACK_IMPORTED_MODULE_4__.buildTransform)(state.transform, options, transformIsNone, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildTransform: function() { return /* binding */ buildTransform; }\n/* harmony export */ });\n/* harmony import */ var _transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = _transform_mjs__WEBPACK_IMPORTED_MODULE_0__.transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(transform, { enableHardwareAcceleration = true, allowTransformNone = true, }, transformIsDefault, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = _transform_mjs__WEBPACK_IMPORTED_MODULE_0__.transformPropOrder[i];\n        if (transform[key] !== undefined) {\n            const transformName = translateAlias[key] || key;\n            transformString += `${transformName}(${transform[key]}) `;\n        }\n    }\n    if (enableHardwareAcceleration && !transform.z) {\n        transformString += \"translateZ(0)\";\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (allowTransformNone && transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvYnVpbGQtdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDs7QUFFckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDhEQUFrQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsK0RBQStEO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkMsb0JBQW9CLDhEQUFrQjtBQUN0QztBQUNBO0FBQ0Esa0NBQWtDLGNBQWMsR0FBRyxlQUFlO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9odG1sL3V0aWxzL2J1aWxkLXRyYW5zZm9ybS5tanM/MjdiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0cmFuc2Zvcm1Qcm9wT3JkZXIgfSBmcm9tICcuL3RyYW5zZm9ybS5tanMnO1xuXG5jb25zdCB0cmFuc2xhdGVBbGlhcyA9IHtcbiAgICB4OiBcInRyYW5zbGF0ZVhcIixcbiAgICB5OiBcInRyYW5zbGF0ZVlcIixcbiAgICB6OiBcInRyYW5zbGF0ZVpcIixcbiAgICB0cmFuc2Zvcm1QZXJzcGVjdGl2ZTogXCJwZXJzcGVjdGl2ZVwiLFxufTtcbmNvbnN0IG51bVRyYW5zZm9ybXMgPSB0cmFuc2Zvcm1Qcm9wT3JkZXIubGVuZ3RoO1xuLyoqXG4gKiBCdWlsZCBhIENTUyB0cmFuc2Zvcm0gc3R5bGUgZnJvbSBpbmRpdmlkdWFsIHgveS9zY2FsZSBldGMgcHJvcGVydGllcy5cbiAqXG4gKiBUaGlzIG91dHB1dHMgd2l0aCBhIGRlZmF1bHQgb3JkZXIgb2YgdHJhbnNmb3Jtcy9zY2FsZXMvcm90YXRpb25zLCB0aGlzIGNhbiBiZSBjdXN0b21pc2VkIGJ5XG4gKiBwcm92aWRpbmcgYSB0cmFuc2Zvcm1UZW1wbGF0ZSBmdW5jdGlvbi5cbiAqL1xuZnVuY3Rpb24gYnVpbGRUcmFuc2Zvcm0odHJhbnNmb3JtLCB7IGVuYWJsZUhhcmR3YXJlQWNjZWxlcmF0aW9uID0gdHJ1ZSwgYWxsb3dUcmFuc2Zvcm1Ob25lID0gdHJ1ZSwgfSwgdHJhbnNmb3JtSXNEZWZhdWx0LCB0cmFuc2Zvcm1UZW1wbGF0ZSkge1xuICAgIC8vIFRoZSB0cmFuc2Zvcm0gc3RyaW5nIHdlJ3JlIGdvaW5nIHRvIGJ1aWxkIGludG8uXG4gICAgbGV0IHRyYW5zZm9ybVN0cmluZyA9IFwiXCI7XG4gICAgLyoqXG4gICAgICogTG9vcCBvdmVyIGFsbCBwb3NzaWJsZSB0cmFuc2Zvcm1zIGluIG9yZGVyLCBhZGRpbmcgdGhlIG9uZXMgdGhhdFxuICAgICAqIGFyZSBwcmVzZW50IHRvIHRoZSB0cmFuc2Zvcm0gc3RyaW5nLlxuICAgICAqL1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtVHJhbnNmb3JtczsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGtleSA9IHRyYW5zZm9ybVByb3BPcmRlcltpXTtcbiAgICAgICAgaWYgKHRyYW5zZm9ybVtrZXldICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zZm9ybU5hbWUgPSB0cmFuc2xhdGVBbGlhc1trZXldIHx8IGtleTtcbiAgICAgICAgICAgIHRyYW5zZm9ybVN0cmluZyArPSBgJHt0cmFuc2Zvcm1OYW1lfSgke3RyYW5zZm9ybVtrZXldfSkgYDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoZW5hYmxlSGFyZHdhcmVBY2NlbGVyYXRpb24gJiYgIXRyYW5zZm9ybS56KSB7XG4gICAgICAgIHRyYW5zZm9ybVN0cmluZyArPSBcInRyYW5zbGF0ZVooMClcIjtcbiAgICB9XG4gICAgdHJhbnNmb3JtU3RyaW5nID0gdHJhbnNmb3JtU3RyaW5nLnRyaW0oKTtcbiAgICAvLyBJZiB3ZSBoYXZlIGEgY3VzdG9tIGB0cmFuc2Zvcm1gIHRlbXBsYXRlLCBwYXNzIG91ciB0cmFuc2Zvcm0gdmFsdWVzIGFuZFxuICAgIC8vIGdlbmVyYXRlZCB0cmFuc2Zvcm1TdHJpbmcgdG8gdGhhdCBiZWZvcmUgcmV0dXJuaW5nXG4gICAgaWYgKHRyYW5zZm9ybVRlbXBsYXRlKSB7XG4gICAgICAgIHRyYW5zZm9ybVN0cmluZyA9IHRyYW5zZm9ybVRlbXBsYXRlKHRyYW5zZm9ybSwgdHJhbnNmb3JtSXNEZWZhdWx0ID8gXCJcIiA6IHRyYW5zZm9ybVN0cmluZyk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGFsbG93VHJhbnNmb3JtTm9uZSAmJiB0cmFuc2Zvcm1Jc0RlZmF1bHQpIHtcbiAgICAgICAgdHJhbnNmb3JtU3RyaW5nID0gXCJub25lXCI7XG4gICAgfVxuICAgIHJldHVybiB0cmFuc2Zvcm1TdHJpbmc7XG59XG5cbmV4cG9ydCB7IGJ1aWxkVHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHtmlRenderState: function() { return /* binding */ createHtmlRenderState; }\n/* harmony export */ });\nconst createHtmlRenderState = () => ({\n    style: {},\n    transform: {},\n    transformOrigin: {},\n    vars: {},\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvY3JlYXRlLXJlbmRlci1zdGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYTtBQUNiLGlCQUFpQjtBQUNqQix1QkFBdUI7QUFDdkIsWUFBWTtBQUNaLENBQUM7O0FBRWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvY3JlYXRlLXJlbmRlci1zdGF0ZS5tanM/NjAwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVIdG1sUmVuZGVyU3RhdGUgPSAoKSA9PiAoe1xuICAgIHN0eWxlOiB7fSxcbiAgICB0cmFuc2Zvcm06IHt9LFxuICAgIHRyYW5zZm9ybU9yaWdpbjoge30sXG4gICAgdmFyczoge30sXG59KTtcblxuZXhwb3J0IHsgY3JlYXRlSHRtbFJlbmRlclN0YXRlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/render.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/render.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderHTML: function() { return /* binding */ renderHTML; }\n/* harmony export */ });\nfunction renderHTML(element, { style, vars }, styleProp, projection) {\n    Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n    // Loop over any CSS variables and assign those.\n    for (const key in vars) {\n        element.style.setProperty(key, vars[key]);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvcmVuZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsK0JBQStCLGFBQWE7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9odG1sL3V0aWxzL3JlbmRlci5tanM/OTNhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByZW5kZXJIVE1MKGVsZW1lbnQsIHsgc3R5bGUsIHZhcnMgfSwgc3R5bGVQcm9wLCBwcm9qZWN0aW9uKSB7XG4gICAgT2JqZWN0LmFzc2lnbihlbGVtZW50LnN0eWxlLCBzdHlsZSwgcHJvamVjdGlvbiAmJiBwcm9qZWN0aW9uLmdldFByb2plY3Rpb25TdHlsZXMoc3R5bGVQcm9wKSk7XG4gICAgLy8gTG9vcCBvdmVyIGFueSBDU1MgdmFyaWFibGVzIGFuZCBhc3NpZ24gdGhvc2UuXG4gICAgZm9yIChjb25zdCBrZXkgaW4gdmFycykge1xuICAgICAgICBlbGVtZW50LnN0eWxlLnNldFByb3BlcnR5KGtleSwgdmFyc1trZXldKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IHJlbmRlckhUTUwgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/render.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrapeMotionValuesFromProps: function() { return /* binding */ scrapeMotionValuesFromProps; }\n/* harmony export */ });\n/* harmony import */ var _motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../motion/utils/is-forced-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(style[key]) ||\n            (prevProps.style && (0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(prevProps.style[key])) ||\n            (0,_motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isForcedMotionValue)(key, props)) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvc2NyYXBlLW1vdGlvbi12YWx1ZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1RjtBQUNkOztBQUV6RTtBQUNBLFlBQVksUUFBUTtBQUNwQjtBQUNBO0FBQ0EsWUFBWSwrRUFBYTtBQUN6QixnQ0FBZ0MsK0VBQWE7QUFDN0MsWUFBWSw2RkFBbUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvaHRtbC91dGlscy9zY3JhcGUtbW90aW9uLXZhbHVlcy5tanM/ZjExNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0ZvcmNlZE1vdGlvblZhbHVlIH0gZnJvbSAnLi4vLi4vLi4vbW90aW9uL3V0aWxzL2lzLWZvcmNlZC1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IGlzTW90aW9uVmFsdWUgfSBmcm9tICcuLi8uLi8uLi92YWx1ZS91dGlscy9pcy1tb3Rpb24tdmFsdWUubWpzJztcblxuZnVuY3Rpb24gc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzKHByb3BzLCBwcmV2UHJvcHMpIHtcbiAgICBjb25zdCB7IHN0eWxlIH0gPSBwcm9wcztcbiAgICBjb25zdCBuZXdWYWx1ZXMgPSB7fTtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBzdHlsZSkge1xuICAgICAgICBpZiAoaXNNb3Rpb25WYWx1ZShzdHlsZVtrZXldKSB8fFxuICAgICAgICAgICAgKHByZXZQcm9wcy5zdHlsZSAmJiBpc01vdGlvblZhbHVlKHByZXZQcm9wcy5zdHlsZVtrZXldKSkgfHxcbiAgICAgICAgICAgIGlzRm9yY2VkTW90aW9uVmFsdWUoa2V5LCBwcm9wcykpIHtcbiAgICAgICAgICAgIG5ld1ZhbHVlc1trZXldID0gc3R5bGVba2V5XTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbmV3VmFsdWVzO1xufVxuXG5leHBvcnQgeyBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transformPropOrder: function() { return /* binding */ transformPropOrder; },\n/* harmony export */   transformProps: function() { return /* binding */ transformProps; }\n/* harmony export */ });\n/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = new Set(transformPropOrder);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvdHJhbnNmb3JtLm1qcz82NjIyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2VuZXJhdGUgYSBsaXN0IG9mIGV2ZXJ5IHBvc3NpYmxlIHRyYW5zZm9ybSBrZXkuXG4gKi9cbmNvbnN0IHRyYW5zZm9ybVByb3BPcmRlciA9IFtcbiAgICBcInRyYW5zZm9ybVBlcnNwZWN0aXZlXCIsXG4gICAgXCJ4XCIsXG4gICAgXCJ5XCIsXG4gICAgXCJ6XCIsXG4gICAgXCJ0cmFuc2xhdGVYXCIsXG4gICAgXCJ0cmFuc2xhdGVZXCIsXG4gICAgXCJ0cmFuc2xhdGVaXCIsXG4gICAgXCJzY2FsZVwiLFxuICAgIFwic2NhbGVYXCIsXG4gICAgXCJzY2FsZVlcIixcbiAgICBcInJvdGF0ZVwiLFxuICAgIFwicm90YXRlWFwiLFxuICAgIFwicm90YXRlWVwiLFxuICAgIFwicm90YXRlWlwiLFxuICAgIFwic2tld1wiLFxuICAgIFwic2tld1hcIixcbiAgICBcInNrZXdZXCIsXG5dO1xuLyoqXG4gKiBBIHF1aWNrIGxvb2t1cCBmb3IgdHJhbnNmb3JtIHByb3BzLlxuICovXG5jb25zdCB0cmFuc2Zvcm1Qcm9wcyA9IG5ldyBTZXQodHJhbnNmb3JtUHJvcE9yZGVyKTtcblxuZXhwb3J0IHsgdHJhbnNmb3JtUHJvcE9yZGVyLCB0cmFuc2Zvcm1Qcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/store.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/store.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   visualElementStore: function() { return /* binding */ visualElementStore; }\n/* harmony export */ });\nconst visualElementStore = new WeakMap();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N0b3JlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N0b3JlLm1qcz8xZDIwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHZpc3VhbEVsZW1lbnRTdG9yZSA9IG5ldyBXZWFrTWFwKCk7XG5cbmV4cG9ydCB7IHZpc3VhbEVsZW1lbnRTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/store.mjs\n"));

/***/ })

}]);