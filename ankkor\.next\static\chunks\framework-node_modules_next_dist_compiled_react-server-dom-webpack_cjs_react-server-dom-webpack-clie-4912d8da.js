"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js ***!
  \*****************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/**\n * @license React\n * react-server-dom-webpack-client.browser.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar ReactDOM = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// -----------------------------------------------------------------------------\nvar enableBinaryFlight = false;\n\nfunction createStringDecoder() {\n  return new TextDecoder();\n}\nvar decoderOptions = {\n  stream: true\n};\nfunction readPartialStringChunk(decoder, buffer) {\n  return decoder.decode(buffer, decoderOptions);\n}\nfunction readFinalStringChunk(decoder, buffer) {\n  return decoder.decode(buffer);\n}\n\nvar badgeFormat = '%c%s%c '; // Same badge styling as DevTools.\n\nvar badgeStyle = // We use a fixed background if light-dark is not supported, otherwise\n// we use a transparent background.\n'background: #e6e6e6;' + 'background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));' + 'color: #000000;' + 'color: light-dark(#000000, #ffffff);' + 'border-radius: 2px';\nvar resetStyle = '';\nvar pad = ' ';\nfunction printToConsole(methodName, args, badgeName) {\n  var offset = 0;\n\n  switch (methodName) {\n    case 'dir':\n    case 'dirxml':\n    case 'groupEnd':\n    case 'table':\n      {\n        // These methods cannot be colorized because they don't take a formatting string.\n        // eslint-disable-next-line react-internal/no-production-logging\n        console[methodName].apply(console, args);\n        return;\n      }\n\n    case 'assert':\n      {\n        // assert takes formatting options as the second argument.\n        offset = 1;\n      }\n  }\n\n  var newArgs = args.slice(0);\n\n  if (typeof newArgs[offset] === 'string') {\n    newArgs.splice(offset, 1, badgeFormat + newArgs[offset], badgeStyle, pad + badgeName + pad, resetStyle);\n  } else {\n    newArgs.splice(offset, 0, badgeFormat, badgeStyle, pad + badgeName + pad, resetStyle);\n  } // eslint-disable-next-line react-internal/no-production-logging\n\n\n  console[methodName].apply(console, newArgs);\n  return;\n}\n\n// This is the parsed shape of the wire format which is why it is\n// condensed to only the essentialy information\nvar ID = 0;\nvar CHUNKS = 1;\nvar NAME = 2; // export const ASYNC = 3;\n// This logic is correct because currently only include the 4th tuple member\n// when the module is async. If that changes we will need to actually assert\n// the value is true. We don't index into the 4th slot because flow does not\n// like the potential out of bounds access\n\nfunction isAsyncImport(metadata) {\n  return metadata.length === 4;\n}\n\nfunction resolveClientReference(bundlerConfig, metadata) {\n  if (bundlerConfig) {\n    var moduleExports = bundlerConfig[metadata[ID]];\n    var resolvedModuleData = moduleExports[metadata[NAME]];\n    var name;\n\n    if (resolvedModuleData) {\n      // The potentially aliased name.\n      name = resolvedModuleData.name;\n    } else {\n      // If we don't have this specific name, we might have the full module.\n      resolvedModuleData = moduleExports['*'];\n\n      if (!resolvedModuleData) {\n        throw new Error('Could not find the module \"' + metadata[ID] + '\" in the React SSR Manifest. ' + 'This is probably a bug in the React Server Components bundler.');\n      }\n\n      name = metadata[NAME];\n    }\n\n    if (isAsyncImport(metadata)) {\n      return [resolvedModuleData.id, resolvedModuleData.chunks, name, 1\n      /* async */\n      ];\n    } else {\n      return [resolvedModuleData.id, resolvedModuleData.chunks, name];\n    }\n  }\n\n  return metadata;\n}\n// If they're still pending they're a thenable. This map also exists\n// in Webpack but unfortunately it's not exposed so we have to\n// replicate it in user space. null means that it has already loaded.\n\nvar chunkCache = new Map();\n\nfunction requireAsyncModule(id) {\n  // We've already loaded all the chunks. We can require the module.\n  var promise = __webpack_require__(id);\n\n  if (typeof promise.then !== 'function') {\n    // This wasn't a promise after all.\n    return null;\n  } else if (promise.status === 'fulfilled') {\n    // This module was already resolved earlier.\n    return null;\n  } else {\n    // Instrument the Promise to stash the result.\n    promise.then(function (value) {\n      var fulfilledThenable = promise;\n      fulfilledThenable.status = 'fulfilled';\n      fulfilledThenable.value = value;\n    }, function (reason) {\n      var rejectedThenable = promise;\n      rejectedThenable.status = 'rejected';\n      rejectedThenable.reason = reason;\n    });\n    return promise;\n  }\n}\n\nfunction ignoreReject() {// We rely on rejected promises to be handled by another listener.\n} // Start preloading the modules since we might need them soon.\n// This function doesn't suspend.\n\n\nfunction preloadModule(metadata) {\n  var chunks = metadata[CHUNKS];\n  var promises = [];\n  var i = 0;\n\n  while (i < chunks.length) {\n    var chunkId = chunks[i++];\n    var chunkFilename = chunks[i++];\n    var entry = chunkCache.get(chunkId);\n\n    if (entry === undefined) {\n      var thenable = loadChunk(chunkId, chunkFilename);\n      promises.push(thenable); // $FlowFixMe[method-unbinding]\n\n      var resolve = chunkCache.set.bind(chunkCache, chunkId, null);\n      thenable.then(resolve, ignoreReject);\n      chunkCache.set(chunkId, thenable);\n    } else if (entry !== null) {\n      promises.push(entry);\n    }\n  }\n\n  if (isAsyncImport(metadata)) {\n    if (promises.length === 0) {\n      return requireAsyncModule(metadata[ID]);\n    } else {\n      return Promise.all(promises).then(function () {\n        return requireAsyncModule(metadata[ID]);\n      });\n    }\n  } else if (promises.length > 0) {\n    return Promise.all(promises);\n  } else {\n    return null;\n  }\n} // Actually require the module or suspend if it's not yet ready.\n// Increase priority if necessary.\n\nfunction requireModule(metadata) {\n  var moduleExports = __webpack_require__(metadata[ID]);\n\n  if (isAsyncImport(metadata)) {\n    if (typeof moduleExports.then !== 'function') ; else if (moduleExports.status === 'fulfilled') {\n      // This Promise should've been instrumented by preloadModule.\n      moduleExports = moduleExports.value;\n    } else {\n      throw moduleExports.reason;\n    }\n  }\n\n  if (metadata[NAME] === '*') {\n    // This is a placeholder value that represents that the caller imported this\n    // as a CommonJS module as is.\n    return moduleExports;\n  }\n\n  if (metadata[NAME] === '') {\n    // This is a placeholder value that represents that the caller accessed the\n    // default property of this if it was an ESM interop module.\n    return moduleExports.__esModule ? moduleExports.default : moduleExports;\n  }\n\n  return moduleExports[metadata[NAME]];\n}\n\nvar chunkMap = new Map();\n/**\n * We patch the chunk filename function in webpack to insert our own resolution\n * of chunks that come from Flight and may not be known to the webpack runtime\n */\n\nvar webpackGetChunkFilename = __webpack_require__.u;\n\n__webpack_require__.u = function (chunkId) {\n  var flightChunk = chunkMap.get(chunkId);\n\n  if (flightChunk !== undefined) {\n    return flightChunk;\n  }\n\n  return webpackGetChunkFilename(chunkId);\n};\n\nfunction loadChunk(chunkId, filename) {\n  chunkMap.set(chunkId, filename);\n  return __webpack_require__.e(chunkId);\n}\n\nvar ReactDOMSharedInternals = ReactDOM.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n// This client file is in the shared folder because it applies to both SSR and browser contexts.\nvar ReactDOMCurrentDispatcher = ReactDOMSharedInternals.Dispatcher;\nfunction dispatchHint(code, model) {\n  var dispatcher = ReactDOMCurrentDispatcher.current;\n\n  if (dispatcher) {\n    switch (code) {\n      case 'D':\n        {\n          var refined = refineModel(code, model);\n          var href = refined;\n          dispatcher.prefetchDNS(href);\n          return;\n        }\n\n      case 'C':\n        {\n          var _refined = refineModel(code, model);\n\n          if (typeof _refined === 'string') {\n            var _href = _refined;\n            dispatcher.preconnect(_href);\n          } else {\n            var _href2 = _refined[0];\n            var crossOrigin = _refined[1];\n            dispatcher.preconnect(_href2, crossOrigin);\n          }\n\n          return;\n        }\n\n      case 'L':\n        {\n          var _refined2 = refineModel(code, model);\n\n          var _href3 = _refined2[0];\n          var as = _refined2[1];\n\n          if (_refined2.length === 3) {\n            var options = _refined2[2];\n            dispatcher.preload(_href3, as, options);\n          } else {\n            dispatcher.preload(_href3, as);\n          }\n\n          return;\n        }\n\n      case 'm':\n        {\n          var _refined3 = refineModel(code, model);\n\n          if (typeof _refined3 === 'string') {\n            var _href4 = _refined3;\n            dispatcher.preloadModule(_href4);\n          } else {\n            var _href5 = _refined3[0];\n            var _options = _refined3[1];\n            dispatcher.preloadModule(_href5, _options);\n          }\n\n          return;\n        }\n\n      case 'S':\n        {\n          var _refined4 = refineModel(code, model);\n\n          if (typeof _refined4 === 'string') {\n            var _href6 = _refined4;\n            dispatcher.preinitStyle(_href6);\n          } else {\n            var _href7 = _refined4[0];\n            var precedence = _refined4[1] === 0 ? undefined : _refined4[1];\n\n            var _options2 = _refined4.length === 3 ? _refined4[2] : undefined;\n\n            dispatcher.preinitStyle(_href7, precedence, _options2);\n          }\n\n          return;\n        }\n\n      case 'X':\n        {\n          var _refined5 = refineModel(code, model);\n\n          if (typeof _refined5 === 'string') {\n            var _href8 = _refined5;\n            dispatcher.preinitScript(_href8);\n          } else {\n            var _href9 = _refined5[0];\n            var _options3 = _refined5[1];\n            dispatcher.preinitScript(_href9, _options3);\n          }\n\n          return;\n        }\n\n      case 'M':\n        {\n          var _refined6 = refineModel(code, model);\n\n          if (typeof _refined6 === 'string') {\n            var _href10 = _refined6;\n            dispatcher.preinitModuleScript(_href10);\n          } else {\n            var _href11 = _refined6[0];\n            var _options4 = _refined6[1];\n            dispatcher.preinitModuleScript(_href11, _options4);\n          }\n\n          return;\n        }\n    }\n  }\n} // Flow is having trouble refining the HintModels so we help it a bit.\n// This should be compiled out in the production build.\n\nfunction refineModel(code, model) {\n  return model;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\nvar getPrototypeOf = Object.getPrototypeOf;\n\n// in case they error.\n\nvar jsxPropsParents = new WeakMap();\nvar jsxChildrenParents = new WeakMap();\n\nfunction isObjectPrototype(object) {\n  if (!object) {\n    return false;\n  }\n\n  var ObjectPrototype = Object.prototype;\n\n  if (object === ObjectPrototype) {\n    return true;\n  } // It might be an object from a different Realm which is\n  // still just a plain simple object.\n\n\n  if (getPrototypeOf(object)) {\n    return false;\n  }\n\n  var names = Object.getOwnPropertyNames(object);\n\n  for (var i = 0; i < names.length; i++) {\n    if (!(names[i] in ObjectPrototype)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isSimpleObject(object) {\n  if (!isObjectPrototype(getPrototypeOf(object))) {\n    return false;\n  }\n\n  var names = Object.getOwnPropertyNames(object);\n\n  for (var i = 0; i < names.length; i++) {\n    var descriptor = Object.getOwnPropertyDescriptor(object, names[i]);\n\n    if (!descriptor) {\n      return false;\n    }\n\n    if (!descriptor.enumerable) {\n      if ((names[i] === 'key' || names[i] === 'ref') && typeof descriptor.get === 'function') {\n        // React adds key and ref getters to props objects to issue warnings.\n        // Those getters will not be transferred to the client, but that's ok,\n        // so we'll special case them.\n        continue;\n      }\n\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction objectName(object) {\n  // $FlowFixMe[method-unbinding]\n  var name = Object.prototype.toString.call(object);\n  return name.replace(/^\\[object (.*)\\]$/, function (m, p0) {\n    return p0;\n  });\n}\n\nfunction describeKeyForErrorMessage(key) {\n  var encodedKey = JSON.stringify(key);\n  return '\"' + key + '\"' === encodedKey ? key : encodedKey;\n}\n\nfunction describeValueForErrorMessage(value) {\n  switch (typeof value) {\n    case 'string':\n      {\n        return JSON.stringify(value.length <= 10 ? value : value.slice(0, 10) + '...');\n      }\n\n    case 'object':\n      {\n        if (isArray(value)) {\n          return '[...]';\n        }\n\n        if (value !== null && value.$$typeof === CLIENT_REFERENCE_TAG) {\n          return describeClientReference();\n        }\n\n        var name = objectName(value);\n\n        if (name === 'Object') {\n          return '{...}';\n        }\n\n        return name;\n      }\n\n    case 'function':\n      {\n        if (value.$$typeof === CLIENT_REFERENCE_TAG) {\n          return describeClientReference();\n        }\n\n        var _name = value.displayName || value.name;\n\n        return _name ? 'function ' + _name : 'function';\n      }\n\n    default:\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      return String(value);\n  }\n}\n\nfunction describeElementType(type) {\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeElementType(type.render);\n\n      case REACT_MEMO_TYPE:\n        return describeElementType(type.type);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeElementType(init(payload));\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar CLIENT_REFERENCE_TAG = Symbol.for('react.client.reference');\n\nfunction describeClientReference(ref) {\n  return 'client';\n}\n\nfunction describeObjectForErrorMessage(objectOrArray, expandedName) {\n  var objKind = objectName(objectOrArray);\n\n  if (objKind !== 'Object' && objKind !== 'Array') {\n    return objKind;\n  }\n\n  var str = '';\n  var start = -1;\n  var length = 0;\n\n  if (isArray(objectOrArray)) {\n    if (jsxChildrenParents.has(objectOrArray)) {\n      // Print JSX Children\n      var type = jsxChildrenParents.get(objectOrArray);\n      str = '<' + describeElementType(type) + '>';\n      var array = objectOrArray;\n\n      for (var i = 0; i < array.length; i++) {\n        var value = array[i];\n        var substr = void 0;\n\n        if (typeof value === 'string') {\n          substr = value;\n        } else if (typeof value === 'object' && value !== null) {\n          substr = '{' + describeObjectForErrorMessage(value) + '}';\n        } else {\n          substr = '{' + describeValueForErrorMessage(value) + '}';\n        }\n\n        if ('' + i === expandedName) {\n          start = str.length;\n          length = substr.length;\n          str += substr;\n        } else if (substr.length < 15 && str.length + substr.length < 40) {\n          str += substr;\n        } else {\n          str += '{...}';\n        }\n      }\n\n      str += '</' + describeElementType(type) + '>';\n    } else {\n      // Print Array\n      str = '[';\n      var _array = objectOrArray;\n\n      for (var _i = 0; _i < _array.length; _i++) {\n        if (_i > 0) {\n          str += ', ';\n        }\n\n        var _value = _array[_i];\n\n        var _substr = void 0;\n\n        if (typeof _value === 'object' && _value !== null) {\n          _substr = describeObjectForErrorMessage(_value);\n        } else {\n          _substr = describeValueForErrorMessage(_value);\n        }\n\n        if ('' + _i === expandedName) {\n          start = str.length;\n          length = _substr.length;\n          str += _substr;\n        } else if (_substr.length < 10 && str.length + _substr.length < 40) {\n          str += _substr;\n        } else {\n          str += '...';\n        }\n      }\n\n      str += ']';\n    }\n  } else {\n    if (objectOrArray.$$typeof === REACT_ELEMENT_TYPE) {\n      str = '<' + describeElementType(objectOrArray.type) + '/>';\n    } else if (objectOrArray.$$typeof === CLIENT_REFERENCE_TAG) {\n      return describeClientReference();\n    } else if (jsxPropsParents.has(objectOrArray)) {\n      // Print JSX\n      var _type = jsxPropsParents.get(objectOrArray);\n\n      str = '<' + (describeElementType(_type) || '...');\n      var object = objectOrArray;\n      var names = Object.keys(object);\n\n      for (var _i2 = 0; _i2 < names.length; _i2++) {\n        str += ' ';\n        var name = names[_i2];\n        str += describeKeyForErrorMessage(name) + '=';\n        var _value2 = object[name];\n\n        var _substr2 = void 0;\n\n        if (name === expandedName && typeof _value2 === 'object' && _value2 !== null) {\n          _substr2 = describeObjectForErrorMessage(_value2);\n        } else {\n          _substr2 = describeValueForErrorMessage(_value2);\n        }\n\n        if (typeof _value2 !== 'string') {\n          _substr2 = '{' + _substr2 + '}';\n        }\n\n        if (name === expandedName) {\n          start = str.length;\n          length = _substr2.length;\n          str += _substr2;\n        } else if (_substr2.length < 10 && str.length + _substr2.length < 40) {\n          str += _substr2;\n        } else {\n          str += '...';\n        }\n      }\n\n      str += '>';\n    } else {\n      // Print Object\n      str = '{';\n      var _object = objectOrArray;\n\n      var _names = Object.keys(_object);\n\n      for (var _i3 = 0; _i3 < _names.length; _i3++) {\n        if (_i3 > 0) {\n          str += ', ';\n        }\n\n        var _name2 = _names[_i3];\n        str += describeKeyForErrorMessage(_name2) + ': ';\n        var _value3 = _object[_name2];\n\n        var _substr3 = void 0;\n\n        if (typeof _value3 === 'object' && _value3 !== null) {\n          _substr3 = describeObjectForErrorMessage(_value3);\n        } else {\n          _substr3 = describeValueForErrorMessage(_value3);\n        }\n\n        if (_name2 === expandedName) {\n          start = str.length;\n          length = _substr3.length;\n          str += _substr3;\n        } else if (_substr3.length < 10 && str.length + _substr3.length < 40) {\n          str += _substr3;\n        } else {\n          str += '...';\n        }\n      }\n\n      str += '}';\n    }\n  }\n\n  if (expandedName === undefined) {\n    return str;\n  }\n\n  if (start > -1 && length > 0) {\n    var highlight = ' '.repeat(start) + '^'.repeat(length);\n    return '\\n  ' + str + '\\n  ' + highlight;\n  }\n\n  return '\\n  ' + str;\n}\n\nvar ObjectPrototype = Object.prototype;\nvar knownServerReferences = new WeakMap(); // Serializable values\n// Thenable<ReactServerValue>\n// function serializeByValueID(id: number): string {\n//   return '$' + id.toString(16);\n// }\n\nfunction serializePromiseID(id) {\n  return '$@' + id.toString(16);\n}\n\nfunction serializeServerReferenceID(id) {\n  return '$F' + id.toString(16);\n}\n\nfunction serializeSymbolReference(name) {\n  return '$S' + name;\n}\n\nfunction serializeFormDataReference(id) {\n  // Why K? F is \"Function\". D is \"Date\". What else?\n  return '$K' + id.toString(16);\n}\n\nfunction serializeNumber(number) {\n  if (Number.isFinite(number)) {\n    if (number === 0 && 1 / number === -Infinity) {\n      return '$-0';\n    } else {\n      return number;\n    }\n  } else {\n    if (number === Infinity) {\n      return '$Infinity';\n    } else if (number === -Infinity) {\n      return '$-Infinity';\n    } else {\n      return '$NaN';\n    }\n  }\n}\n\nfunction serializeUndefined() {\n  return '$undefined';\n}\n\nfunction serializeDateFromDateJSON(dateJSON) {\n  // JSON.stringify automatically calls Date.prototype.toJSON which calls toISOString.\n  // We need only tack on a $D prefix.\n  return '$D' + dateJSON;\n}\n\nfunction serializeBigInt(n) {\n  return '$n' + n.toString(10);\n}\n\nfunction serializeMapID(id) {\n  return '$Q' + id.toString(16);\n}\n\nfunction serializeSetID(id) {\n  return '$W' + id.toString(16);\n}\n\nfunction escapeStringValue(value) {\n  if (value[0] === '$') {\n    // We need to escape $ prefixed strings since we use those to encode\n    // references to IDs and as special symbol values.\n    return '$' + value;\n  } else {\n    return value;\n  }\n}\n\nfunction processReply(root, formFieldPrefix, resolve, reject) {\n  var nextPartId = 1;\n  var pendingParts = 0;\n  var formData = null;\n\n  function resolveToJSON(key, value) {\n    var parent = this; // Make sure that `parent[key]` wasn't JSONified before `value` was passed to us\n\n    {\n      // $FlowFixMe[incompatible-use]\n      var originalValue = parent[key];\n\n      if (typeof originalValue === 'object' && originalValue !== value && !(originalValue instanceof Date)) {\n        if (objectName(originalValue) !== 'Object') {\n          error('Only plain objects can be passed to Server Functions from the Client. ' + '%s objects are not supported.%s', objectName(originalValue), describeObjectForErrorMessage(parent, key));\n        } else {\n          error('Only plain objects can be passed to Server Functions from the Client. ' + 'Objects with toJSON methods are not supported. Convert it manually ' + 'to a simple value before passing it to props.%s', describeObjectForErrorMessage(parent, key));\n        }\n      }\n    }\n\n    if (value === null) {\n      return null;\n    }\n\n    if (typeof value === 'object') {\n      // $FlowFixMe[method-unbinding]\n      if (typeof value.then === 'function') {\n        // We assume that any object with a .then property is a \"Thenable\" type,\n        // or a Promise type. Either of which can be represented by a Promise.\n        if (formData === null) {\n          // Upgrade to use FormData to allow us to stream this value.\n          formData = new FormData();\n        }\n\n        pendingParts++;\n        var promiseId = nextPartId++;\n        var thenable = value;\n        thenable.then(function (partValue) {\n          var partJSON = JSON.stringify(partValue, resolveToJSON); // $FlowFixMe[incompatible-type] We know it's not null because we assigned it above.\n\n          var data = formData; // eslint-disable-next-line react-internal/safe-string-coercion\n\n          data.append(formFieldPrefix + promiseId, partJSON);\n          pendingParts--;\n\n          if (pendingParts === 0) {\n            resolve(data);\n          }\n        }, function (reason) {\n          // In the future we could consider serializing this as an error\n          // that throws on the server instead.\n          reject(reason);\n        });\n        return serializePromiseID(promiseId);\n      }\n\n      if (isArray(value)) {\n        // $FlowFixMe[incompatible-return]\n        return value;\n      } // TODO: Should we the Object.prototype.toString.call() to test for cross-realm objects?\n\n\n      if (value instanceof FormData) {\n        if (formData === null) {\n          // Upgrade to use FormData to allow us to use rich objects as its values.\n          formData = new FormData();\n        }\n\n        var data = formData;\n        var refId = nextPartId++; // Copy all the form fields with a prefix for this reference.\n        // These must come first in the form order because we assume that all the\n        // fields are available before this is referenced.\n\n        var prefix = formFieldPrefix + refId + '_'; // $FlowFixMe[prop-missing]: FormData has forEach.\n\n        value.forEach(function (originalValue, originalKey) {\n          data.append(prefix + originalKey, originalValue);\n        });\n        return serializeFormDataReference(refId);\n      }\n\n      if (value instanceof Map) {\n        var partJSON = JSON.stringify(Array.from(value), resolveToJSON);\n\n        if (formData === null) {\n          formData = new FormData();\n        }\n\n        var mapId = nextPartId++;\n        formData.append(formFieldPrefix + mapId, partJSON);\n        return serializeMapID(mapId);\n      }\n\n      if (value instanceof Set) {\n        var _partJSON = JSON.stringify(Array.from(value), resolveToJSON);\n\n        if (formData === null) {\n          formData = new FormData();\n        }\n\n        var setId = nextPartId++;\n        formData.append(formFieldPrefix + setId, _partJSON);\n        return serializeSetID(setId);\n      }\n\n      var iteratorFn = getIteratorFn(value);\n\n      if (iteratorFn) {\n        return Array.from(value);\n      } // Verify that this is a simple plain object.\n\n\n      var proto = getPrototypeOf(value);\n\n      if (proto !== ObjectPrototype && (proto === null || getPrototypeOf(proto) !== null)) {\n        throw new Error('Only plain objects, and a few built-ins, can be passed to Server Actions. ' + 'Classes or null prototypes are not supported.');\n      }\n\n      {\n        if (value.$$typeof === REACT_ELEMENT_TYPE) {\n          error('React Element cannot be passed to Server Functions from the Client.%s', describeObjectForErrorMessage(parent, key));\n        } else if (value.$$typeof === REACT_LAZY_TYPE) {\n          error('React Lazy cannot be passed to Server Functions from the Client.%s', describeObjectForErrorMessage(parent, key));\n        } else if (value.$$typeof === (REACT_PROVIDER_TYPE)) {\n          error('React Context Providers cannot be passed to Server Functions from the Client.%s', describeObjectForErrorMessage(parent, key));\n        } else if (objectName(value) !== 'Object') {\n          error('Only plain objects can be passed to Server Functions from the Client. ' + '%s objects are not supported.%s', objectName(value), describeObjectForErrorMessage(parent, key));\n        } else if (!isSimpleObject(value)) {\n          error('Only plain objects can be passed to Server Functions from the Client. ' + 'Classes or other objects with methods are not supported.%s', describeObjectForErrorMessage(parent, key));\n        } else if (Object.getOwnPropertySymbols) {\n          var symbols = Object.getOwnPropertySymbols(value);\n\n          if (symbols.length > 0) {\n            error('Only plain objects can be passed to Server Functions from the Client. ' + 'Objects with symbol properties like %s are not supported.%s', symbols[0].description, describeObjectForErrorMessage(parent, key));\n          }\n        }\n      } // $FlowFixMe[incompatible-return]\n\n\n      return value;\n    }\n\n    if (typeof value === 'string') {\n      // TODO: Maybe too clever. If we support URL there's no similar trick.\n      if (value[value.length - 1] === 'Z') {\n        // Possibly a Date, whose toJSON automatically calls toISOString\n        // $FlowFixMe[incompatible-use]\n        var _originalValue = parent[key];\n\n        if (_originalValue instanceof Date) {\n          return serializeDateFromDateJSON(value);\n        }\n      }\n\n      return escapeStringValue(value);\n    }\n\n    if (typeof value === 'boolean') {\n      return value;\n    }\n\n    if (typeof value === 'number') {\n      return serializeNumber(value);\n    }\n\n    if (typeof value === 'undefined') {\n      return serializeUndefined();\n    }\n\n    if (typeof value === 'function') {\n      var metaData = knownServerReferences.get(value);\n\n      if (metaData !== undefined) {\n        var metaDataJSON = JSON.stringify(metaData, resolveToJSON);\n\n        if (formData === null) {\n          // Upgrade to use FormData to allow us to stream this value.\n          formData = new FormData();\n        } // The reference to this function came from the same client so we can pass it back.\n\n\n        var _refId = nextPartId++; // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n        formData.set(formFieldPrefix + _refId, metaDataJSON);\n        return serializeServerReferenceID(_refId);\n      }\n\n      throw new Error('Client Functions cannot be passed directly to Server Functions. ' + 'Only Functions passed from the Server can be passed back again.');\n    }\n\n    if (typeof value === 'symbol') {\n      // $FlowFixMe[incompatible-type] `description` might be undefined\n      var name = value.description;\n\n      if (Symbol.for(name) !== value) {\n        throw new Error('Only global symbols received from Symbol.for(...) can be passed to Server Functions. ' + (\"The symbol Symbol.for(\" + // $FlowFixMe[incompatible-type] `description` might be undefined\n        value.description + \") cannot be found among global symbols.\"));\n      }\n\n      return serializeSymbolReference(name);\n    }\n\n    if (typeof value === 'bigint') {\n      return serializeBigInt(value);\n    }\n\n    throw new Error(\"Type \" + typeof value + \" is not supported as an argument to a Server Function.\");\n  } // $FlowFixMe[incompatible-type] it's not going to be undefined because we'll encode it.\n\n\n  var json = JSON.stringify(root, resolveToJSON);\n\n  if (formData === null) {\n    // If it's a simple data structure, we just use plain JSON.\n    resolve(json);\n  } else {\n    // Otherwise, we use FormData to let us stream in the result.\n    formData.set(formFieldPrefix + '0', json);\n\n    if (pendingParts === 0) {\n      // $FlowFixMe[incompatible-call] this has already been refined.\n      resolve(formData);\n    }\n  }\n}\n\nfunction registerServerReference(proxy, reference, encodeFormAction) {\n\n  knownServerReferences.set(proxy, reference);\n} // $FlowFixMe[method-unbinding]\n\nfunction createServerReference(id, callServer, encodeFormAction) {\n  var proxy = function () {\n    // $FlowFixMe[method-unbinding]\n    var args = Array.prototype.slice.call(arguments);\n    return callServer(id, args);\n  };\n\n  registerServerReference(proxy, {\n    id: id,\n    bound: null\n  });\n  return proxy;\n}\n\nvar ROW_ID = 0;\nvar ROW_TAG = 1;\nvar ROW_LENGTH = 2;\nvar ROW_CHUNK_BY_NEWLINE = 3;\nvar ROW_CHUNK_BY_LENGTH = 4;\nvar PENDING = 'pending';\nvar BLOCKED = 'blocked';\nvar CYCLIC = 'cyclic';\nvar RESOLVED_MODEL = 'resolved_model';\nvar RESOLVED_MODULE = 'resolved_module';\nvar INITIALIZED = 'fulfilled';\nvar ERRORED = 'rejected'; // $FlowFixMe[missing-this-annot]\n\nfunction Chunk(status, value, reason, response) {\n  this.status = status;\n  this.value = value;\n  this.reason = reason;\n  this._response = response;\n\n  {\n    this._debugInfo = null;\n  }\n} // We subclass Promise.prototype so that we get other methods like .catch\n\n\nChunk.prototype = Object.create(Promise.prototype); // TODO: This doesn't return a new Promise chain unlike the real .then\n\nChunk.prototype.then = function (resolve, reject) {\n  var chunk = this; // If we have resolved content, we try to initialize it first which\n  // might put us back into one of the other states.\n\n  switch (chunk.status) {\n    case RESOLVED_MODEL:\n      initializeModelChunk(chunk);\n      break;\n\n    case RESOLVED_MODULE:\n      initializeModuleChunk(chunk);\n      break;\n  } // The status might have changed after initialization.\n\n\n  switch (chunk.status) {\n    case INITIALIZED:\n      resolve(chunk.value);\n      break;\n\n    case PENDING:\n    case BLOCKED:\n    case CYCLIC:\n      if (resolve) {\n        if (chunk.value === null) {\n          chunk.value = [];\n        }\n\n        chunk.value.push(resolve);\n      }\n\n      if (reject) {\n        if (chunk.reason === null) {\n          chunk.reason = [];\n        }\n\n        chunk.reason.push(reject);\n      }\n\n      break;\n\n    default:\n      reject(chunk.reason);\n      break;\n  }\n};\n\nfunction readChunk(chunk) {\n  // If we have resolved content, we try to initialize it first which\n  // might put us back into one of the other states.\n  switch (chunk.status) {\n    case RESOLVED_MODEL:\n      initializeModelChunk(chunk);\n      break;\n\n    case RESOLVED_MODULE:\n      initializeModuleChunk(chunk);\n      break;\n  } // The status might have changed after initialization.\n\n\n  switch (chunk.status) {\n    case INITIALIZED:\n      return chunk.value;\n\n    case PENDING:\n    case BLOCKED:\n    case CYCLIC:\n      // eslint-disable-next-line no-throw-literal\n      throw chunk;\n\n    default:\n      throw chunk.reason;\n  }\n}\n\nfunction getRoot(response) {\n  var chunk = getChunk(response, 0);\n  return chunk;\n}\n\nfunction createPendingChunk(response) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(PENDING, null, null, response);\n}\n\nfunction createBlockedChunk(response) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(BLOCKED, null, null, response);\n}\n\nfunction createErrorChunk(response, error) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(ERRORED, null, error, response);\n}\n\nfunction wakeChunk(listeners, value) {\n  for (var i = 0; i < listeners.length; i++) {\n    var listener = listeners[i];\n    listener(value);\n  }\n}\n\nfunction wakeChunkIfInitialized(chunk, resolveListeners, rejectListeners) {\n  switch (chunk.status) {\n    case INITIALIZED:\n      wakeChunk(resolveListeners, chunk.value);\n      break;\n\n    case PENDING:\n    case BLOCKED:\n    case CYCLIC:\n      chunk.value = resolveListeners;\n      chunk.reason = rejectListeners;\n      break;\n\n    case ERRORED:\n      if (rejectListeners) {\n        wakeChunk(rejectListeners, chunk.reason);\n      }\n\n      break;\n  }\n}\n\nfunction triggerErrorOnChunk(chunk, error) {\n  if (chunk.status !== PENDING && chunk.status !== BLOCKED) {\n    // We already resolved. We didn't expect to see this.\n    return;\n  }\n\n  var listeners = chunk.reason;\n  var erroredChunk = chunk;\n  erroredChunk.status = ERRORED;\n  erroredChunk.reason = error;\n\n  if (listeners !== null) {\n    wakeChunk(listeners, error);\n  }\n}\n\nfunction createResolvedModelChunk(response, value) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(RESOLVED_MODEL, value, null, response);\n}\n\nfunction createResolvedModuleChunk(response, value) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(RESOLVED_MODULE, value, null, response);\n}\n\nfunction createInitializedTextChunk(response, value) {\n  // $FlowFixMe[invalid-constructor] Flow doesn't support functions as constructors\n  return new Chunk(INITIALIZED, value, null, response);\n}\n\nfunction resolveModelChunk(chunk, value) {\n  if (chunk.status !== PENDING) {\n    // We already resolved. We didn't expect to see this.\n    return;\n  }\n\n  var resolveListeners = chunk.value;\n  var rejectListeners = chunk.reason;\n  var resolvedChunk = chunk;\n  resolvedChunk.status = RESOLVED_MODEL;\n  resolvedChunk.value = value;\n\n  if (resolveListeners !== null) {\n    // This is unfortunate that we're reading this eagerly if\n    // we already have listeners attached since they might no\n    // longer be rendered or might not be the highest pri.\n    initializeModelChunk(resolvedChunk); // The status might have changed after initialization.\n\n    wakeChunkIfInitialized(chunk, resolveListeners, rejectListeners);\n  }\n}\n\nfunction resolveModuleChunk(chunk, value) {\n  if (chunk.status !== PENDING && chunk.status !== BLOCKED) {\n    // We already resolved. We didn't expect to see this.\n    return;\n  }\n\n  var resolveListeners = chunk.value;\n  var rejectListeners = chunk.reason;\n  var resolvedChunk = chunk;\n  resolvedChunk.status = RESOLVED_MODULE;\n  resolvedChunk.value = value;\n\n  if (resolveListeners !== null) {\n    initializeModuleChunk(resolvedChunk);\n    wakeChunkIfInitialized(chunk, resolveListeners, rejectListeners);\n  }\n}\n\nvar initializingChunk = null;\nvar initializingChunkBlockedModel = null;\n\nfunction initializeModelChunk(chunk) {\n  var prevChunk = initializingChunk;\n  var prevBlocked = initializingChunkBlockedModel;\n  initializingChunk = chunk;\n  initializingChunkBlockedModel = null;\n  var resolvedModel = chunk.value; // We go to the CYCLIC state until we've fully resolved this.\n  // We do this before parsing in case we try to initialize the same chunk\n  // while parsing the model. Such as in a cyclic reference.\n\n  var cyclicChunk = chunk;\n  cyclicChunk.status = CYCLIC;\n  cyclicChunk.value = null;\n  cyclicChunk.reason = null;\n\n  try {\n    var value = parseModel(chunk._response, resolvedModel);\n\n    if (initializingChunkBlockedModel !== null && initializingChunkBlockedModel.deps > 0) {\n      initializingChunkBlockedModel.value = value; // We discovered new dependencies on modules that are not yet resolved.\n      // We have to go the BLOCKED state until they're resolved.\n\n      var blockedChunk = chunk;\n      blockedChunk.status = BLOCKED;\n      blockedChunk.value = null;\n      blockedChunk.reason = null;\n    } else {\n      var resolveListeners = cyclicChunk.value;\n      var initializedChunk = chunk;\n      initializedChunk.status = INITIALIZED;\n      initializedChunk.value = value;\n\n      if (resolveListeners !== null) {\n        wakeChunk(resolveListeners, value);\n      }\n    }\n  } catch (error) {\n    var erroredChunk = chunk;\n    erroredChunk.status = ERRORED;\n    erroredChunk.reason = error;\n  } finally {\n    initializingChunk = prevChunk;\n    initializingChunkBlockedModel = prevBlocked;\n  }\n}\n\nfunction initializeModuleChunk(chunk) {\n  try {\n    var value = requireModule(chunk.value);\n    var initializedChunk = chunk;\n    initializedChunk.status = INITIALIZED;\n    initializedChunk.value = value;\n  } catch (error) {\n    var erroredChunk = chunk;\n    erroredChunk.status = ERRORED;\n    erroredChunk.reason = error;\n  }\n} // Report that any missing chunks in the model is now going to throw this\n// error upon read. Also notify any pending promises.\n\n\nfunction reportGlobalError(response, error) {\n  response._chunks.forEach(function (chunk) {\n    // If this chunk was already resolved or errored, it won't\n    // trigger an error but if it wasn't then we need to\n    // because we won't be getting any new data to resolve it.\n    if (chunk.status === PENDING) {\n      triggerErrorOnChunk(chunk, error);\n    }\n  });\n}\n\nfunction createElement(type, key, props) {\n  var element;\n\n  {\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      type: type,\n      key: key,\n      ref: null,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: null\n    };\n  }\n\n  {\n    // We don't really need to add any of these but keeping them for good measure.\n    // Unfortunately, _store is enumerable in jest matchers so for equality to\n    // work, I need to keep it or make _store non-enumerable in the other file.\n    element._store = {};\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: true // This element has already been validated on the server.\n\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n  }\n\n  return element;\n}\n\nfunction createLazyChunkWrapper(chunk) {\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: chunk,\n    _init: readChunk\n  };\n\n  {\n    // Ensure we have a live array to track future debug info.\n    var chunkDebugInfo = chunk._debugInfo || (chunk._debugInfo = []);\n    lazyType._debugInfo = chunkDebugInfo;\n  }\n\n  return lazyType;\n}\n\nfunction getChunk(response, id) {\n  var chunks = response._chunks;\n  var chunk = chunks.get(id);\n\n  if (!chunk) {\n    chunk = createPendingChunk(response);\n    chunks.set(id, chunk);\n  }\n\n  return chunk;\n}\n\nfunction createModelResolver(chunk, parentObject, key, cyclic) {\n  var blocked;\n\n  if (initializingChunkBlockedModel) {\n    blocked = initializingChunkBlockedModel;\n\n    if (!cyclic) {\n      blocked.deps++;\n    }\n  } else {\n    blocked = initializingChunkBlockedModel = {\n      deps: cyclic ? 0 : 1,\n      value: null\n    };\n  }\n\n  return function (value) {\n    parentObject[key] = value;\n    blocked.deps--;\n\n    if (blocked.deps === 0) {\n      if (chunk.status !== BLOCKED) {\n        return;\n      }\n\n      var resolveListeners = chunk.value;\n      var initializedChunk = chunk;\n      initializedChunk.status = INITIALIZED;\n      initializedChunk.value = blocked.value;\n\n      if (resolveListeners !== null) {\n        wakeChunk(resolveListeners, blocked.value);\n      }\n    }\n  };\n}\n\nfunction createModelReject(chunk) {\n  return function (error) {\n    return triggerErrorOnChunk(chunk, error);\n  };\n}\n\nfunction createServerReferenceProxy(response, metaData) {\n  var callServer = response._callServer;\n\n  var proxy = function () {\n    // $FlowFixMe[method-unbinding]\n    var args = Array.prototype.slice.call(arguments);\n    var p = metaData.bound;\n\n    if (!p) {\n      return callServer(metaData.id, args);\n    }\n\n    if (p.status === INITIALIZED) {\n      var bound = p.value;\n      return callServer(metaData.id, bound.concat(args));\n    } // Since this is a fake Promise whose .then doesn't chain, we have to wrap it.\n    // TODO: Remove the wrapper once that's fixed.\n\n\n    return Promise.resolve(p).then(function (bound) {\n      return callServer(metaData.id, bound.concat(args));\n    });\n  };\n\n  registerServerReference(proxy, metaData);\n  return proxy;\n}\n\nfunction getOutlinedModel(response, id) {\n  var chunk = getChunk(response, id);\n\n  switch (chunk.status) {\n    case RESOLVED_MODEL:\n      initializeModelChunk(chunk);\n      break;\n  } // The status might have changed after initialization.\n\n\n  switch (chunk.status) {\n    case INITIALIZED:\n      {\n        return chunk.value;\n      }\n    // We always encode it first in the stream so it won't be pending.\n\n    default:\n      throw chunk.reason;\n  }\n}\n\nfunction parseModelString(response, parentObject, key, value) {\n  if (value[0] === '$') {\n    if (value === '$') {\n      // A very common symbol.\n      return REACT_ELEMENT_TYPE;\n    }\n\n    switch (value[1]) {\n      case '$':\n        {\n          // This was an escaped string value.\n          return value.slice(1);\n        }\n\n      case 'L':\n        {\n          // Lazy node\n          var id = parseInt(value.slice(2), 16);\n          var chunk = getChunk(response, id); // We create a React.lazy wrapper around any lazy values.\n          // When passed into React, we'll know how to suspend on this.\n\n          return createLazyChunkWrapper(chunk);\n        }\n\n      case '@':\n        {\n          // Promise\n          if (value.length === 2) {\n            // Infinite promise that never resolves.\n            return new Promise(function () {});\n          }\n\n          var _id = parseInt(value.slice(2), 16);\n\n          var _chunk = getChunk(response, _id);\n\n          return _chunk;\n        }\n\n      case 'S':\n        {\n          // Symbol\n          return Symbol.for(value.slice(2));\n        }\n\n      case 'F':\n        {\n          // Server Reference\n          var _id2 = parseInt(value.slice(2), 16);\n\n          var metadata = getOutlinedModel(response, _id2);\n          return createServerReferenceProxy(response, metadata);\n        }\n\n      case 'Q':\n        {\n          // Map\n          var _id3 = parseInt(value.slice(2), 16);\n\n          var data = getOutlinedModel(response, _id3);\n          return new Map(data);\n        }\n\n      case 'W':\n        {\n          // Set\n          var _id4 = parseInt(value.slice(2), 16);\n\n          var _data = getOutlinedModel(response, _id4);\n\n          return new Set(_data);\n        }\n\n      case 'I':\n        {\n          // $Infinity\n          return Infinity;\n        }\n\n      case '-':\n        {\n          // $-0 or $-Infinity\n          if (value === '$-0') {\n            return -0;\n          } else {\n            return -Infinity;\n          }\n        }\n\n      case 'N':\n        {\n          // $NaN\n          return NaN;\n        }\n\n      case 'u':\n        {\n          // matches \"$undefined\"\n          // Special encoding for `undefined` which can't be serialized as JSON otherwise.\n          return undefined;\n        }\n\n      case 'D':\n        {\n          // Date\n          return new Date(Date.parse(value.slice(2)));\n        }\n\n      case 'n':\n        {\n          // BigInt\n          return BigInt(value.slice(2));\n        }\n\n      case 'E':\n        {\n          {\n            // In DEV mode we allow indirect eval to produce functions for logging.\n            // This should not compile to eval() because then it has local scope access.\n            try {\n              // eslint-disable-next-line no-eval\n              return (0, eval)(value.slice(2));\n            } catch (x) {\n              // We currently use this to express functions so we fail parsing it,\n              // let's just return a blank function as a place holder.\n              return function () {};\n            }\n          } // Fallthrough\n\n        }\n\n      default:\n        {\n          // We assume that anything else is a reference ID.\n          var _id5 = parseInt(value.slice(1), 16);\n\n          var _chunk2 = getChunk(response, _id5);\n\n          switch (_chunk2.status) {\n            case RESOLVED_MODEL:\n              initializeModelChunk(_chunk2);\n              break;\n\n            case RESOLVED_MODULE:\n              initializeModuleChunk(_chunk2);\n              break;\n          } // The status might have changed after initialization.\n\n\n          switch (_chunk2.status) {\n            case INITIALIZED:\n              var chunkValue = _chunk2.value;\n\n              if (_chunk2._debugInfo) {\n                // If we have a direct reference to an object that was rendered by a synchronous\n                // server component, it might have some debug info about how it was rendered.\n                // We forward this to the underlying object. This might be a React Element or\n                // an Array fragment.\n                // If this was a string / number return value we lose the debug info. We choose\n                // that tradeoff to allow sync server components to return plain values and not\n                // use them as React Nodes necessarily. We could otherwise wrap them in a Lazy.\n                if (typeof chunkValue === 'object' && chunkValue !== null && (Array.isArray(chunkValue) || chunkValue.$$typeof === REACT_ELEMENT_TYPE) && !chunkValue._debugInfo) {\n                  // We should maybe use a unique symbol for arrays but this is a React owned array.\n                  // $FlowFixMe[prop-missing]: This should be added to elements.\n                  Object.defineProperty(chunkValue, '_debugInfo', {\n                    configurable: false,\n                    enumerable: false,\n                    writable: true,\n                    value: _chunk2._debugInfo\n                  });\n                }\n              }\n\n              return chunkValue;\n\n            case PENDING:\n            case BLOCKED:\n            case CYCLIC:\n              var parentChunk = initializingChunk;\n\n              _chunk2.then(createModelResolver(parentChunk, parentObject, key, _chunk2.status === CYCLIC), createModelReject(parentChunk));\n\n              return null;\n\n            default:\n              throw _chunk2.reason;\n          }\n        }\n    }\n  }\n\n  return value;\n}\n\nfunction parseModelTuple(response, value) {\n  var tuple = value;\n\n  if (tuple[0] === REACT_ELEMENT_TYPE) {\n    // TODO: Consider having React just directly accept these arrays as elements.\n    // Or even change the ReactElement type to be an array.\n    return createElement(tuple[1], tuple[2], tuple[3]);\n  }\n\n  return value;\n}\n\nfunction missingCall() {\n  throw new Error('Trying to call a function from \"use server\" but the callServer option ' + 'was not implemented in your router runtime.');\n}\n\nfunction createResponse(bundlerConfig, moduleLoading, callServer, encodeFormAction, nonce) {\n  var chunks = new Map();\n  var response = {\n    _bundlerConfig: bundlerConfig,\n    _moduleLoading: moduleLoading,\n    _callServer: callServer !== undefined ? callServer : missingCall,\n    _encodeFormAction: encodeFormAction,\n    _nonce: nonce,\n    _chunks: chunks,\n    _stringDecoder: createStringDecoder(),\n    _fromJSON: null,\n    _rowState: 0,\n    _rowID: 0,\n    _rowTag: 0,\n    _rowLength: 0,\n    _buffer: []\n  }; // Don't inline this call because it causes closure to outline the call above.\n\n  response._fromJSON = createFromJSONCallback(response);\n  return response;\n}\n\nfunction resolveModel(response, id, model) {\n  var chunks = response._chunks;\n  var chunk = chunks.get(id);\n\n  if (!chunk) {\n    chunks.set(id, createResolvedModelChunk(response, model));\n  } else {\n    resolveModelChunk(chunk, model);\n  }\n}\n\nfunction resolveText(response, id, text) {\n  var chunks = response._chunks; // We assume that we always reference large strings after they've been\n  // emitted.\n\n  chunks.set(id, createInitializedTextChunk(response, text));\n}\n\nfunction resolveModule(response, id, model) {\n  var chunks = response._chunks;\n  var chunk = chunks.get(id);\n  var clientReferenceMetadata = parseModel(response, model);\n  var clientReference = resolveClientReference(response._bundlerConfig, clientReferenceMetadata);\n  // For now we preload all modules as early as possible since it's likely\n  // that we'll need them.\n\n  var promise = preloadModule(clientReference);\n\n  if (promise) {\n    var blockedChunk;\n\n    if (!chunk) {\n      // Technically, we should just treat promise as the chunk in this\n      // case. Because it'll just behave as any other promise.\n      blockedChunk = createBlockedChunk(response);\n      chunks.set(id, blockedChunk);\n    } else {\n      // This can't actually happen because we don't have any forward\n      // references to modules.\n      blockedChunk = chunk;\n      blockedChunk.status = BLOCKED;\n    }\n\n    promise.then(function () {\n      return resolveModuleChunk(blockedChunk, clientReference);\n    }, function (error) {\n      return triggerErrorOnChunk(blockedChunk, error);\n    });\n  } else {\n    if (!chunk) {\n      chunks.set(id, createResolvedModuleChunk(response, clientReference));\n    } else {\n      // This can't actually happen because we don't have any forward\n      // references to modules.\n      resolveModuleChunk(chunk, clientReference);\n    }\n  }\n}\n\nfunction resolveErrorDev(response, id, digest, message, stack) {\n\n\n  var error = new Error(message || 'An error occurred in the Server Components render but no message was provided');\n  error.stack = stack;\n  error.digest = digest;\n  var errorWithDigest = error;\n  var chunks = response._chunks;\n  var chunk = chunks.get(id);\n\n  if (!chunk) {\n    chunks.set(id, createErrorChunk(response, errorWithDigest));\n  } else {\n    triggerErrorOnChunk(chunk, errorWithDigest);\n  }\n}\n\nfunction resolveHint(response, code, model) {\n  var hintModel = parseModel(response, model);\n  dispatchHint(code, hintModel);\n}\n\nfunction resolveDebugInfo(response, id, debugInfo) {\n\n  var chunk = getChunk(response, id);\n  var chunkDebugInfo = chunk._debugInfo || (chunk._debugInfo = []);\n  chunkDebugInfo.push(debugInfo);\n}\n\nfunction resolveConsoleEntry(response, value) {\n\n  var payload = parseModel(response, value);\n  var methodName = payload[0]; // TODO: Restore the fake stack before logging.\n  // const stackTrace = payload[1];\n\n  var env = payload[2];\n  var args = payload.slice(3);\n  printToConsole(methodName, args, env);\n}\n\nfunction processFullRow(response, id, tag, buffer, chunk) {\n\n  var stringDecoder = response._stringDecoder;\n  var row = '';\n\n  for (var i = 0; i < buffer.length; i++) {\n    row += readPartialStringChunk(stringDecoder, buffer[i]);\n  }\n\n  row += readFinalStringChunk(stringDecoder, chunk);\n\n  switch (tag) {\n    case 73\n    /* \"I\" */\n    :\n      {\n        resolveModule(response, id, row);\n        return;\n      }\n\n    case 72\n    /* \"H\" */\n    :\n      {\n        var code = row[0];\n        resolveHint(response, code, row.slice(1));\n        return;\n      }\n\n    case 69\n    /* \"E\" */\n    :\n      {\n        var errorInfo = JSON.parse(row);\n\n        {\n          resolveErrorDev(response, id, errorInfo.digest, errorInfo.message, errorInfo.stack);\n        }\n\n        return;\n      }\n\n    case 84\n    /* \"T\" */\n    :\n      {\n        resolveText(response, id, row);\n        return;\n      }\n\n    case 68\n    /* \"D\" */\n    :\n      {\n        {\n          var debugInfo = JSON.parse(row);\n          resolveDebugInfo(response, id, debugInfo);\n          return;\n        } // Fallthrough to share the error with Console entries.\n\n      }\n\n    case 87\n    /* \"W\" */\n    :\n      {\n        {\n          resolveConsoleEntry(response, row);\n          return;\n        }\n      }\n\n    case 80\n    /* \"P\" */\n    :\n    // Fallthrough\n\n    default:\n      /* \"\"\" \"{\" \"[\" \"t\" \"f\" \"n\" \"0\" - \"9\" */\n      {\n        // We assume anything else is JSON.\n        resolveModel(response, id, row);\n        return;\n      }\n  }\n}\n\nfunction processBinaryChunk(response, chunk) {\n  var i = 0;\n  var rowState = response._rowState;\n  var rowID = response._rowID;\n  var rowTag = response._rowTag;\n  var rowLength = response._rowLength;\n  var buffer = response._buffer;\n  var chunkLength = chunk.length;\n\n  while (i < chunkLength) {\n    var lastIdx = -1;\n\n    switch (rowState) {\n      case ROW_ID:\n        {\n          var byte = chunk[i++];\n\n          if (byte === 58\n          /* \":\" */\n          ) {\n              // Finished the rowID, next we'll parse the tag.\n              rowState = ROW_TAG;\n            } else {\n            rowID = rowID << 4 | (byte > 96 ? byte - 87 : byte - 48);\n          }\n\n          continue;\n        }\n\n      case ROW_TAG:\n        {\n          var resolvedRowTag = chunk[i];\n\n          if (resolvedRowTag === 84\n          /* \"T\" */\n          || enableBinaryFlight \n          /* \"V\" */\n          ) {\n              rowTag = resolvedRowTag;\n              rowState = ROW_LENGTH;\n              i++;\n            } else if (resolvedRowTag > 64 && resolvedRowTag < 91\n          /* \"A\"-\"Z\" */\n          ) {\n              rowTag = resolvedRowTag;\n              rowState = ROW_CHUNK_BY_NEWLINE;\n              i++;\n            } else {\n            rowTag = 0;\n            rowState = ROW_CHUNK_BY_NEWLINE; // This was an unknown tag so it was probably part of the data.\n          }\n\n          continue;\n        }\n\n      case ROW_LENGTH:\n        {\n          var _byte = chunk[i++];\n\n          if (_byte === 44\n          /* \",\" */\n          ) {\n              // Finished the rowLength, next we'll buffer up to that length.\n              rowState = ROW_CHUNK_BY_LENGTH;\n            } else {\n            rowLength = rowLength << 4 | (_byte > 96 ? _byte - 87 : _byte - 48);\n          }\n\n          continue;\n        }\n\n      case ROW_CHUNK_BY_NEWLINE:\n        {\n          // We're looking for a newline\n          lastIdx = chunk.indexOf(10\n          /* \"\\n\" */\n          , i);\n          break;\n        }\n\n      case ROW_CHUNK_BY_LENGTH:\n        {\n          // We're looking for the remaining byte length\n          lastIdx = i + rowLength;\n\n          if (lastIdx > chunk.length) {\n            lastIdx = -1;\n          }\n\n          break;\n        }\n    }\n\n    var offset = chunk.byteOffset + i;\n\n    if (lastIdx > -1) {\n      // We found the last chunk of the row\n      var length = lastIdx - i;\n      var lastChunk = new Uint8Array(chunk.buffer, offset, length);\n      processFullRow(response, rowID, rowTag, buffer, lastChunk); // Reset state machine for a new row\n\n      i = lastIdx;\n\n      if (rowState === ROW_CHUNK_BY_NEWLINE) {\n        // If we're trailing by a newline we need to skip it.\n        i++;\n      }\n\n      rowState = ROW_ID;\n      rowTag = 0;\n      rowID = 0;\n      rowLength = 0;\n      buffer.length = 0;\n    } else {\n      // The rest of this row is in a future chunk. We stash the rest of the\n      // current chunk until we can process the full row.\n      var _length = chunk.byteLength - i;\n\n      var remainingSlice = new Uint8Array(chunk.buffer, offset, _length);\n      buffer.push(remainingSlice); // Update how many bytes we're still waiting for. If we're looking for\n      // a newline, this doesn't hurt since we'll just ignore it.\n\n      rowLength -= remainingSlice.byteLength;\n      break;\n    }\n  }\n\n  response._rowState = rowState;\n  response._rowID = rowID;\n  response._rowTag = rowTag;\n  response._rowLength = rowLength;\n}\n\nfunction parseModel(response, json) {\n  return JSON.parse(json, response._fromJSON);\n}\n\nfunction createFromJSONCallback(response) {\n  // $FlowFixMe[missing-this-annot]\n  return function (key, value) {\n    if (typeof value === 'string') {\n      // We can't use .bind here because we need the \"this\" value.\n      return parseModelString(response, this, key, value);\n    }\n\n    if (typeof value === 'object' && value !== null) {\n      return parseModelTuple(response, value);\n    }\n\n    return value;\n  };\n}\n\nfunction close(response) {\n  // In case there are any remaining unresolved chunks, they won't\n  // be resolved now. So we need to issue an error to those.\n  // Ideally we should be able to early bail out if we kept a\n  // ref count of pending chunks.\n  reportGlobalError(response, new Error('Connection closed.'));\n}\n\nfunction createResponseFromOptions(options) {\n  return createResponse(null, null, options && options.callServer ? options.callServer : undefined, undefined, // encodeFormAction\n  undefined // nonce\n  );\n}\n\nfunction startReadingFromStream(response, stream) {\n  var reader = stream.getReader();\n\n  function progress(_ref) {\n    var done = _ref.done,\n        value = _ref.value;\n\n    if (done) {\n      close(response);\n      return;\n    }\n\n    var buffer = value;\n    processBinaryChunk(response, buffer);\n    return reader.read().then(progress).catch(error);\n  }\n\n  function error(e) {\n    reportGlobalError(response, e);\n  }\n\n  reader.read().then(progress).catch(error);\n}\n\nfunction createFromReadableStream(stream, options) {\n  var response = createResponseFromOptions(options);\n  startReadingFromStream(response, stream);\n  return getRoot(response);\n}\n\nfunction createFromFetch(promiseForResponse, options) {\n  var response = createResponseFromOptions(options);\n  promiseForResponse.then(function (r) {\n    startReadingFromStream(response, r.body);\n  }, function (e) {\n    reportGlobalError(response, e);\n  });\n  return getRoot(response);\n}\n\nfunction encodeReply(value)\n/* We don't use URLSearchParams yet but maybe */\n{\n  return new Promise(function (resolve, reject) {\n    processReply(value, '', resolve, reject);\n  });\n}\n\nexports.createFromFetch = createFromFetch;\nexports.createFromReadableStream = createFromReadableStream;\nexports.createServerReference = createServerReference;\nexports.encodeReply = encodeReply;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js\n"));

/***/ })

}]);