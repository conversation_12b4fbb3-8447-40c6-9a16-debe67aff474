/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_compiled_r"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var enableSchedulerDebugging = false;\nvar enableProfiling = false;\nvar frameYieldMs = 5;\nvar userBlockingPriorityTimeout = 250;\nvar normalPriorityTimeout = 5000;\nvar lowPriorityTimeout = 10000;\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  siftUp(heap, node, index);\n}\nfunction peek(heap) {\n  return heap.length === 0 ? null : heap[0];\n}\nfunction pop(heap) {\n  if (heap.length === 0) {\n    return null;\n  }\n\n  var first = heap[0];\n  var last = heap.pop();\n\n  if (last !== first) {\n    heap[0] = last;\n    siftDown(heap, last, 0);\n  }\n\n  return first;\n}\n\nfunction siftUp(heap, node, i) {\n  var index = i;\n\n  while (index > 0) {\n    var parentIndex = index - 1 >>> 1;\n    var parent = heap[parentIndex];\n\n    if (compare(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node;\n      heap[index] = parent;\n      index = parentIndex;\n    } else {\n      // The parent is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction siftDown(heap, node, i) {\n  var index = i;\n  var length = heap.length;\n  var halfLength = length >>> 1;\n\n  while (index < halfLength) {\n    var leftIndex = (index + 1) * 2 - 1;\n    var left = heap[leftIndex];\n    var rightIndex = leftIndex + 1;\n    var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n    if (compare(left, node) < 0) {\n      if (rightIndex < length && compare(right, left) < 0) {\n        heap[index] = right;\n        heap[rightIndex] = node;\n        index = rightIndex;\n      } else {\n        heap[index] = left;\n        heap[leftIndex] = node;\n        index = leftIndex;\n      }\n    } else if (rightIndex < length && compare(right, node) < 0) {\n      heap[index] = right;\n      heap[rightIndex] = node;\n      index = rightIndex;\n    } else {\n      // Neither child is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction compare(a, b) {\n  // Compare sort index first, then task id.\n  var diff = a.sortIndex - b.sortIndex;\n  return diff !== 0 ? diff : a.id - b.id;\n}\n\n// TODO: Use symbols?\nvar ImmediatePriority = 1;\nvar UserBlockingPriority = 2;\nvar NormalPriority = 3;\nvar LowPriority = 4;\nvar IdlePriority = 5;\n\nfunction markTaskErrored(task, ms) {\n}\n\n/* eslint-disable no-var */\nexports.unstable_now = void 0;\nvar hasPerformanceNow = // $FlowFixMe[method-unbinding]\ntypeof performance === 'object' && typeof performance.now === 'function';\n\nif (hasPerformanceNow) {\n  var localPerformance = performance;\n\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date;\n  var initialTime = localDate.now();\n\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n} // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n// Math.pow(2, 30) - 1\n// 0b111111111111111111111111111111\n\n\nvar maxSigned31BitInt = 1073741823; // Tasks are stored on a min heap\n\nvar taskQueue = [];\nvar timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\nvar taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\nvar currentTask = null;\nvar currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n\nvar isPerformingWork = false;\nvar isHostCallbackScheduled = false;\nvar isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n\nvar localSetTimeout = typeof setTimeout === 'function' ? setTimeout : null;\nvar localClearTimeout = typeof clearTimeout === 'function' ? clearTimeout : null;\nvar localSetImmediate = typeof setImmediate !== 'undefined' ? setImmediate : null; // IE and Node.js + jsdom\n\ntypeof navigator !== 'undefined' && // $FlowFixMe[prop-missing]\nnavigator.scheduling !== undefined && // $FlowFixMe[incompatible-type]\nnavigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n\nfunction advanceTimers(currentTime) {\n  // Check for tasks that are no longer delayed and add them to the queue.\n  var timer = peek(timerQueue);\n\n  while (timer !== null) {\n    if (timer.callback === null) {\n      // Timer was cancelled.\n      pop(timerQueue);\n    } else if (timer.startTime <= currentTime) {\n      // Timer fired. Transfer to the task queue.\n      pop(timerQueue);\n      timer.sortIndex = timer.expirationTime;\n      push(taskQueue, timer);\n    } else {\n      // Remaining timers are pending.\n      return;\n    }\n\n    timer = peek(timerQueue);\n  }\n}\n\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = false;\n  advanceTimers(currentTime);\n\n  if (!isHostCallbackScheduled) {\n    if (peek(taskQueue) !== null) {\n      isHostCallbackScheduled = true;\n      requestHostCallback();\n    } else {\n      var firstTimer = peek(timerQueue);\n\n      if (firstTimer !== null) {\n        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n      }\n    }\n  }\n}\n\nfunction flushWork(initialTime) {\n\n\n  isHostCallbackScheduled = false;\n\n  if (isHostTimeoutScheduled) {\n    // We scheduled a timeout but it's no longer needed. Cancel it.\n    isHostTimeoutScheduled = false;\n    cancelHostTimeout();\n  }\n\n  isPerformingWork = true;\n  var previousPriorityLevel = currentPriorityLevel;\n\n  try {\n    var currentTime; if (enableProfiling) ; else {\n      // No catch in prod code path.\n      return workLoop(initialTime);\n    }\n  } finally {\n    currentTask = null;\n    currentPriorityLevel = previousPriorityLevel;\n    isPerformingWork = false;\n  }\n}\n\nfunction workLoop(initialTime) {\n  var currentTime = initialTime;\n  advanceTimers(currentTime);\n  currentTask = peek(taskQueue);\n\n  while (currentTask !== null && !(enableSchedulerDebugging )) {\n    if (currentTask.expirationTime > currentTime && shouldYieldToHost()) {\n      // This currentTask hasn't expired, and we've reached the deadline.\n      break;\n    } // $FlowFixMe[incompatible-use] found when upgrading Flow\n\n\n    var callback = currentTask.callback;\n\n    if (typeof callback === 'function') {\n      // $FlowFixMe[incompatible-use] found when upgrading Flow\n      currentTask.callback = null; // $FlowFixMe[incompatible-use] found when upgrading Flow\n\n      currentPriorityLevel = currentTask.priorityLevel; // $FlowFixMe[incompatible-use] found when upgrading Flow\n\n      var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n\n      var continuationCallback = callback(didUserCallbackTimeout);\n      currentTime = exports.unstable_now();\n\n      if (typeof continuationCallback === 'function') {\n        // If a continuation is returned, immediately yield to the main thread\n        // regardless of how much time is left in the current time slice.\n        // $FlowFixMe[incompatible-use] found when upgrading Flow\n        currentTask.callback = continuationCallback;\n\n        advanceTimers(currentTime);\n        return true;\n      } else {\n\n        if (currentTask === peek(taskQueue)) {\n          pop(taskQueue);\n        }\n\n        advanceTimers(currentTime);\n      }\n    } else {\n      pop(taskQueue);\n    }\n\n    currentTask = peek(taskQueue);\n  } // Return whether there's additional work\n\n\n  if (currentTask !== null) {\n    return true;\n  } else {\n    var firstTimer = peek(timerQueue);\n\n    if (firstTimer !== null) {\n      requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n    }\n\n    return false;\n  }\n}\n\nfunction unstable_runWithPriority(priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n    case LowPriority:\n    case IdlePriority:\n      break;\n\n    default:\n      priorityLevel = NormalPriority;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_next(eventHandler) {\n  var priorityLevel;\n\n  switch (currentPriorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n      // Shift down to normal priority\n      priorityLevel = NormalPriority;\n      break;\n\n    default:\n      // Anything lower than normal priority should remain at the current level.\n      priorityLevel = currentPriorityLevel;\n      break;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_wrapCallback(callback) {\n  var parentPriorityLevel = currentPriorityLevel; // $FlowFixMe[incompatible-return]\n  // $FlowFixMe[missing-this-annot]\n\n  return function () {\n    // This is a fork of runWithPriority, inlined for performance.\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n}\n\nfunction unstable_scheduleCallback(priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  var startTime;\n\n  if (typeof options === 'object' && options !== null) {\n    var delay = options.delay;\n\n    if (typeof delay === 'number' && delay > 0) {\n      startTime = currentTime + delay;\n    } else {\n      startTime = currentTime;\n    }\n  } else {\n    startTime = currentTime;\n  }\n\n  var timeout;\n\n  switch (priorityLevel) {\n    case ImmediatePriority:\n      // Times out immediately\n      timeout = -1;\n      break;\n\n    case UserBlockingPriority:\n      // Eventually times out\n      timeout = userBlockingPriorityTimeout;\n      break;\n\n    case IdlePriority:\n      // Never times out\n      timeout = maxSigned31BitInt;\n      break;\n\n    case LowPriority:\n      // Eventually times out\n      timeout = lowPriorityTimeout;\n      break;\n\n    case NormalPriority:\n    default:\n      // Eventually times out\n      timeout = normalPriorityTimeout;\n      break;\n  }\n\n  var expirationTime = startTime + timeout;\n  var newTask = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: startTime,\n    expirationTime: expirationTime,\n    sortIndex: -1\n  };\n\n  if (startTime > currentTime) {\n    // This is a delayed task.\n    newTask.sortIndex = startTime;\n    push(timerQueue, newTask);\n\n    if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n      // All tasks are delayed, and this is the task with the earliest delay.\n      if (isHostTimeoutScheduled) {\n        // Cancel an existing timeout.\n        cancelHostTimeout();\n      } else {\n        isHostTimeoutScheduled = true;\n      } // Schedule a timeout.\n\n\n      requestHostTimeout(handleTimeout, startTime - currentTime);\n    }\n  } else {\n    newTask.sortIndex = expirationTime;\n    push(taskQueue, newTask);\n    // wait until the next time we yield.\n\n\n    if (!isHostCallbackScheduled && !isPerformingWork) {\n      isHostCallbackScheduled = true;\n      requestHostCallback();\n    }\n  }\n\n  return newTask;\n}\n\nfunction unstable_pauseExecution() {\n}\n\nfunction unstable_continueExecution() {\n\n  if (!isHostCallbackScheduled && !isPerformingWork) {\n    isHostCallbackScheduled = true;\n    requestHostCallback();\n  }\n}\n\nfunction unstable_getFirstCallbackNode() {\n  return peek(taskQueue);\n}\n\nfunction unstable_cancelCallback(task) {\n  // remove from the queue because you can't remove arbitrary nodes from an\n  // array based heap, only the first one.)\n\n\n  task.callback = null;\n}\n\nfunction unstable_getCurrentPriorityLevel() {\n  return currentPriorityLevel;\n}\n\nvar isMessageLoopRunning = false;\nvar taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n// thread, like user events. By default, it yields multiple times per frame.\n// It does not attempt to align with frame boundaries, since most tasks don't\n// need to be frame aligned; for those that do, use requestAnimationFrame.\n\nvar frameInterval = frameYieldMs;\nvar startTime = -1;\n\nfunction shouldYieldToHost() {\n  var timeElapsed = exports.unstable_now() - startTime;\n\n  if (timeElapsed < frameInterval) {\n    // The main thread has only been blocked for a really short amount of time;\n    // smaller than a single frame. Don't yield yet.\n    return false;\n  } // The main thread has been blocked for a non-negligible amount of time. We\n\n\n  return true;\n}\n\nfunction requestPaint() {\n\n}\n\nfunction forceFrameRate(fps) {\n  if (fps < 0 || fps > 125) {\n    // Using console['error'] to evade Babel and ESLint\n    console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n    return;\n  }\n\n  if (fps > 0) {\n    frameInterval = Math.floor(1000 / fps);\n  } else {\n    // reset the framerate\n    frameInterval = frameYieldMs;\n  }\n}\n\nvar performWorkUntilDeadline = function () {\n  if (isMessageLoopRunning) {\n    var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n    // has been blocked.\n\n    startTime = currentTime; // If a scheduler task throws, exit the current browser task so the\n    // error can be observed.\n    //\n    // Intentionally not using a try-catch, since that makes some debugging\n    // techniques harder. Instead, if `flushWork` errors, then `hasMoreWork` will\n    // remain true, and we'll continue the work loop.\n\n    var hasMoreWork = true;\n\n    try {\n      hasMoreWork = flushWork(currentTime);\n    } finally {\n      if (hasMoreWork) {\n        // If there's more work, schedule the next message event at the end\n        // of the preceding one.\n        schedulePerformWorkUntilDeadline();\n      } else {\n        isMessageLoopRunning = false;\n      }\n    }\n  } // Yielding to the browser will give it a chance to paint, so we can\n};\n\nvar schedulePerformWorkUntilDeadline;\n\nif (typeof localSetImmediate === 'function') {\n  // Node.js and old IE.\n  // There's a few reasons for why we prefer setImmediate.\n  //\n  // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n  // (Even though this is a DOM fork of the Scheduler, you could get here\n  // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n  // https://github.com/facebook/react/issues/20756\n  //\n  // But also, it runs earlier which is the semantic we want.\n  // If other browsers ever implement it, it's better to use it.\n  // Although both of these would be inferior to native scheduling.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetImmediate(performWorkUntilDeadline);\n  };\n} else if (typeof MessageChannel !== 'undefined') {\n  // DOM and Worker environments.\n  // We prefer MessageChannel because of the 4ms setTimeout clamping.\n  var channel = new MessageChannel();\n  var port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n\n  schedulePerformWorkUntilDeadline = function () {\n    port.postMessage(null);\n  };\n} else {\n  // We should only fallback here in non-browser environments.\n  schedulePerformWorkUntilDeadline = function () {\n    // $FlowFixMe[not-a-function] nullable value\n    localSetTimeout(performWorkUntilDeadline, 0);\n  };\n}\n\nfunction requestHostCallback() {\n  if (!isMessageLoopRunning) {\n    isMessageLoopRunning = true;\n    schedulePerformWorkUntilDeadline();\n  }\n}\n\nfunction requestHostTimeout(callback, ms) {\n  // $FlowFixMe[not-a-function] nullable value\n  taskTimeoutID = localSetTimeout(function () {\n    callback(exports.unstable_now());\n  }, ms);\n}\n\nfunction cancelHostTimeout() {\n  // $FlowFixMe[not-a-function] nullable value\n  localClearTimeout(taskTimeoutID);\n  taskTimeoutID = -1;\n}\nvar unstable_Profiling = null;\n\nexports.unstable_IdlePriority = IdlePriority;\nexports.unstable_ImmediatePriority = ImmediatePriority;\nexports.unstable_LowPriority = LowPriority;\nexports.unstable_NormalPriority = NormalPriority;\nexports.unstable_Profiling = unstable_Profiling;\nexports.unstable_UserBlockingPriority = UserBlockingPriority;\nexports.unstable_cancelCallback = unstable_cancelCallback;\nexports.unstable_continueExecution = unstable_continueExecution;\nexports.unstable_forceFrameRate = forceFrameRate;\nexports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\nexports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\nexports.unstable_next = unstable_next;\nexports.unstable_pauseExecution = unstable_pauseExecution;\nexports.unstable_requestPaint = requestPaint;\nexports.unstable_runWithPriority = unstable_runWithPriority;\nexports.unstable_scheduleCallback = unstable_scheduleCallback;\nexports.unstable_shouldYield = shouldYieldToHost;\nexports.unstable_wrapCallback = unstable_wrapCallback;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/index.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/scheduler/index.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvc2NoZWR1bGVyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSwwS0FBMEQ7QUFDNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9zY2hlZHVsZXIvaW5kZXguanM/NjIwNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvc2NoZWR1bGVyLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3NjaGVkdWxlci5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js ***!
  \*************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;Object.defineProperty(r,\"__esModule\",{value:true});var n=\"<unknown>\";function parse(e){var r=e.split(\"\\n\");return r.reduce((function(e,r){var n=parseChrome(r)||parseWinjs(r)||parseGecko(r)||parseNode(r)||parseJSC(r);if(n){e.push(n)}return e}),[])}var a=/^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;var l=/\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;function parseChrome(e){var r=a.exec(e);if(!r){return null}var u=r[2]&&r[2].indexOf(\"native\")===0;var t=r[2]&&r[2].indexOf(\"eval\")===0;var i=l.exec(r[2]);if(t&&i!=null){r[2]=i[1];r[3]=i[2];r[4]=i[3]}return{file:!u?r[2]:null,methodName:r[1]||n,arguments:u?[r[2]]:[],lineNumber:r[3]?+r[3]:null,column:r[4]?+r[4]:null}}var u=/^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;function parseWinjs(e){var r=u.exec(e);if(!r){return null}return{file:r[2],methodName:r[1]||n,arguments:[],lineNumber:+r[3],column:r[4]?+r[4]:null}}var t=/^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;var i=/(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;function parseGecko(e){var r=t.exec(e);if(!r){return null}var a=r[3]&&r[3].indexOf(\" > eval\")>-1;var l=i.exec(r[3]);if(a&&l!=null){r[3]=l[1];r[4]=l[2];r[5]=null}return{file:r[3],methodName:r[1]||n,arguments:r[2]?r[2].split(\",\"):[],lineNumber:r[4]?+r[4]:null,column:r[5]?+r[5]:null}}var s=/^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;function parseJSC(e){var r=s.exec(e);if(!r){return null}return{file:r[3],methodName:r[1]||n,arguments:[],lineNumber:+r[4],column:r[5]?+r[5]:null}}var o=/^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;function parseNode(e){var r=o.exec(e);if(!r){return null}return{file:r[2],methodName:r[1]||n,arguments:[],lineNumber:+r[3],column:r[4]?+r[4]:null}}r.parse=parse})();module.exports=e})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/compiled/strip-ansi/index.js ***!
  \*************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("var __dirname = \"/\";\n\n(()=>{\n    \"use strict\";\n    var e = {\n        511: (e)=>{\n            e.exports = function() {\n                let { onlyFirst: e = false } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                const r = [\n                    \"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\",\n                    \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))\"\n                ].join(\"|\");\n                return new RegExp(r, e ? undefined : \"g\");\n            };\n        },\n        532: (e, r, _)=>{\n            const t = _(511);\n            e.exports = (e)=>typeof e === \"string\" ? e.replace(t(), \"\") : e;\n        }\n    };\n    var r = {};\n    function __nccwpck_require__(_) {\n        var t = r[_];\n        if (t !== undefined) {\n            return t.exports;\n        }\n        var a = r[_] = {\n            exports: {}\n        };\n        var n = true;\n        try {\n            e[_](a, a.exports, __nccwpck_require__);\n            n = false;\n        } finally{\n            if (n) delete r[_];\n        }\n        return a.exports;\n    }\n    if (typeof __nccwpck_require__ !== \"undefined\") __nccwpck_require__.ab = __dirname + \"/\";\n    var _ = __nccwpck_require__(532);\n    module.exports = _;\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/MTMxMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-runtime.js ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLHNMQUFrRTtBQUNwRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1ydW50aW1lLmpzPzRkNzUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\n"));

/***/ })

}]);