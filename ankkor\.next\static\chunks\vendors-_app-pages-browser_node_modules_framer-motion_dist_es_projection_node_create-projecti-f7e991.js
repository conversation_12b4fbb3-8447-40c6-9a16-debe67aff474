"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-_app-pages-browser_node_modules_framer-motion_dist_es_projection_node_create-projecti-f7e991"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanDirtyNodes: function() { return /* binding */ cleanDirtyNodes; },\n/* harmony export */   createProjectionNode: function() { return /* binding */ createProjectionNode; },\n/* harmony export */   mixAxis: function() { return /* binding */ mixAxis; },\n/* harmony export */   mixAxisDelta: function() { return /* binding */ mixAxisDelta; },\n/* harmony export */   mixBox: function() { return /* binding */ mixBox; },\n/* harmony export */   propagateDirtyNodes: function() { return /* binding */ propagateDirtyNodes; }\n/* harmony export */ });\n/* harmony import */ var _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/subscription-manager.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\");\n/* harmony import */ var _animation_mix_values_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../animation/mix-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs\");\n/* harmony import */ var _geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../geometry/copy.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/copy.mjs\");\n/* harmony import */ var _geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../geometry/delta-apply.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\");\n/* harmony import */ var _geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../geometry/delta-calc.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n/* harmony import */ var _geometry_delta_remove_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../geometry/delta-remove.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs\");\n/* harmony import */ var _geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../geometry/models.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _animation_utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../animation/utils/transitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/transitions.mjs\");\n/* harmony import */ var _geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../geometry/utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/utils.mjs\");\n/* harmony import */ var _shared_stack_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../shared/stack.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/shared/stack.mjs\");\n/* harmony import */ var _styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../styles/scale-correction.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n/* harmony import */ var _styles_transform_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../styles/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/styles/transform.mjs\");\n/* harmony import */ var _utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../utils/each-axis.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs\");\n/* harmony import */ var _utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils/has-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs\");\n/* harmony import */ var _render_utils_flat_tree_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../render/utils/flat-tree.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs\");\n/* harmony import */ var _value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../../value/utils/resolve-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs\");\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/state.mjs\");\n/* harmony import */ var _utils_delay_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/delay.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/delay.mjs\");\n/* harmony import */ var _utils_mix_mjs__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../utils/mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _debug_record_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../debug/record.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/debug/record.mjs\");\n/* harmony import */ var _render_dom_utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../render/dom/utils/is-svg-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-svg-element.mjs\");\n/* harmony import */ var _animation_interfaces_single_value_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../animation/interfaces/single-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/single-value.mjs\");\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n    type: \"projectionFrame\",\n    totalNodes: 0,\n    resolvedTargetDeltas: 0,\n    recalculatedProjection: 0,\n};\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                projectionFrameData.totalNodes =\n                    projectionFrameData.resolvedTargetDeltas =\n                        projectionFrameData.recalculatedProjection =\n                            0;\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                (0,_debug_record_mjs__WEBPACK_IMPORTED_MODULE_0__.record)(projectionFrameData);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new _render_utils_flat_tree_mjs__WEBPACK_IMPORTED_MODULE_1__.FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__.SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n            if (this.instance)\n                return;\n            this.isSVG = (0,_render_dom_utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_3__.isSVGElement)(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = (0,_utils_delay_mjs__WEBPACK_IMPORTED_MODULE_4__.delay)(resizeUnblockUpdate, 250);\n                    if (_state_mjs__WEBPACK_IMPORTED_MODULE_5__.globalProjectionState.hasAnimatedSinceResize) {\n                        _state_mjs__WEBPACK_IMPORTED_MODULE_5__.globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !(0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.boxEqualsRounded)(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (this.options.layoutRoot ||\n                        (this.resumeFrom && this.resumeFrom.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...(0,_animation_utils_transitions_mjs__WEBPACK_IMPORTED_MODULE_7__.getValueTransition)(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.cancelFrame)(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = performance.now();\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.delta = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_9__.clamp)(0, 1000 / 60, now - _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.timestamp);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.timestamp = now;\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.isProcessing = true;\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.steps.update.process(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.steps.preRender.process(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.steps.render.process(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                queueMicrotask(() => this.update());\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !(0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.isDeltaZero)(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(box.x, scroll.offset.x);\n                (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(boxWithoutScroll.x, -rootScroll.offset.x);\n                            (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(boxWithoutScroll.x, scroll.offset.x);\n                    (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.translateAxis)(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.transformBox)(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!(0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(node.latestValues))\n                    continue;\n                (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.transformBox)(withTransforms, node.latestValues);\n            }\n            if ((0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(this.latestValues)) {\n                (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.transformBox)(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!(0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(node.latestValues))\n                    continue;\n                (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasScale)(node.latestValues) && node.updateSnapshot();\n                const sourceBox = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                const nodeBox = node.measurePageBox();\n                (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(sourceBox, nodeBox);\n                (0,_geometry_delta_remove_mjs__WEBPACK_IMPORTED_MODULE_14__.removeBoxTransforms)(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if ((0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(this.latestValues)) {\n                (0,_geometry_delta_remove_mjs__WEBPACK_IMPORTED_MODULE_14__.removeBoxTransforms)(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) ||\n                this.attemptToResolveRelativeTarget);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            // TODO If this is unsuccessful this currently happens every frame\n            if (!this.targetDelta && !this.relativeTarget) {\n                // TODO: This is a semi-repetition of further down this function, make DRY\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    this.relativeTargetOrigin = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativePosition)(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                this.targetWithTransforms = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativeBox)(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(this.target, this.layout.layoutBox);\n                }\n                (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.applyBoxDelta)(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    this.relativeTargetOrigin = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativePosition)(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            projectionFrameData.resolvedTargetDeltas++;\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasScale)(this.parent.latestValues) ||\n                (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.has2DTranslate)(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            var _a;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.applyTreeDeltas)(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.projectionTransform) {\n                    this.projectionDelta = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n                    this.projectionTransform = \"none\";\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta) {\n                this.projectionDelta = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n                this.projectionDeltaWithTransform = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n            }\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcBoxDelta)(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = (0,_styles_transform_mjs__WEBPACK_IMPORTED_MODULE_16__.buildProjectionTransform)(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            projectionFrameData.recalculatedProjection++;\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.scheduleRender && this.options.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot\n                ? snapshot.latestValues\n                : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativePosition)(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        (0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.boxEquals)(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    (0,_animation_mix_values_mjs__WEBPACK_IMPORTED_MODULE_17__.mixValues)(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation && this.currentAnimation.stop();\n            if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n                this.resumingFrom.currentAnimation.stop();\n            }\n            if (this.pendingAnimation) {\n                (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.cancelFrame)(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_8__.frame.update(() => {\n                _state_mjs__WEBPACK_IMPORTED_MODULE_5__.globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = (0,_animation_interfaces_single_value_mjs__WEBPACK_IMPORTED_MODULE_18__.animateSingleValue)(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onComplete: () => {\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                const xLength = (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcLength)(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcLength)(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            (0,_geometry_copy_mjs__WEBPACK_IMPORTED_MODULE_13__.copyBoxInto)(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            (0,_geometry_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_12__.transformBox)(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcBoxDelta)(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new _shared_stack_mjs__WEBPACK_IMPORTED_MODULE_19__.NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected rotation values, we can early return without a forced render.\n            let hasRotate = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ) {\n                hasRotate = true;\n            }\n            // If there's no rotation values, we don't need to do any more.\n            if (!hasRotate)\n                return;\n            const resetValues = {};\n            // Check the rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                const key = \"rotate\" + transformAxes[i];\n                // Record the rotation and then temporarily set it to 0\n                if (latestValues[key]) {\n                    resetValues[key] = latestValues[key];\n                    visualElement.setStaticValue(key, 0);\n                }\n            }\n            // Force a render of this element to apply the transform with all rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            var _a, _b;\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_20__.resolveMotionValue)(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_20__.resolveMotionValue)(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !(0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTransform)(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = (0,_styles_transform_mjs__WEBPACK_IMPORTED_MODULE_16__.buildProjectionTransform)(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in _styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_21__.scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = _styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_21__.scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_20__.resolveMotionValue)(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            (0,_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_22__.eachAxis)((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcLength)(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            (0,_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_22__.eachAxis)((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcLength)(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n        (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcBoxDelta)(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createDelta)();\n        if (isShared) {\n            (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcBoxDelta)(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcBoxDelta)(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !(0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.isDeltaZero)(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativePosition)(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = (0,_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_10__.createBox)();\n                    (0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.calcRelativePosition)(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!(0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.boxEqualsRounded)(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    projectionFrameData.totalNodes++;\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetRotation(node) {\n    node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_23__.mix)(delta.translate, 0, p);\n    output.scale = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_23__.mix)(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_23__.mix)(from.min, to.min, p);\n    output.max = (0,_utils_mix_mjs__WEBPACK_IMPORTED_MODULE_23__.mix)(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_24__.noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !(0,_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_15__.isNear)((0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.aspectRatio)(snapshot), (0,_geometry_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.aspectRatio)(layout), 0.2)));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9ub2RlL2NyZWF0ZS1wcm9qZWN0aW9uLW5vZGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkU7QUFDbkI7QUFDTDtBQUN1RDtBQUNXO0FBQ2xEO0FBQ0g7QUFDVztBQUNtQjtBQUM5QztBQUNpQjtBQUNFO0FBQ2pCO0FBQ2tDO0FBQ3hCO0FBQ29CO0FBQzVCO0FBQ047QUFDSjtBQUNNO0FBQ3lCO0FBQ1E7QUFDbkM7QUFDbUM7QUFDckM7O0FBRTVDO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHdGQUF3RjtBQUN4SDtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix5REFBTTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHNCQUFzQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsaUVBQVE7QUFDekM7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGdGQUFtQjtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixrRkFBWTtBQUNyQztBQUNBLG9CQUFvQixrQ0FBa0M7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyx1REFBSztBQUN2Qyx3QkFBd0IsNkRBQXFCO0FBQzdDLHdCQUF3Qiw2REFBcUI7QUFDN0M7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELHVFQUF1RTtBQUM3SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIscURBQXFEO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIscUVBQWdCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixvRkFBa0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksaUVBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixzQkFBc0I7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSwyREFBUyxTQUFTLHVEQUFLLHFCQUFxQiwyREFBUztBQUNqRSxZQUFZLDJEQUFTO0FBQ3JCLFlBQVksMkRBQVM7QUFDckIsWUFBWSx1REFBSyxnQkFBZ0IsMkRBQVM7QUFDMUMsWUFBWSx1REFBSyxtQkFBbUIsMkRBQVM7QUFDN0MsWUFBWSx1REFBSyxnQkFBZ0IsMkRBQVM7QUFDMUMsWUFBWSwyREFBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHVEQUFLO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVEQUFLO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHNCQUFzQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsZ0VBQVM7QUFDNUM7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELGdFQUFXO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHVFQUFZO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBLHVCQUF1QixnRUFBUztBQUNoQztBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQSxnQkFBZ0IseUVBQWE7QUFDN0IsZ0JBQWdCLHlFQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGdFQUFTO0FBQzlDLFlBQVksZ0VBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsc0JBQXNCO0FBQ2xEO0FBQ0Esd0JBQXdCLGtCQUFrQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0VBQVc7QUFDbkMsZ0NBQWdDLHFCQUFxQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHlFQUFhO0FBQ3pDLDRCQUE0Qix5RUFBYTtBQUN6QztBQUNBO0FBQ0Esb0JBQW9CLHlFQUFhO0FBQ2pDLG9CQUFvQix5RUFBYTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGdFQUFTO0FBQzVDLFlBQVksZ0VBQVc7QUFDdkIsNEJBQTRCLHNCQUFzQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHdFQUFZO0FBQ2hDO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUIsdUVBQVk7QUFDakM7QUFDQSxnQkFBZ0Isd0VBQVk7QUFDNUI7QUFDQSxnQkFBZ0IsdUVBQVk7QUFDNUIsZ0JBQWdCLHdFQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLGdFQUFTO0FBQ2pELFlBQVksZ0VBQVc7QUFDdkIsNEJBQTRCLHNCQUFzQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsdUVBQVk7QUFDakM7QUFDQSxnQkFBZ0IsbUVBQVE7QUFDeEIsa0NBQWtDLGdFQUFTO0FBQzNDO0FBQ0EsZ0JBQWdCLGdFQUFXO0FBQzNCLGdCQUFnQixnRkFBbUI7QUFDbkM7QUFDQSxnQkFBZ0IsdUVBQVk7QUFDNUIsZ0JBQWdCLGdGQUFtQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwyREFBUztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFtQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLDJEQUFTO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsZ0VBQVM7QUFDbkQsZ0RBQWdELGdFQUFTO0FBQ3pELG9CQUFvQiwrRUFBb0I7QUFDeEMsb0JBQW9CLGdFQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixnRUFBUztBQUN2Qyw0Q0FBNEMsZ0VBQVM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDBFQUFlO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdFQUFXO0FBQy9CO0FBQ0EsZ0JBQWdCLHlFQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZ0VBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGdFQUFTO0FBQ25ELGdEQUFnRCxnRUFBUztBQUN6RCxvQkFBb0IsK0VBQW9CO0FBQ3hDLG9CQUFvQixnRUFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUVBQVE7QUFDeEIsZ0JBQWdCLHlFQUFjO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCwyREFBUztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGdFQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksMkVBQWU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsa0VBQVc7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGtFQUFXO0FBQ2xELG9EQUFvRCxrRUFBVztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSx1RUFBWTtBQUN4Qix1Q0FBdUMsZ0ZBQXdCO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEMsZ0NBQWdDLGtFQUFXO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsZ0VBQVM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLCtFQUFvQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsOERBQVM7QUFDakM7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLGdFQUFTO0FBQ3RELG9CQUFvQixnRUFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IscUVBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpRUFBVztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyx1REFBSztBQUN6QyxnQkFBZ0IsNkRBQXFCO0FBQ3JDLHdDQUF3QywyRkFBa0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHFEQUFxRDtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLGdFQUFTO0FBQ2pELGdDQUFnQyxxRUFBVTtBQUMxQztBQUNBO0FBQ0EsZ0NBQWdDLHFFQUFVO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0VBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksd0VBQVk7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSx1RUFBWTtBQUN4QjtBQUNBO0FBQ0E7QUFDQSxtREFBbUQseURBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsV0FBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsV0FBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsaURBQWlELElBQUk7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxZQUFZO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLDBCQUEwQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsMEZBQWtCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsMEZBQWtCO0FBQzFDO0FBQ0EsMENBQTBDLHVFQUFZO0FBQ3REO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLGdGQUF3QjtBQUN2RDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsT0FBTztBQUMzQix3Q0FBd0MsZUFBZSxJQUFJLGVBQWU7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwwRUFBZTtBQUM3QztBQUNBO0FBQ0Esd0JBQXdCLG1CQUFtQixFQUFFLDBFQUFlO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsU0FBUztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMEZBQWtCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxRQUFRLHFGQUFxRjtBQUM3STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsaURBQWlEO0FBQ2pFLGdCQUFnQixnQkFBZ0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLCtEQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixxRUFBVTtBQUN6QztBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxZQUFZLCtEQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixxRUFBVTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSw0QkFBNEIsa0VBQVc7QUFDdkMsUUFBUSx1RUFBWTtBQUNwQiw0QkFBNEIsa0VBQVc7QUFDdkM7QUFDQSxZQUFZLHVFQUFZO0FBQ3hCO0FBQ0E7QUFDQSxZQUFZLHVFQUFZO0FBQ3hCO0FBQ0Esa0NBQWtDLGdFQUFXO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsaURBQWlEO0FBQ3pFO0FBQ0EsNkNBQTZDLGdFQUFTO0FBQ3RELG9CQUFvQiwrRUFBb0I7QUFDeEMsMkNBQTJDLGdFQUFTO0FBQ3BELG9CQUFvQiwrRUFBb0I7QUFDeEMseUJBQXlCLHFFQUFnQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxnQkFBZ0IsaUJBQWlCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLGNBQWM7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGdCQUFnQjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLG9EQUFHO0FBQzFCLG1CQUFtQixvREFBRztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixvREFBRztBQUNwQixpQkFBaUIsb0RBQUc7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxrREFBSTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsaUVBQU0sQ0FBQyxnRUFBVyxZQUFZLGdFQUFXO0FBQ3REOztBQUVxRyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3Byb2plY3Rpb24vbm9kZS9jcmVhdGUtcHJvamVjdGlvbi1ub2RlLm1qcz8wZWYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN1YnNjcmlwdGlvbk1hbmFnZXIgfSBmcm9tICcuLi8uLi91dGlscy9zdWJzY3JpcHRpb24tbWFuYWdlci5tanMnO1xuaW1wb3J0IHsgbWl4VmFsdWVzIH0gZnJvbSAnLi4vYW5pbWF0aW9uL21peC12YWx1ZXMubWpzJztcbmltcG9ydCB7IGNvcHlCb3hJbnRvIH0gZnJvbSAnLi4vZ2VvbWV0cnkvY29weS5tanMnO1xuaW1wb3J0IHsgdHJhbnNsYXRlQXhpcywgdHJhbnNmb3JtQm94LCBhcHBseUJveERlbHRhLCBhcHBseVRyZWVEZWx0YXMgfSBmcm9tICcuLi9nZW9tZXRyeS9kZWx0YS1hcHBseS5tanMnO1xuaW1wb3J0IHsgY2FsY1JlbGF0aXZlUG9zaXRpb24sIGNhbGNSZWxhdGl2ZUJveCwgY2FsY0JveERlbHRhLCBjYWxjTGVuZ3RoLCBpc05lYXIgfSBmcm9tICcuLi9nZW9tZXRyeS9kZWx0YS1jYWxjLm1qcyc7XG5pbXBvcnQgeyByZW1vdmVCb3hUcmFuc2Zvcm1zIH0gZnJvbSAnLi4vZ2VvbWV0cnkvZGVsdGEtcmVtb3ZlLm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVCb3gsIGNyZWF0ZURlbHRhIH0gZnJvbSAnLi4vZ2VvbWV0cnkvbW9kZWxzLm1qcyc7XG5pbXBvcnQgeyBnZXRWYWx1ZVRyYW5zaXRpb24gfSBmcm9tICcuLi8uLi9hbmltYXRpb24vdXRpbHMvdHJhbnNpdGlvbnMubWpzJztcbmltcG9ydCB7IGJveEVxdWFsc1JvdW5kZWQsIGlzRGVsdGFaZXJvLCBhc3BlY3RSYXRpbywgYm94RXF1YWxzIH0gZnJvbSAnLi4vZ2VvbWV0cnkvdXRpbHMubWpzJztcbmltcG9ydCB7IE5vZGVTdGFjayB9IGZyb20gJy4uL3NoYXJlZC9zdGFjay5tanMnO1xuaW1wb3J0IHsgc2NhbGVDb3JyZWN0b3JzIH0gZnJvbSAnLi4vc3R5bGVzL3NjYWxlLWNvcnJlY3Rpb24ubWpzJztcbmltcG9ydCB7IGJ1aWxkUHJvamVjdGlvblRyYW5zZm9ybSB9IGZyb20gJy4uL3N0eWxlcy90cmFuc2Zvcm0ubWpzJztcbmltcG9ydCB7IGVhY2hBeGlzIH0gZnJvbSAnLi4vdXRpbHMvZWFjaC1heGlzLm1qcyc7XG5pbXBvcnQgeyBoYXNUcmFuc2Zvcm0sIGhhc1NjYWxlLCBoYXMyRFRyYW5zbGF0ZSB9IGZyb20gJy4uL3V0aWxzL2hhcy10cmFuc2Zvcm0ubWpzJztcbmltcG9ydCB7IEZsYXRUcmVlIH0gZnJvbSAnLi4vLi4vcmVuZGVyL3V0aWxzL2ZsYXQtdHJlZS5tanMnO1xuaW1wb3J0IHsgcmVzb2x2ZU1vdGlvblZhbHVlIH0gZnJvbSAnLi4vLi4vdmFsdWUvdXRpbHMvcmVzb2x2ZS1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IGdsb2JhbFByb2plY3Rpb25TdGF0ZSB9IGZyb20gJy4vc3RhdGUubWpzJztcbmltcG9ydCB7IGRlbGF5IH0gZnJvbSAnLi4vLi4vdXRpbHMvZGVsYXkubWpzJztcbmltcG9ydCB7IG1peCB9IGZyb20gJy4uLy4uL3V0aWxzL21peC5tanMnO1xuaW1wb3J0IHsgcmVjb3JkIH0gZnJvbSAnLi4vLi4vZGVidWcvcmVjb3JkLm1qcyc7XG5pbXBvcnQgeyBpc1NWR0VsZW1lbnQgfSBmcm9tICcuLi8uLi9yZW5kZXIvZG9tL3V0aWxzL2lzLXN2Zy1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyBhbmltYXRlU2luZ2xlVmFsdWUgfSBmcm9tICcuLi8uLi9hbmltYXRpb24vaW50ZXJmYWNlcy9zaW5nbGUtdmFsdWUubWpzJztcbmltcG9ydCB7IGNsYW1wIH0gZnJvbSAnLi4vLi4vdXRpbHMvY2xhbXAubWpzJztcbmltcG9ydCB7IGNhbmNlbEZyYW1lLCBmcmFtZURhdGEsIHN0ZXBzLCBmcmFtZSB9IGZyb20gJy4uLy4uL2ZyYW1lbG9vcC9mcmFtZS5tanMnO1xuaW1wb3J0IHsgbm9vcCB9IGZyb20gJy4uLy4uL3V0aWxzL25vb3AubWpzJztcblxuY29uc3QgdHJhbnNmb3JtQXhlcyA9IFtcIlwiLCBcIlhcIiwgXCJZXCIsIFwiWlwiXTtcbmNvbnN0IGhpZGRlblZpc2liaWxpdHkgPSB7IHZpc2liaWxpdHk6IFwiaGlkZGVuXCIgfTtcbi8qKlxuICogV2UgdXNlIDEwMDAgYXMgdGhlIGFuaW1hdGlvbiB0YXJnZXQgYXMgMC0xMDAwIG1hcHMgYmV0dGVyIHRvIHBpeGVscyB0aGFuIDAtMVxuICogd2hpY2ggaGFzIGEgbm90aWNlYWJsZSBkaWZmZXJlbmNlIGluIHNwcmluZyBhbmltYXRpb25zXG4gKi9cbmNvbnN0IGFuaW1hdGlvblRhcmdldCA9IDEwMDA7XG5sZXQgaWQgPSAwO1xuLyoqXG4gKiBVc2UgYSBtdXRhYmxlIGRhdGEgb2JqZWN0IGZvciBkZWJ1ZyBkYXRhIHNvIGFzIHRvIG5vdCBjcmVhdGUgYSBuZXdcbiAqIG9iamVjdCBldmVyeSBmcmFtZS5cbiAqL1xuY29uc3QgcHJvamVjdGlvbkZyYW1lRGF0YSA9IHtcbiAgICB0eXBlOiBcInByb2plY3Rpb25GcmFtZVwiLFxuICAgIHRvdGFsTm9kZXM6IDAsXG4gICAgcmVzb2x2ZWRUYXJnZXREZWx0YXM6IDAsXG4gICAgcmVjYWxjdWxhdGVkUHJvamVjdGlvbjogMCxcbn07XG5mdW5jdGlvbiBjcmVhdGVQcm9qZWN0aW9uTm9kZSh7IGF0dGFjaFJlc2l6ZUxpc3RlbmVyLCBkZWZhdWx0UGFyZW50LCBtZWFzdXJlU2Nyb2xsLCBjaGVja0lzU2Nyb2xsUm9vdCwgcmVzZXRUcmFuc2Zvcm0sIH0pIHtcbiAgICByZXR1cm4gY2xhc3MgUHJvamVjdGlvbk5vZGUge1xuICAgICAgICBjb25zdHJ1Y3RvcihsYXRlc3RWYWx1ZXMgPSB7fSwgcGFyZW50ID0gZGVmYXVsdFBhcmVudCA9PT0gbnVsbCB8fCBkZWZhdWx0UGFyZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkZWZhdWx0UGFyZW50KCkpIHtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQSB1bmlxdWUgSUQgZ2VuZXJhdGVkIGZvciBldmVyeSBwcm9qZWN0aW9uIG5vZGUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMuaWQgPSBpZCsrO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBBbiBpZCB0aGF0IHJlcHJlc2VudHMgYSB1bmlxdWUgc2Vzc2lvbiBpbnN0aWdhdGVkIGJ5IHN0YXJ0VXBkYXRlLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmFuaW1hdGlvbklkID0gMDtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQSBTZXQgY29udGFpbmluZyBhbGwgdGhpcyBjb21wb25lbnQncyBjaGlsZHJlbi4gVGhpcyBpcyB1c2VkIHRvIGl0ZXJhdGVcbiAgICAgICAgICAgICAqIHRocm91Z2ggdGhlIGNoaWxkcmVuLlxuICAgICAgICAgICAgICpcbiAgICAgICAgICAgICAqIFRPRE86IFRoaXMgY291bGQgYmUgZmFzdGVyIHRvIGl0ZXJhdGUgYXMgYSBmbGF0IGFycmF5IHN0b3JlZCBvbiB0aGUgcm9vdCBub2RlLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmNoaWxkcmVuID0gbmV3IFNldCgpO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBPcHRpb25zIGZvciB0aGUgbm9kZS4gV2UgdXNlIHRoaXMgdG8gY29uZmlndXJlIHdoYXQga2luZCBvZiBsYXlvdXQgYW5pbWF0aW9uc1xuICAgICAgICAgICAgICogd2Ugc2hvdWxkIHBlcmZvcm0gKGlmIGFueSkuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMub3B0aW9ucyA9IHt9O1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBXZSB1c2UgdGhpcyB0byBkZXRlY3Qgd2hlbiBpdHMgc2FmZSB0byBzaHV0IGRvd24gcGFydCBvZiBhIHByb2plY3Rpb24gdHJlZS5cbiAgICAgICAgICAgICAqIFdlIGhhdmUgdG8ga2VlcCBwcm9qZWN0aW5nIGNoaWxkcmVuIGZvciBzY2FsZSBjb3JyZWN0aW9uIGFuZCByZWxhdGl2ZSBwcm9qZWN0aW9uXG4gICAgICAgICAgICAgKiB1bnRpbCBhbGwgdGhlaXIgcGFyZW50cyBzdG9wIHBlcmZvcm1pbmcgbGF5b3V0IGFuaW1hdGlvbnMuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMuaXNUcmVlQW5pbWF0aW5nID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLmlzQW5pbWF0aW9uQmxvY2tlZCA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBGbGFnIHRvIHRydWUgaWYgd2UgdGhpbmsgdGhpcyBsYXlvdXQgaGFzIGJlZW4gY2hhbmdlZC4gV2UgY2FuJ3QgYWx3YXlzIGtub3cgdGhpcyxcbiAgICAgICAgICAgICAqIGN1cnJlbnRseSB3ZSBzZXQgaXQgdG8gdHJ1ZSBldmVyeSB0aW1lIGEgY29tcG9uZW50IHJlbmRlcnMsIG9yIGlmIGl0IGhhcyBhIGxheW91dERlcGVuZGVuY3lcbiAgICAgICAgICAgICAqIGlmIHRoYXQgaGFzIGNoYW5nZWQgYmV0d2VlbiByZW5kZXJzLiBBZGRpdGlvbmFsbHksIGNvbXBvbmVudHMgY2FuIGJlIGdyb3VwZWQgYnkgTGF5b3V0R3JvdXBcbiAgICAgICAgICAgICAqIGFuZCBpZiBvbmUgbm9kZSBpcyBkaXJ0aWVkLCB0aGV5IGFsbCBhcmUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMuaXNMYXlvdXREaXJ0eSA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBGbGFnIHRvIHRydWUgaWYgd2UgdGhpbmsgdGhlIHByb2plY3Rpb24gY2FsY3VsYXRpb25zIGZvciB0aGlzIG5vZGUgbmVlZHNcbiAgICAgICAgICAgICAqIHJlY2FsY3VsYXRpbmcgYXMgYSByZXN1bHQgb2YgYW4gdXBkYXRlZCB0cmFuc2Zvcm0gb3IgbGF5b3V0IGFuaW1hdGlvbi5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5pc1Byb2plY3Rpb25EaXJ0eSA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBGbGFnIHRvIHRydWUgaWYgdGhlIGxheW91dCAqb3IqIHRyYW5zZm9ybSBoYXMgY2hhbmdlZC4gVGhpcyB0aGVuIGdldHMgcHJvcGFnYXRlZFxuICAgICAgICAgICAgICogdGhyb3VnaG91dCB0aGUgcHJvamVjdGlvbiB0cmVlLCBmb3JjaW5nIGFueSBlbGVtZW50IGJlbG93IHRvIHJlY2FsY3VsYXRlIG9uIHRoZSBuZXh0IGZyYW1lLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmlzU2hhcmVkUHJvamVjdGlvbkRpcnR5ID0gZmFsc2U7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIEZsYWcgdHJhbnNmb3JtIGRpcnR5LiBUaGlzIGdldHMgcHJvcGFnYXRlZCB0aHJvdWdob3V0IHRoZSB3aG9sZSB0cmVlIGJ1dCBpcyBvbmx5XG4gICAgICAgICAgICAgKiByZXNwZWN0ZWQgYnkgc2hhcmVkIG5vZGVzLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmlzVHJhbnNmb3JtRGlydHkgPSBmYWxzZTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQmxvY2sgbGF5b3V0IHVwZGF0ZXMgZm9yIGluc3RhbnQgbGF5b3V0IHRyYW5zaXRpb25zIHRocm91Z2hvdXQgdGhlIHRyZWUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMudXBkYXRlTWFudWFsbHlCbG9ja2VkID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUJsb2NrZWRCeVJlc2l6ZSA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBTZXQgdG8gdHJ1ZSBiZXR3ZWVuIHRoZSBzdGFydCBvZiB0aGUgZmlyc3QgYHdpbGxVcGRhdGVgIGNhbGwgYW5kIHRoZSBlbmQgb2YgdGhlIGBkaWRVcGRhdGVgXG4gICAgICAgICAgICAgKiBjYWxsLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmlzVXBkYXRpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgdGhpcyBpcyBhbiBTVkcgZWxlbWVudCB3ZSBjdXJyZW50bHkgZGlzYWJsZSBwcm9qZWN0aW9uIHRyYW5zZm9ybXNcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5pc1NWRyA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBGbGFnIHRvIHRydWUgKGR1cmluZyBwcm9tb3Rpb24pIGlmIGEgbm9kZSBkb2luZyBhbiBpbnN0YW50IGxheW91dCB0cmFuc2l0aW9uIG5lZWRzIHRvIHJlc2V0XG4gICAgICAgICAgICAgKiBpdHMgcHJvamVjdGlvbiBzdHlsZXMuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMubmVlZHNSZXNldCA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBGbGFncyB3aGV0aGVyIHRoaXMgbm9kZSBzaG91bGQgaGF2ZSBpdHMgdHJhbnNmb3JtIHJlc2V0IHByaW9yIHRvIG1lYXN1cmluZy5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5zaG91bGRSZXNldFRyYW5zZm9ybSA9IGZhbHNlO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBBbiBvYmplY3QgcmVwcmVzZW50aW5nIHRoZSBjYWxjdWxhdGVkIGNvbnRleHR1YWwvYWNjdW11bGF0ZWQvdHJlZSBzY2FsZS5cbiAgICAgICAgICAgICAqIFRoaXMgd2lsbCBiZSB1c2VkIHRvIHNjYWxlIGNhbGN1bGNhdGVkIHByb2plY3Rpb24gdHJhbnNmb3JtcywgYXMgdGhlc2UgYXJlXG4gICAgICAgICAgICAgKiBjYWxjdWxhdGVkIGluIHNjcmVlbi1zcGFjZSBidXQgbmVlZCB0byBiZSBzY2FsZWQgZm9yIGVsZW1lbnRzIHRvIGxheW91dGx5XG4gICAgICAgICAgICAgKiBtYWtlIGl0IHRvIHRoZWlyIGNhbGN1bGF0ZWQgZGVzdGluYXRpb25zLlxuICAgICAgICAgICAgICpcbiAgICAgICAgICAgICAqIFRPRE86IExhenktaW5pdFxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLnRyZWVTY2FsZSA9IHsgeDogMSwgeTogMSB9O1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0aGlzLmV2ZW50SGFuZGxlcnMgPSBuZXcgTWFwKCk7XG4gICAgICAgICAgICB0aGlzLmhhc1RyZWVBbmltYXRlZCA9IGZhbHNlO1xuICAgICAgICAgICAgLy8gTm90ZTogQ3VycmVudGx5IG9ubHkgcnVubmluZyBvbiByb290IG5vZGVcbiAgICAgICAgICAgIHRoaXMudXBkYXRlU2NoZWR1bGVkID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLnByb2plY3Rpb25VcGRhdGVTY2hlZHVsZWQgPSBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMuY2hlY2tVcGRhdGVGYWlsZWQgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNVcGRhdGluZykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmlzVXBkYXRpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGVhckFsbFNuYXBzaG90cygpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFRoaXMgaXMgYSBtdWx0aS1zdGVwIHByb2Nlc3MgYXMgc2hhcmVkIG5vZGVzIG1pZ2h0IGJlIG9mIGRpZmZlcmVudCBkZXB0aHMuIE5vZGVzXG4gICAgICAgICAgICAgKiBhcmUgc29ydGVkIGJ5IGRlcHRoIG9yZGVyLCBzbyB3ZSBuZWVkIHRvIHJlc29sdmUgdGhlIGVudGlyZSB0cmVlIGJlZm9yZSBtb3ZpbmcgdG9cbiAgICAgICAgICAgICAqIHRoZSBuZXh0IHN0ZXAuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMudXBkYXRlUHJvamVjdGlvbiA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnByb2plY3Rpb25VcGRhdGVTY2hlZHVsZWQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBSZXNldCBkZWJ1ZyBjb3VudHMuIE1hbnVhbGx5IHJlc2V0dGluZyByYXRoZXIgdGhhbiBjcmVhdGluZyBhIG5ld1xuICAgICAgICAgICAgICAgICAqIG9iamVjdCBlYWNoIGZyYW1lLlxuICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgIHByb2plY3Rpb25GcmFtZURhdGEudG90YWxOb2RlcyA9XG4gICAgICAgICAgICAgICAgICAgIHByb2plY3Rpb25GcmFtZURhdGEucmVzb2x2ZWRUYXJnZXREZWx0YXMgPVxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvamVjdGlvbkZyYW1lRGF0YS5yZWNhbGN1bGF0ZWRQcm9qZWN0aW9uID1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAwO1xuICAgICAgICAgICAgICAgIHRoaXMubm9kZXMuZm9yRWFjaChwcm9wYWdhdGVEaXJ0eU5vZGVzKTtcbiAgICAgICAgICAgICAgICB0aGlzLm5vZGVzLmZvckVhY2gocmVzb2x2ZVRhcmdldERlbHRhKTtcbiAgICAgICAgICAgICAgICB0aGlzLm5vZGVzLmZvckVhY2goY2FsY1Byb2plY3Rpb24pO1xuICAgICAgICAgICAgICAgIHRoaXMubm9kZXMuZm9yRWFjaChjbGVhbkRpcnR5Tm9kZXMpO1xuICAgICAgICAgICAgICAgIHJlY29yZChwcm9qZWN0aW9uRnJhbWVEYXRhKTtcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICB0aGlzLmhhc1Byb2plY3RlZCA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5pc1Zpc2libGUgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5hbmltYXRpb25Qcm9ncmVzcyA9IDA7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFNoYXJlZCBsYXlvdXRcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgLy8gVE9ETyBPbmx5IHJ1bm5pbmcgb24gcm9vdCBub2RlXG4gICAgICAgICAgICB0aGlzLnNoYXJlZE5vZGVzID0gbmV3IE1hcCgpO1xuICAgICAgICAgICAgdGhpcy5sYXRlc3RWYWx1ZXMgPSBsYXRlc3RWYWx1ZXM7XG4gICAgICAgICAgICB0aGlzLnJvb3QgPSBwYXJlbnQgPyBwYXJlbnQucm9vdCB8fCBwYXJlbnQgOiB0aGlzO1xuICAgICAgICAgICAgdGhpcy5wYXRoID0gcGFyZW50ID8gWy4uLnBhcmVudC5wYXRoLCBwYXJlbnRdIDogW107XG4gICAgICAgICAgICB0aGlzLnBhcmVudCA9IHBhcmVudDtcbiAgICAgICAgICAgIHRoaXMuZGVwdGggPSBwYXJlbnQgPyBwYXJlbnQuZGVwdGggKyAxIDogMDtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5wYXRoLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5wYXRoW2ldLnNob3VsZFJlc2V0VHJhbnNmb3JtID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLnJvb3QgPT09IHRoaXMpXG4gICAgICAgICAgICAgICAgdGhpcy5ub2RlcyA9IG5ldyBGbGF0VHJlZSgpO1xuICAgICAgICB9XG4gICAgICAgIGFkZEV2ZW50TGlzdGVuZXIobmFtZSwgaGFuZGxlcikge1xuICAgICAgICAgICAgaWYgKCF0aGlzLmV2ZW50SGFuZGxlcnMuaGFzKG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5ldmVudEhhbmRsZXJzLnNldChuYW1lLCBuZXcgU3Vic2NyaXB0aW9uTWFuYWdlcigpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0aGlzLmV2ZW50SGFuZGxlcnMuZ2V0KG5hbWUpLmFkZChoYW5kbGVyKTtcbiAgICAgICAgfVxuICAgICAgICBub3RpZnlMaXN0ZW5lcnMobmFtZSwgLi4uYXJncykge1xuICAgICAgICAgICAgY29uc3Qgc3Vic2NyaXB0aW9uTWFuYWdlciA9IHRoaXMuZXZlbnRIYW5kbGVycy5nZXQobmFtZSk7XG4gICAgICAgICAgICBzdWJzY3JpcHRpb25NYW5hZ2VyICYmIHN1YnNjcmlwdGlvbk1hbmFnZXIubm90aWZ5KC4uLmFyZ3MpO1xuICAgICAgICB9XG4gICAgICAgIGhhc0xpc3RlbmVycyhuYW1lKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5ldmVudEhhbmRsZXJzLmhhcyhuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICAvKipcbiAgICAgICAgICogTGlmZWN5Y2xlc1xuICAgICAgICAgKi9cbiAgICAgICAgbW91bnQoaW5zdGFuY2UsIGlzTGF5b3V0RGlydHkgPSB0aGlzLnJvb3QuaGFzVHJlZUFuaW1hdGVkKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5pbnN0YW5jZSlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB0aGlzLmlzU1ZHID0gaXNTVkdFbGVtZW50KGluc3RhbmNlKTtcbiAgICAgICAgICAgIHRoaXMuaW5zdGFuY2UgPSBpbnN0YW5jZTtcbiAgICAgICAgICAgIGNvbnN0IHsgbGF5b3V0SWQsIGxheW91dCwgdmlzdWFsRWxlbWVudCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgaWYgKHZpc3VhbEVsZW1lbnQgJiYgIXZpc3VhbEVsZW1lbnQuY3VycmVudCkge1xuICAgICAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQubW91bnQoaW5zdGFuY2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5yb290Lm5vZGVzLmFkZCh0aGlzKTtcbiAgICAgICAgICAgIHRoaXMucGFyZW50ICYmIHRoaXMucGFyZW50LmNoaWxkcmVuLmFkZCh0aGlzKTtcbiAgICAgICAgICAgIGlmIChpc0xheW91dERpcnR5ICYmIChsYXlvdXQgfHwgbGF5b3V0SWQpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5pc0xheW91dERpcnR5ID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChhdHRhY2hSZXNpemVMaXN0ZW5lcikge1xuICAgICAgICAgICAgICAgIGxldCBjYW5jZWxEZWxheTtcbiAgICAgICAgICAgICAgICBjb25zdCByZXNpemVVbmJsb2NrVXBkYXRlID0gKCkgPT4gKHRoaXMucm9vdC51cGRhdGVCbG9ja2VkQnlSZXNpemUgPSBmYWxzZSk7XG4gICAgICAgICAgICAgICAgYXR0YWNoUmVzaXplTGlzdGVuZXIoaW5zdGFuY2UsICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yb290LnVwZGF0ZUJsb2NrZWRCeVJlc2l6ZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGNhbmNlbERlbGF5ICYmIGNhbmNlbERlbGF5KCk7XG4gICAgICAgICAgICAgICAgICAgIGNhbmNlbERlbGF5ID0gZGVsYXkocmVzaXplVW5ibG9ja1VwZGF0ZSwgMjUwKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGdsb2JhbFByb2plY3Rpb25TdGF0ZS5oYXNBbmltYXRlZFNpbmNlUmVzaXplKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBnbG9iYWxQcm9qZWN0aW9uU3RhdGUuaGFzQW5pbWF0ZWRTaW5jZVJlc2l6ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5ub2Rlcy5mb3JFYWNoKGZpbmlzaEFuaW1hdGlvbik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChsYXlvdXRJZCkge1xuICAgICAgICAgICAgICAgIHRoaXMucm9vdC5yZWdpc3RlclNoYXJlZE5vZGUobGF5b3V0SWQsIHRoaXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gT25seSByZWdpc3RlciB0aGUgaGFuZGxlciBpZiBpdCByZXF1aXJlcyBsYXlvdXQgYW5pbWF0aW9uXG4gICAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLmFuaW1hdGUgIT09IGZhbHNlICYmXG4gICAgICAgICAgICAgICAgdmlzdWFsRWxlbWVudCAmJlxuICAgICAgICAgICAgICAgIChsYXlvdXRJZCB8fCBsYXlvdXQpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5hZGRFdmVudExpc3RlbmVyKFwiZGlkVXBkYXRlXCIsICh7IGRlbHRhLCBoYXNMYXlvdXRDaGFuZ2VkLCBoYXNSZWxhdGl2ZVRhcmdldENoYW5nZWQsIGxheW91dDogbmV3TGF5b3V0LCB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmlzVHJlZUFuaW1hdGlvbkJsb2NrZWQoKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50YXJnZXQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlVGFyZ2V0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IENoZWNrIGhlcmUgaWYgYW4gYW5pbWF0aW9uIGV4aXN0c1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsYXlvdXRUcmFuc2l0aW9uID0gdGhpcy5vcHRpb25zLnRyYW5zaXRpb24gfHxcbiAgICAgICAgICAgICAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQuZ2V0RGVmYXVsdFRyYW5zaXRpb24oKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdExheW91dFRyYW5zaXRpb247XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgb25MYXlvdXRBbmltYXRpb25TdGFydCwgb25MYXlvdXRBbmltYXRpb25Db21wbGV0ZSwgfSA9IHZpc3VhbEVsZW1lbnQuZ2V0UHJvcHMoKTtcbiAgICAgICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICAgICAqIFRoZSB0YXJnZXQgbGF5b3V0IG9mIHRoZSBlbGVtZW50IG1pZ2h0IHN0YXkgdGhlIHNhbWUsXG4gICAgICAgICAgICAgICAgICAgICAqIGJ1dCBpdHMgcG9zaXRpb24gcmVsYXRpdmUgdG8gaXRzIHBhcmVudCBoYXMgY2hhbmdlZC5cbiAgICAgICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldENoYW5nZWQgPSAhdGhpcy50YXJnZXRMYXlvdXQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICFib3hFcXVhbHNSb3VuZGVkKHRoaXMudGFyZ2V0TGF5b3V0LCBuZXdMYXlvdXQpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICBoYXNSZWxhdGl2ZVRhcmdldENoYW5nZWQ7XG4gICAgICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAgICAgKiBJZiB0aGUgbGF5b3V0IGhhc24ndCBzZWVtZWQgdG8gaGF2ZSBjaGFuZ2VkLCBpdCBtaWdodCBiZSB0aGF0IHRoZVxuICAgICAgICAgICAgICAgICAgICAgKiBlbGVtZW50IGlzIHZpc3VhbGx5IGluIHRoZSBzYW1lIHBsYWNlIGluIHRoZSBkb2N1bWVudCBidXQgaXRzIHBvc2l0aW9uXG4gICAgICAgICAgICAgICAgICAgICAqIHJlbGF0aXZlIHRvIGl0cyBwYXJlbnQgaGFzIGluZGVlZCBjaGFuZ2VkLiBTbyBoZXJlIHdlIGNoZWNrIGZvciB0aGF0LlxuICAgICAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFzT25seVJlbGF0aXZlVGFyZ2V0Q2hhbmdlZCA9ICFoYXNMYXlvdXRDaGFuZ2VkICYmIGhhc1JlbGF0aXZlVGFyZ2V0Q2hhbmdlZDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5sYXlvdXRSb290IHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAodGhpcy5yZXN1bWVGcm9tICYmIHRoaXMucmVzdW1lRnJvbS5pbnN0YW5jZSkgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhc09ubHlSZWxhdGl2ZVRhcmdldENoYW5nZWQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgIChoYXNMYXlvdXRDaGFuZ2VkICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKHRhcmdldENoYW5nZWQgfHwgIXRoaXMuY3VycmVudEFuaW1hdGlvbikpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5yZXN1bWVGcm9tKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXN1bWluZ0Zyb20gPSB0aGlzLnJlc3VtZUZyb207XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXN1bWluZ0Zyb20ucmVzdW1pbmdGcm9tID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRBbmltYXRpb25PcmlnaW4oZGVsdGEsIGhhc09ubHlSZWxhdGl2ZVRhcmdldENoYW5nZWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYW5pbWF0aW9uT3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5nZXRWYWx1ZVRyYW5zaXRpb24obGF5b3V0VHJhbnNpdGlvbiwgXCJsYXlvdXRcIiksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25QbGF5OiBvbkxheW91dEFuaW1hdGlvblN0YXJ0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ29tcGxldGU6IG9uTGF5b3V0QW5pbWF0aW9uQ29tcGxldGUsXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHZpc3VhbEVsZW1lbnQuc2hvdWxkUmVkdWNlTW90aW9uIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcHRpb25zLmxheW91dFJvb3QpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb25PcHRpb25zLmRlbGF5ID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb25PcHRpb25zLnR5cGUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhcnRBbmltYXRpb24oYW5pbWF0aW9uT3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgICAgICAgICAqIElmIHRoZSBsYXlvdXQgaGFzbid0IGNoYW5nZWQgYW5kIHdlIGhhdmUgYW4gYW5pbWF0aW9uIHRoYXQgaGFzbid0IHN0YXJ0ZWQgeWV0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICogZmluaXNoIGl0IGltbWVkaWF0ZWx5LiBPdGhlcndpc2UgaXQgd2lsbCBiZSBhbmltYXRpbmcgZnJvbSBhIGxvY2F0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgKiB0aGF0IHdhcyBwcm9iYWJseSBuZXZlciBjb21taXRlZCB0byBzY3JlZW4gYW5kIGxvb2sgbGlrZSBhIGp1bXB5IGJveC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFoYXNMYXlvdXRDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmluaXNoQW5pbWF0aW9uKHRoaXMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNMZWFkKCkgJiYgdGhpcy5vcHRpb25zLm9uRXhpdENvbXBsZXRlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcHRpb25zLm9uRXhpdENvbXBsZXRlKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy50YXJnZXRMYXlvdXQgPSBuZXdMYXlvdXQ7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdW5tb3VudCgpIHtcbiAgICAgICAgICAgIHRoaXMub3B0aW9ucy5sYXlvdXRJZCAmJiB0aGlzLndpbGxVcGRhdGUoKTtcbiAgICAgICAgICAgIHRoaXMucm9vdC5ub2Rlcy5yZW1vdmUodGhpcyk7XG4gICAgICAgICAgICBjb25zdCBzdGFjayA9IHRoaXMuZ2V0U3RhY2soKTtcbiAgICAgICAgICAgIHN0YWNrICYmIHN0YWNrLnJlbW92ZSh0aGlzKTtcbiAgICAgICAgICAgIHRoaXMucGFyZW50ICYmIHRoaXMucGFyZW50LmNoaWxkcmVuLmRlbGV0ZSh0aGlzKTtcbiAgICAgICAgICAgIHRoaXMuaW5zdGFuY2UgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICBjYW5jZWxGcmFtZSh0aGlzLnVwZGF0ZVByb2plY3Rpb24pO1xuICAgICAgICB9XG4gICAgICAgIC8vIG9ubHkgb24gdGhlIHJvb3RcbiAgICAgICAgYmxvY2tVcGRhdGUoKSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZU1hbnVhbGx5QmxvY2tlZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgdW5ibG9ja1VwZGF0ZSgpIHtcbiAgICAgICAgICAgIHRoaXMudXBkYXRlTWFudWFsbHlCbG9ja2VkID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaXNVcGRhdGVCbG9ja2VkKCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMudXBkYXRlTWFudWFsbHlCbG9ja2VkIHx8IHRoaXMudXBkYXRlQmxvY2tlZEJ5UmVzaXplO1xuICAgICAgICB9XG4gICAgICAgIGlzVHJlZUFuaW1hdGlvbkJsb2NrZWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gKHRoaXMuaXNBbmltYXRpb25CbG9ja2VkIHx8XG4gICAgICAgICAgICAgICAgKHRoaXMucGFyZW50ICYmIHRoaXMucGFyZW50LmlzVHJlZUFuaW1hdGlvbkJsb2NrZWQoKSkgfHxcbiAgICAgICAgICAgICAgICBmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gTm90ZTogY3VycmVudGx5IG9ubHkgcnVubmluZyBvbiByb290IG5vZGVcbiAgICAgICAgc3RhcnRVcGRhdGUoKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5pc1VwZGF0ZUJsb2NrZWQoKSlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB0aGlzLmlzVXBkYXRpbmcgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5ub2RlcyAmJiB0aGlzLm5vZGVzLmZvckVhY2gocmVzZXRSb3RhdGlvbik7XG4gICAgICAgICAgICB0aGlzLmFuaW1hdGlvbklkKys7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0VHJhbnNmb3JtVGVtcGxhdGUoKSB7XG4gICAgICAgICAgICBjb25zdCB7IHZpc3VhbEVsZW1lbnQgfSA9IHRoaXMub3B0aW9ucztcbiAgICAgICAgICAgIHJldHVybiB2aXN1YWxFbGVtZW50ICYmIHZpc3VhbEVsZW1lbnQuZ2V0UHJvcHMoKS50cmFuc2Zvcm1UZW1wbGF0ZTtcbiAgICAgICAgfVxuICAgICAgICB3aWxsVXBkYXRlKHNob3VsZE5vdGlmeUxpc3RlbmVycyA9IHRydWUpIHtcbiAgICAgICAgICAgIHRoaXMucm9vdC5oYXNUcmVlQW5pbWF0ZWQgPSB0cnVlO1xuICAgICAgICAgICAgaWYgKHRoaXMucm9vdC5pc1VwZGF0ZUJsb2NrZWQoKSkge1xuICAgICAgICAgICAgICAgIHRoaXMub3B0aW9ucy5vbkV4aXRDb21wbGV0ZSAmJiB0aGlzLm9wdGlvbnMub25FeGl0Q29tcGxldGUoKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhdGhpcy5yb290LmlzVXBkYXRpbmcgJiYgdGhpcy5yb290LnN0YXJ0VXBkYXRlKCk7XG4gICAgICAgICAgICBpZiAodGhpcy5pc0xheW91dERpcnR5KVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIHRoaXMuaXNMYXlvdXREaXJ0eSA9IHRydWU7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMucGF0aC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLnBhdGhbaV07XG4gICAgICAgICAgICAgICAgbm9kZS5zaG91bGRSZXNldFRyYW5zZm9ybSA9IHRydWU7XG4gICAgICAgICAgICAgICAgbm9kZS51cGRhdGVTY3JvbGwoXCJzbmFwc2hvdFwiKTtcbiAgICAgICAgICAgICAgICBpZiAobm9kZS5vcHRpb25zLmxheW91dFJvb3QpIHtcbiAgICAgICAgICAgICAgICAgICAgbm9kZS53aWxsVXBkYXRlKGZhbHNlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB7IGxheW91dElkLCBsYXlvdXQgfSA9IHRoaXMub3B0aW9ucztcbiAgICAgICAgICAgIGlmIChsYXlvdXRJZCA9PT0gdW5kZWZpbmVkICYmICFsYXlvdXQpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtVGVtcGxhdGUgPSB0aGlzLmdldFRyYW5zZm9ybVRlbXBsYXRlKCk7XG4gICAgICAgICAgICB0aGlzLnByZXZUcmFuc2Zvcm1UZW1wbGF0ZVZhbHVlID0gdHJhbnNmb3JtVGVtcGxhdGVcbiAgICAgICAgICAgICAgICA/IHRyYW5zZm9ybVRlbXBsYXRlKHRoaXMubGF0ZXN0VmFsdWVzLCBcIlwiKVxuICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgdGhpcy51cGRhdGVTbmFwc2hvdCgpO1xuICAgICAgICAgICAgc2hvdWxkTm90aWZ5TGlzdGVuZXJzICYmIHRoaXMubm90aWZ5TGlzdGVuZXJzKFwid2lsbFVwZGF0ZVwiKTtcbiAgICAgICAgfVxuICAgICAgICB1cGRhdGUoKSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZVNjaGVkdWxlZCA9IGZhbHNlO1xuICAgICAgICAgICAgY29uc3QgdXBkYXRlV2FzQmxvY2tlZCA9IHRoaXMuaXNVcGRhdGVCbG9ja2VkKCk7XG4gICAgICAgICAgICAvLyBXaGVuIGRvaW5nIGFuIGluc3RhbnQgdHJhbnNpdGlvbiwgd2Ugc2tpcCB0aGUgbGF5b3V0IHVwZGF0ZSxcbiAgICAgICAgICAgIC8vIGJ1dCBzaG91bGQgc3RpbGwgY2xlYW4gdXAgdGhlIG1lYXN1cmVtZW50cyBzbyB0aGF0IHRoZSBuZXh0XG4gICAgICAgICAgICAvLyBzbmFwc2hvdCBjb3VsZCBiZSB0YWtlbiBjb3JyZWN0bHkuXG4gICAgICAgICAgICBpZiAodXBkYXRlV2FzQmxvY2tlZCkge1xuICAgICAgICAgICAgICAgIHRoaXMudW5ibG9ja1VwZGF0ZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMuY2xlYXJBbGxTbmFwc2hvdHMoKTtcbiAgICAgICAgICAgICAgICB0aGlzLm5vZGVzLmZvckVhY2goY2xlYXJNZWFzdXJlbWVudHMpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghdGhpcy5pc1VwZGF0aW5nKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5ub2Rlcy5mb3JFYWNoKGNsZWFySXNMYXlvdXREaXJ0eSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmlzVXBkYXRpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogV3JpdGVcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5ub2Rlcy5mb3JFYWNoKHJlc2V0VHJhbnNmb3JtU3R5bGUpO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBSZWFkID09PT09PT09PT09PT09PT09PVxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICAvLyBVcGRhdGUgbGF5b3V0IG1lYXN1cmVtZW50cyBvZiB1cGRhdGVkIGNoaWxkcmVuXG4gICAgICAgICAgICB0aGlzLm5vZGVzLmZvckVhY2godXBkYXRlTGF5b3V0KTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogV3JpdGVcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgLy8gTm90aWZ5IGxpc3RlbmVycyB0aGF0IHRoZSBsYXlvdXQgaXMgdXBkYXRlZFxuICAgICAgICAgICAgdGhpcy5ub2Rlcy5mb3JFYWNoKG5vdGlmeUxheW91dFVwZGF0ZSk7XG4gICAgICAgICAgICB0aGlzLmNsZWFyQWxsU25hcHNob3RzKCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIE1hbnVhbGx5IGZsdXNoIGFueSBwZW5kaW5nIHVwZGF0ZXMuIElkZWFsbHlcbiAgICAgICAgICAgICAqIHdlIGNvdWxkIGxlYXZlIHRoaXMgdG8gdGhlIGZvbGxvd2luZyByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgYnV0IHRoaXMgc2VlbXNcbiAgICAgICAgICAgICAqIHRvIGxlYXZlIGEgZmxhc2ggb2YgaW5jb3JyZWN0bHkgc3R5bGVkIGNvbnRlbnQuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGNvbnN0IG5vdyA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgICAgICAgICAgZnJhbWVEYXRhLmRlbHRhID0gY2xhbXAoMCwgMTAwMCAvIDYwLCBub3cgLSBmcmFtZURhdGEudGltZXN0YW1wKTtcbiAgICAgICAgICAgIGZyYW1lRGF0YS50aW1lc3RhbXAgPSBub3c7XG4gICAgICAgICAgICBmcmFtZURhdGEuaXNQcm9jZXNzaW5nID0gdHJ1ZTtcbiAgICAgICAgICAgIHN0ZXBzLnVwZGF0ZS5wcm9jZXNzKGZyYW1lRGF0YSk7XG4gICAgICAgICAgICBzdGVwcy5wcmVSZW5kZXIucHJvY2VzcyhmcmFtZURhdGEpO1xuICAgICAgICAgICAgc3RlcHMucmVuZGVyLnByb2Nlc3MoZnJhbWVEYXRhKTtcbiAgICAgICAgICAgIGZyYW1lRGF0YS5pc1Byb2Nlc3NpbmcgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBkaWRVcGRhdGUoKSB7XG4gICAgICAgICAgICBpZiAoIXRoaXMudXBkYXRlU2NoZWR1bGVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVTY2hlZHVsZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHF1ZXVlTWljcm90YXNrKCgpID0+IHRoaXMudXBkYXRlKCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNsZWFyQWxsU25hcHNob3RzKCkge1xuICAgICAgICAgICAgdGhpcy5ub2Rlcy5mb3JFYWNoKGNsZWFyU25hcHNob3QpO1xuICAgICAgICAgICAgdGhpcy5zaGFyZWROb2Rlcy5mb3JFYWNoKHJlbW92ZUxlYWRTbmFwc2hvdHMpO1xuICAgICAgICB9XG4gICAgICAgIHNjaGVkdWxlVXBkYXRlUHJvamVjdGlvbigpIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5wcm9qZWN0aW9uVXBkYXRlU2NoZWR1bGVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5wcm9qZWN0aW9uVXBkYXRlU2NoZWR1bGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBmcmFtZS5wcmVSZW5kZXIodGhpcy51cGRhdGVQcm9qZWN0aW9uLCBmYWxzZSwgdHJ1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgc2NoZWR1bGVDaGVja0FmdGVyVW5tb3VudCgpIHtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgdGhlIHVubW91bnRpbmcgbm9kZSBpcyBpbiBhIGxheW91dEdyb3VwIGFuZCBkaWQgdHJpZ2dlciBhIHdpbGxVcGRhdGUsXG4gICAgICAgICAgICAgKiB3ZSBtYW51YWxseSBjYWxsIGRpZFVwZGF0ZSB0byBnaXZlIGEgY2hhbmNlIHRvIHRoZSBzaWJsaW5ncyB0byBhbmltYXRlLlxuICAgICAgICAgICAgICogT3RoZXJ3aXNlLCBjbGVhbnVwIGFsbCBzbmFwc2hvdHMgdG8gcHJldmVudHMgZnV0dXJlIG5vZGVzIGZyb20gcmV1c2luZyB0aGVtLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBmcmFtZS5wb3N0UmVuZGVyKCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5pc0xheW91dERpcnR5KSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucm9vdC5kaWRVcGRhdGUoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucm9vdC5jaGVja1VwZGF0ZUZhaWxlZCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBVcGRhdGUgbWVhc3VyZW1lbnRzXG4gICAgICAgICAqL1xuICAgICAgICB1cGRhdGVTbmFwc2hvdCgpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnNuYXBzaG90IHx8ICF0aGlzLmluc3RhbmNlKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIHRoaXMuc25hcHNob3QgPSB0aGlzLm1lYXN1cmUoKTtcbiAgICAgICAgfVxuICAgICAgICB1cGRhdGVMYXlvdXQoKSB7XG4gICAgICAgICAgICBpZiAoIXRoaXMuaW5zdGFuY2UpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgLy8gVE9ETzogSW5jb3Jwb3JhdGUgaW50byBhIGZvcndhcmRlZCBzY3JvbGwgb2Zmc2V0XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZVNjcm9sbCgpO1xuICAgICAgICAgICAgaWYgKCEodGhpcy5vcHRpb25zLmFsd2F5c01lYXN1cmVMYXlvdXQgJiYgdGhpcy5pc0xlYWQoKSkgJiZcbiAgICAgICAgICAgICAgICAhdGhpcy5pc0xheW91dERpcnR5KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBXaGVuIGEgbm9kZSBpcyBtb3VudGVkLCBpdCBzaW1wbHkgcmVzdW1lcyBmcm9tIHRoZSBwcmV2TGVhZCdzXG4gICAgICAgICAgICAgKiBzbmFwc2hvdCBpbnN0ZWFkIG9mIHRha2luZyBhIG5ldyBvbmUsIGJ1dCB0aGUgYW5jZXN0b3JzIHNjcm9sbFxuICAgICAgICAgICAgICogbWlnaHQgaGF2ZSB1cGRhdGVkIHdoaWxlIHRoZSBwcmV2TGVhZCBpcyB1bm1vdW50ZWQuIFdlIG5lZWQgdG9cbiAgICAgICAgICAgICAqIHVwZGF0ZSB0aGUgc2Nyb2xsIGFnYWluIHRvIG1ha2Ugc3VyZSB0aGUgbGF5b3V0IHdlIG1lYXN1cmUgaXNcbiAgICAgICAgICAgICAqIHVwIHRvIGRhdGUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICh0aGlzLnJlc3VtZUZyb20gJiYgIXRoaXMucmVzdW1lRnJvbS5pbnN0YW5jZSkge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5wYXRoLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLnBhdGhbaV07XG4gICAgICAgICAgICAgICAgICAgIG5vZGUudXBkYXRlU2Nyb2xsKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgcHJldkxheW91dCA9IHRoaXMubGF5b3V0O1xuICAgICAgICAgICAgdGhpcy5sYXlvdXQgPSB0aGlzLm1lYXN1cmUoZmFsc2UpO1xuICAgICAgICAgICAgdGhpcy5sYXlvdXRDb3JyZWN0ZWQgPSBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgIHRoaXMuaXNMYXlvdXREaXJ0eSA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5wcm9qZWN0aW9uRGVsdGEgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB0aGlzLm5vdGlmeUxpc3RlbmVycyhcIm1lYXN1cmVcIiwgdGhpcy5sYXlvdXQubGF5b3V0Qm94KTtcbiAgICAgICAgICAgIGNvbnN0IHsgdmlzdWFsRWxlbWVudCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgdmlzdWFsRWxlbWVudCAmJlxuICAgICAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQubm90aWZ5KFwiTGF5b3V0TWVhc3VyZVwiLCB0aGlzLmxheW91dC5sYXlvdXRCb3gsIHByZXZMYXlvdXQgPyBwcmV2TGF5b3V0LmxheW91dEJveCA6IHVuZGVmaW5lZCk7XG4gICAgICAgIH1cbiAgICAgICAgdXBkYXRlU2Nyb2xsKHBoYXNlID0gXCJtZWFzdXJlXCIpIHtcbiAgICAgICAgICAgIGxldCBuZWVkc01lYXN1cmVtZW50ID0gQm9vbGVhbih0aGlzLm9wdGlvbnMubGF5b3V0U2Nyb2xsICYmIHRoaXMuaW5zdGFuY2UpO1xuICAgICAgICAgICAgaWYgKHRoaXMuc2Nyb2xsICYmXG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGwuYW5pbWF0aW9uSWQgPT09IHRoaXMucm9vdC5hbmltYXRpb25JZCAmJlxuICAgICAgICAgICAgICAgIHRoaXMuc2Nyb2xsLnBoYXNlID09PSBwaGFzZSkge1xuICAgICAgICAgICAgICAgIG5lZWRzTWVhc3VyZW1lbnQgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChuZWVkc01lYXN1cmVtZW50KSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGwgPSB7XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbklkOiB0aGlzLnJvb3QuYW5pbWF0aW9uSWQsXG4gICAgICAgICAgICAgICAgICAgIHBoYXNlLFxuICAgICAgICAgICAgICAgICAgICBpc1Jvb3Q6IGNoZWNrSXNTY3JvbGxSb290KHRoaXMuaW5zdGFuY2UpLFxuICAgICAgICAgICAgICAgICAgICBvZmZzZXQ6IG1lYXN1cmVTY3JvbGwodGhpcy5pbnN0YW5jZSksXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXNldFRyYW5zZm9ybSgpIHtcbiAgICAgICAgICAgIGlmICghcmVzZXRUcmFuc2Zvcm0pXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgaXNSZXNldFJlcXVlc3RlZCA9IHRoaXMuaXNMYXlvdXREaXJ0eSB8fCB0aGlzLnNob3VsZFJlc2V0VHJhbnNmb3JtO1xuICAgICAgICAgICAgY29uc3QgaGFzUHJvamVjdGlvbiA9IHRoaXMucHJvamVjdGlvbkRlbHRhICYmICFpc0RlbHRhWmVybyh0aGlzLnByb2plY3Rpb25EZWx0YSk7XG4gICAgICAgICAgICBjb25zdCB0cmFuc2Zvcm1UZW1wbGF0ZSA9IHRoaXMuZ2V0VHJhbnNmb3JtVGVtcGxhdGUoKTtcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zZm9ybVRlbXBsYXRlVmFsdWUgPSB0cmFuc2Zvcm1UZW1wbGF0ZVxuICAgICAgICAgICAgICAgID8gdHJhbnNmb3JtVGVtcGxhdGUodGhpcy5sYXRlc3RWYWx1ZXMsIFwiXCIpXG4gICAgICAgICAgICAgICAgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICBjb25zdCB0cmFuc2Zvcm1UZW1wbGF0ZUhhc0NoYW5nZWQgPSB0cmFuc2Zvcm1UZW1wbGF0ZVZhbHVlICE9PSB0aGlzLnByZXZUcmFuc2Zvcm1UZW1wbGF0ZVZhbHVlO1xuICAgICAgICAgICAgaWYgKGlzUmVzZXRSZXF1ZXN0ZWQgJiZcbiAgICAgICAgICAgICAgICAoaGFzUHJvamVjdGlvbiB8fFxuICAgICAgICAgICAgICAgICAgICBoYXNUcmFuc2Zvcm0odGhpcy5sYXRlc3RWYWx1ZXMpIHx8XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybVRlbXBsYXRlSGFzQ2hhbmdlZCkpIHtcbiAgICAgICAgICAgICAgICByZXNldFRyYW5zZm9ybSh0aGlzLmluc3RhbmNlLCB0cmFuc2Zvcm1UZW1wbGF0ZVZhbHVlKTtcbiAgICAgICAgICAgICAgICB0aGlzLnNob3VsZFJlc2V0VHJhbnNmb3JtID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5zY2hlZHVsZVJlbmRlcigpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIG1lYXN1cmUocmVtb3ZlVHJhbnNmb3JtID0gdHJ1ZSkge1xuICAgICAgICAgICAgY29uc3QgcGFnZUJveCA9IHRoaXMubWVhc3VyZVBhZ2VCb3goKTtcbiAgICAgICAgICAgIGxldCBsYXlvdXRCb3ggPSB0aGlzLnJlbW92ZUVsZW1lbnRTY3JvbGwocGFnZUJveCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIE1lYXN1cmVtZW50cyB0YWtlbiBkdXJpbmcgdGhlIHByZS1yZW5kZXIgc3RhZ2VcbiAgICAgICAgICAgICAqIHN0aWxsIGhhdmUgdHJhbnNmb3JtcyBhcHBsaWVkIHNvIHdlIHJlbW92ZSB0aGVtXG4gICAgICAgICAgICAgKiB2aWEgY2FsY3VsYXRpb24uXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmIChyZW1vdmVUcmFuc2Zvcm0pIHtcbiAgICAgICAgICAgICAgICBsYXlvdXRCb3ggPSB0aGlzLnJlbW92ZVRyYW5zZm9ybShsYXlvdXRCb3gpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcm91bmRCb3gobGF5b3V0Qm94KTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uSWQ6IHRoaXMucm9vdC5hbmltYXRpb25JZCxcbiAgICAgICAgICAgICAgICBtZWFzdXJlZEJveDogcGFnZUJveCxcbiAgICAgICAgICAgICAgICBsYXlvdXRCb3gsXG4gICAgICAgICAgICAgICAgbGF0ZXN0VmFsdWVzOiB7fSxcbiAgICAgICAgICAgICAgICBzb3VyY2U6IHRoaXMuaWQsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIG1lYXN1cmVQYWdlQm94KCkge1xuICAgICAgICAgICAgY29uc3QgeyB2aXN1YWxFbGVtZW50IH0gPSB0aGlzLm9wdGlvbnM7XG4gICAgICAgICAgICBpZiAoIXZpc3VhbEVsZW1lbnQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgY29uc3QgYm94ID0gdmlzdWFsRWxlbWVudC5tZWFzdXJlVmlld3BvcnRCb3goKTtcbiAgICAgICAgICAgIC8vIFJlbW92ZSB2aWV3cG9ydCBzY3JvbGwgdG8gZ2l2ZSBwYWdlLXJlbGF0aXZlIGNvb3JkaW5hdGVzXG4gICAgICAgICAgICBjb25zdCB7IHNjcm9sbCB9ID0gdGhpcy5yb290O1xuICAgICAgICAgICAgaWYgKHNjcm9sbCkge1xuICAgICAgICAgICAgICAgIHRyYW5zbGF0ZUF4aXMoYm94LngsIHNjcm9sbC5vZmZzZXQueCk7XG4gICAgICAgICAgICAgICAgdHJhbnNsYXRlQXhpcyhib3gueSwgc2Nyb2xsLm9mZnNldC55KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBib3g7XG4gICAgICAgIH1cbiAgICAgICAgcmVtb3ZlRWxlbWVudFNjcm9sbChib3gpIHtcbiAgICAgICAgICAgIGNvbnN0IGJveFdpdGhvdXRTY3JvbGwgPSBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgIGNvcHlCb3hJbnRvKGJveFdpdGhvdXRTY3JvbGwsIGJveCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFBlcmZvcm1hbmNlIFRPRE86IEtlZXAgYSBjdW11bGF0aXZlIHNjcm9sbCBvZmZzZXQgZG93biB0aGUgdHJlZVxuICAgICAgICAgICAgICogcmF0aGVyIHRoYW4gbG9vcCBiYWNrIHVwIHRoZSBwYXRoLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMucGF0aC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLnBhdGhbaV07XG4gICAgICAgICAgICAgICAgY29uc3QgeyBzY3JvbGwsIG9wdGlvbnMgfSA9IG5vZGU7XG4gICAgICAgICAgICAgICAgaWYgKG5vZGUgIT09IHRoaXMucm9vdCAmJiBzY3JvbGwgJiYgb3B0aW9ucy5sYXlvdXRTY3JvbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICAgICAqIElmIHRoaXMgaXMgYSBuZXcgc2Nyb2xsIHJvb3QsIHdlIHdhbnQgdG8gcmVtb3ZlIGFsbCBwcmV2aW91cyBzY3JvbGxzXG4gICAgICAgICAgICAgICAgICAgICAqIGZyb20gdGhlIHZpZXdwb3J0IGJveC5cbiAgICAgICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgICAgIGlmIChzY3JvbGwuaXNSb290KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb3B5Qm94SW50byhib3hXaXRob3V0U2Nyb2xsLCBib3gpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBzY3JvbGw6IHJvb3RTY3JvbGwgfSA9IHRoaXMucm9vdDtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAgICAgICAgICogVW5kbyB0aGUgYXBwbGljYXRpb24gb2YgcGFnZSBzY3JvbGwgdGhhdCB3YXMgb3JpZ2luYWxseSBhZGRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICogdG8gdGhlIG1lYXN1cmVkIGJvdW5kaW5nIGJveC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvb3RTY3JvbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2xhdGVBeGlzKGJveFdpdGhvdXRTY3JvbGwueCwgLXJvb3RTY3JvbGwub2Zmc2V0LngpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zbGF0ZUF4aXMoYm94V2l0aG91dFNjcm9sbC55LCAtcm9vdFNjcm9sbC5vZmZzZXQueSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdHJhbnNsYXRlQXhpcyhib3hXaXRob3V0U2Nyb2xsLngsIHNjcm9sbC5vZmZzZXQueCk7XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zbGF0ZUF4aXMoYm94V2l0aG91dFNjcm9sbC55LCBzY3JvbGwub2Zmc2V0LnkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBib3hXaXRob3V0U2Nyb2xsO1xuICAgICAgICB9XG4gICAgICAgIGFwcGx5VHJhbnNmb3JtKGJveCwgdHJhbnNmb3JtT25seSA9IGZhbHNlKSB7XG4gICAgICAgICAgICBjb25zdCB3aXRoVHJhbnNmb3JtcyA9IGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgY29weUJveEludG8od2l0aFRyYW5zZm9ybXMsIGJveCk7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMucGF0aC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLnBhdGhbaV07XG4gICAgICAgICAgICAgICAgaWYgKCF0cmFuc2Zvcm1Pbmx5ICYmXG4gICAgICAgICAgICAgICAgICAgIG5vZGUub3B0aW9ucy5sYXlvdXRTY3JvbGwgJiZcbiAgICAgICAgICAgICAgICAgICAgbm9kZS5zY3JvbGwgJiZcbiAgICAgICAgICAgICAgICAgICAgbm9kZSAhPT0gbm9kZS5yb290KSB7XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybUJveCh3aXRoVHJhbnNmb3Jtcywge1xuICAgICAgICAgICAgICAgICAgICAgICAgeDogLW5vZGUuc2Nyb2xsLm9mZnNldC54LFxuICAgICAgICAgICAgICAgICAgICAgICAgeTogLW5vZGUuc2Nyb2xsLm9mZnNldC55LFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFoYXNUcmFuc2Zvcm0obm9kZS5sYXRlc3RWYWx1ZXMpKVxuICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm1Cb3god2l0aFRyYW5zZm9ybXMsIG5vZGUubGF0ZXN0VmFsdWVzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChoYXNUcmFuc2Zvcm0odGhpcy5sYXRlc3RWYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtQm94KHdpdGhUcmFuc2Zvcm1zLCB0aGlzLmxhdGVzdFZhbHVlcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gd2l0aFRyYW5zZm9ybXM7XG4gICAgICAgIH1cbiAgICAgICAgcmVtb3ZlVHJhbnNmb3JtKGJveCkge1xuICAgICAgICAgICAgY29uc3QgYm94V2l0aG91dFRyYW5zZm9ybSA9IGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgY29weUJveEludG8oYm94V2l0aG91dFRyYW5zZm9ybSwgYm94KTtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5wYXRoLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgbm9kZSA9IHRoaXMucGF0aFtpXTtcbiAgICAgICAgICAgICAgICBpZiAoIW5vZGUuaW5zdGFuY2UpXG4gICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgIGlmICghaGFzVHJhbnNmb3JtKG5vZGUubGF0ZXN0VmFsdWVzKSlcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgaGFzU2NhbGUobm9kZS5sYXRlc3RWYWx1ZXMpICYmIG5vZGUudXBkYXRlU25hcHNob3QoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBzb3VyY2VCb3ggPSBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgICAgICBjb25zdCBub2RlQm94ID0gbm9kZS5tZWFzdXJlUGFnZUJveCgpO1xuICAgICAgICAgICAgICAgIGNvcHlCb3hJbnRvKHNvdXJjZUJveCwgbm9kZUJveCk7XG4gICAgICAgICAgICAgICAgcmVtb3ZlQm94VHJhbnNmb3Jtcyhib3hXaXRob3V0VHJhbnNmb3JtLCBub2RlLmxhdGVzdFZhbHVlcywgbm9kZS5zbmFwc2hvdCA/IG5vZGUuc25hcHNob3QubGF5b3V0Qm94IDogdW5kZWZpbmVkLCBzb3VyY2VCb3gpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGhhc1RyYW5zZm9ybSh0aGlzLmxhdGVzdFZhbHVlcykpIHtcbiAgICAgICAgICAgICAgICByZW1vdmVCb3hUcmFuc2Zvcm1zKGJveFdpdGhvdXRUcmFuc2Zvcm0sIHRoaXMubGF0ZXN0VmFsdWVzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBib3hXaXRob3V0VHJhbnNmb3JtO1xuICAgICAgICB9XG4gICAgICAgIHNldFRhcmdldERlbHRhKGRlbHRhKSB7XG4gICAgICAgICAgICB0aGlzLnRhcmdldERlbHRhID0gZGVsdGE7XG4gICAgICAgICAgICB0aGlzLnJvb3Quc2NoZWR1bGVVcGRhdGVQcm9qZWN0aW9uKCk7XG4gICAgICAgICAgICB0aGlzLmlzUHJvamVjdGlvbkRpcnR5ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBzZXRPcHRpb25zKG9wdGlvbnMpIHtcbiAgICAgICAgICAgIHRoaXMub3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICAuLi50aGlzLm9wdGlvbnMsXG4gICAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgICAgICBjcm9zc2ZhZGU6IG9wdGlvbnMuY3Jvc3NmYWRlICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLmNyb3NzZmFkZSA6IHRydWUsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGNsZWFyTWVhc3VyZW1lbnRzKCkge1xuICAgICAgICAgICAgdGhpcy5zY3JvbGwgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB0aGlzLmxheW91dCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIHRoaXMuc25hcHNob3QgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB0aGlzLnByZXZUcmFuc2Zvcm1UZW1wbGF0ZVZhbHVlID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgdGhpcy50YXJnZXREZWx0YSA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIHRoaXMudGFyZ2V0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgdGhpcy5pc0xheW91dERpcnR5ID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgZm9yY2VSZWxhdGl2ZVBhcmVudFRvUmVzb2x2ZVRhcmdldCgpIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5yZWxhdGl2ZVBhcmVudClcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoZSBwYXJlbnQgdGFyZ2V0IGlzbid0IHVwLXRvLWRhdGUsIGZvcmNlIGl0IHRvIHVwZGF0ZS5cbiAgICAgICAgICAgICAqIFRoaXMgaXMgYW4gdW5mb3J0dW5hdGUgZGUtb3B0aW1pc2F0aW9uIGFzIGl0IG1lYW5zIGFueSB1cGRhdGluZyByZWxhdGl2ZVxuICAgICAgICAgICAgICogcHJvamVjdGlvbiB3aWxsIGNhdXNlIGFsbCB0aGUgcmVsYXRpdmUgcGFyZW50cyB0byByZWNhbGN1bGF0ZSBiYWNrXG4gICAgICAgICAgICAgKiB1cCB0aGUgdHJlZS5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMucmVsYXRpdmVQYXJlbnQucmVzb2x2ZWRSZWxhdGl2ZVRhcmdldEF0ICE9PVxuICAgICAgICAgICAgICAgIGZyYW1lRGF0YS50aW1lc3RhbXApIHtcbiAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlUGFyZW50LnJlc29sdmVUYXJnZXREZWx0YSh0cnVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlVGFyZ2V0RGVsdGEoZm9yY2VSZWNhbGN1bGF0aW9uID0gZmFsc2UpIHtcbiAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogT25jZSB0aGUgZGlydHkgc3RhdHVzIG9mIG5vZGVzIGhhcyBiZWVuIHNwcmVhZCB0aHJvdWdoIHRoZSB0cmVlLCB3ZSBhbHNvXG4gICAgICAgICAgICAgKiBuZWVkIHRvIGNoZWNrIGlmIHdlIGhhdmUgYSBzaGFyZWQgbm9kZSBvZiBhIGRpZmZlcmVudCBkZXB0aCB0aGF0IGhhcyBpdHNlbGZcbiAgICAgICAgICAgICAqIGJlZW4gZGlydGllZC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY29uc3QgbGVhZCA9IHRoaXMuZ2V0TGVhZCgpO1xuICAgICAgICAgICAgdGhpcy5pc1Byb2plY3Rpb25EaXJ0eSB8fCAodGhpcy5pc1Byb2plY3Rpb25EaXJ0eSA9IGxlYWQuaXNQcm9qZWN0aW9uRGlydHkpO1xuICAgICAgICAgICAgdGhpcy5pc1RyYW5zZm9ybURpcnR5IHx8ICh0aGlzLmlzVHJhbnNmb3JtRGlydHkgPSBsZWFkLmlzVHJhbnNmb3JtRGlydHkpO1xuICAgICAgICAgICAgdGhpcy5pc1NoYXJlZFByb2plY3Rpb25EaXJ0eSB8fCAodGhpcy5pc1NoYXJlZFByb2plY3Rpb25EaXJ0eSA9IGxlYWQuaXNTaGFyZWRQcm9qZWN0aW9uRGlydHkpO1xuICAgICAgICAgICAgY29uc3QgaXNTaGFyZWQgPSBCb29sZWFuKHRoaXMucmVzdW1pbmdGcm9tKSB8fCB0aGlzICE9PSBsZWFkO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBXZSBkb24ndCB1c2UgdHJhbnNmb3JtIGZvciB0aGlzIHN0ZXAgb2YgcHJvY2Vzc2luZyBzbyB3ZSBkb24ndFxuICAgICAgICAgICAgICogbmVlZCB0byBjaGVjayB3aGV0aGVyIGFueSBub2RlcyBoYXZlIGNoYW5nZWQgdHJhbnNmb3JtLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCBjYW5Ta2lwID0gIShmb3JjZVJlY2FsY3VsYXRpb24gfHxcbiAgICAgICAgICAgICAgICAoaXNTaGFyZWQgJiYgdGhpcy5pc1NoYXJlZFByb2plY3Rpb25EaXJ0eSkgfHxcbiAgICAgICAgICAgICAgICB0aGlzLmlzUHJvamVjdGlvbkRpcnR5IHx8XG4gICAgICAgICAgICAgICAgKChfYSA9IHRoaXMucGFyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuaXNQcm9qZWN0aW9uRGlydHkpIHx8XG4gICAgICAgICAgICAgICAgdGhpcy5hdHRlbXB0VG9SZXNvbHZlUmVsYXRpdmVUYXJnZXQpO1xuICAgICAgICAgICAgaWYgKGNhblNraXApXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgeyBsYXlvdXQsIGxheW91dElkIH0gPSB0aGlzLm9wdGlvbnM7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHdlIGhhdmUgbm8gbGF5b3V0LCB3ZSBjYW4ndCBwZXJmb3JtIHByb2plY3Rpb24sIHNvIGVhcmx5IHJldHVyblxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBpZiAoIXRoaXMubGF5b3V0IHx8ICEobGF5b3V0IHx8IGxheW91dElkKSlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB0aGlzLnJlc29sdmVkUmVsYXRpdmVUYXJnZXRBdCA9IGZyYW1lRGF0YS50aW1lc3RhbXA7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHdlIGRvbid0IGhhdmUgYSB0YXJnZXREZWx0YSBidXQgZG8gaGF2ZSBhIGxheW91dCwgd2UgY2FuIGF0dGVtcHQgdG8gcmVzb2x2ZVxuICAgICAgICAgICAgICogYSByZWxhdGl2ZVBhcmVudC4gVGhpcyB3aWxsIGFsbG93IGEgY29tcG9uZW50IHRvIHBlcmZvcm0gc2NhbGUgY29ycmVjdGlvblxuICAgICAgICAgICAgICogZXZlbiBpZiBubyBhbmltYXRpb24gaGFzIHN0YXJ0ZWQuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIC8vIFRPRE8gSWYgdGhpcyBpcyB1bnN1Y2Nlc3NmdWwgdGhpcyBjdXJyZW50bHkgaGFwcGVucyBldmVyeSBmcmFtZVxuICAgICAgICAgICAgaWYgKCF0aGlzLnRhcmdldERlbHRhICYmICF0aGlzLnJlbGF0aXZlVGFyZ2V0KSB7XG4gICAgICAgICAgICAgICAgLy8gVE9ETzogVGhpcyBpcyBhIHNlbWktcmVwZXRpdGlvbiBvZiBmdXJ0aGVyIGRvd24gdGhpcyBmdW5jdGlvbiwgbWFrZSBEUllcbiAgICAgICAgICAgICAgICBjb25zdCByZWxhdGl2ZVBhcmVudCA9IHRoaXMuZ2V0Q2xvc2VzdFByb2plY3RpbmdQYXJlbnQoKTtcbiAgICAgICAgICAgICAgICBpZiAocmVsYXRpdmVQYXJlbnQgJiZcbiAgICAgICAgICAgICAgICAgICAgcmVsYXRpdmVQYXJlbnQubGF5b3V0ICYmXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYW5pbWF0aW9uUHJvZ3Jlc3MgIT09IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVBhcmVudCA9IHJlbGF0aXZlUGFyZW50O1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcmNlUmVsYXRpdmVQYXJlbnRUb1Jlc29sdmVUYXJnZXQoKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVRhcmdldCA9IGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlVGFyZ2V0T3JpZ2luID0gY3JlYXRlQm94KCk7XG4gICAgICAgICAgICAgICAgICAgIGNhbGNSZWxhdGl2ZVBvc2l0aW9uKHRoaXMucmVsYXRpdmVUYXJnZXRPcmlnaW4sIHRoaXMubGF5b3V0LmxheW91dEJveCwgcmVsYXRpdmVQYXJlbnQubGF5b3V0LmxheW91dEJveCk7XG4gICAgICAgICAgICAgICAgICAgIGNvcHlCb3hJbnRvKHRoaXMucmVsYXRpdmVUYXJnZXQsIHRoaXMucmVsYXRpdmVUYXJnZXRPcmlnaW4pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVBhcmVudCA9IHRoaXMucmVsYXRpdmVUYXJnZXQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB3ZSBoYXZlIG5vIHJlbGF0aXZlIHRhcmdldCBvciBubyB0YXJnZXQgZGVsdGEgb3VyIHRhcmdldCBpc24ndCB2YWxpZFxuICAgICAgICAgICAgICogZm9yIHRoaXMgZnJhbWUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICghdGhpcy5yZWxhdGl2ZVRhcmdldCAmJiAhdGhpcy50YXJnZXREZWx0YSlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIExhenktaW5pdCB0YXJnZXQgZGF0YSBzdHJ1Y3R1cmVcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKCF0aGlzLnRhcmdldCkge1xuICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0ID0gY3JlYXRlQm94KCk7XG4gICAgICAgICAgICAgICAgdGhpcy50YXJnZXRXaXRoVHJhbnNmb3JtcyA9IGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB3ZSd2ZSBnb3QgYSByZWxhdGl2ZSBib3ggZm9yIHRoaXMgY29tcG9uZW50LCByZXNvbHZlIGl0IGludG8gYSB0YXJnZXQgcmVsYXRpdmUgdG8gdGhlIHBhcmVudC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMucmVsYXRpdmVUYXJnZXQgJiZcbiAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlVGFyZ2V0T3JpZ2luICYmXG4gICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVBhcmVudCAmJlxuICAgICAgICAgICAgICAgIHRoaXMucmVsYXRpdmVQYXJlbnQudGFyZ2V0KSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mb3JjZVJlbGF0aXZlUGFyZW50VG9SZXNvbHZlVGFyZ2V0KCk7XG4gICAgICAgICAgICAgICAgY2FsY1JlbGF0aXZlQm94KHRoaXMudGFyZ2V0LCB0aGlzLnJlbGF0aXZlVGFyZ2V0LCB0aGlzLnJlbGF0aXZlUGFyZW50LnRhcmdldCk7XG4gICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICogSWYgd2UndmUgb25seSBnb3QgYSB0YXJnZXREZWx0YSwgcmVzb2x2ZSBpdCBpbnRvIGEgdGFyZ2V0XG4gICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0aGlzLnRhcmdldERlbHRhKSB7XG4gICAgICAgICAgICAgICAgaWYgKEJvb2xlYW4odGhpcy5yZXN1bWluZ0Zyb20pKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IFRoaXMgaXMgY3JlYXRpbmcgYSBuZXcgb2JqZWN0IGV2ZXJ5IGZyYW1lXG4gICAgICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0ID0gdGhpcy5hcHBseVRyYW5zZm9ybSh0aGlzLmxheW91dC5sYXlvdXRCb3gpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY29weUJveEludG8odGhpcy50YXJnZXQsIHRoaXMubGF5b3V0LmxheW91dEJveCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGFwcGx5Qm94RGVsdGEodGhpcy50YXJnZXQsIHRoaXMudGFyZ2V0RGVsdGEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICogSWYgbm8gdGFyZ2V0LCB1c2Ugb3duIGxheW91dCBhcyB0YXJnZXRcbiAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICBjb3B5Qm94SW50byh0aGlzLnRhcmdldCwgdGhpcy5sYXlvdXQubGF5b3V0Qm94KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgd2UndmUgYmVlbiB0b2xkIHRvIGF0dGVtcHQgdG8gcmVzb2x2ZSBhIHJlbGF0aXZlIHRhcmdldCwgZG8gc28uXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICh0aGlzLmF0dGVtcHRUb1Jlc29sdmVSZWxhdGl2ZVRhcmdldCkge1xuICAgICAgICAgICAgICAgIHRoaXMuYXR0ZW1wdFRvUmVzb2x2ZVJlbGF0aXZlVGFyZ2V0ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVsYXRpdmVQYXJlbnQgPSB0aGlzLmdldENsb3Nlc3RQcm9qZWN0aW5nUGFyZW50KCk7XG4gICAgICAgICAgICAgICAgaWYgKHJlbGF0aXZlUGFyZW50ICYmXG4gICAgICAgICAgICAgICAgICAgIEJvb2xlYW4ocmVsYXRpdmVQYXJlbnQucmVzdW1pbmdGcm9tKSA9PT1cbiAgICAgICAgICAgICAgICAgICAgICAgIEJvb2xlYW4odGhpcy5yZXN1bWluZ0Zyb20pICYmXG4gICAgICAgICAgICAgICAgICAgICFyZWxhdGl2ZVBhcmVudC5vcHRpb25zLmxheW91dFNjcm9sbCAmJlxuICAgICAgICAgICAgICAgICAgICByZWxhdGl2ZVBhcmVudC50YXJnZXQgJiZcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hbmltYXRpb25Qcm9ncmVzcyAhPT0gMSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlUGFyZW50ID0gcmVsYXRpdmVQYXJlbnQ7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZm9yY2VSZWxhdGl2ZVBhcmVudFRvUmVzb2x2ZVRhcmdldCgpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlVGFyZ2V0ID0gY3JlYXRlQm94KCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVsYXRpdmVUYXJnZXRPcmlnaW4gPSBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgICAgICAgICAgY2FsY1JlbGF0aXZlUG9zaXRpb24odGhpcy5yZWxhdGl2ZVRhcmdldE9yaWdpbiwgdGhpcy50YXJnZXQsIHJlbGF0aXZlUGFyZW50LnRhcmdldCk7XG4gICAgICAgICAgICAgICAgICAgIGNvcHlCb3hJbnRvKHRoaXMucmVsYXRpdmVUYXJnZXQsIHRoaXMucmVsYXRpdmVUYXJnZXRPcmlnaW4pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVBhcmVudCA9IHRoaXMucmVsYXRpdmVUYXJnZXQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJbmNyZWFzZSBkZWJ1ZyBjb3VudGVyIGZvciByZXNvbHZlZCB0YXJnZXQgZGVsdGFzXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHByb2plY3Rpb25GcmFtZURhdGEucmVzb2x2ZWRUYXJnZXREZWx0YXMrKztcbiAgICAgICAgfVxuICAgICAgICBnZXRDbG9zZXN0UHJvamVjdGluZ1BhcmVudCgpIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5wYXJlbnQgfHxcbiAgICAgICAgICAgICAgICBoYXNTY2FsZSh0aGlzLnBhcmVudC5sYXRlc3RWYWx1ZXMpIHx8XG4gICAgICAgICAgICAgICAgaGFzMkRUcmFuc2xhdGUodGhpcy5wYXJlbnQubGF0ZXN0VmFsdWVzKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAodGhpcy5wYXJlbnQuaXNQcm9qZWN0aW5nKCkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQuZ2V0Q2xvc2VzdFByb2plY3RpbmdQYXJlbnQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpc1Byb2plY3RpbmcoKSB7XG4gICAgICAgICAgICByZXR1cm4gQm9vbGVhbigodGhpcy5yZWxhdGl2ZVRhcmdldCB8fFxuICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0RGVsdGEgfHxcbiAgICAgICAgICAgICAgICB0aGlzLm9wdGlvbnMubGF5b3V0Um9vdCkgJiZcbiAgICAgICAgICAgICAgICB0aGlzLmxheW91dCk7XG4gICAgICAgIH1cbiAgICAgICAgY2FsY1Byb2plY3Rpb24oKSB7XG4gICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICBjb25zdCBsZWFkID0gdGhpcy5nZXRMZWFkKCk7XG4gICAgICAgICAgICBjb25zdCBpc1NoYXJlZCA9IEJvb2xlYW4odGhpcy5yZXN1bWluZ0Zyb20pIHx8IHRoaXMgIT09IGxlYWQ7XG4gICAgICAgICAgICBsZXQgY2FuU2tpcCA9IHRydWU7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoaXMgaXMgYSBub3JtYWwgbGF5b3V0IGFuaW1hdGlvbiBhbmQgbmVpdGhlciB0aGlzIG5vZGUgbm9yIGl0cyBuZWFyZXN0IHByb2plY3RpbmdcbiAgICAgICAgICAgICAqIGlzIGRpcnR5IHRoZW4gd2UgY2FuJ3Qgc2tpcC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNQcm9qZWN0aW9uRGlydHkgfHwgKChfYSA9IHRoaXMucGFyZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuaXNQcm9qZWN0aW9uRGlydHkpKSB7XG4gICAgICAgICAgICAgICAgY2FuU2tpcCA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB0aGlzIGlzIGEgc2hhcmVkIGxheW91dCBhbmltYXRpb24gYW5kIHRoaXMgbm9kZSdzIHNoYXJlZCBwcm9qZWN0aW9uIGlzIGRpcnR5IHRoZW5cbiAgICAgICAgICAgICAqIHdlIGNhbid0IHNraXAuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmIChpc1NoYXJlZCAmJlxuICAgICAgICAgICAgICAgICh0aGlzLmlzU2hhcmVkUHJvamVjdGlvbkRpcnR5IHx8IHRoaXMuaXNUcmFuc2Zvcm1EaXJ0eSkpIHtcbiAgICAgICAgICAgICAgICBjYW5Ta2lwID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHdlIGhhdmUgcmVzb2x2ZWQgdGhlIHRhcmdldCB0aGlzIGZyYW1lIHdlIG11c3QgcmVjYWxjdWxhdGUgdGhlXG4gICAgICAgICAgICAgKiBwcm9qZWN0aW9uIHRvIGVuc3VyZSBpdCB2aXN1YWxseSByZXByZXNlbnRzIHRoZSBpbnRlcm5hbCBjYWxjdWxhdGlvbnMuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICh0aGlzLnJlc29sdmVkUmVsYXRpdmVUYXJnZXRBdCA9PT0gZnJhbWVEYXRhLnRpbWVzdGFtcCkge1xuICAgICAgICAgICAgICAgIGNhblNraXAgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjYW5Ta2lwKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGNvbnN0IHsgbGF5b3V0LCBsYXlvdXRJZCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB0aGlzIHNlY3Rpb24gb2YgdGhlIHRyZWUgaXNuJ3QgYW5pbWF0aW5nIHdlIGNhblxuICAgICAgICAgICAgICogZGVsZXRlIG91ciB0YXJnZXQgc291cmNlcyBmb3IgdGhlIGZvbGxvd2luZyBmcmFtZS5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5pc1RyZWVBbmltYXRpbmcgPSBCb29sZWFuKCh0aGlzLnBhcmVudCAmJiB0aGlzLnBhcmVudC5pc1RyZWVBbmltYXRpbmcpIHx8XG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50QW5pbWF0aW9uIHx8XG4gICAgICAgICAgICAgICAgdGhpcy5wZW5kaW5nQW5pbWF0aW9uKTtcbiAgICAgICAgICAgIGlmICghdGhpcy5pc1RyZWVBbmltYXRpbmcpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRhcmdldERlbHRhID0gdGhpcy5yZWxhdGl2ZVRhcmdldCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghdGhpcy5sYXlvdXQgfHwgIShsYXlvdXQgfHwgbGF5b3V0SWQpKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogUmVzZXQgdGhlIGNvcnJlY3RlZCBib3ggd2l0aCB0aGUgbGF0ZXN0IHZhbHVlcyBmcm9tIGJveCwgYXMgd2UncmUgdGhlbiBnb2luZ1xuICAgICAgICAgICAgICogdG8gcGVyZm9ybSBtdXRhdGl2ZSBvcGVyYXRpb25zIG9uIGl0LlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb3B5Qm94SW50byh0aGlzLmxheW91dENvcnJlY3RlZCwgdGhpcy5sYXlvdXQubGF5b3V0Qm94KTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogUmVjb3JkIHByZXZpb3VzIHRyZWUgc2NhbGVzIGJlZm9yZSB1cGRhdGluZy5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY29uc3QgcHJldlRyZWVTY2FsZVggPSB0aGlzLnRyZWVTY2FsZS54O1xuICAgICAgICAgICAgY29uc3QgcHJldlRyZWVTY2FsZVkgPSB0aGlzLnRyZWVTY2FsZS55O1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBBcHBseSBhbGwgdGhlIHBhcmVudCBkZWx0YXMgdG8gdGhpcyBib3ggdG8gcHJvZHVjZSB0aGUgY29ycmVjdGVkIGJveC4gVGhpc1xuICAgICAgICAgICAgICogaXMgdGhlIGxheW91dCBib3gsIGFzIGl0IHdpbGwgYXBwZWFyIG9uIHNjcmVlbiBhcyBhIHJlc3VsdCBvZiB0aGUgdHJhbnNmb3JtcyBvZiBpdHMgcGFyZW50cy5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgYXBwbHlUcmVlRGVsdGFzKHRoaXMubGF5b3V0Q29ycmVjdGVkLCB0aGlzLnRyZWVTY2FsZSwgdGhpcy5wYXRoLCBpc1NoYXJlZCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoaXMgbGF5ZXIgbmVlZHMgdG8gcGVyZm9ybSBzY2FsZSBjb3JyZWN0aW9uIGJ1dCBkb2Vzbid0IGhhdmUgYSB0YXJnZXQsXG4gICAgICAgICAgICAgKiB1c2UgdGhlIGxheW91dCBhcyB0aGUgdGFyZ2V0LlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBpZiAobGVhZC5sYXlvdXQgJiZcbiAgICAgICAgICAgICAgICAhbGVhZC50YXJnZXQgJiZcbiAgICAgICAgICAgICAgICAodGhpcy50cmVlU2NhbGUueCAhPT0gMSB8fCB0aGlzLnRyZWVTY2FsZS55ICE9PSAxKSkge1xuICAgICAgICAgICAgICAgIGxlYWQudGFyZ2V0ID0gbGVhZC5sYXlvdXQubGF5b3V0Qm94O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgeyB0YXJnZXQgfSA9IGxlYWQ7XG4gICAgICAgICAgICBpZiAoIXRhcmdldCkge1xuICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAqIElmIHdlIGRvbid0IGhhdmUgYSB0YXJnZXQgdG8gcHJvamVjdCBpbnRvLCBidXQgd2Ugd2VyZSBwcmV2aW91c2x5XG4gICAgICAgICAgICAgICAgICogcHJvamVjdGluZywgd2Ugd2FudCB0byByZW1vdmUgdGhlIHN0b3JlZCB0cmFuc2Zvcm0gYW5kIHNjaGVkdWxlXG4gICAgICAgICAgICAgICAgICogYSByZW5kZXIgdG8gZW5zdXJlIHRoZSBlbGVtZW50cyByZWZsZWN0IHRoZSByZW1vdmVkIHRyYW5zZm9ybS5cbiAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICBpZiAodGhpcy5wcm9qZWN0aW9uVHJhbnNmb3JtKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucHJvamVjdGlvbkRlbHRhID0gY3JlYXRlRGVsdGEoKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9qZWN0aW9uVHJhbnNmb3JtID0gXCJub25lXCI7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2NoZWR1bGVSZW5kZXIoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCF0aGlzLnByb2plY3Rpb25EZWx0YSkge1xuICAgICAgICAgICAgICAgIHRoaXMucHJvamVjdGlvbkRlbHRhID0gY3JlYXRlRGVsdGEoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnByb2plY3Rpb25EZWx0YVdpdGhUcmFuc2Zvcm0gPSBjcmVhdGVEZWx0YSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgcHJldlByb2plY3Rpb25UcmFuc2Zvcm0gPSB0aGlzLnByb2plY3Rpb25UcmFuc2Zvcm07XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFVwZGF0ZSB0aGUgZGVsdGEgYmV0d2VlbiB0aGUgY29ycmVjdGVkIGJveCBhbmQgdGhlIHRhcmdldCBib3ggYmVmb3JlIHVzZXItc2V0IHRyYW5zZm9ybXMgd2VyZSBhcHBsaWVkLlxuICAgICAgICAgICAgICogVGhpcyB3aWxsIGFsbG93IHVzIHRvIGNhbGN1bGF0ZSB0aGUgY29ycmVjdGVkIGJvcmRlclJhZGl1cyBhbmQgYm94U2hhZG93IHRvIGNvbXBlbnNhdGVcbiAgICAgICAgICAgICAqIGZvciBvdXIgbGF5b3V0IHJlcHJvamVjdGlvbiwgYnV0IHN0aWxsIGFsbG93IHRoZW0gdG8gYmUgc2NhbGVkIGNvcnJlY3RseSBieSB0aGUgdXNlci5cbiAgICAgICAgICAgICAqIEl0IG1pZ2h0IGJlIHRoYXQgdG8gc2ltcGxpZnkgdGhpcyB3ZSBtYXkgd2FudCB0byBhY2NlcHQgdGhhdCB1c2VyLXNldCBzY2FsZSBpcyBhbHNvIGNvcnJlY3RlZFxuICAgICAgICAgICAgICogYW5kIHdlIHdvdWxkbid0IGhhdmUgdG8ga2VlcCBhbmQgY2FsYyBib3RoIGRlbHRhcywgT1Igd2UgY291bGQgc3VwcG9ydCBhIHVzZXIgc2V0dGluZ1xuICAgICAgICAgICAgICogdG8gYWxsb3cgcGVvcGxlIHRvIGNob29zZSB3aGV0aGVyIHRoZXNlIHN0eWxlcyBhcmUgY29ycmVjdGVkIGJhc2VkIG9uIGp1c3QgdGhlXG4gICAgICAgICAgICAgKiBsYXlvdXQgcmVwcm9qZWN0aW9uIG9yIHRoZSBmaW5hbCBib3VuZGluZyBib3guXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGNhbGNCb3hEZWx0YSh0aGlzLnByb2plY3Rpb25EZWx0YSwgdGhpcy5sYXlvdXRDb3JyZWN0ZWQsIHRhcmdldCwgdGhpcy5sYXRlc3RWYWx1ZXMpO1xuICAgICAgICAgICAgdGhpcy5wcm9qZWN0aW9uVHJhbnNmb3JtID0gYnVpbGRQcm9qZWN0aW9uVHJhbnNmb3JtKHRoaXMucHJvamVjdGlvbkRlbHRhLCB0aGlzLnRyZWVTY2FsZSk7XG4gICAgICAgICAgICBpZiAodGhpcy5wcm9qZWN0aW9uVHJhbnNmb3JtICE9PSBwcmV2UHJvamVjdGlvblRyYW5zZm9ybSB8fFxuICAgICAgICAgICAgICAgIHRoaXMudHJlZVNjYWxlLnggIT09IHByZXZUcmVlU2NhbGVYIHx8XG4gICAgICAgICAgICAgICAgdGhpcy50cmVlU2NhbGUueSAhPT0gcHJldlRyZWVTY2FsZVkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmhhc1Byb2plY3RlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgdGhpcy5zY2hlZHVsZVJlbmRlcigpO1xuICAgICAgICAgICAgICAgIHRoaXMubm90aWZ5TGlzdGVuZXJzKFwicHJvamVjdGlvblVwZGF0ZVwiLCB0YXJnZXQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJbmNyZWFzZSBkZWJ1ZyBjb3VudGVyIGZvciByZWNhbGN1bGF0ZWQgcHJvamVjdGlvbnNcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgcHJvamVjdGlvbkZyYW1lRGF0YS5yZWNhbGN1bGF0ZWRQcm9qZWN0aW9uKys7XG4gICAgICAgIH1cbiAgICAgICAgaGlkZSgpIHtcbiAgICAgICAgICAgIHRoaXMuaXNWaXNpYmxlID0gZmFsc2U7XG4gICAgICAgICAgICAvLyBUT0RPOiBTY2hlZHVsZSByZW5kZXJcbiAgICAgICAgfVxuICAgICAgICBzaG93KCkge1xuICAgICAgICAgICAgdGhpcy5pc1Zpc2libGUgPSB0cnVlO1xuICAgICAgICAgICAgLy8gVE9ETzogU2NoZWR1bGUgcmVuZGVyXG4gICAgICAgIH1cbiAgICAgICAgc2NoZWR1bGVSZW5kZXIobm90aWZ5QWxsID0gdHJ1ZSkge1xuICAgICAgICAgICAgdGhpcy5vcHRpb25zLnNjaGVkdWxlUmVuZGVyICYmIHRoaXMub3B0aW9ucy5zY2hlZHVsZVJlbmRlcigpO1xuICAgICAgICAgICAgaWYgKG5vdGlmeUFsbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHN0YWNrID0gdGhpcy5nZXRTdGFjaygpO1xuICAgICAgICAgICAgICAgIHN0YWNrICYmIHN0YWNrLnNjaGVkdWxlUmVuZGVyKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAodGhpcy5yZXN1bWluZ0Zyb20gJiYgIXRoaXMucmVzdW1pbmdGcm9tLmluc3RhbmNlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5yZXN1bWluZ0Zyb20gPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgc2V0QW5pbWF0aW9uT3JpZ2luKGRlbHRhLCBoYXNPbmx5UmVsYXRpdmVUYXJnZXRDaGFuZ2VkID0gZmFsc2UpIHtcbiAgICAgICAgICAgIGNvbnN0IHNuYXBzaG90ID0gdGhpcy5zbmFwc2hvdDtcbiAgICAgICAgICAgIGNvbnN0IHNuYXBzaG90TGF0ZXN0VmFsdWVzID0gc25hcHNob3RcbiAgICAgICAgICAgICAgICA/IHNuYXBzaG90LmxhdGVzdFZhbHVlc1xuICAgICAgICAgICAgICAgIDoge307XG4gICAgICAgICAgICBjb25zdCBtaXhlZFZhbHVlcyA9IHsgLi4udGhpcy5sYXRlc3RWYWx1ZXMgfTtcbiAgICAgICAgICAgIGNvbnN0IHRhcmdldERlbHRhID0gY3JlYXRlRGVsdGEoKTtcbiAgICAgICAgICAgIGlmICghdGhpcy5yZWxhdGl2ZVBhcmVudCB8fFxuICAgICAgICAgICAgICAgICF0aGlzLnJlbGF0aXZlUGFyZW50Lm9wdGlvbnMubGF5b3V0Um9vdCkge1xuICAgICAgICAgICAgICAgIHRoaXMucmVsYXRpdmVUYXJnZXQgPSB0aGlzLnJlbGF0aXZlVGFyZ2V0T3JpZ2luID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5hdHRlbXB0VG9SZXNvbHZlUmVsYXRpdmVUYXJnZXQgPSAhaGFzT25seVJlbGF0aXZlVGFyZ2V0Q2hhbmdlZDtcbiAgICAgICAgICAgIGNvbnN0IHJlbGF0aXZlTGF5b3V0ID0gY3JlYXRlQm94KCk7XG4gICAgICAgICAgICBjb25zdCBzbmFwc2hvdFNvdXJjZSA9IHNuYXBzaG90ID8gc25hcHNob3Quc291cmNlIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgY29uc3QgbGF5b3V0U291cmNlID0gdGhpcy5sYXlvdXQgPyB0aGlzLmxheW91dC5zb3VyY2UgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICBjb25zdCBpc1NoYXJlZExheW91dEFuaW1hdGlvbiA9IHNuYXBzaG90U291cmNlICE9PSBsYXlvdXRTb3VyY2U7XG4gICAgICAgICAgICBjb25zdCBzdGFjayA9IHRoaXMuZ2V0U3RhY2soKTtcbiAgICAgICAgICAgIGNvbnN0IGlzT25seU1lbWJlciA9ICFzdGFjayB8fCBzdGFjay5tZW1iZXJzLmxlbmd0aCA8PSAxO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkQ3Jvc3NmYWRlT3BhY2l0eSA9IEJvb2xlYW4oaXNTaGFyZWRMYXlvdXRBbmltYXRpb24gJiZcbiAgICAgICAgICAgICAgICAhaXNPbmx5TWVtYmVyICYmXG4gICAgICAgICAgICAgICAgdGhpcy5vcHRpb25zLmNyb3NzZmFkZSA9PT0gdHJ1ZSAmJlxuICAgICAgICAgICAgICAgICF0aGlzLnBhdGguc29tZShoYXNPcGFjaXR5Q3Jvc3NmYWRlKSk7XG4gICAgICAgICAgICB0aGlzLmFuaW1hdGlvblByb2dyZXNzID0gMDtcbiAgICAgICAgICAgIGxldCBwcmV2UmVsYXRpdmVUYXJnZXQ7XG4gICAgICAgICAgICB0aGlzLm1peFRhcmdldERlbHRhID0gKGxhdGVzdCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHByb2dyZXNzID0gbGF0ZXN0IC8gMTAwMDtcbiAgICAgICAgICAgICAgICBtaXhBeGlzRGVsdGEodGFyZ2V0RGVsdGEueCwgZGVsdGEueCwgcHJvZ3Jlc3MpO1xuICAgICAgICAgICAgICAgIG1peEF4aXNEZWx0YSh0YXJnZXREZWx0YS55LCBkZWx0YS55LCBwcm9ncmVzcyk7XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRUYXJnZXREZWx0YSh0YXJnZXREZWx0YSk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMucmVsYXRpdmVUYXJnZXQgJiZcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxhdGl2ZVRhcmdldE9yaWdpbiAmJlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmxheW91dCAmJlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbGF0aXZlUGFyZW50ICYmXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVsYXRpdmVQYXJlbnQubGF5b3V0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNhbGNSZWxhdGl2ZVBvc2l0aW9uKHJlbGF0aXZlTGF5b3V0LCB0aGlzLmxheW91dC5sYXlvdXRCb3gsIHRoaXMucmVsYXRpdmVQYXJlbnQubGF5b3V0LmxheW91dEJveCk7XG4gICAgICAgICAgICAgICAgICAgIG1peEJveCh0aGlzLnJlbGF0aXZlVGFyZ2V0LCB0aGlzLnJlbGF0aXZlVGFyZ2V0T3JpZ2luLCByZWxhdGl2ZUxheW91dCwgcHJvZ3Jlc3MpO1xuICAgICAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgICAgICogSWYgdGhpcyBpcyBhbiB1bmNoYW5nZWQgcmVsYXRpdmUgdGFyZ2V0IHdlIGNhbiBjb25zaWRlciB0aGVcbiAgICAgICAgICAgICAgICAgICAgICogcHJvamVjdGlvbiBub3QgZGlydHkuXG4gICAgICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgICAgICBpZiAocHJldlJlbGF0aXZlVGFyZ2V0ICYmXG4gICAgICAgICAgICAgICAgICAgICAgICBib3hFcXVhbHModGhpcy5yZWxhdGl2ZVRhcmdldCwgcHJldlJlbGF0aXZlVGFyZ2V0KSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Byb2plY3Rpb25EaXJ0eSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmICghcHJldlJlbGF0aXZlVGFyZ2V0KVxuICAgICAgICAgICAgICAgICAgICAgICAgcHJldlJlbGF0aXZlVGFyZ2V0ID0gY3JlYXRlQm94KCk7XG4gICAgICAgICAgICAgICAgICAgIGNvcHlCb3hJbnRvKHByZXZSZWxhdGl2ZVRhcmdldCwgdGhpcy5yZWxhdGl2ZVRhcmdldCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChpc1NoYXJlZExheW91dEFuaW1hdGlvbikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmFuaW1hdGlvblZhbHVlcyA9IG1peGVkVmFsdWVzO1xuICAgICAgICAgICAgICAgICAgICBtaXhWYWx1ZXMobWl4ZWRWYWx1ZXMsIHNuYXBzaG90TGF0ZXN0VmFsdWVzLCB0aGlzLmxhdGVzdFZhbHVlcywgcHJvZ3Jlc3MsIHNob3VsZENyb3NzZmFkZU9wYWNpdHksIGlzT25seU1lbWJlcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMucm9vdC5zY2hlZHVsZVVwZGF0ZVByb2plY3Rpb24oKTtcbiAgICAgICAgICAgICAgICB0aGlzLnNjaGVkdWxlUmVuZGVyKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5hbmltYXRpb25Qcm9ncmVzcyA9IHByb2dyZXNzO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHRoaXMubWl4VGFyZ2V0RGVsdGEodGhpcy5vcHRpb25zLmxheW91dFJvb3QgPyAxMDAwIDogMCk7XG4gICAgICAgIH1cbiAgICAgICAgc3RhcnRBbmltYXRpb24ob3B0aW9ucykge1xuICAgICAgICAgICAgdGhpcy5ub3RpZnlMaXN0ZW5lcnMoXCJhbmltYXRpb25TdGFydFwiKTtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudEFuaW1hdGlvbiAmJiB0aGlzLmN1cnJlbnRBbmltYXRpb24uc3RvcCgpO1xuICAgICAgICAgICAgaWYgKHRoaXMucmVzdW1pbmdGcm9tICYmIHRoaXMucmVzdW1pbmdGcm9tLmN1cnJlbnRBbmltYXRpb24pIHtcbiAgICAgICAgICAgICAgICB0aGlzLnJlc3VtaW5nRnJvbS5jdXJyZW50QW5pbWF0aW9uLnN0b3AoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLnBlbmRpbmdBbmltYXRpb24pIHtcbiAgICAgICAgICAgICAgICBjYW5jZWxGcmFtZSh0aGlzLnBlbmRpbmdBbmltYXRpb24pO1xuICAgICAgICAgICAgICAgIHRoaXMucGVuZGluZ0FuaW1hdGlvbiA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogU3RhcnQgdGhlIGFuaW1hdGlvbiBpbiB0aGUgbmV4dCBmcmFtZSB0byBoYXZlIGEgZnJhbWUgd2l0aCBwcm9ncmVzcyAwLFxuICAgICAgICAgICAgICogd2hlcmUgdGhlIHRhcmdldCBpcyB0aGUgc2FtZSBhcyB3aGVuIHRoZSBhbmltYXRpb24gc3RhcnRlZCwgc28gd2UgY2FuXG4gICAgICAgICAgICAgKiBjYWxjdWxhdGUgdGhlIHJlbGF0aXZlIHBvc2l0aW9ucyBjb3JyZWN0bHkgZm9yIGluc3RhbnQgdHJhbnNpdGlvbnMuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHRoaXMucGVuZGluZ0FuaW1hdGlvbiA9IGZyYW1lLnVwZGF0ZSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgZ2xvYmFsUHJvamVjdGlvblN0YXRlLmhhc0FuaW1hdGVkU2luY2VSZXNpemUgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudEFuaW1hdGlvbiA9IGFuaW1hdGVTaW5nbGVWYWx1ZSgwLCBhbmltYXRpb25UYXJnZXQsIHtcbiAgICAgICAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgICAgICAgICAgb25VcGRhdGU6IChsYXRlc3QpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubWl4VGFyZ2V0RGVsdGEobGF0ZXN0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnMub25VcGRhdGUgJiYgb3B0aW9ucy5vblVwZGF0ZShsYXRlc3QpO1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBvbkNvbXBsZXRlOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zLm9uQ29tcGxldGUgJiYgb3B0aW9ucy5vbkNvbXBsZXRlKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbXBsZXRlQW5pbWF0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMucmVzdW1pbmdGcm9tKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVzdW1pbmdGcm9tLmN1cnJlbnRBbmltYXRpb24gPSB0aGlzLmN1cnJlbnRBbmltYXRpb247XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMucGVuZGluZ0FuaW1hdGlvbiA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbXBsZXRlQW5pbWF0aW9uKCkge1xuICAgICAgICAgICAgaWYgKHRoaXMucmVzdW1pbmdGcm9tKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5yZXN1bWluZ0Zyb20uY3VycmVudEFuaW1hdGlvbiA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgICAgICB0aGlzLnJlc3VtaW5nRnJvbS5wcmVzZXJ2ZU9wYWNpdHkgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBzdGFjayA9IHRoaXMuZ2V0U3RhY2soKTtcbiAgICAgICAgICAgIHN0YWNrICYmIHN0YWNrLmV4aXRBbmltYXRpb25Db21wbGV0ZSgpO1xuICAgICAgICAgICAgdGhpcy5yZXN1bWluZ0Zyb20gPVxuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudEFuaW1hdGlvbiA9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYW5pbWF0aW9uVmFsdWVzID1cbiAgICAgICAgICAgICAgICAgICAgICAgIHVuZGVmaW5lZDtcbiAgICAgICAgICAgIHRoaXMubm90aWZ5TGlzdGVuZXJzKFwiYW5pbWF0aW9uQ29tcGxldGVcIik7XG4gICAgICAgIH1cbiAgICAgICAgZmluaXNoQW5pbWF0aW9uKCkge1xuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudEFuaW1hdGlvbikge1xuICAgICAgICAgICAgICAgIHRoaXMubWl4VGFyZ2V0RGVsdGEgJiYgdGhpcy5taXhUYXJnZXREZWx0YShhbmltYXRpb25UYXJnZXQpO1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudEFuaW1hdGlvbi5zdG9wKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNvbXBsZXRlQW5pbWF0aW9uKCk7XG4gICAgICAgIH1cbiAgICAgICAgYXBwbHlUcmFuc2Zvcm1zVG9UYXJnZXQoKSB7XG4gICAgICAgICAgICBjb25zdCBsZWFkID0gdGhpcy5nZXRMZWFkKCk7XG4gICAgICAgICAgICBsZXQgeyB0YXJnZXRXaXRoVHJhbnNmb3JtcywgdGFyZ2V0LCBsYXlvdXQsIGxhdGVzdFZhbHVlcyB9ID0gbGVhZDtcbiAgICAgICAgICAgIGlmICghdGFyZ2V0V2l0aFRyYW5zZm9ybXMgfHwgIXRhcmdldCB8fCAhbGF5b3V0KVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgd2UncmUgb25seSBhbmltYXRpbmcgcG9zaXRpb24sIGFuZCB0aGlzIGVsZW1lbnQgaXNuJ3QgdGhlIGxlYWQgZWxlbWVudCxcbiAgICAgICAgICAgICAqIHRoZW4gaW5zdGVhZCBvZiBwcm9qZWN0aW5nIGludG8gdGhlIGxlYWQgYm94IHdlIGluc3RlYWQgd2FudCB0byBjYWxjdWxhdGVcbiAgICAgICAgICAgICAqIGEgbmV3IHRhcmdldCB0aGF0IGFsaWducyB0aGUgdHdvIGJveGVzIGJ1dCBtYWludGFpbnMgdGhlIGxheW91dCBzaGFwZS5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMgIT09IGxlYWQgJiZcbiAgICAgICAgICAgICAgICB0aGlzLmxheW91dCAmJlxuICAgICAgICAgICAgICAgIGxheW91dCAmJlxuICAgICAgICAgICAgICAgIHNob3VsZEFuaW1hdGVQb3NpdGlvbk9ubHkodGhpcy5vcHRpb25zLmFuaW1hdGlvblR5cGUsIHRoaXMubGF5b3V0LmxheW91dEJveCwgbGF5b3V0LmxheW91dEJveCkpIHtcbiAgICAgICAgICAgICAgICB0YXJnZXQgPSB0aGlzLnRhcmdldCB8fCBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgICAgICBjb25zdCB4TGVuZ3RoID0gY2FsY0xlbmd0aCh0aGlzLmxheW91dC5sYXlvdXRCb3gueCk7XG4gICAgICAgICAgICAgICAgdGFyZ2V0LngubWluID0gbGVhZC50YXJnZXQueC5taW47XG4gICAgICAgICAgICAgICAgdGFyZ2V0LngubWF4ID0gdGFyZ2V0LngubWluICsgeExlbmd0aDtcbiAgICAgICAgICAgICAgICBjb25zdCB5TGVuZ3RoID0gY2FsY0xlbmd0aCh0aGlzLmxheW91dC5sYXlvdXRCb3gueSk7XG4gICAgICAgICAgICAgICAgdGFyZ2V0LnkubWluID0gbGVhZC50YXJnZXQueS5taW47XG4gICAgICAgICAgICAgICAgdGFyZ2V0LnkubWF4ID0gdGFyZ2V0LnkubWluICsgeUxlbmd0aDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvcHlCb3hJbnRvKHRhcmdldFdpdGhUcmFuc2Zvcm1zLCB0YXJnZXQpO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBBcHBseSB0aGUgbGF0ZXN0IHVzZXItc2V0IHRyYW5zZm9ybXMgdG8gdGhlIHRhcmdldEJveCB0byBwcm9kdWNlIHRoZSB0YXJnZXRCb3hGaW5hbC5cbiAgICAgICAgICAgICAqIFRoaXMgaXMgdGhlIGZpbmFsIGJveCB0aGF0IHdlIHdpbGwgdGhlbiBwcm9qZWN0IGludG8gYnkgY2FsY3VsYXRpbmcgYSB0cmFuc2Zvcm0gZGVsdGEgYW5kXG4gICAgICAgICAgICAgKiBhcHBseWluZyBpdCB0byB0aGUgY29ycmVjdGVkIGJveC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdHJhbnNmb3JtQm94KHRhcmdldFdpdGhUcmFuc2Zvcm1zLCBsYXRlc3RWYWx1ZXMpO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBVcGRhdGUgdGhlIGRlbHRhIGJldHdlZW4gdGhlIGNvcnJlY3RlZCBib3ggYW5kIHRoZSBmaW5hbCB0YXJnZXQgYm94LCBhZnRlclxuICAgICAgICAgICAgICogdXNlci1zZXQgdHJhbnNmb3JtcyBhcmUgYXBwbGllZCB0byBpdC4gVGhpcyB3aWxsIGJlIHVzZWQgYnkgdGhlIHJlbmRlcmVyIHRvXG4gICAgICAgICAgICAgKiBjcmVhdGUgYSB0cmFuc2Zvcm0gc3R5bGUgdGhhdCB3aWxsIHJlcHJvamVjdCB0aGUgZWxlbWVudCBmcm9tIGl0cyBsYXlvdXQgbGF5b3V0XG4gICAgICAgICAgICAgKiBpbnRvIHRoZSBkZXNpcmVkIGJvdW5kaW5nIGJveC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY2FsY0JveERlbHRhKHRoaXMucHJvamVjdGlvbkRlbHRhV2l0aFRyYW5zZm9ybSwgdGhpcy5sYXlvdXRDb3JyZWN0ZWQsIHRhcmdldFdpdGhUcmFuc2Zvcm1zLCBsYXRlc3RWYWx1ZXMpO1xuICAgICAgICB9XG4gICAgICAgIHJlZ2lzdGVyU2hhcmVkTm9kZShsYXlvdXRJZCwgbm9kZSkge1xuICAgICAgICAgICAgaWYgKCF0aGlzLnNoYXJlZE5vZGVzLmhhcyhsYXlvdXRJZCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNoYXJlZE5vZGVzLnNldChsYXlvdXRJZCwgbmV3IE5vZGVTdGFjaygpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHN0YWNrID0gdGhpcy5zaGFyZWROb2Rlcy5nZXQobGF5b3V0SWQpO1xuICAgICAgICAgICAgc3RhY2suYWRkKG5vZGUpO1xuICAgICAgICAgICAgY29uc3QgY29uZmlnID0gbm9kZS5vcHRpb25zLmluaXRpYWxQcm9tb3Rpb25Db25maWc7XG4gICAgICAgICAgICBub2RlLnByb21vdGUoe1xuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGNvbmZpZyA/IGNvbmZpZy50cmFuc2l0aW9uIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgIHByZXNlcnZlRm9sbG93T3BhY2l0eTogY29uZmlnICYmIGNvbmZpZy5zaG91bGRQcmVzZXJ2ZUZvbGxvd09wYWNpdHlcbiAgICAgICAgICAgICAgICAgICAgPyBjb25maWcuc2hvdWxkUHJlc2VydmVGb2xsb3dPcGFjaXR5KG5vZGUpXG4gICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgaXNMZWFkKCkge1xuICAgICAgICAgICAgY29uc3Qgc3RhY2sgPSB0aGlzLmdldFN0YWNrKCk7XG4gICAgICAgICAgICByZXR1cm4gc3RhY2sgPyBzdGFjay5sZWFkID09PSB0aGlzIDogdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBnZXRMZWFkKCkge1xuICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgY29uc3QgeyBsYXlvdXRJZCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgcmV0dXJuIGxheW91dElkID8gKChfYSA9IHRoaXMuZ2V0U3RhY2soKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmxlYWQpIHx8IHRoaXMgOiB0aGlzO1xuICAgICAgICB9XG4gICAgICAgIGdldFByZXZMZWFkKCkge1xuICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgY29uc3QgeyBsYXlvdXRJZCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgcmV0dXJuIGxheW91dElkID8gKF9hID0gdGhpcy5nZXRTdGFjaygpKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucHJldkxlYWQgOiB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0U3RhY2soKSB7XG4gICAgICAgICAgICBjb25zdCB7IGxheW91dElkIH0gPSB0aGlzLm9wdGlvbnM7XG4gICAgICAgICAgICBpZiAobGF5b3V0SWQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucm9vdC5zaGFyZWROb2Rlcy5nZXQobGF5b3V0SWQpO1xuICAgICAgICB9XG4gICAgICAgIHByb21vdGUoeyBuZWVkc1Jlc2V0LCB0cmFuc2l0aW9uLCBwcmVzZXJ2ZUZvbGxvd09wYWNpdHksIH0gPSB7fSkge1xuICAgICAgICAgICAgY29uc3Qgc3RhY2sgPSB0aGlzLmdldFN0YWNrKCk7XG4gICAgICAgICAgICBpZiAoc3RhY2spXG4gICAgICAgICAgICAgICAgc3RhY2sucHJvbW90ZSh0aGlzLCBwcmVzZXJ2ZUZvbGxvd09wYWNpdHkpO1xuICAgICAgICAgICAgaWYgKG5lZWRzUmVzZXQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnByb2plY3Rpb25EZWx0YSA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgICAgICB0aGlzLm5lZWRzUmVzZXQgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRyYW5zaXRpb24pXG4gICAgICAgICAgICAgICAgdGhpcy5zZXRPcHRpb25zKHsgdHJhbnNpdGlvbiB9KTtcbiAgICAgICAgfVxuICAgICAgICByZWxlZ2F0ZSgpIHtcbiAgICAgICAgICAgIGNvbnN0IHN0YWNrID0gdGhpcy5nZXRTdGFjaygpO1xuICAgICAgICAgICAgaWYgKHN0YWNrKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0YWNrLnJlbGVnYXRlKHRoaXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJlc2V0Um90YXRpb24oKSB7XG4gICAgICAgICAgICBjb25zdCB7IHZpc3VhbEVsZW1lbnQgfSA9IHRoaXMub3B0aW9ucztcbiAgICAgICAgICAgIGlmICghdmlzdWFsRWxlbWVudClcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAvLyBJZiB0aGVyZSdzIG5vIGRldGVjdGVkIHJvdGF0aW9uIHZhbHVlcywgd2UgY2FuIGVhcmx5IHJldHVybiB3aXRob3V0IGEgZm9yY2VkIHJlbmRlci5cbiAgICAgICAgICAgIGxldCBoYXNSb3RhdGUgPSBmYWxzZTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQW4gdW5yb2xsZWQgY2hlY2sgZm9yIHJvdGF0aW9uIHZhbHVlcy4gTW9zdCBlbGVtZW50cyBkb24ndCBoYXZlIGFueSByb3RhdGlvbiBhbmRcbiAgICAgICAgICAgICAqIHNraXBwaW5nIHRoZSBuZXN0ZWQgbG9vcCBhbmQgbmV3IG9iamVjdCBjcmVhdGlvbiBpcyA1MCUgZmFzdGVyLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCB7IGxhdGVzdFZhbHVlcyB9ID0gdmlzdWFsRWxlbWVudDtcbiAgICAgICAgICAgIGlmIChsYXRlc3RWYWx1ZXMucm90YXRlIHx8XG4gICAgICAgICAgICAgICAgbGF0ZXN0VmFsdWVzLnJvdGF0ZVggfHxcbiAgICAgICAgICAgICAgICBsYXRlc3RWYWx1ZXMucm90YXRlWSB8fFxuICAgICAgICAgICAgICAgIGxhdGVzdFZhbHVlcy5yb3RhdGVaKSB7XG4gICAgICAgICAgICAgICAgaGFzUm90YXRlID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZXJlJ3Mgbm8gcm90YXRpb24gdmFsdWVzLCB3ZSBkb24ndCBuZWVkIHRvIGRvIGFueSBtb3JlLlxuICAgICAgICAgICAgaWYgKCFoYXNSb3RhdGUpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgcmVzZXRWYWx1ZXMgPSB7fTtcbiAgICAgICAgICAgIC8vIENoZWNrIHRoZSByb3RhdGUgdmFsdWUgb2YgYWxsIGF4ZXMgYW5kIHJlc2V0IHRvIDBcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJhbnNmb3JtQXhlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IFwicm90YXRlXCIgKyB0cmFuc2Zvcm1BeGVzW2ldO1xuICAgICAgICAgICAgICAgIC8vIFJlY29yZCB0aGUgcm90YXRpb24gYW5kIHRoZW4gdGVtcG9yYXJpbHkgc2V0IGl0IHRvIDBcbiAgICAgICAgICAgICAgICBpZiAobGF0ZXN0VmFsdWVzW2tleV0pIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzZXRWYWx1ZXNba2V5XSA9IGxhdGVzdFZhbHVlc1trZXldO1xuICAgICAgICAgICAgICAgICAgICB2aXN1YWxFbGVtZW50LnNldFN0YXRpY1ZhbHVlKGtleSwgMCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gRm9yY2UgYSByZW5kZXIgb2YgdGhpcyBlbGVtZW50IHRvIGFwcGx5IHRoZSB0cmFuc2Zvcm0gd2l0aCBhbGwgcm90YXRpb25zXG4gICAgICAgICAgICAvLyBzZXQgdG8gMC5cbiAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQucmVuZGVyKCk7XG4gICAgICAgICAgICAvLyBQdXQgYmFjayBhbGwgdGhlIHZhbHVlcyB3ZSByZXNldFxuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gcmVzZXRWYWx1ZXMpIHtcbiAgICAgICAgICAgICAgICB2aXN1YWxFbGVtZW50LnNldFN0YXRpY1ZhbHVlKGtleSwgcmVzZXRWYWx1ZXNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBTY2hlZHVsZSBhIHJlbmRlciBmb3IgdGhlIG5leHQgZnJhbWUuIFRoaXMgZW5zdXJlcyB3ZSB3b24ndCB2aXN1YWxseVxuICAgICAgICAgICAgLy8gc2VlIHRoZSBlbGVtZW50IHdpdGggdGhlIHJlc2V0IHJvdGF0ZSB2YWx1ZSBhcHBsaWVkLlxuICAgICAgICAgICAgdmlzdWFsRWxlbWVudC5zY2hlZHVsZVJlbmRlcigpO1xuICAgICAgICB9XG4gICAgICAgIGdldFByb2plY3Rpb25TdHlsZXMoc3R5bGVQcm9wKSB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgaWYgKCF0aGlzLmluc3RhbmNlIHx8IHRoaXMuaXNTVkcpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICAgICAgICAgIGlmICghdGhpcy5pc1Zpc2libGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gaGlkZGVuVmlzaWJpbGl0eTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHN0eWxlcyA9IHtcbiAgICAgICAgICAgICAgICB2aXNpYmlsaXR5OiBcIlwiLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zZm9ybVRlbXBsYXRlID0gdGhpcy5nZXRUcmFuc2Zvcm1UZW1wbGF0ZSgpO1xuICAgICAgICAgICAgaWYgKHRoaXMubmVlZHNSZXNldCkge1xuICAgICAgICAgICAgICAgIHRoaXMubmVlZHNSZXNldCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIHN0eWxlcy5vcGFjaXR5ID0gXCJcIjtcbiAgICAgICAgICAgICAgICBzdHlsZXMucG9pbnRlckV2ZW50cyA9XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmVNb3Rpb25WYWx1ZShzdHlsZVByb3AgPT09IG51bGwgfHwgc3R5bGVQcm9wID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzdHlsZVByb3AucG9pbnRlckV2ZW50cykgfHwgXCJcIjtcbiAgICAgICAgICAgICAgICBzdHlsZXMudHJhbnNmb3JtID0gdHJhbnNmb3JtVGVtcGxhdGVcbiAgICAgICAgICAgICAgICAgICAgPyB0cmFuc2Zvcm1UZW1wbGF0ZSh0aGlzLmxhdGVzdFZhbHVlcywgXCJcIilcbiAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIjtcbiAgICAgICAgICAgICAgICByZXR1cm4gc3R5bGVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgbGVhZCA9IHRoaXMuZ2V0TGVhZCgpO1xuICAgICAgICAgICAgaWYgKCF0aGlzLnByb2plY3Rpb25EZWx0YSB8fCAhdGhpcy5sYXlvdXQgfHwgIWxlYWQudGFyZ2V0KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZW1wdHlTdHlsZXMgPSB7fTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLmxheW91dElkKSB7XG4gICAgICAgICAgICAgICAgICAgIGVtcHR5U3R5bGVzLm9wYWNpdHkgPVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5sYXRlc3RWYWx1ZXMub3BhY2l0eSAhPT0gdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB0aGlzLmxhdGVzdFZhbHVlcy5vcGFjaXR5XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAxO1xuICAgICAgICAgICAgICAgICAgICBlbXB0eVN0eWxlcy5wb2ludGVyRXZlbnRzID1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmVNb3Rpb25WYWx1ZShzdHlsZVByb3AgPT09IG51bGwgfHwgc3R5bGVQcm9wID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzdHlsZVByb3AucG9pbnRlckV2ZW50cykgfHwgXCJcIjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaGFzUHJvamVjdGVkICYmICFoYXNUcmFuc2Zvcm0odGhpcy5sYXRlc3RWYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIGVtcHR5U3R5bGVzLnRyYW5zZm9ybSA9IHRyYW5zZm9ybVRlbXBsYXRlXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHRyYW5zZm9ybVRlbXBsYXRlKHt9LCBcIlwiKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIjtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5oYXNQcm9qZWN0ZWQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVtcHR5U3R5bGVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgdmFsdWVzVG9SZW5kZXIgPSBsZWFkLmFuaW1hdGlvblZhbHVlcyB8fCBsZWFkLmxhdGVzdFZhbHVlcztcbiAgICAgICAgICAgIHRoaXMuYXBwbHlUcmFuc2Zvcm1zVG9UYXJnZXQoKTtcbiAgICAgICAgICAgIHN0eWxlcy50cmFuc2Zvcm0gPSBidWlsZFByb2plY3Rpb25UcmFuc2Zvcm0odGhpcy5wcm9qZWN0aW9uRGVsdGFXaXRoVHJhbnNmb3JtLCB0aGlzLnRyZWVTY2FsZSwgdmFsdWVzVG9SZW5kZXIpO1xuICAgICAgICAgICAgaWYgKHRyYW5zZm9ybVRlbXBsYXRlKSB7XG4gICAgICAgICAgICAgICAgc3R5bGVzLnRyYW5zZm9ybSA9IHRyYW5zZm9ybVRlbXBsYXRlKHZhbHVlc1RvUmVuZGVyLCBzdHlsZXMudHJhbnNmb3JtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgeCwgeSB9ID0gdGhpcy5wcm9qZWN0aW9uRGVsdGE7XG4gICAgICAgICAgICBzdHlsZXMudHJhbnNmb3JtT3JpZ2luID0gYCR7eC5vcmlnaW4gKiAxMDB9JSAke3kub3JpZ2luICogMTAwfSUgMGA7XG4gICAgICAgICAgICBpZiAobGVhZC5hbmltYXRpb25WYWx1ZXMpIHtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBJZiB0aGUgbGVhZCBjb21wb25lbnQgaXMgYW5pbWF0aW5nLCBhc3NpZ24gdGhpcyBlaXRoZXIgdGhlIGVudGVyaW5nL2xlYXZpbmdcbiAgICAgICAgICAgICAgICAgKiBvcGFjaXR5XG4gICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgc3R5bGVzLm9wYWNpdHkgPVxuICAgICAgICAgICAgICAgICAgICBsZWFkID09PSB0aGlzXG4gICAgICAgICAgICAgICAgICAgICAgICA/IChfYiA9IChfYSA9IHZhbHVlc1RvUmVuZGVyLm9wYWNpdHkpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHRoaXMubGF0ZXN0VmFsdWVzLm9wYWNpdHkpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IDFcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdGhpcy5wcmVzZXJ2ZU9wYWNpdHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHRoaXMubGF0ZXN0VmFsdWVzLm9wYWNpdHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHZhbHVlc1RvUmVuZGVyLm9wYWNpdHlFeGl0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICogT3Igd2UncmUgbm90IGFuaW1hdGluZyBhdCBhbGwsIHNldCB0aGUgbGVhZCBjb21wb25lbnQgdG8gaXRzIGxheW91dFxuICAgICAgICAgICAgICAgICAqIG9wYWNpdHkgYW5kIG90aGVyIGNvbXBvbmVudHMgdG8gaGlkZGVuLlxuICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgIHN0eWxlcy5vcGFjaXR5ID1cbiAgICAgICAgICAgICAgICAgICAgbGVhZCA9PT0gdGhpc1xuICAgICAgICAgICAgICAgICAgICAgICAgPyB2YWx1ZXNUb1JlbmRlci5vcGFjaXR5ICE9PSB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHZhbHVlc1RvUmVuZGVyLm9wYWNpdHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVzVG9SZW5kZXIub3BhY2l0eUV4aXQgIT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdmFsdWVzVG9SZW5kZXIub3BhY2l0eUV4aXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIEFwcGx5IHNjYWxlIGNvcnJlY3Rpb25cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gc2NhbGVDb3JyZWN0b3JzKSB7XG4gICAgICAgICAgICAgICAgaWYgKHZhbHVlc1RvUmVuZGVyW2tleV0gPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBjb3JyZWN0LCBhcHBseVRvIH0gPSBzY2FsZUNvcnJlY3RvcnNba2V5XTtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBPbmx5IGFwcGx5IHNjYWxlIGNvcnJlY3Rpb24gdG8gdGhlIHZhbHVlIGlmIHdlIGhhdmUgYW5cbiAgICAgICAgICAgICAgICAgKiBhY3RpdmUgcHJvamVjdGlvbiB0cmFuc2Zvcm0uIE90aGVyd2lzZSB0aGVzZSB2YWx1ZXMgYmVjb21lXG4gICAgICAgICAgICAgICAgICogdnVsbmVyYWJsZSB0byBkaXN0b3J0aW9uIGlmIHRoZSBlbGVtZW50IGNoYW5nZXMgc2l6ZSB3aXRob3V0XG4gICAgICAgICAgICAgICAgICogYSBjb3JyZXNwb25kaW5nIGxheW91dCBhbmltYXRpb24uXG4gICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgY29uc3QgY29ycmVjdGVkID0gc3R5bGVzLnRyYW5zZm9ybSA9PT0gXCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgPyB2YWx1ZXNUb1JlbmRlcltrZXldXG4gICAgICAgICAgICAgICAgICAgIDogY29ycmVjdCh2YWx1ZXNUb1JlbmRlcltrZXldLCBsZWFkKTtcbiAgICAgICAgICAgICAgICBpZiAoYXBwbHlUbykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBudW0gPSBhcHBseVRvLmxlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBudW07IGkrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGVzW2FwcGx5VG9baV1dID0gY29ycmVjdGVkO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBzdHlsZXNba2V5XSA9IGNvcnJlY3RlZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIERpc2FibGUgcG9pbnRlciBldmVudHMgb24gZm9sbG93IGNvbXBvbmVudHMuIFRoaXMgaXMgdG8gZW5zdXJlXG4gICAgICAgICAgICAgKiB0aGF0IGlmIGEgZm9sbG93IGNvbXBvbmVudCBjb3ZlcnMgYSBsZWFkIGNvbXBvbmVudCBpdCBkb2Vzbid0IGJsb2NrXG4gICAgICAgICAgICAgKiBwb2ludGVyIGV2ZW50cyBvbiB0aGUgbGVhZC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5sYXlvdXRJZCkge1xuICAgICAgICAgICAgICAgIHN0eWxlcy5wb2ludGVyRXZlbnRzID1cbiAgICAgICAgICAgICAgICAgICAgbGVhZCA9PT0gdGhpc1xuICAgICAgICAgICAgICAgICAgICAgICAgPyByZXNvbHZlTW90aW9uVmFsdWUoc3R5bGVQcm9wID09PSBudWxsIHx8IHN0eWxlUHJvcCA9PT0gdm9pZCAwID8gdm9pZCAwIDogc3R5bGVQcm9wLnBvaW50ZXJFdmVudHMpIHx8IFwiXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJub25lXCI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gc3R5bGVzO1xuICAgICAgICB9XG4gICAgICAgIGNsZWFyU25hcHNob3QoKSB7XG4gICAgICAgICAgICB0aGlzLnJlc3VtZUZyb20gPSB0aGlzLnNuYXBzaG90ID0gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgICAgIC8vIE9ubHkgcnVuIG9uIHJvb3RcbiAgICAgICAgcmVzZXRUcmVlKCkge1xuICAgICAgICAgICAgdGhpcy5yb290Lm5vZGVzLmZvckVhY2goKG5vZGUpID0+IHsgdmFyIF9hOyByZXR1cm4gKF9hID0gbm9kZS5jdXJyZW50QW5pbWF0aW9uKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc3RvcCgpOyB9KTtcbiAgICAgICAgICAgIHRoaXMucm9vdC5ub2Rlcy5mb3JFYWNoKGNsZWFyTWVhc3VyZW1lbnRzKTtcbiAgICAgICAgICAgIHRoaXMucm9vdC5zaGFyZWROb2Rlcy5jbGVhcigpO1xuICAgICAgICB9XG4gICAgfTtcbn1cbmZ1bmN0aW9uIHVwZGF0ZUxheW91dChub2RlKSB7XG4gICAgbm9kZS51cGRhdGVMYXlvdXQoKTtcbn1cbmZ1bmN0aW9uIG5vdGlmeUxheW91dFVwZGF0ZShub2RlKSB7XG4gICAgdmFyIF9hO1xuICAgIGNvbnN0IHNuYXBzaG90ID0gKChfYSA9IG5vZGUucmVzdW1lRnJvbSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNuYXBzaG90KSB8fCBub2RlLnNuYXBzaG90O1xuICAgIGlmIChub2RlLmlzTGVhZCgpICYmXG4gICAgICAgIG5vZGUubGF5b3V0ICYmXG4gICAgICAgIHNuYXBzaG90ICYmXG4gICAgICAgIG5vZGUuaGFzTGlzdGVuZXJzKFwiZGlkVXBkYXRlXCIpKSB7XG4gICAgICAgIGNvbnN0IHsgbGF5b3V0Qm94OiBsYXlvdXQsIG1lYXN1cmVkQm94OiBtZWFzdXJlZExheW91dCB9ID0gbm9kZS5sYXlvdXQ7XG4gICAgICAgIGNvbnN0IHsgYW5pbWF0aW9uVHlwZSB9ID0gbm9kZS5vcHRpb25zO1xuICAgICAgICBjb25zdCBpc1NoYXJlZCA9IHNuYXBzaG90LnNvdXJjZSAhPT0gbm9kZS5sYXlvdXQuc291cmNlO1xuICAgICAgICAvLyBUT0RPIE1heWJlIHdlIHdhbnQgdG8gYWxzbyByZXNpemUgdGhlIGxheW91dCBzbmFwc2hvdCBzbyB3ZSBkb24ndCB0cmlnZ2VyXG4gICAgICAgIC8vIGFuaW1hdGlvbnMgZm9yIGluc3RhbmNlIGlmIGxheW91dD1cInNpemVcIiBhbmQgYW4gZWxlbWVudCBoYXMgb25seSBjaGFuZ2VkIHBvc2l0aW9uXG4gICAgICAgIGlmIChhbmltYXRpb25UeXBlID09PSBcInNpemVcIikge1xuICAgICAgICAgICAgZWFjaEF4aXMoKGF4aXMpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBheGlzU25hcHNob3QgPSBpc1NoYXJlZFxuICAgICAgICAgICAgICAgICAgICA/IHNuYXBzaG90Lm1lYXN1cmVkQm94W2F4aXNdXG4gICAgICAgICAgICAgICAgICAgIDogc25hcHNob3QubGF5b3V0Qm94W2F4aXNdO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxlbmd0aCA9IGNhbGNMZW5ndGgoYXhpc1NuYXBzaG90KTtcbiAgICAgICAgICAgICAgICBheGlzU25hcHNob3QubWluID0gbGF5b3V0W2F4aXNdLm1pbjtcbiAgICAgICAgICAgICAgICBheGlzU25hcHNob3QubWF4ID0gYXhpc1NuYXBzaG90Lm1pbiArIGxlbmd0aDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHNob3VsZEFuaW1hdGVQb3NpdGlvbk9ubHkoYW5pbWF0aW9uVHlwZSwgc25hcHNob3QubGF5b3V0Qm94LCBsYXlvdXQpKSB7XG4gICAgICAgICAgICBlYWNoQXhpcygoYXhpcykgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGF4aXNTbmFwc2hvdCA9IGlzU2hhcmVkXG4gICAgICAgICAgICAgICAgICAgID8gc25hcHNob3QubWVhc3VyZWRCb3hbYXhpc11cbiAgICAgICAgICAgICAgICAgICAgOiBzbmFwc2hvdC5sYXlvdXRCb3hbYXhpc107XG4gICAgICAgICAgICAgICAgY29uc3QgbGVuZ3RoID0gY2FsY0xlbmd0aChsYXlvdXRbYXhpc10pO1xuICAgICAgICAgICAgICAgIGF4aXNTbmFwc2hvdC5tYXggPSBheGlzU25hcHNob3QubWluICsgbGVuZ3RoO1xuICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAqIEVuc3VyZSByZWxhdGl2ZSB0YXJnZXQgZ2V0cyByZXNpemVkIGFuZCByZXJlbmRlcmVyZFxuICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgIGlmIChub2RlLnJlbGF0aXZlVGFyZ2V0ICYmICFub2RlLmN1cnJlbnRBbmltYXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgbm9kZS5pc1Byb2plY3Rpb25EaXJ0eSA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIG5vZGUucmVsYXRpdmVUYXJnZXRbYXhpc10ubWF4ID1cbiAgICAgICAgICAgICAgICAgICAgICAgIG5vZGUucmVsYXRpdmVUYXJnZXRbYXhpc10ubWluICsgbGVuZ3RoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGxheW91dERlbHRhID0gY3JlYXRlRGVsdGEoKTtcbiAgICAgICAgY2FsY0JveERlbHRhKGxheW91dERlbHRhLCBsYXlvdXQsIHNuYXBzaG90LmxheW91dEJveCk7XG4gICAgICAgIGNvbnN0IHZpc3VhbERlbHRhID0gY3JlYXRlRGVsdGEoKTtcbiAgICAgICAgaWYgKGlzU2hhcmVkKSB7XG4gICAgICAgICAgICBjYWxjQm94RGVsdGEodmlzdWFsRGVsdGEsIG5vZGUuYXBwbHlUcmFuc2Zvcm0obWVhc3VyZWRMYXlvdXQsIHRydWUpLCBzbmFwc2hvdC5tZWFzdXJlZEJveCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjYWxjQm94RGVsdGEodmlzdWFsRGVsdGEsIGxheW91dCwgc25hcHNob3QubGF5b3V0Qm94KTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBoYXNMYXlvdXRDaGFuZ2VkID0gIWlzRGVsdGFaZXJvKGxheW91dERlbHRhKTtcbiAgICAgICAgbGV0IGhhc1JlbGF0aXZlVGFyZ2V0Q2hhbmdlZCA9IGZhbHNlO1xuICAgICAgICBpZiAoIW5vZGUucmVzdW1lRnJvbSkge1xuICAgICAgICAgICAgY29uc3QgcmVsYXRpdmVQYXJlbnQgPSBub2RlLmdldENsb3Nlc3RQcm9qZWN0aW5nUGFyZW50KCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoZSByZWxhdGl2ZVBhcmVudCBpcyBpdHNlbGYgcmVzdW1pbmcgZnJvbSBhIGRpZmZlcmVudCBlbGVtZW50IHRoZW5cbiAgICAgICAgICAgICAqIHRoZSByZWxhdGl2ZSBzbmFwc2hvdCBpcyBub3QgcmVsYXZlbnRcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKHJlbGF0aXZlUGFyZW50ICYmICFyZWxhdGl2ZVBhcmVudC5yZXN1bWVGcm9tKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBzbmFwc2hvdDogcGFyZW50U25hcHNob3QsIGxheW91dDogcGFyZW50TGF5b3V0IH0gPSByZWxhdGl2ZVBhcmVudDtcbiAgICAgICAgICAgICAgICBpZiAocGFyZW50U25hcHNob3QgJiYgcGFyZW50TGF5b3V0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlbGF0aXZlU25hcHNob3QgPSBjcmVhdGVCb3goKTtcbiAgICAgICAgICAgICAgICAgICAgY2FsY1JlbGF0aXZlUG9zaXRpb24ocmVsYXRpdmVTbmFwc2hvdCwgc25hcHNob3QubGF5b3V0Qm94LCBwYXJlbnRTbmFwc2hvdC5sYXlvdXRCb3gpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZWxhdGl2ZUxheW91dCA9IGNyZWF0ZUJveCgpO1xuICAgICAgICAgICAgICAgICAgICBjYWxjUmVsYXRpdmVQb3NpdGlvbihyZWxhdGl2ZUxheW91dCwgbGF5b3V0LCBwYXJlbnRMYXlvdXQubGF5b3V0Qm94KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFib3hFcXVhbHNSb3VuZGVkKHJlbGF0aXZlU25hcHNob3QsIHJlbGF0aXZlTGF5b3V0KSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaGFzUmVsYXRpdmVUYXJnZXRDaGFuZ2VkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAocmVsYXRpdmVQYXJlbnQub3B0aW9ucy5sYXlvdXRSb290KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBub2RlLnJlbGF0aXZlVGFyZ2V0ID0gcmVsYXRpdmVMYXlvdXQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBub2RlLnJlbGF0aXZlVGFyZ2V0T3JpZ2luID0gcmVsYXRpdmVTbmFwc2hvdDtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5vZGUucmVsYXRpdmVQYXJlbnQgPSByZWxhdGl2ZVBhcmVudDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBub2RlLm5vdGlmeUxpc3RlbmVycyhcImRpZFVwZGF0ZVwiLCB7XG4gICAgICAgICAgICBsYXlvdXQsXG4gICAgICAgICAgICBzbmFwc2hvdCxcbiAgICAgICAgICAgIGRlbHRhOiB2aXN1YWxEZWx0YSxcbiAgICAgICAgICAgIGxheW91dERlbHRhLFxuICAgICAgICAgICAgaGFzTGF5b3V0Q2hhbmdlZCxcbiAgICAgICAgICAgIGhhc1JlbGF0aXZlVGFyZ2V0Q2hhbmdlZCxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKG5vZGUuaXNMZWFkKCkpIHtcbiAgICAgICAgY29uc3QgeyBvbkV4aXRDb21wbGV0ZSB9ID0gbm9kZS5vcHRpb25zO1xuICAgICAgICBvbkV4aXRDb21wbGV0ZSAmJiBvbkV4aXRDb21wbGV0ZSgpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDbGVhcmluZyB0cmFuc2l0aW9uXG4gICAgICogVE9ETzogSW52ZXN0aWdhdGUgd2h5IHRoaXMgdHJhbnNpdGlvbiBpcyBiZWluZyBwYXNzZWQgaW4gYXMge3R5cGU6IGZhbHNlIH0gZnJvbSBGcmFtZXJcbiAgICAgKiBhbmQgd2h5IHdlIG5lZWQgaXQgYXQgYWxsXG4gICAgICovXG4gICAgbm9kZS5vcHRpb25zLnRyYW5zaXRpb24gPSB1bmRlZmluZWQ7XG59XG5mdW5jdGlvbiBwcm9wYWdhdGVEaXJ0eU5vZGVzKG5vZGUpIHtcbiAgICAvKipcbiAgICAgKiBJbmNyZWFzZSBkZWJ1ZyBjb3VudGVyIGZvciBub2RlcyBlbmNvdW50ZXJlZCB0aGlzIGZyYW1lXG4gICAgICovXG4gICAgcHJvamVjdGlvbkZyYW1lRGF0YS50b3RhbE5vZGVzKys7XG4gICAgaWYgKCFub2RlLnBhcmVudClcbiAgICAgICAgcmV0dXJuO1xuICAgIC8qKlxuICAgICAqIElmIHRoaXMgbm9kZSBpc24ndCBwcm9qZWN0aW5nLCBwcm9wYWdhdGUgaXNQcm9qZWN0aW9uRGlydHkuIEl0IHdpbGwgaGF2ZVxuICAgICAqIG5vIHBlcmZvcm1hbmNlIGltcGFjdCBidXQgaXQgd2lsbCBhbGxvdyB0aGUgbmV4dCBjaGlsZCB0aGF0ICppcyogcHJvamVjdGluZ1xuICAgICAqIGJ1dCAqaXNuJ3QqIGRpcnR5IHRvIGp1c3QgY2hlY2sgaXRzIHBhcmVudCB0byBzZWUgaWYgKmFueSogYW5jZXN0b3IgbmVlZHNcbiAgICAgKiBjb3JyZWN0aW5nLlxuICAgICAqL1xuICAgIGlmICghbm9kZS5pc1Byb2plY3RpbmcoKSkge1xuICAgICAgICBub2RlLmlzUHJvamVjdGlvbkRpcnR5ID0gbm9kZS5wYXJlbnQuaXNQcm9qZWN0aW9uRGlydHk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFByb3BhZ2F0ZSBpc1NoYXJlZFByb2plY3Rpb25EaXJ0eSBhbmQgaXNUcmFuc2Zvcm1EaXJ0eVxuICAgICAqIHRocm91Z2hvdXQgdGhlIHdob2xlIHRyZWUuIEEgZnV0dXJlIHJldmlzaW9uIGNhbiB0YWtlIGFub3RoZXIgbG9vayBhdFxuICAgICAqIHRoaXMgYnV0IGZvciBzYWZldHkgd2Ugc3RpbGwgcmVjYWxjdWFsdGUgc2hhcmVkIG5vZGVzLlxuICAgICAqL1xuICAgIG5vZGUuaXNTaGFyZWRQcm9qZWN0aW9uRGlydHkgfHwgKG5vZGUuaXNTaGFyZWRQcm9qZWN0aW9uRGlydHkgPSBCb29sZWFuKG5vZGUuaXNQcm9qZWN0aW9uRGlydHkgfHxcbiAgICAgICAgbm9kZS5wYXJlbnQuaXNQcm9qZWN0aW9uRGlydHkgfHxcbiAgICAgICAgbm9kZS5wYXJlbnQuaXNTaGFyZWRQcm9qZWN0aW9uRGlydHkpKTtcbiAgICBub2RlLmlzVHJhbnNmb3JtRGlydHkgfHwgKG5vZGUuaXNUcmFuc2Zvcm1EaXJ0eSA9IG5vZGUucGFyZW50LmlzVHJhbnNmb3JtRGlydHkpO1xufVxuZnVuY3Rpb24gY2xlYW5EaXJ0eU5vZGVzKG5vZGUpIHtcbiAgICBub2RlLmlzUHJvamVjdGlvbkRpcnR5ID1cbiAgICAgICAgbm9kZS5pc1NoYXJlZFByb2plY3Rpb25EaXJ0eSA9XG4gICAgICAgICAgICBub2RlLmlzVHJhbnNmb3JtRGlydHkgPVxuICAgICAgICAgICAgICAgIGZhbHNlO1xufVxuZnVuY3Rpb24gY2xlYXJTbmFwc2hvdChub2RlKSB7XG4gICAgbm9kZS5jbGVhclNuYXBzaG90KCk7XG59XG5mdW5jdGlvbiBjbGVhck1lYXN1cmVtZW50cyhub2RlKSB7XG4gICAgbm9kZS5jbGVhck1lYXN1cmVtZW50cygpO1xufVxuZnVuY3Rpb24gY2xlYXJJc0xheW91dERpcnR5KG5vZGUpIHtcbiAgICBub2RlLmlzTGF5b3V0RGlydHkgPSBmYWxzZTtcbn1cbmZ1bmN0aW9uIHJlc2V0VHJhbnNmb3JtU3R5bGUobm9kZSkge1xuICAgIGNvbnN0IHsgdmlzdWFsRWxlbWVudCB9ID0gbm9kZS5vcHRpb25zO1xuICAgIGlmICh2aXN1YWxFbGVtZW50ICYmIHZpc3VhbEVsZW1lbnQuZ2V0UHJvcHMoKS5vbkJlZm9yZUxheW91dE1lYXN1cmUpIHtcbiAgICAgICAgdmlzdWFsRWxlbWVudC5ub3RpZnkoXCJCZWZvcmVMYXlvdXRNZWFzdXJlXCIpO1xuICAgIH1cbiAgICBub2RlLnJlc2V0VHJhbnNmb3JtKCk7XG59XG5mdW5jdGlvbiBmaW5pc2hBbmltYXRpb24obm9kZSkge1xuICAgIG5vZGUuZmluaXNoQW5pbWF0aW9uKCk7XG4gICAgbm9kZS50YXJnZXREZWx0YSA9IG5vZGUucmVsYXRpdmVUYXJnZXQgPSBub2RlLnRhcmdldCA9IHVuZGVmaW5lZDtcbiAgICBub2RlLmlzUHJvamVjdGlvbkRpcnR5ID0gdHJ1ZTtcbn1cbmZ1bmN0aW9uIHJlc29sdmVUYXJnZXREZWx0YShub2RlKSB7XG4gICAgbm9kZS5yZXNvbHZlVGFyZ2V0RGVsdGEoKTtcbn1cbmZ1bmN0aW9uIGNhbGNQcm9qZWN0aW9uKG5vZGUpIHtcbiAgICBub2RlLmNhbGNQcm9qZWN0aW9uKCk7XG59XG5mdW5jdGlvbiByZXNldFJvdGF0aW9uKG5vZGUpIHtcbiAgICBub2RlLnJlc2V0Um90YXRpb24oKTtcbn1cbmZ1bmN0aW9uIHJlbW92ZUxlYWRTbmFwc2hvdHMoc3RhY2spIHtcbiAgICBzdGFjay5yZW1vdmVMZWFkU25hcHNob3QoKTtcbn1cbmZ1bmN0aW9uIG1peEF4aXNEZWx0YShvdXRwdXQsIGRlbHRhLCBwKSB7XG4gICAgb3V0cHV0LnRyYW5zbGF0ZSA9IG1peChkZWx0YS50cmFuc2xhdGUsIDAsIHApO1xuICAgIG91dHB1dC5zY2FsZSA9IG1peChkZWx0YS5zY2FsZSwgMSwgcCk7XG4gICAgb3V0cHV0Lm9yaWdpbiA9IGRlbHRhLm9yaWdpbjtcbiAgICBvdXRwdXQub3JpZ2luUG9pbnQgPSBkZWx0YS5vcmlnaW5Qb2ludDtcbn1cbmZ1bmN0aW9uIG1peEF4aXMob3V0cHV0LCBmcm9tLCB0bywgcCkge1xuICAgIG91dHB1dC5taW4gPSBtaXgoZnJvbS5taW4sIHRvLm1pbiwgcCk7XG4gICAgb3V0cHV0Lm1heCA9IG1peChmcm9tLm1heCwgdG8ubWF4LCBwKTtcbn1cbmZ1bmN0aW9uIG1peEJveChvdXRwdXQsIGZyb20sIHRvLCBwKSB7XG4gICAgbWl4QXhpcyhvdXRwdXQueCwgZnJvbS54LCB0by54LCBwKTtcbiAgICBtaXhBeGlzKG91dHB1dC55LCBmcm9tLnksIHRvLnksIHApO1xufVxuZnVuY3Rpb24gaGFzT3BhY2l0eUNyb3NzZmFkZShub2RlKSB7XG4gICAgcmV0dXJuIChub2RlLmFuaW1hdGlvblZhbHVlcyAmJiBub2RlLmFuaW1hdGlvblZhbHVlcy5vcGFjaXR5RXhpdCAhPT0gdW5kZWZpbmVkKTtcbn1cbmNvbnN0IGRlZmF1bHRMYXlvdXRUcmFuc2l0aW9uID0ge1xuICAgIGR1cmF0aW9uOiAwLjQ1LFxuICAgIGVhc2U6IFswLjQsIDAsIDAuMSwgMV0sXG59O1xuY29uc3QgdXNlckFnZW50Q29udGFpbnMgPSAoc3RyaW5nKSA9PiB0eXBlb2YgbmF2aWdhdG9yICE9PSBcInVuZGVmaW5lZFwiICYmXG4gICAgbmF2aWdhdG9yLnVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHN0cmluZyk7XG4vKipcbiAqIE1lYXN1cmVkIGJvdW5kaW5nIGJveGVzIG11c3QgYmUgcm91bmRlZCBpbiBTYWZhcmkgYW5kXG4gKiBsZWZ0IHVudG91Y2hlZCBpbiBDaHJvbWUsIG90aGVyd2lzZSBub24taW50ZWdlciBsYXlvdXRzIHdpdGhpbiBzY2FsZWQtdXAgZWxlbWVudHNcbiAqIGNhbiBhcHBlYXIgdG8ganVtcC5cbiAqL1xuY29uc3Qgcm91bmRQb2ludCA9IHVzZXJBZ2VudENvbnRhaW5zKFwiYXBwbGV3ZWJraXQvXCIpICYmICF1c2VyQWdlbnRDb250YWlucyhcImNocm9tZS9cIilcbiAgICA/IE1hdGgucm91bmRcbiAgICA6IG5vb3A7XG5mdW5jdGlvbiByb3VuZEF4aXMoYXhpcykge1xuICAgIC8vIFJvdW5kIHRvIHRoZSBuZWFyZXN0IC41IHBpeGVscyB0byBzdXBwb3J0IHN1YnBpeGVsIGxheW91dHNcbiAgICBheGlzLm1pbiA9IHJvdW5kUG9pbnQoYXhpcy5taW4pO1xuICAgIGF4aXMubWF4ID0gcm91bmRQb2ludChheGlzLm1heCk7XG59XG5mdW5jdGlvbiByb3VuZEJveChib3gpIHtcbiAgICByb3VuZEF4aXMoYm94LngpO1xuICAgIHJvdW5kQXhpcyhib3gueSk7XG59XG5mdW5jdGlvbiBzaG91bGRBbmltYXRlUG9zaXRpb25Pbmx5KGFuaW1hdGlvblR5cGUsIHNuYXBzaG90LCBsYXlvdXQpIHtcbiAgICByZXR1cm4gKGFuaW1hdGlvblR5cGUgPT09IFwicG9zaXRpb25cIiB8fFxuICAgICAgICAoYW5pbWF0aW9uVHlwZSA9PT0gXCJwcmVzZXJ2ZS1hc3BlY3RcIiAmJlxuICAgICAgICAgICAgIWlzTmVhcihhc3BlY3RSYXRpbyhzbmFwc2hvdCksIGFzcGVjdFJhdGlvKGxheW91dCksIDAuMikpKTtcbn1cblxuZXhwb3J0IHsgY2xlYW5EaXJ0eU5vZGVzLCBjcmVhdGVQcm9qZWN0aW9uTm9kZSwgbWl4QXhpcywgbWl4QXhpc0RlbHRhLCBtaXhCb3gsIHByb3BhZ2F0ZURpcnR5Tm9kZXMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs\n"));

/***/ })

}]);