"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_b"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/bailout-to-client-rendering.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/bailout-to-client-rendering.js ***!
  \*********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"bailoutToClientRendering\", ({\n    enumerable: true,\n    get: function() {\n        return bailoutToClientRendering;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nfunction bailoutToClientRendering(reason) {\n    const staticGenerationStore = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (staticGenerationStore == null ? void 0 : staticGenerationStore.forceStatic) return;\n    if (staticGenerationStore == null ? void 0 : staticGenerationStore.isStaticGeneration) throw new _bailouttocsr.BailoutToCSRError(reason);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=bailout-to-client-rendering.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYmFpbG91dC10by1jbGllbnQtcmVuZGVyaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7NERBR2dCQTs7O2VBQUFBOzs7MENBSGtCO2tFQUNXO0FBRXRDLFNBQVNBLHlCQUF5QkMsTUFBYztJQUNyRCxNQUFNQyx3QkFBd0JDLHNDQUFBQSw0QkFBNEIsQ0FBQ0MsUUFBUTtJQUVuRSxJQUFJRix5QkFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsc0JBQXVCRyxXQUFXLEVBQUU7SUFFeEMsSUFBSUgseUJBQUFBLE9BQUFBLEtBQUFBLElBQUFBLHNCQUF1Qkksa0JBQWtCLEVBQzNDLE1BQU0sSUFBSUMsY0FBQUEsaUJBQWlCLENBQUNOO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvYmFpbG91dC10by1jbGllbnQtcmVuZGVyaW5nLnRzPzI1YzQiXSwibmFtZXMiOlsiYmFpbG91dFRvQ2xpZW50UmVuZGVyaW5nIiwicmVhc29uIiwic3RhdGljR2VuZXJhdGlvblN0b3JlIiwic3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSIsImdldFN0b3JlIiwiZm9yY2VTdGF0aWMiLCJpc1N0YXRpY0dlbmVyYXRpb24iLCJCYWlsb3V0VG9DU1JFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/bailout-to-client-rendering.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-page.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _searchparams = __webpack_require__(/*! ./search-params */ \"(app-pages-browser)/./node_modules/next/dist/client/components/search-params.js\");\nfunction ClientPageRoot(param) {\n    let { Component, props } = param;\n    // We expect to be passed searchParams but even if we aren't we can construct one from\n    // an empty object. We only do this if we are in a static generation as a performance\n    // optimization. Ideally we'd unconditionally construct the tracked params but since\n    // this creates a proxy which is slow and this would happen even for client navigations\n    // that are done entirely dynamically and we know there the dynamic tracking is a noop\n    // in this dynamic case we can safely elide it.\n    props.searchParams = (0, _searchparams.createDynamicallyTrackedSearchParams)(props.searchParams || {});\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n        ...props\n    });\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTyxNQUFBQSxnQkFBU0MsbUJBQUFBLENBTWY7U0FOOEJBLGVBRTdCQyxLQUlEO0lBQ0MsTUFBQUMsU0FBQSxFQUFBRCxLQUFBLEtBQUFFO0lBQ0Esc0ZBQXFGO0lBQ3JGLHFGQUFvRjtJQUNwRjtJQUNBLHVGQUFzRjtJQUN0RixzRkFBK0M7SUFDL0NGLCtDQUFxQkc7SUFHckJILE1BQUFJLFlBQUEsR0FBTyxJQUFBTixjQUFBSyxvQ0FBQ0YsRUFBQUEsTUFBQUEsWUFBQUEsSUFBQUEsQ0FBQUE7V0FBbUIsa0JBQUFJLFlBQUFDLEdBQUEsRUFBQUwsV0FBQTs7SUFDN0I7O0tBakIrQkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS50c3g/YzlhZiJdLCJuYW1lcyI6WyJfc2VhcmNocGFyYW1zIiwiQ2xpZW50UGFnZVJvb3QiLCJwcm9wcyIsIkNvbXBvbmVudCIsInBhcmFtIiwiY3JlYXRlRHluYW1pY2FsbHlUcmFja2VkU2VhcmNoUGFyYW1zIiwic2VhcmNoUGFyYW1zIiwiX2pzeHJ1bnRpbWUiLCJqc3giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/dev-root-not-found-boundary.js ***!
  \*********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DevRootNotFoundBoundary: function() {\n        return DevRootNotFoundBoundary;\n    },\n    bailOnNotFound: function() {\n        return bailOnNotFound;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\");\nfunction bailOnNotFound() {\n    throw new Error(\"notFound() is not allowed to use in root layout\");\n}\nfunction NotAllowedRootNotFoundError() {\n    bailOnNotFound();\n    return null;\n}\n_c = NotAllowedRootNotFoundError;\nfunction DevRootNotFoundBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_notfoundboundary.NotFoundBoundary, {\n        notFound: /*#__PURE__*/ (0, _jsxruntime.jsx)(NotAllowedRootNotFoundError, {}),\n        children: children\n    });\n}\n_c1 = DevRootNotFoundBoundary;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-root-not-found-boundary.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"NotAllowedRootNotFoundError\");\n$RefreshReg$(_c1, \"DevRootNotFoundBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZGV2LXJvb3Qtbm90LWZvdW5kLWJvdW5kYXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O1FBY2dCQSxTQUFBQTs2QkFBQUE7O0lBVEFDO29CQUFBQTs7Ozs7O3NEQUZpQkMsQ0FBQSxDQUFBQyxtQkFBQUEsQ0FBQTtBQUUxQixNQUFBQyxvQkFBU0gsbUJBQUFBLENBQUFBLGtIQUFBQTtTQUNkQTtJQUNGLFVBQUFJLE1BQUE7QUFFQTtTQUNFSjtJQUNBQTtJQUNGO0FBRU87S0FKTEE7U0FJc0NELHdCQUFBTSxLQUFBO0lBS3RDLE1BQUFDLFFBQUEsS0FBQUQ7V0FDb0JFLFdBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQVVDLEdBQUEsRUFBQUwsa0JBQUNNLGdCQUFBQSxFQUFBQTtrQkFDMUJILFdBQUFBLEdBQUFBLENBQUFBLEdBQUFBLFlBQUFBLEdBQUFBLEVBQUFBLDZCQUFBQSxDQUFBQTs7SUFHUDs7TUFWd0NQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvZGV2LXJvb3Qtbm90LWZvdW5kLWJvdW5kYXJ5LnRzeD8wYmM4Il0sIm5hbWVzIjpbIkRldlJvb3ROb3RGb3VuZEJvdW5kYXJ5IiwiYmFpbE9uTm90Rm91bmQiLCJfIiwicmVxdWlyZSIsIl9ub3Rmb3VuZGJvdW5kYXJ5IiwiRXJyb3IiLCJwYXJhbSIsImNoaWxkcmVuIiwibm90Rm91bmQiLCJqc3giLCJOb3RBbGxvd2VkUm9vdE5vdEZvdW5kRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/error-boundary.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorBoundary: function() {\n        return ErrorBoundary;\n    },\n    ErrorBoundaryHandler: function() {\n        return ErrorBoundaryHandler;\n    },\n    GlobalError: function() {\n        return GlobalError;\n    },\n    // Exported so that the import signature in the loaders can be identical to user\n    // supplied custom global error signatures.\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigation = __webpack_require__(/*! ./navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\nconst _isnextroutererror = __webpack_require__(/*! ./is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: \"100vh\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n    },\n    text: {\n        fontSize: \"14px\",\n        fontWeight: 400,\n        lineHeight: \"28px\",\n        margin: \"0 8px\"\n    }\n};\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError(param) {\n    let { error } = param;\n    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if ((store == null ? void 0 : store.isRevalidate) || (store == null ? void 0 : store.isStaticGeneration)) {\n        console.error(error);\n        throw error;\n    }\n    return null;\n}\n_c = HandleISRError;\nclass ErrorBoundaryHandler extends _react.default.Component {\n    static getDerivedStateFromError(error) {\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            // Re-throw if an expected internal Next.js router error occurs\n            // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n            throw error;\n        }\n        return {\n            error\n        };\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.error) {\n            return {\n                error: null,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            error: state.error,\n            previousPathname: props.pathname\n        };\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific the the `@types/react` version.\n    render() {\n        if (this.state.error) {\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: this.state.error\n                    }),\n                    this.props.errorStyles,\n                    this.props.errorScripts,\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(this.props.errorComponent, {\n                        error: this.state.error,\n                        reset: this.reset\n                    })\n                ]\n            });\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.reset = ()=>{\n            this.setState({\n                error: null\n            });\n        };\n        this.state = {\n            error: null,\n            previousPathname: this.props.pathname\n        };\n    }\n}\nfunction GlobalError(param) {\n    let { error } = param;\n    const digest = error == null ? void 0 : error.digest;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n        id: \"__next_error__\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: error\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                        style: styles.error,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h2\", {\n                                    style: styles.text,\n                                    children: \"Application error: a \" + (digest ? \"server\" : \"client\") + \"-side exception has occurred (see the \" + (digest ? \"server logs\" : \"browser console\") + \" for more information).\"\n                                }),\n                                digest ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    style: styles.text,\n                                    children: \"Digest: \" + digest\n                                }) : null\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = GlobalError;\nconst _default = GlobalError;\nfunction ErrorBoundary(param) {\n    let { errorComponent, errorStyles, errorScripts, children } = param;\n    const pathname = (0, _navigation.usePathname)();\n    if (errorComponent) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorBoundaryHandler, {\n            pathname: pathname,\n            errorComponent: errorComponent,\n            errorStyles: errorStyles,\n            errorScripts: errorScripts,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = ErrorBoundary;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"HandleISRError\");\n$RefreshReg$(_c1, \"GlobalError\");\n$RefreshReg$(_c2, \"ErrorBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description);\n        this.description = description;\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== \"object\" || err === null || !(\"digest\" in err) || typeof err.digest !== \"string\") {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLG9CQUFrQjtlQUFsQkE7O0lBUUdDLHNCQUFvQjtlQUFwQkE7OztBQVZoQixNQUFNQyxxQkFBcUI7QUFFcEIsTUFBTUYsMkJBQTJCRztJQUd0Q0MsWUFBWUMsV0FBbUMsQ0FBRTtRQUMvQyxLQUFLLENBQUMsMkJBQXlCQTthQURMQSxXQUFBQSxHQUFBQTthQUY1QkMsTUFBQUEsR0FBb0NKO0lBSXBDO0FBQ0Y7QUFFTyxTQUFTRCxxQkFBcUJNLEdBQVk7SUFDL0MsSUFDRSxPQUFPQSxRQUFRLFlBQ2ZBLFFBQVEsUUFDUixDQUFFLGFBQVlBLEdBQUFBLEtBQ2QsT0FBT0EsSUFBSUQsTUFBTSxLQUFLLFVBQ3RCO1FBQ0EsT0FBTztJQUNUO0lBRUEsT0FBT0MsSUFBSUQsTUFBTSxLQUFLSjtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL2hvb2tzLXNlcnZlci1jb250ZXh0LnRzPzBhYzgiXSwibmFtZXMiOlsiRHluYW1pY1NlcnZlckVycm9yIiwiaXNEeW5hbWljU2VydmVyRXJyb3IiLCJEWU5BTUlDX0VSUk9SX0NPREUiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwiZGVzY3JpcHRpb24iLCJkaWdlc3QiLCJlcnIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/is-hydration-error.js ***!
  \************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isHydrationError\", ({\n    enumerable: true,\n    get: function() {\n        return isHydrationError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst hydrationErrorRegex = /hydration failed|while hydrating|content does not match|did not match/i;\nfunction isHydrationError(error) {\n    return (0, _iserror.default)(error) && hydrationErrorRegex.test(error.message);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-hydration-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaXMtaHlkcmF0aW9uLWVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7b0RBS2dCQTs7O2VBQUFBOzs7OzhFQUxJO0FBRXBCLE1BQU1DLHNCQUNKO0FBRUssU0FBU0QsaUJBQWlCRSxLQUFjO0lBQzdDLE9BQU9DLENBQUFBLEdBQUFBLFNBQUFBLE9BQU8sRUFBQ0QsVUFBVUQsb0JBQW9CRyxJQUFJLENBQUNGLE1BQU1HLE9BQU87QUFDakUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9pcy1oeWRyYXRpb24tZXJyb3IudHM/NGNmNiJdLCJuYW1lcyI6WyJpc0h5ZHJhdGlvbkVycm9yIiwiaHlkcmF0aW9uRXJyb3JSZWdleCIsImVycm9yIiwiaXNFcnJvciIsInRlc3QiLCJtZXNzYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/is-next-router-error.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isNextRouterError\", ({\n    enumerable: true,\n    get: function() {\n        return isNextRouterError;\n    }\n}));\nconst _notfound = __webpack_require__(/*! ./not-found */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nfunction isNextRouterError(error) {\n    return error && error.digest && ((0, _redirect.isRedirectError)(error) || (0, _notfound.isNotFoundError)(error));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-next-router-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaXMtbmV4dC1yb3V0ZXItZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFHZ0JBOzs7ZUFBQUE7OztzQ0FIZ0I7c0NBQ0E7QUFFekIsU0FBU0Esa0JBQWtCQyxLQUFVO0lBQzFDLE9BQ0VBLFNBQVNBLE1BQU1DLE1BQU0sSUFBS0MsQ0FBQUEsQ0FBQUEsR0FBQUEsVUFBQUEsZUFBZSxFQUFDRixVQUFVRyxDQUFBQSxHQUFBQSxVQUFBQSxlQUFlLEVBQUNILE1BQUFBO0FBRXhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvaXMtbmV4dC1yb3V0ZXItZXJyb3IudHM/MDRlOSJdLCJuYW1lcyI6WyJpc05leHRSb3V0ZXJFcnJvciIsImVycm9yIiwiZGlnZXN0IiwiaXNSZWRpcmVjdEVycm9yIiwiaXNOb3RGb3VuZEVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\n"));

/***/ })

}]);