"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_graphql_language_parser_mjs-c45803c0"],{

/***/ "(app-pages-browser)/./node_modules/graphql/language/parser.mjs":
/*!**************************************************!*\
  !*** ./node_modules/graphql/language/parser.mjs ***!
  \**************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: function() { return /* binding */ Parser; },\n/* harmony export */   parse: function() { return /* binding */ parse; },\n/* harmony export */   parseConstValue: function() { return /* binding */ parseConstValue; },\n/* harmony export */   parseType: function() { return /* binding */ parseType; },\n/* harmony export */   parseValue: function() { return /* binding */ parseValue; }\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(app-pages-browser)/./node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ast.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./directiveLocation.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/directiveLocation.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./kinds.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexer.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/lexer.mjs\");\n/* harmony import */ var _source_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./source.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/source.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n\n\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nfunction parse(source, options) {\n  const parser = new Parser(source, options);\n  const document = parser.parseDocument();\n  Object.defineProperty(document, 'tokenCount', {\n    enumerable: false,\n    value: parser.tokenCount,\n  });\n  return document;\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nfunction parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nfunction parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nfunction parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nclass Parser {\n  constructor(source, options = {}) {\n    const sourceObj = (0,_source_mjs__WEBPACK_IMPORTED_MODULE_1__.isSource)(source) ? source : new _source_mjs__WEBPACK_IMPORTED_MODULE_1__.Source(source);\n    this._lexer = new _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n\n  get tokenCount() {\n    return this._tokenCounter;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAME,\n      value: token.value,\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DOCUMENT,\n      definitions: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF,\n        this.parseDefinition,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF,\n      ),\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription\n      ? this._lexer.lookahead()\n      : this._lexer.token;\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n\n      if (hasDescription) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          this._lexer.token.start,\n          'Unexpected description, descriptions are supported only on type definitions.',\n        );\n      }\n\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n        operation: _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    const operation = this.parseOperationType();\n    let name;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY;\n\n      case 'mutation':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.MUTATION;\n\n      case 'subscription':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.SUBSCRIPTION;\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseVariableDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n        ? this.parseConstValueLiteral()\n        : undefined,\n      directives: this.parseConstDirectives(),\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE,\n      name: this.parseName(),\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SELECTION_SET,\n      selections: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n        this.parseSelection,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n      ),\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD)\n      ? this.parseFragment()\n      : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)\n        ? this.parseSelectionSet()\n        : undefined,\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  }\n\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n\n    switch (token.kind) {\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FLOAT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING:\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME:\n        this.advanceLexer();\n\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: true,\n            });\n\n          case 'false':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: false,\n            });\n\n          case 'null':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NULL,\n            });\n\n          default:\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM,\n              value: token.value,\n            });\n        }\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n\n          if (this._lexer.token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n              this._lexer.source,\n              token.start,\n              `Unexpected variable \"$${varName}\" in constant value.`,\n            );\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n\n        return this.parseVariable();\n\n      default:\n        throw this.unexpected();\n    }\n  }\n\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.STRING,\n      value: token.value,\n      block: token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING,\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST,\n      values: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R),\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT,\n      fields: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R),\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n\n    while (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST_TYPE,\n        type: innerType,\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BANG)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NON_NULL_TYPE,\n        type,\n      });\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAMED_TYPE,\n      name: this.parseName(),\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING) || this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type,\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements')\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AMP, this.parseNamedType)\n      : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseFieldDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives,\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives,\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseNamedType)\n      : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseEnumValueDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (\n      this._lexer.token.value === 'true' ||\n      this._lexer.token.value === 'false' ||\n      this._lexer.token.value === 'null'\n    ) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        this._lexer.token.start,\n        `${getTokenDesc(\n          this._lexer.token,\n        )} is reserved and cannot be used for an enum value.`,\n      );\n    }\n\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations,\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n\n    if (Object.prototype.hasOwnProperty.call(_directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__.DirectiveLocation, name.value)) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.Location(\n        startToken,\n        this._lexer.lastToken,\n        this._lexer.source,\n      );\n    }\n\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        token.start,\n        `Expected \"${value}\", found ${getTokenDesc(token)}.`,\n      );\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token =\n      atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Unexpected ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  }\n\n  advanceLexer() {\n    const { maxTokens } = this._options;\n\n    const token = this._lexer.advance();\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF) {\n      ++this._tokenCounter;\n\n      if (maxTokens !== undefined && this._tokenCounter > maxTokens) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          token.start,\n          `Document contains more that ${maxTokens} tokens. Parsing aborted.`,\n        );\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return (0,_lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPunctuatorTokenKind)(kind) ? `\"${kind}\"` : kind;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/language/parser.mjs\n"));

/***/ })

}]);