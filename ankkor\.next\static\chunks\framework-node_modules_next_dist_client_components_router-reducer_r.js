"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_router-reducer_r"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js ***!
  \*******************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addRefreshMarkerToActiveParallelSegments: function() {\n        return addRefreshMarkerToActiveParallelSegments;\n    },\n    refreshInactiveParallelSegments: function() {\n        return refreshInactiveParallelSegments;\n    }\n});\nconst _applyflightdata = __webpack_require__(/*! ./apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nasync function refreshInactiveParallelSegments(options) {\n    const fetchedSegments = new Set();\n    await refreshInactiveParallelSegmentsImpl({\n        ...options,\n        rootTree: options.updatedTree,\n        fetchedSegments\n    });\n}\nasync function refreshInactiveParallelSegmentsImpl(param) {\n    let { state, updatedTree, updatedCache, includeNextUrl, fetchedSegments, rootTree = updatedTree, canonicalUrl } = param;\n    const [, parallelRoutes, refetchPath, refetchMarker] = updatedTree;\n    const fetchPromises = [];\n    if (refetchPath && refetchPath !== canonicalUrl && refetchMarker === \"refresh\" && // it's possible for the tree to contain multiple segments that contain data at the same URL\n    // we keep track of them so we can dedupe the requests\n    !fetchedSegments.has(refetchPath)) {\n        fetchedSegments.add(refetchPath) // Mark this URL as fetched\n        ;\n        // Eagerly kick off the fetch for the refetch path & the parallel routes. This should be fine to do as they each operate\n        // independently on their own cache nodes, and `applyFlightData` will copy anything it doesn't care about from the existing cache.\n        const fetchPromise = (0, _fetchserverresponse.fetchServerResponse)(new URL(refetchPath, location.origin), // and might not contain the data we need to patch in interception route data (such as dynamic params from a previous segment)\n        [\n            rootTree[0],\n            rootTree[1],\n            rootTree[2],\n            \"refetch\"\n        ], includeNextUrl ? state.nextUrl : null, state.buildId).then((fetchResponse)=>{\n            const flightData = fetchResponse[0];\n            if (typeof flightData !== \"string\") {\n                for (const flightDataPath of flightData){\n                    // we only pass the new cache as this function is called after clearing the router cache\n                    // and filling in the new page data from the server. Meaning the existing cache is actually the cache that's\n                    // just been created & has been written to, but hasn't been \"committed\" yet.\n                    (0, _applyflightdata.applyFlightData)(updatedCache, updatedCache, flightDataPath);\n                }\n            } else {\n            // When flightData is a string, it suggests that the server response should have triggered an MPA navigation\n            // I'm not 100% sure of this decision, but it seems unlikely that we'd want to introduce a redirect side effect\n            // when refreshing on-screen data, so handling this has been ommitted.\n            }\n        });\n        fetchPromises.push(fetchPromise);\n    }\n    for(const key in parallelRoutes){\n        const parallelFetchPromise = refreshInactiveParallelSegmentsImpl({\n            state,\n            updatedTree: parallelRoutes[key],\n            updatedCache,\n            includeNextUrl,\n            fetchedSegments,\n            rootTree,\n            canonicalUrl\n        });\n        fetchPromises.push(parallelFetchPromise);\n    }\n    await Promise.all(fetchPromises);\n}\nfunction addRefreshMarkerToActiveParallelSegments(tree, path) {\n    const [segment, parallelRoutes, , refetchMarker] = tree;\n    // a page segment might also contain concatenated search params, so we do a partial match on the key\n    if (segment.includes(_segment.PAGE_SEGMENT_KEY) && refetchMarker !== \"refresh\") {\n        tree[2] = path;\n        tree[3] = \"refresh\";\n    }\n    for(const key in parallelRoutes){\n        addRefreshMarkerToActiveParallelSegments(parallelRoutes[key], path);\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=refetch-inactive-parallel-segments.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBWWFBLHFCQUFtQjtlQUFuQkE7O0lBSkFDLGlCQUFlO2VBQWZBOztJQUdBQyxpQkFBZTtlQUFmQTs7SUFKQUMsZ0JBQWM7ZUFBZEE7O0lBRUFDLGdCQUFjO2VBQWRBOztJQUlBQyxzQkFBb0I7ZUFBcEJBOztJQUhBQyxxQkFBbUI7ZUFBbkJBOzs7Ozs7OztJQXVRR0MsWUFBVTtlQUFWQTs7O0FBMVFULE1BQU1KLGlCQUFpQjtBQUN2QixNQUFNRixrQkFBa0I7QUFDeEIsTUFBTUcsaUJBQWlCO0FBQ3ZCLE1BQU1FLHNCQUFzQjtBQUM1QixNQUFNSixrQkFBa0I7QUFDeEIsTUFBTUYsc0JBQXNCO0FBQzVCLE1BQU1LLHVCQUF1Qjs7VUF1SXhCRyxZQUFBQTs7OztHQUFBQSxnQkFBQUEsQ0FBQUEsZUFBQUEsQ0FBQUEsQ0FBQUE7O1VBOERBQyx3QkFBQUE7Ozs7O0dBQUFBLDRCQUFBQSxDQUFBQSwyQkFBQUEsQ0FBQUEsQ0FBQUE7QUErREwsU0FBU0YsV0FBV0csS0FBVTtJQUNuQyw2RUFBNkU7SUFDN0UsNkVBQTZFO0lBQzdFLHVFQUF1RTtJQUN2RSxnQkFBZ0I7SUFDaEIsT0FDRUEsU0FDQyxRQUFPQSxVQUFVLFlBQVksT0FBT0EsVUFBVSxlQUMvQyxPQUFPQSxNQUFNQyxJQUFJLEtBQUs7QUFFMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yb3V0ZXItcmVkdWNlci9yb3V0ZXItcmVkdWNlci10eXBlcy50cz80ZGVhIl0sIm5hbWVzIjpbIkFDVElPTl9GQVNUX1JFRlJFU0giLCJBQ1RJT05fTkFWSUdBVEUiLCJBQ1RJT05fUFJFRkVUQ0giLCJBQ1RJT05fUkVGUkVTSCIsIkFDVElPTl9SRVNUT1JFIiwiQUNUSU9OX1NFUlZFUl9BQ1RJT04iLCJBQ1RJT05fU0VSVkVSX1BBVENIIiwiaXNUaGVuYWJsZSIsIlByZWZldGNoS2luZCIsIlByZWZldGNoQ2FjaGVFbnRyeVN0YXR1cyIsInZhbHVlIiwidGhlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer.js ***!
  \***********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"reducer\", ({\n    enumerable: true,\n    get: function() {\n        return reducer;\n    }\n}));\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _navigatereducer = __webpack_require__(/*! ./reducers/navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _serverpatchreducer = __webpack_require__(/*! ./reducers/server-patch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js\");\nconst _restorereducer = __webpack_require__(/*! ./reducers/restore-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js\");\nconst _refreshreducer = __webpack_require__(/*! ./reducers/refresh-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _fastrefreshreducer = __webpack_require__(/*! ./reducers/fast-refresh-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/fast-refresh-reducer.js\");\nconst _serveractionreducer = __webpack_require__(/*! ./reducers/server-action-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js\");\n/**\n * Reducer that handles the app-router state updates.\n */ function clientReducer(state, action) {\n    switch(action.type){\n        case _routerreducertypes.ACTION_NAVIGATE:\n            {\n                return (0, _navigatereducer.navigateReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_SERVER_PATCH:\n            {\n                return (0, _serverpatchreducer.serverPatchReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_RESTORE:\n            {\n                return (0, _restorereducer.restoreReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_REFRESH:\n            {\n                return (0, _refreshreducer.refreshReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_FAST_REFRESH:\n            {\n                return (0, _fastrefreshreducer.fastRefreshReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_PREFETCH:\n            {\n                return (0, _prefetchreducer.prefetchReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_SERVER_ACTION:\n            {\n                return (0, _serveractionreducer.serverActionReducer)(state, action);\n            }\n        // This case should never be hit as dispatch is strongly typed.\n        default:\n            throw new Error(\"Unknown action\");\n    }\n}\nfunction serverReducer(state, _action) {\n    return state;\n}\nconst reducer = typeof window === \"undefined\" ? serverReducer : clientReducer;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"shouldHardNavigate\", ({\n    enumerable: true,\n    get: function() {\n        return shouldHardNavigate;\n    }\n}));\nconst _matchsegments = __webpack_require__(/*! ../match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nfunction shouldHardNavigate(flightSegmentPath, flightRouterState) {\n    const [segment, parallelRoutes] = flightRouterState;\n    // TODO-APP: Check if `as` can be replaced.\n    const [currentSegment, parallelRouteKey] = flightSegmentPath;\n    // Check if current segment matches the existing segment.\n    if (!(0, _matchsegments.matchSegment)(currentSegment, segment)) {\n        // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n        if (Array.isArray(currentSegment)) {\n            return true;\n        }\n        // If the existing segment did not match soft navigation is triggered.\n        return false;\n    }\n    const lastSegment = flightSegmentPath.length <= 2;\n    if (lastSegment) {\n        return false;\n    }\n    return shouldHardNavigate(flightSegmentPath.slice(2), parallelRoutes[parallelRouteKey]);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=should-hard-navigate.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvc2hvdWxkLWhhcmQtbmF2aWdhdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztzREFRZ0JBOzs7ZUFBQUE7OzsyQ0FIYTtBQUd0QixTQUFTQSxtQkFDZEMsaUJBQWlDLEVBQ2pDQyxpQkFBb0M7SUFFcEMsTUFBTSxDQUFDQyxTQUFTQyxlQUFlLEdBQUdGO0lBQ2xDLDJDQUEyQztJQUMzQyxNQUFNLENBQUNHLGdCQUFnQkMsaUJBQWlCLEdBQUdMO0lBSzNDLHlEQUF5RDtJQUN6RCxJQUFJLENBQUNNLENBQUFBLEdBQUFBLGVBQUFBLFlBQVksRUFBQ0YsZ0JBQWdCRixVQUFVO1FBQzFDLGtHQUFrRztRQUNsRyxJQUFJSyxNQUFNQyxPQUFPLENBQUNKLGlCQUFpQjtZQUNqQyxPQUFPO1FBQ1Q7UUFFQSxzRUFBc0U7UUFDdEUsT0FBTztJQUNUO0lBQ0EsTUFBTUssY0FBY1Qsa0JBQWtCVSxNQUFNLElBQUk7SUFFaEQsSUFBSUQsYUFBYTtRQUNmLE9BQU87SUFDVDtJQUVBLE9BQU9WLG1CQUNMQyxrQkFBa0JXLEtBQUssQ0FBQyxJQUN4QlIsY0FBYyxDQUFDRSxpQkFBaUI7QUFFcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yb3V0ZXItcmVkdWNlci9zaG91bGQtaGFyZC1uYXZpZ2F0ZS50cz8xMjcxIl0sIm5hbWVzIjpbInNob3VsZEhhcmROYXZpZ2F0ZSIsImZsaWdodFNlZ21lbnRQYXRoIiwiZmxpZ2h0Um91dGVyU3RhdGUiLCJzZWdtZW50IiwicGFyYWxsZWxSb3V0ZXMiLCJjdXJyZW50U2VnbWVudCIsInBhcmFsbGVsUm91dGVLZXkiLCJtYXRjaFNlZ21lbnQiLCJBcnJheSIsImlzQXJyYXkiLCJsYXN0U2VnbWVudCIsImxlbmd0aCIsInNsaWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js\n"));

/***/ })

}]);