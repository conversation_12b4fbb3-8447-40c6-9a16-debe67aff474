"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_router-reducer_reducers_f"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/fast-refresh-reducer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/fast-refresh-reducer.js ***!
  \**************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"fastRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return fastRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction fastRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), [\n        state.tree[0],\n        state.tree[1],\n        state.tree[2],\n        \"refetch\"\n    ], includeNextUrl ? state.nextUrl : null, state.buildId);\n    return cache.lazyData.then((param)=>{\n        let [flightData, canonicalUrlOverride] = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === \"string\") {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const flightDataPath of flightData){\n            // FlightDataPath with more than two items means unexpected Flight data was returned\n            if (flightDataPath.length !== 3) {\n                // TODO-APP: handle this case better\n                console.log(\"REFRESH FAILED\");\n                return state;\n            }\n            // Given the path can only have two items the items are only the router state and rsc for the root.\n            const [treePatch] = flightDataPath;\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                \"\"\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, flightDataPath);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction fastRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst fastRefreshReducer =  false ? 0 : fastRefreshReducerImpl;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fast-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/fast-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js ***!
  \************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findHeadInCache\", ({\n    enumerable: true,\n    get: function() {\n        return findHeadInCache;\n    }\n}));\nconst _createroutercachekey = __webpack_require__(/*! ../create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nfunction findHeadInCache(cache, parallelRoutes) {\n    return findHeadInCacheImpl(cache, parallelRoutes, \"\");\n}\nfunction findHeadInCacheImpl(cache, parallelRoutes, keyPrefix) {\n    const isLastItem = Object.keys(parallelRoutes).length === 0;\n    if (isLastItem) {\n        // Returns the entire Cache Node of the segment whose head we will render.\n        return [\n            cache,\n            keyPrefix\n        ];\n    }\n    for(const key in parallelRoutes){\n        const [segment, childParallelRoutes] = parallelRoutes[key];\n        const childSegmentMap = cache.parallelRoutes.get(key);\n        if (!childSegmentMap) {\n            continue;\n        }\n        const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(segment);\n        const cacheNode = childSegmentMap.get(cacheKey);\n        if (!cacheNode) {\n            continue;\n        }\n        const item = findHeadInCacheImpl(cacheNode, childParallelRoutes, keyPrefix + \"/\" + cacheKey);\n        if (item) {\n            return item;\n        }\n    }\n    return null;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=find-head-in-cache.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvZmluZC1oZWFkLWluLWNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBSWdCQTs7O2VBQUFBOzs7a0RBRnFCO0FBRTlCLFNBQVNBLGdCQUNkQyxLQUFnQixFQUNoQkMsY0FBb0M7SUFFcEMsT0FBT0Msb0JBQW9CRixPQUFPQyxnQkFBZ0I7QUFDcEQ7QUFFQSxTQUFTQyxvQkFDUEYsS0FBZ0IsRUFDaEJDLGNBQW9DLEVBQ3BDRSxTQUFpQjtJQUVqQixNQUFNQyxhQUFhQyxPQUFPQyxJQUFJLENBQUNMLGdCQUFnQk0sTUFBTSxLQUFLO0lBQzFELElBQUlILFlBQVk7UUFDZCwwRUFBMEU7UUFDMUUsT0FBTztZQUFDSjtZQUFPRztTQUFVO0lBQzNCO0lBQ0EsSUFBSyxNQUFNSyxPQUFPUCxlQUFnQjtRQUNoQyxNQUFNLENBQUNRLFNBQVNDLG9CQUFvQixHQUFHVCxjQUFjLENBQUNPLElBQUk7UUFDMUQsTUFBTUcsa0JBQWtCWCxNQUFNQyxjQUFjLENBQUNXLEdBQUcsQ0FBQ0o7UUFDakQsSUFBSSxDQUFDRyxpQkFBaUI7WUFDcEI7UUFDRjtRQUVBLE1BQU1FLFdBQVdDLENBQUFBLEdBQUFBLHNCQUFBQSxvQkFBb0IsRUFBQ0w7UUFFdEMsTUFBTU0sWUFBWUosZ0JBQWdCQyxHQUFHLENBQUNDO1FBQ3RDLElBQUksQ0FBQ0UsV0FBVztZQUNkO1FBQ0Y7UUFFQSxNQUFNQyxPQUFPZCxvQkFDWGEsV0FDQUwscUJBQ0FQLFlBQVksTUFBTVU7UUFFcEIsSUFBSUcsTUFBTTtZQUNSLE9BQU9BO1FBQ1Q7SUFDRjtJQUVBLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL2ZpbmQtaGVhZC1pbi1jYWNoZS50cz9kMjlmIl0sIm5hbWVzIjpbImZpbmRIZWFkSW5DYWNoZSIsImNhY2hlIiwicGFyYWxsZWxSb3V0ZXMiLCJmaW5kSGVhZEluQ2FjaGVJbXBsIiwia2V5UHJlZml4IiwiaXNMYXN0SXRlbSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJrZXkiLCJzZWdtZW50IiwiY2hpbGRQYXJhbGxlbFJvdXRlcyIsImNoaWxkU2VnbWVudE1hcCIsImdldCIsImNhY2hlS2V5IiwiY3JlYXRlUm91dGVyQ2FjaGVLZXkiLCJjYWNoZU5vZGUiLCJpdGVtIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js ***!
  \***********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSegmentValue\", ({\n    enumerable: true,\n    get: function() {\n        return getSegmentValue;\n    }\n}));\nfunction getSegmentValue(segment) {\n    return Array.isArray(segment) ? segment[1] : segment;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-segment-value.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvZ2V0LXNlZ21lbnQtdmFsdWUuanMiLCJtYXBwaW5ncyI6Ijs7OzttREFFZ0JBOzs7ZUFBQUE7OztBQUFULFNBQVNBLGdCQUFnQkMsT0FBZ0I7SUFDOUMsT0FBT0MsTUFBTUMsT0FBTyxDQUFDRixXQUFXQSxPQUFPLENBQUMsRUFBRSxHQUFHQTtBQUMvQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL2dldC1zZWdtZW50LXZhbHVlLnRzPzkxZTEiXSwibmFtZXMiOlsiZ2V0U2VnbWVudFZhbHVlIiwic2VnbWVudCIsIkFycmF5IiwiaXNBcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js ***!
  \********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasInterceptionRouteInCurrentTree\", ({\n    enumerable: true,\n    get: function() {\n        return hasInterceptionRouteInCurrentTree;\n    }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"(app-pages-browser)/./node_modules/next/dist/server/future/helpers/interception-routes.js\");\nfunction hasInterceptionRouteInCurrentTree(param) {\n    let [segment, parallelRoutes] = param;\n    // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n    if (Array.isArray(segment) && (segment[2] === \"di\" || segment[2] === \"ci\")) {\n        return true;\n    }\n    // If segment is not an array, apply the existing string-based check\n    if (typeof segment === \"string\" && (0, _interceptionroutes.isInterceptionRouteAppPath)(segment)) {\n        return true;\n    }\n    // Iterate through parallelRoutes if they exist\n    if (parallelRoutes) {\n        for(const key in parallelRoutes){\n            if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-interception-route-in-current-tree.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvaGFzLWludGVyY2VwdGlvbi1yb3V0ZS1pbi1jdXJyZW50LXRyZWUuanMiLCJtYXBwaW5ncyI6Ijs7OztxRUFHZ0JBOzs7ZUFBQUE7OztnREFGMkI7QUFFcEMsU0FBU0Esa0NBQWtDQyxLQUc5QjtJQUg4QixLQUNoREMsU0FDQUMsZUFDa0IsR0FIOEJGO0lBSWhELHdHQUF3RztJQUN4RyxJQUFJRyxNQUFNQyxPQUFPLENBQUNILFlBQWFBLENBQUFBLE9BQU8sQ0FBQyxFQUFFLEtBQUssUUFBUUEsT0FBTyxDQUFDLEVBQUUsS0FBSyxPQUFPO1FBQzFFLE9BQU87SUFDVDtJQUVBLG9FQUFvRTtJQUNwRSxJQUFJLE9BQU9BLFlBQVksWUFBWUksQ0FBQUEsR0FBQUEsb0JBQUFBLDBCQUEwQixFQUFDSixVQUFVO1FBQ3RFLE9BQU87SUFDVDtJQUVBLCtDQUErQztJQUMvQyxJQUFJQyxnQkFBZ0I7UUFDbEIsSUFBSyxNQUFNSSxPQUFPSixlQUFnQjtZQUNoQyxJQUFJSCxrQ0FBa0NHLGNBQWMsQ0FBQ0ksSUFBSSxHQUFHO2dCQUMxRCxPQUFPO1lBQ1Q7UUFDRjtJQUNGO0lBRUEsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvaGFzLWludGVyY2VwdGlvbi1yb3V0ZS1pbi1jdXJyZW50LXRyZWUudHM/ZWNhNyJdLCJuYW1lcyI6WyJoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUiLCJwYXJhbSIsInNlZ21lbnQiLCJwYXJhbGxlbFJvdXRlcyIsIkFycmF5IiwiaXNBcnJheSIsImlzSW50ZXJjZXB0aW9uUm91dGVBcHBQYXRoIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleExternalUrl: function() {\n        return handleExternalUrl;\n    },\n    navigateReducer: function() {\n        return navigateReducer;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _invalidatecachebelowflightsegmentpath = __webpack_require__(/*! ../invalidate-cache-below-flight-segmentpath */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _shouldhardnavigate = __webpack_require__(/*! ../should-hard-navigate */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _segment = __webpack_require__(/*! ../../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _pprnavigations = __webpack_require__(/*! ../ppr-navigations */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ../prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _clearcachenodedataforsegmentpath = __webpack_require__(/*! ../clear-cache-node-data-for-segment-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/clear-cache-node-data-for-segment-path.js\");\nfunction handleExternalUrl(state, mutable, url, pendingPush) {\n    mutable.mpaNavigation = true;\n    mutable.canonicalUrl = url;\n    mutable.pendingPush = pendingPush;\n    mutable.scrollableSegments = undefined;\n    return (0, _handlemutable.handleMutable)(state, mutable);\n}\nfunction generateSegmentsFromPatch(flightRouterPatch) {\n    const segments = [];\n    const [segment, parallelRoutes] = flightRouterPatch;\n    if (Object.keys(parallelRoutes).length === 0) {\n        return [\n            [\n                segment\n            ]\n        ];\n    }\n    for (const [parallelRouteKey, parallelRoute] of Object.entries(parallelRoutes)){\n        for (const childSegment of generateSegmentsFromPatch(parallelRoute)){\n            // If the segment is empty, it means we are at the root of the tree\n            if (segment === \"\") {\n                segments.push([\n                    parallelRouteKey,\n                    ...childSegment\n                ]);\n            } else {\n                segments.push([\n                    segment,\n                    parallelRouteKey,\n                    ...childSegment\n                ]);\n            }\n        }\n    }\n    return segments;\n}\nfunction triggerLazyFetchForLeafSegments(newCache, currentCache, flightSegmentPath, treePatch) {\n    let appliedPatch = false;\n    newCache.rsc = currentCache.rsc;\n    newCache.prefetchRsc = currentCache.prefetchRsc;\n    newCache.loading = currentCache.loading;\n    newCache.parallelRoutes = new Map(currentCache.parallelRoutes);\n    const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map((segment)=>[\n            ...flightSegmentPath,\n            ...segment\n        ]);\n    for (const segmentPaths of segmentPathsToFill){\n        (0, _clearcachenodedataforsegmentpath.clearCacheNodeDataForSegmentPath)(newCache, currentCache, segmentPaths);\n        appliedPatch = true;\n    }\n    return appliedPatch;\n}\nconst navigateReducer =  false ? 0 : navigateReducer_noPPR;\n// This is the implementation when PPR is disabled. We can assume its behavior\n// is relatively stable because it's been running in production for a while.\nfunction navigateReducer_noPPR(state, action) {\n    const { url, isExternalUrl, navigateType, shouldScroll } = action;\n    const mutable = {};\n    const { hash } = url;\n    const href = (0, _createhreffromurl.createHrefFromUrl)(url);\n    const pendingPush = navigateType === \"push\";\n    // we want to prune the prefetch cache on every navigation to avoid it growing too large\n    (0, _prefetchcacheutils.prunePrefetchCache)(state.prefetchCache);\n    mutable.preserveCustomHistoryState = false;\n    if (isExternalUrl) {\n        return handleExternalUrl(state, mutable, url.toString(), pendingPush);\n    }\n    const prefetchValues = (0, _prefetchcacheutils.getOrCreatePrefetchCacheEntry)({\n        url,\n        nextUrl: state.nextUrl,\n        tree: state.tree,\n        buildId: state.buildId,\n        prefetchCache: state.prefetchCache\n    });\n    const { treeAtTimeOfPrefetch, data } = prefetchValues;\n    _prefetchreducer.prefetchQueue.bump(data);\n    return data.then((param)=>{\n        let [flightData, canonicalUrlOverride] = param;\n        let isFirstRead = false;\n        // we only want to mark this once\n        if (!prefetchValues.lastUsedTime) {\n            // important: we should only mark the cache node as dirty after we unsuspend from the call above\n            prefetchValues.lastUsedTime = Date.now();\n            isFirstRead = true;\n        }\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === \"string\") {\n            return handleExternalUrl(state, mutable, flightData, pendingPush);\n        }\n        // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n        // which will trigger an MPA navigation.\n        if (document.getElementById(\"__next-page-redirect\")) {\n            return handleExternalUrl(state, mutable, href, pendingPush);\n        }\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        let scrollableSegments = [];\n        for (const flightDataPath of flightData){\n            const flightSegmentPath = flightDataPath.slice(0, -4);\n            // The one before last item is the router state tree patch\n            const treePatch = flightDataPath.slice(-3)[0];\n            // TODO-APP: remove ''\n            const flightSegmentPathWithLeadingEmpty = [\n                \"\",\n                ...flightSegmentPath\n            ];\n            // Create new tree based on the flightSegmentPath and router state patch\n            let newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, currentTree, treePatch, href);\n            // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n            // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n            if (newTree === null) {\n                newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, treeAtTimeOfPrefetch, treePatch, href);\n            }\n            if (newTree !== null) {\n                if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                    return handleExternalUrl(state, mutable, href, pendingPush);\n                }\n                const cache = (0, _approuter.createEmptyCacheNode)();\n                let applied = false;\n                if (prefetchValues.status === _routerreducertypes.PrefetchCacheEntryStatus.stale && !isFirstRead) {\n                    // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n                    // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n                    // while copying over the `loading` for the segment that contains the page data.\n                    // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n                    applied = triggerLazyFetchForLeafSegments(cache, currentCache, flightSegmentPath, treePatch);\n                    // since we re-used the stale cache's loading state & refreshed the data,\n                    // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n                    prefetchValues.lastUsedTime = Date.now();\n                } else {\n                    applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, flightDataPath, prefetchValues);\n                }\n                const hardNavigate = (0, _shouldhardnavigate.shouldHardNavigate)(flightSegmentPathWithLeadingEmpty, currentTree);\n                if (hardNavigate) {\n                    // Copy rsc for the root node of the cache.\n                    cache.rsc = currentCache.rsc;\n                    cache.prefetchRsc = currentCache.prefetchRsc;\n                    (0, _invalidatecachebelowflightsegmentpath.invalidateCacheBelowFlightSegmentPath)(cache, currentCache, flightSegmentPath);\n                    // Ensure the existing cache value is used when the cache was not invalidated.\n                    mutable.cache = cache;\n                } else if (applied) {\n                    mutable.cache = cache;\n                    // If we applied the cache, we update the \"current cache\" value so any other\n                    // segments in the FlightDataPath will be able to reference the updated cache.\n                    currentCache = cache;\n                }\n                currentTree = newTree;\n                for (const subSegment of generateSegmentsFromPatch(treePatch)){\n                    const scrollableSegmentPath = [\n                        ...flightSegmentPath,\n                        ...subSegment\n                    ];\n                    // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n                    if (scrollableSegmentPath[scrollableSegmentPath.length - 1] !== _segment.DEFAULT_SEGMENT_KEY) {\n                        scrollableSegments.push(scrollableSegmentPath);\n                    }\n                }\n            }\n        }\n        mutable.patchedTree = currentTree;\n        mutable.canonicalUrl = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : href;\n        mutable.pendingPush = pendingPush;\n        mutable.scrollableSegments = scrollableSegments;\n        mutable.hashFragment = hash;\n        mutable.shouldScroll = shouldScroll;\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\n// This is the experimental PPR implementation. It's closer to the behavior we\n// want, but is likelier to include accidental regressions because it rewrites\n// existing functionality.\nfunction navigateReducer_PPR(state, action) {\n    const { url, isExternalUrl, navigateType, shouldScroll } = action;\n    const mutable = {};\n    const { hash } = url;\n    const href = (0, _createhreffromurl.createHrefFromUrl)(url);\n    const pendingPush = navigateType === \"push\";\n    // we want to prune the prefetch cache on every navigation to avoid it growing too large\n    (0, _prefetchcacheutils.prunePrefetchCache)(state.prefetchCache);\n    mutable.preserveCustomHistoryState = false;\n    if (isExternalUrl) {\n        return handleExternalUrl(state, mutable, url.toString(), pendingPush);\n    }\n    const prefetchValues = (0, _prefetchcacheutils.getOrCreatePrefetchCacheEntry)({\n        url,\n        nextUrl: state.nextUrl,\n        tree: state.tree,\n        buildId: state.buildId,\n        prefetchCache: state.prefetchCache\n    });\n    const { treeAtTimeOfPrefetch, data } = prefetchValues;\n    _prefetchreducer.prefetchQueue.bump(data);\n    return data.then((param)=>{\n        let [flightData, canonicalUrlOverride, _postponed] = param;\n        let isFirstRead = false;\n        // we only want to mark this once\n        if (!prefetchValues.lastUsedTime) {\n            // important: we should only mark the cache node as dirty after we unsuspend from the call above\n            prefetchValues.lastUsedTime = Date.now();\n            isFirstRead = true;\n        }\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === \"string\") {\n            return handleExternalUrl(state, mutable, flightData, pendingPush);\n        }\n        // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n        // which will trigger an MPA navigation.\n        if (document.getElementById(\"__next-page-redirect\")) {\n            return handleExternalUrl(state, mutable, href, pendingPush);\n        }\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        let scrollableSegments = [];\n        // TODO: In practice, this is always a single item array. We probably\n        // aren't going to every send multiple segments, at least not in this\n        // format. So we could remove the extra wrapper for now until\n        // that settles.\n        for (const flightDataPath of flightData){\n            const flightSegmentPath = flightDataPath.slice(0, -4);\n            // The one before last item is the router state tree patch\n            const treePatch = flightDataPath.slice(-3)[0];\n            // TODO-APP: remove ''\n            const flightSegmentPathWithLeadingEmpty = [\n                \"\",\n                ...flightSegmentPath\n            ];\n            // Create new tree based on the flightSegmentPath and router state patch\n            let newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, currentTree, treePatch, href);\n            // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n            // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n            if (newTree === null) {\n                newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, treeAtTimeOfPrefetch, treePatch, href);\n            }\n            if (newTree !== null) {\n                if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                    return handleExternalUrl(state, mutable, href, pendingPush);\n                }\n                if (// will always send back a static response that's rendered from\n                // the root. If for some reason it doesn't, we fall back to the\n                // non-PPR implementation.\n                // TODO: We should get rid of the else branch and do all navigations\n                // via updateCacheNodeOnNavigation. The current structure is just\n                // an incremental step.\n                flightDataPath.length === 3) {\n                    const prefetchedTree = flightDataPath[0];\n                    const seedData = flightDataPath[1];\n                    const head = flightDataPath[2];\n                    const task = (0, _pprnavigations.updateCacheNodeOnNavigation)(currentCache, currentTree, prefetchedTree, seedData, head);\n                    if (task !== null && task.node !== null) {\n                        // We've created a new Cache Node tree that contains a prefetched\n                        // version of the next page. This can be rendered instantly.\n                        // Use the tree computed by updateCacheNodeOnNavigation instead\n                        // of the one computed by applyRouterStatePatchToTree.\n                        // TODO: We should remove applyRouterStatePatchToTree\n                        // from the PPR path entirely.\n                        const patchedRouterState = task.route;\n                        newTree = patchedRouterState;\n                        const newCache = task.node;\n                        // The prefetched tree has dynamic holes in it. We initiate a\n                        // dynamic request to fill them in.\n                        //\n                        // Do not block on the result. We'll immediately render the Cache\n                        // Node tree and suspend on the dynamic parts. When the request\n                        // comes in, we'll fill in missing data and ping React to\n                        // re-render. Unlike the lazy fetching model in the non-PPR\n                        // implementation, this is modeled as a single React update +\n                        // streaming, rather than multiple top-level updates. (However,\n                        // even in the new model, we'll still need to sometimes update the\n                        // root multiple times per navigation, like if the server sends us\n                        // a different response than we expected. For now, we revert back\n                        // to the lazy fetching mechanism in that case.)\n                        (0, _pprnavigations.listenForDynamicRequest)(task, (0, _fetchserverresponse.fetchServerResponse)(url, currentTree, state.nextUrl, state.buildId));\n                        mutable.cache = newCache;\n                    } else {\n                        // Nothing changed, so reuse the old cache.\n                        // TODO: What if the head changed but not any of the segment data?\n                        // Is that possible? If so, we should clone the whole tree and\n                        // update the head.\n                        newTree = prefetchedTree;\n                    }\n                } else {\n                    // The static response does not include any dynamic holes, so\n                    // there's no need to do a second request.\n                    // TODO: As an incremental step this just reverts back to the\n                    // non-PPR implementation. We can simplify this branch further,\n                    // given that PPR prefetches are always static and return the whole\n                    // tree. Or in the meantime we could factor it out into a\n                    // separate function.\n                    const cache = (0, _approuter.createEmptyCacheNode)();\n                    let applied = false;\n                    if (prefetchValues.status === _routerreducertypes.PrefetchCacheEntryStatus.stale && !isFirstRead) {\n                        // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n                        // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n                        // while copying over the `loading` for the segment that contains the page data.\n                        // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n                        applied = triggerLazyFetchForLeafSegments(cache, currentCache, flightSegmentPath, treePatch);\n                        // since we re-used the stale cache's loading state & refreshed the data,\n                        // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n                        prefetchValues.lastUsedTime = Date.now();\n                    } else {\n                        applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, flightDataPath, prefetchValues);\n                    }\n                    const hardNavigate = (0, _shouldhardnavigate.shouldHardNavigate)(flightSegmentPathWithLeadingEmpty, currentTree);\n                    if (hardNavigate) {\n                        // Copy rsc for the root node of the cache.\n                        cache.rsc = currentCache.rsc;\n                        cache.prefetchRsc = currentCache.prefetchRsc;\n                        (0, _invalidatecachebelowflightsegmentpath.invalidateCacheBelowFlightSegmentPath)(cache, currentCache, flightSegmentPath);\n                        // Ensure the existing cache value is used when the cache was not invalidated.\n                        mutable.cache = cache;\n                    } else if (applied) {\n                        mutable.cache = cache;\n                        // If we applied the cache, we update the \"current cache\" value so any other\n                        // segments in the FlightDataPath will be able to reference the updated cache.\n                        currentCache = cache;\n                    }\n                }\n                currentTree = newTree;\n                for (const subSegment of generateSegmentsFromPatch(treePatch)){\n                    const scrollableSegmentPath = [\n                        ...flightSegmentPath,\n                        ...subSegment\n                    ];\n                    // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n                    if (scrollableSegmentPath[scrollableSegmentPath.length - 1] !== _segment.DEFAULT_SEGMENT_KEY) {\n                        scrollableSegments.push(scrollableSegmentPath);\n                    }\n                }\n            }\n        }\n        mutable.patchedTree = currentTree;\n        mutable.canonicalUrl = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : href;\n        mutable.pendingPush = pendingPush;\n        mutable.scrollableSegments = scrollableSegments;\n        mutable.hashFragment = hash;\n        mutable.shouldScroll = shouldScroll;\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=navigate-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    prefetchQueue: function() {\n        return prefetchQueue;\n    },\n    prefetchReducer: function() {\n        return prefetchReducer;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _promisequeue = __webpack_require__(/*! ../../promise-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ../prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst prefetchQueue = new _promisequeue.PromiseQueue(5);\nfunction prefetchReducer(state, action) {\n    // let's prune the prefetch cache before we do anything else\n    (0, _prefetchcacheutils.prunePrefetchCache)(state.prefetchCache);\n    const { url } = action;\n    url.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    (0, _prefetchcacheutils.getOrCreatePrefetchCacheEntry)({\n        url,\n        nextUrl: state.nextUrl,\n        prefetchCache: state.prefetchCache,\n        kind: action.kind,\n        tree: state.tree,\n        buildId: state.buildId\n    });\n    return state;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvcHJlZmV0Y2gtcmVkdWNlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFZYUEsZUFBYTtlQUFiQTs7SUFFR0MsaUJBQWU7ZUFBZkE7Ozs4Q0FUcUI7MENBQ1I7Z0RBSXRCO0FBRUEsTUFBTUQsZ0JBQWdCLElBQUlFLGNBQUFBLFlBQVksQ0FBQztBQUV2QyxTQUFTRCxnQkFDZEUsS0FBMkIsRUFDM0JDLE1BQXNCO0lBRXRCLDREQUE0RDtJQUM1REMsQ0FBQUEsR0FBQUEsb0JBQUFBLGtCQUFrQixFQUFDRixNQUFNRyxhQUFhO0lBRXRDLE1BQU0sRUFBRUMsR0FBRyxFQUFFLEdBQUdIO0lBQ2hCRyxJQUFJQyxZQUFZLENBQUNDLE1BQU0sQ0FBQ0Msa0JBQUFBLG9CQUFvQjtJQUU1Q0MsQ0FBQUEsR0FBQUEsb0JBQUFBLDZCQUE2QixFQUFDO1FBQzVCSjtRQUNBSyxTQUFTVCxNQUFNUyxPQUFPO1FBQ3RCTixlQUFlSCxNQUFNRyxhQUFhO1FBQ2xDTyxNQUFNVCxPQUFPUyxJQUFJO1FBQ2pCQyxNQUFNWCxNQUFNVyxJQUFJO1FBQ2hCQyxTQUFTWixNQUFNWSxPQUFPO0lBQ3hCO0lBRUEsT0FBT1o7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL3ByZWZldGNoLXJlZHVjZXIudHM/NTZlOCJdLCJuYW1lcyI6WyJwcmVmZXRjaFF1ZXVlIiwicHJlZmV0Y2hSZWR1Y2VyIiwiUHJvbWlzZVF1ZXVlIiwic3RhdGUiLCJhY3Rpb24iLCJwcnVuZVByZWZldGNoQ2FjaGUiLCJwcmVmZXRjaENhY2hlIiwidXJsIiwic2VhcmNoUGFyYW1zIiwiZGVsZXRlIiwiTkVYVF9SU0NfVU5JT05fUVVFUlkiLCJnZXRPckNyZWF0ZVByZWZldGNoQ2FjaGVFbnRyeSIsIm5leHRVcmwiLCJraW5kIiwidHJlZSIsImJ1aWxkSWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\n"));

/***/ })

}]);