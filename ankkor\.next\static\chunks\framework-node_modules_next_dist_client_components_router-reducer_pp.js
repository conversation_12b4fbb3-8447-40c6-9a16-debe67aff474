"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_router-reducer_pp"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js ***!
  \************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    abortTask: function() {\n        return abortTask;\n    },\n    listenForDynamicRequest: function() {\n        return listenForDynamicRequest;\n    },\n    updateCacheNodeOnNavigation: function() {\n        return updateCacheNodeOnNavigation;\n    },\n    updateCacheNodeOnPopstateRestoration: function() {\n        return updateCacheNodeOnPopstateRestoration;\n    }\n});\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _matchsegments = __webpack_require__(/*! ../match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nfunction updateCacheNodeOnNavigation(oldCacheNode, oldRouterState, newRouterState, prefetchData, prefetchHead) {\n    // Diff the old and new trees to reuse the shared layouts.\n    const oldRouterStateChildren = oldRouterState[1];\n    const newRouterStateChildren = newRouterState[1];\n    const prefetchDataChildren = prefetchData[1];\n    const oldParallelRoutes = oldCacheNode.parallelRoutes;\n    // Clone the current set of segment children, even if they aren't active in\n    // the new tree.\n    // TODO: We currently retain all the inactive segments indefinitely, until\n    // there's an explicit refresh, or a parent layout is lazily refreshed. We\n    // rely on this for popstate navigations, which update the Router State Tree\n    // but do not eagerly perform a data fetch, because they expect the segment\n    // data to already be in the Cache Node tree. For highly static sites that\n    // are mostly read-only, this may happen only rarely, causing memory to\n    // leak. We should figure out a better model for the lifetime of inactive\n    // segments, so we can maintain instant back/forward navigations without\n    // leaking memory indefinitely.\n    const prefetchParallelRoutes = new Map(oldParallelRoutes);\n    // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n    // the Route Tree that was returned by the server — for example, in the case\n    // of default parallel routes, we preserve the currently active segment. To\n    // avoid mutating the original tree, we clone the router state children along\n    // the return path.\n    let patchedRouterStateChildren = {};\n    let taskChildren = null;\n    for(let parallelRouteKey in newRouterStateChildren){\n        const newRouterStateChild = newRouterStateChildren[parallelRouteKey];\n        const oldRouterStateChild = oldRouterStateChildren[parallelRouteKey];\n        const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey);\n        const prefetchDataChild = prefetchDataChildren[parallelRouteKey];\n        const newSegmentChild = newRouterStateChild[0];\n        const newSegmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(newSegmentChild);\n        const oldSegmentChild = oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined;\n        const oldCacheNodeChild = oldSegmentMapChild !== undefined ? oldSegmentMapChild.get(newSegmentKeyChild) : undefined;\n        let taskChild;\n        if (newSegmentChild === _segment.PAGE_SEGMENT_KEY) {\n            // This is a leaf segment — a page, not a shared layout. We always apply\n            // its data.\n            taskChild = spawnPendingTask(newRouterStateChild, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead);\n        } else if (newSegmentChild === _segment.DEFAULT_SEGMENT_KEY) {\n            // This is another kind of leaf segment — a default route.\n            //\n            // Default routes have special behavior. When there's no matching segment\n            // for a parallel route, Next.js preserves the currently active segment\n            // during a client navigation — but not for initial render. The server\n            // leaves it to the client to account for this. So we need to handle\n            // it here.\n            if (oldRouterStateChild !== undefined) {\n                // Reuse the existing Router State for this segment. We spawn a \"task\"\n                // just to keep track of the updated router state; unlike most, it's\n                // already fulfilled and won't be affected by the dynamic response.\n                taskChild = spawnReusedTask(oldRouterStateChild);\n            } else {\n                // There's no currently active segment. Switch to the \"create\" path.\n                taskChild = spawnPendingTask(newRouterStateChild, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead);\n            }\n        } else if (oldSegmentChild !== undefined && (0, _matchsegments.matchSegment)(newSegmentChild, oldSegmentChild)) {\n            if (oldCacheNodeChild !== undefined && oldRouterStateChild !== undefined) {\n                // This segment exists in both the old and new trees.\n                if (prefetchDataChild !== undefined && prefetchDataChild !== null) {\n                    // Recursively update the children.\n                    taskChild = updateCacheNodeOnNavigation(oldCacheNodeChild, oldRouterStateChild, newRouterStateChild, prefetchDataChild, prefetchHead);\n                } else {\n                    // The server didn't send any prefetch data for this segment. This\n                    // shouldn't happen because the Route Tree and the Seed Data tree\n                    // should always be the same shape, but until we unify those types\n                    // it's still possible. For now we're going to deopt and trigger a\n                    // lazy fetch during render.\n                    taskChild = spawnTaskForMissingData(newRouterStateChild);\n                }\n            } else {\n                // Either there's no existing Cache Node for this segment, or this\n                // segment doesn't exist in the old Router State tree. Switch to the\n                // \"create\" path.\n                taskChild = spawnPendingTask(newRouterStateChild, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead);\n            }\n        } else {\n            // This is a new tree. Switch to the \"create\" path.\n            taskChild = spawnPendingTask(newRouterStateChild, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead);\n        }\n        if (taskChild !== null) {\n            // Something changed in the child tree. Keep track of the child task.\n            if (taskChildren === null) {\n                taskChildren = new Map();\n            }\n            taskChildren.set(parallelRouteKey, taskChild);\n            const newCacheNodeChild = taskChild.node;\n            if (newCacheNodeChild !== null) {\n                const newSegmentMapChild = new Map(oldSegmentMapChild);\n                newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild);\n                prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n            }\n            // The child tree's route state may be different from the prefetched\n            // route sent by the server. We need to clone it as we traverse back up\n            // the tree.\n            patchedRouterStateChildren[parallelRouteKey] = taskChild.route;\n        } else {\n            // The child didn't change. We can use the prefetched router state.\n            patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild;\n        }\n    }\n    if (taskChildren === null) {\n        // No new tasks were spawned.\n        return null;\n    }\n    const newCacheNode = {\n        lazyData: null,\n        rsc: oldCacheNode.rsc,\n        // We intentionally aren't updating the prefetchRsc field, since this node\n        // is already part of the current tree, because it would be weird for\n        // prefetch data to be newer than the final data. It probably won't ever be\n        // observable anyway, but it could happen if the segment is unmounted then\n        // mounted again, because LayoutRouter will momentarily switch to rendering\n        // prefetchRsc, via useDeferredValue.\n        prefetchRsc: oldCacheNode.prefetchRsc,\n        head: oldCacheNode.head,\n        prefetchHead: oldCacheNode.prefetchHead,\n        loading: oldCacheNode.loading,\n        // Everything is cloned except for the children, which we computed above.\n        parallelRoutes: prefetchParallelRoutes,\n        lazyDataResolved: false\n    };\n    return {\n        // Return a cloned copy of the router state with updated children.\n        route: patchRouterStateWithNewChildren(newRouterState, patchedRouterStateChildren),\n        node: newCacheNode,\n        children: taskChildren\n    };\n}\nfunction patchRouterStateWithNewChildren(baseRouterState, newChildren) {\n    const clone = [\n        baseRouterState[0],\n        newChildren\n    ];\n    // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n    // confirm whether we need to copy all of these fields. Not sure the server\n    // ever sends, e.g. the refetch marker.\n    if (2 in baseRouterState) {\n        clone[2] = baseRouterState[2];\n    }\n    if (3 in baseRouterState) {\n        clone[3] = baseRouterState[3];\n    }\n    if (4 in baseRouterState) {\n        clone[4] = baseRouterState[4];\n    }\n    return clone;\n}\nfunction spawnPendingTask(routerState, prefetchData, prefetchHead) {\n    // Create a task that will later be fulfilled by data from the server.\n    const pendingCacheNode = createPendingCacheNode(routerState, prefetchData, prefetchHead);\n    return {\n        route: routerState,\n        node: pendingCacheNode,\n        children: null\n    };\n}\nfunction spawnReusedTask(reusedRouterState) {\n    // Create a task that reuses an existing segment, e.g. when reusing\n    // the current active segment in place of a default route.\n    return {\n        route: reusedRouterState,\n        node: null,\n        children: null\n    };\n}\nfunction spawnTaskForMissingData(routerState) {\n    // Create a task for a new subtree that wasn't prefetched by the server.\n    // This shouldn't really ever happen but it's here just in case the Seed Data\n    // Tree and the Router State Tree disagree unexpectedly.\n    const pendingCacheNode = createPendingCacheNode(routerState, null, null);\n    return {\n        route: routerState,\n        node: pendingCacheNode,\n        children: null\n    };\n}\nfunction listenForDynamicRequest(task, responsePromise) {\n    responsePromise.then((response)=>{\n        const flightData = response[0];\n        for (const flightDataPath of flightData){\n            const segmentPath = flightDataPath.slice(0, -3);\n            const serverRouterState = flightDataPath[flightDataPath.length - 3];\n            const dynamicData = flightDataPath[flightDataPath.length - 2];\n            const dynamicHead = flightDataPath[flightDataPath.length - 1];\n            if (typeof segmentPath === \"string\") {\n                continue;\n            }\n            writeDynamicDataIntoPendingTask(task, segmentPath, serverRouterState, dynamicData, dynamicHead);\n        }\n        // Now that we've exhausted all the data we received from the server, if\n        // there are any remaining pending tasks in the tree, abort them now.\n        // If there's any missing data, it will trigger a lazy fetch.\n        abortTask(task, null);\n    }, (error)=>{\n        // This will trigger an error during render\n        abortTask(task, error);\n    });\n}\nfunction writeDynamicDataIntoPendingTask(rootTask, segmentPath, serverRouterState, dynamicData, dynamicHead) {\n    // The data sent by the server represents only a subtree of the app. We need\n    // to find the part of the task tree that matches the server response, and\n    // fulfill it using the dynamic data.\n    //\n    // segmentPath represents the parent path of subtree. It's a repeating pattern\n    // of parallel route key and segment:\n    //\n    //   [string, Segment, string, Segment, string, Segment, ...]\n    //\n    // Iterate through the path and finish any tasks that match this payload.\n    let task = rootTask;\n    for(let i = 0; i < segmentPath.length; i += 2){\n        const parallelRouteKey = segmentPath[i];\n        const segment = segmentPath[i + 1];\n        const taskChildren = task.children;\n        if (taskChildren !== null) {\n            const taskChild = taskChildren.get(parallelRouteKey);\n            if (taskChild !== undefined) {\n                const taskSegment = taskChild.route[0];\n                if ((0, _matchsegments.matchSegment)(segment, taskSegment)) {\n                    // Found a match for this task. Keep traversing down the task tree.\n                    task = taskChild;\n                    continue;\n                }\n            }\n        }\n        // We didn't find a child task that matches the server data. Exit. We won't\n        // abort the task, though, because a different FlightDataPath may be able to\n        // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n        // once we've run out of data.\n        return;\n    }\n    finishTaskUsingDynamicDataPayload(task, serverRouterState, dynamicData, dynamicHead);\n}\nfunction finishTaskUsingDynamicDataPayload(task, serverRouterState, dynamicData, dynamicHead) {\n    // dynamicData may represent a larger subtree than the task. Before we can\n    // finish the task, we need to line them up.\n    const taskChildren = task.children;\n    const taskNode = task.node;\n    if (taskChildren === null) {\n        // We've reached the leaf node of the pending task. The server data tree\n        // lines up the pending Cache Node tree. We can now switch to the\n        // normal algorithm.\n        if (taskNode !== null) {\n            finishPendingCacheNode(taskNode, task.route, serverRouterState, dynamicData, dynamicHead);\n            // Null this out to indicate that the task is complete.\n            task.node = null;\n        }\n        return;\n    }\n    // The server returned more data than we need to finish the task. Skip over\n    // the extra segments until we reach the leaf task node.\n    const serverChildren = serverRouterState[1];\n    const dynamicDataChildren = dynamicData[1];\n    for(const parallelRouteKey in serverRouterState){\n        const serverRouterStateChild = serverChildren[parallelRouteKey];\n        const dynamicDataChild = dynamicDataChildren[parallelRouteKey];\n        const taskChild = taskChildren.get(parallelRouteKey);\n        if (taskChild !== undefined) {\n            const taskSegment = taskChild.route[0];\n            if ((0, _matchsegments.matchSegment)(serverRouterStateChild[0], taskSegment) && dynamicDataChild !== null && dynamicDataChild !== undefined) {\n                // Found a match for this task. Keep traversing down the task tree.\n                return finishTaskUsingDynamicDataPayload(taskChild, serverRouterStateChild, dynamicDataChild, dynamicHead);\n            }\n        }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    }\n}\nfunction createPendingCacheNode(routerState, prefetchData, prefetchHead) {\n    const routerStateChildren = routerState[1];\n    const prefetchDataChildren = prefetchData !== null ? prefetchData[1] : null;\n    const parallelRoutes = new Map();\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const prefetchDataChild = prefetchDataChildren !== null ? prefetchDataChildren[parallelRouteKey] : null;\n        const segmentChild = routerStateChild[0];\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const newCacheNodeChild = createPendingCacheNode(routerStateChild, prefetchDataChild === undefined ? null : prefetchDataChild, prefetchHead);\n        const newSegmentMapChild = new Map();\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild);\n        parallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n    }\n    // The head is assigned to every leaf segment delivered by the server. Based\n    // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n    const isLeafSegment = parallelRoutes.size === 0;\n    const maybePrefetchRsc = prefetchData !== null ? prefetchData[2] : null;\n    const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null;\n    return {\n        lazyData: null,\n        parallelRoutes: parallelRoutes,\n        prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n        prefetchHead: isLeafSegment ? prefetchHead : null,\n        loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n        // Create a deferred promise. This will be fulfilled once the dynamic\n        // response is received from the server.\n        rsc: createDeferredRsc(),\n        head: isLeafSegment ? createDeferredRsc() : null,\n        lazyDataResolved: false\n    };\n}\nfunction finishPendingCacheNode(cacheNode, taskState, serverState, dynamicData, dynamicHead) {\n    // Writes a dynamic response into an existing Cache Node tree. This does _not_\n    // create a new tree, it updates the existing tree in-place. So it must follow\n    // the Suspense rules of cache safety — it can resolve pending promises, but\n    // it cannot overwrite existing data. It can add segments to the tree (because\n    // a missing segment will cause the layout router to suspend).\n    // but it cannot delete them.\n    //\n    // We must resolve every promise in the tree, or else it will suspend\n    // indefinitely. If we did not receive data for a segment, we will resolve its\n    // data promise to `null` to trigger a lazy fetch during render.\n    const taskStateChildren = taskState[1];\n    const serverStateChildren = serverState[1];\n    const dataChildren = dynamicData[1];\n    // The router state that we traverse the tree with (taskState) is the same one\n    // that we used to construct the pending Cache Node tree. That way we're sure\n    // to resolve all the pending promises.\n    const parallelRoutes = cacheNode.parallelRoutes;\n    for(let parallelRouteKey in taskStateChildren){\n        const taskStateChild = taskStateChildren[parallelRouteKey];\n        const serverStateChild = serverStateChildren[parallelRouteKey];\n        const dataChild = dataChildren[parallelRouteKey];\n        const segmentMapChild = parallelRoutes.get(parallelRouteKey);\n        const taskSegmentChild = taskStateChild[0];\n        const taskSegmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(taskSegmentChild);\n        const cacheNodeChild = segmentMapChild !== undefined ? segmentMapChild.get(taskSegmentKeyChild) : undefined;\n        if (cacheNodeChild !== undefined) {\n            if (serverStateChild !== undefined && (0, _matchsegments.matchSegment)(taskSegmentChild, serverStateChild[0])) {\n                if (dataChild !== undefined && dataChild !== null) {\n                    // This is the happy path. Recursively update all the children.\n                    finishPendingCacheNode(cacheNodeChild, taskStateChild, serverStateChild, dataChild, dynamicHead);\n                } else {\n                    // The server never returned data for this segment. Trigger a lazy\n                    // fetch during render. This shouldn't happen because the Route Tree\n                    // and the Seed Data tree sent by the server should always be the same\n                    // shape when part of the same server response.\n                    abortPendingCacheNode(taskStateChild, cacheNodeChild, null);\n                }\n            } else {\n                // The server never returned data for this segment. Trigger a lazy\n                // fetch during render.\n                abortPendingCacheNode(taskStateChild, cacheNodeChild, null);\n            }\n        } else {\n        // The server response matches what was expected to receive, but there's\n        // no matching Cache Node in the task tree. This is a bug in the\n        // implementation because we should have created a node for every\n        // segment in the tree that's associated with this task.\n        }\n    }\n    // Use the dynamic data from the server to fulfill the deferred RSC promise\n    // on the Cache Node.\n    const rsc = cacheNode.rsc;\n    const dynamicSegmentData = dynamicData[2];\n    if (rsc === null) {\n        // This is a lazy cache node. We can overwrite it. This is only safe\n        // because we know that the LayoutRouter suspends if `rsc` is `null`.\n        cacheNode.rsc = dynamicSegmentData;\n    } else if (isDeferredRsc(rsc)) {\n        // This is a deferred RSC promise. We can fulfill it with the data we just\n        // received from the server. If it was already resolved by a different\n        // navigation, then this does nothing because we can't overwrite data.\n        rsc.resolve(dynamicSegmentData);\n    } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n    }\n    // Check if this is a leaf segment. If so, it will have a `head` property with\n    // a pending promise that needs to be resolved with the dynamic head from\n    // the server.\n    const head = cacheNode.head;\n    if (isDeferredRsc(head)) {\n        head.resolve(dynamicHead);\n    }\n}\nfunction abortTask(task, error) {\n    const cacheNode = task.node;\n    if (cacheNode === null) {\n        // This indicates the task is already complete.\n        return;\n    }\n    const taskChildren = task.children;\n    if (taskChildren === null) {\n        // Reached the leaf task node. This is the root of a pending cache\n        // node tree.\n        abortPendingCacheNode(task.route, cacheNode, error);\n    } else {\n        // This is an intermediate task node. Keep traversing until we reach a\n        // task node with no children. That will be the root of the cache node tree\n        // that needs to be resolved.\n        for (const taskChild of taskChildren.values()){\n            abortTask(taskChild, error);\n        }\n    }\n    // Null this out to indicate that the task is complete.\n    task.node = null;\n}\nfunction abortPendingCacheNode(routerState, cacheNode, error) {\n    // For every pending segment in the tree, resolve its `rsc` promise to `null`\n    // to trigger a lazy fetch during render.\n    //\n    // Or, if an error object is provided, it will error instead.\n    const routerStateChildren = routerState[1];\n    const parallelRoutes = cacheNode.parallelRoutes;\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const segmentMapChild = parallelRoutes.get(parallelRouteKey);\n        if (segmentMapChild === undefined) {\n            continue;\n        }\n        const segmentChild = routerStateChild[0];\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const cacheNodeChild = segmentMapChild.get(segmentKeyChild);\n        if (cacheNodeChild !== undefined) {\n            abortPendingCacheNode(routerStateChild, cacheNodeChild, error);\n        } else {\n        // This shouldn't happen because we're traversing the same tree that was\n        // used to construct the cache nodes in the first place.\n        }\n    }\n    const rsc = cacheNode.rsc;\n    if (isDeferredRsc(rsc)) {\n        if (error === null) {\n            // This will trigger a lazy fetch during render.\n            rsc.resolve(null);\n        } else {\n            // This will trigger an error during rendering.\n            rsc.reject(error);\n        }\n    }\n    // Check if this is a leaf segment. If so, it will have a `head` property with\n    // a pending promise that needs to be resolved. If an error was provided, we\n    // will not resolve it with an error, since this is rendered at the root of\n    // the app. We want the segment to error, not the entire app.\n    const head = cacheNode.head;\n    if (isDeferredRsc(head)) {\n        head.resolve(null);\n    }\n}\nfunction updateCacheNodeOnPopstateRestoration(oldCacheNode, routerState) {\n    // A popstate navigation reads data from the local cache. It does not issue\n    // new network requests (unless the cache entries have been evicted). So, we\n    // update the cache to drop the prefetch  data for any segment whose dynamic\n    // data was already received. This prevents an unnecessary flash back to PPR\n    // state during a back/forward navigation.\n    //\n    // This function clones the entire cache node tree and sets the `prefetchRsc`\n    // field to `null` to prevent it from being rendered. We can't mutate the node\n    // in place because this is a concurrent data structure.\n    const routerStateChildren = routerState[1];\n    const oldParallelRoutes = oldCacheNode.parallelRoutes;\n    const newParallelRoutes = new Map(oldParallelRoutes);\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const segmentChild = routerStateChild[0];\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey);\n        if (oldSegmentMapChild !== undefined) {\n            const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild);\n            if (oldCacheNodeChild !== undefined) {\n                const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(oldCacheNodeChild, routerStateChild);\n                const newSegmentMapChild = new Map(oldSegmentMapChild);\n                newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild);\n                newParallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n            }\n        }\n    }\n    // Only show prefetched data if the dynamic data is still pending.\n    //\n    // Tehnically, what we're actually checking is whether the dynamic network\n    // response was received. But since it's a streaming response, this does not\n    // mean that all the dynamic data has fully streamed in. It just means that\n    // _some_ of the dynamic data was received. But as a heuristic, we assume that\n    // the rest dynamic data will stream in quickly, so it's still better to skip\n    // the prefetch state.\n    const rsc = oldCacheNode.rsc;\n    const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === \"pending\";\n    return {\n        lazyData: null,\n        rsc,\n        head: oldCacheNode.head,\n        prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : null,\n        prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n        loading: shouldUsePrefetch ? oldCacheNode.loading : null,\n        // These are the cloned children we computed above\n        parallelRoutes: newParallelRoutes,\n        lazyDataResolved: false\n    };\n}\nconst DEFERRED = Symbol();\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value) {\n    return value && value.tag === DEFERRED;\n}\nfunction createDeferredRsc() {\n    let resolve;\n    let reject;\n    const pendingRsc = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    pendingRsc.status = \"pending\";\n    pendingRsc.resolve = (value)=>{\n        if (pendingRsc.status === \"pending\") {\n            const fulfilledRsc = pendingRsc;\n            fulfilledRsc.status = \"fulfilled\";\n            fulfilledRsc.value = value;\n            resolve(value);\n        }\n    };\n    pendingRsc.reject = (error)=>{\n        if (pendingRsc.status === \"pending\") {\n            const rejectedRsc = pendingRsc;\n            rejectedRsc.status = \"rejected\";\n            rejectedRsc.reason = error;\n            reject(error);\n        }\n    };\n    pendingRsc.tag = DEFERRED;\n    return pendingRsc;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ppr-navigations.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createPrefetchCacheEntryForInitialLoad: function() {\n        return createPrefetchCacheEntryForInitialLoad;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKey(url, nextUrl) {\n    const pathnameFromUrl = (0, _createhreffromurl.createHrefFromUrl)(url, false);\n    // nextUrl is used as a cache key delimiter since entries can vary based on the Next-URL header\n    if (nextUrl) {\n        return nextUrl + \"%\" + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, buildId, prefetchCache, kind } = param;\n    let existingCacheEntry = undefined;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    const interceptionCacheKey = createPrefetchCacheKey(url, nextUrl);\n    const interceptionData = prefetchCache.get(interceptionCacheKey);\n    if (interceptionData) {\n        existingCacheEntry = interceptionData;\n    } else {\n        // If we dont find a more specific interception route prefetch entry, we check for a regular prefetch entry\n        const prefetchCacheKey = createPrefetchCacheKey(url);\n        const prefetchData = prefetchCache.get(prefetchCacheKey);\n        if (prefetchData) {\n            existingCacheEntry = prefetchData;\n        }\n    }\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            return createLazyPrefetchEntry({\n                tree,\n                url,\n                buildId,\n                nextUrl,\n                prefetchCache,\n                // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                // rather than assuming the same intent as the previous entry, to be consistent with how we\n                // lazily create prefetch entries when intent is left unspecified.\n                kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        buildId,\n        nextUrl,\n        prefetchCache,\n        kind: kind || // in dev, there's never gonna be a prefetch entry so we want to prefetch here\n        ( true ? _routerreducertypes.PrefetchKind.AUTO : 0)\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache } = param;\n    const existingCacheKey = createPrefetchCacheKey(url);\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, nextUrl);\n    prefetchCache.set(newCacheKey, existingCacheEntry);\n    prefetchCache.delete(existingCacheKey);\n}\nfunction createPrefetchCacheEntryForInitialLoad(param) {\n    let { nextUrl, tree, prefetchCache, url, kind, data } = param;\n    const [, , , intercept] = data;\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = intercept ? createPrefetchCacheKey(url, nextUrl) : createPrefetchCacheKey(url);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, buildId, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, tree, nextUrl, buildId, kind).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            const [, , , intercepted] = prefetchResponse;\n            if (intercepted) {\n                prefixExistingPrefetchCacheEntry({\n                    url,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 30 seconds (dynamic)\nconst DYNAMIC_STALETIME_MS = Number(\"30\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime } = param;\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === \"auto\") {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === \"full\") {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ })

}]);