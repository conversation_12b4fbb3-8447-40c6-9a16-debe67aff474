"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_upstash_redis_chunk-5XANP4AV_mjs-ec81489a"],{

/***/ "(app-pages-browser)/./node_modules/@upstash/redis/chunk-5XANP4AV.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@upstash/redis/chunk-5XANP4AV.mjs ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpClient: function() { return /* binding */ HttpClient; },\n/* harmony export */   Redis: function() { return /* binding */ Redis; },\n/* harmony export */   VERSION: function() { return /* binding */ VERSION; },\n/* harmony export */   error_exports: function() { return /* binding */ error_exports; }\n/* harmony export */ });\n/* harmony import */ var crypto_js_enc_hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js/enc-hex.js */ \"(app-pages-browser)/./node_modules/crypto-js/enc-hex.js\");\n/* harmony import */ var crypto_js_sha1_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto-js/sha1.js */ \"(app-pages-browser)/./node_modules/crypto-js/sha1.js\");\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// pkg/error.ts\nvar error_exports = {};\n__export(error_exports, {\n  UpstashError: () => UpstashError,\n  UrlError: () => UrlError\n});\nvar UpstashError = class extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"UpstashError\";\n  }\n};\nvar UrlError = class extends Error {\n  constructor(url) {\n    super(\n      `Upstash Redis client was passed an invalid URL. You should pass a URL starting with https. Received: \"${url}\". `\n    );\n    this.name = \"UrlError\";\n  }\n};\n\n// pkg/util.ts\nfunction parseRecursive(obj) {\n  const parsed = Array.isArray(obj) ? obj.map((o) => {\n    try {\n      return parseRecursive(o);\n    } catch {\n      return o;\n    }\n  }) : JSON.parse(obj);\n  if (typeof parsed === \"number\" && parsed.toString() !== obj) {\n    return obj;\n  }\n  return parsed;\n}\nfunction parseResponse(result) {\n  try {\n    return parseRecursive(result);\n  } catch {\n    return result;\n  }\n}\nfunction deserializeScanResponse(result) {\n  return [result[0], ...parseResponse(result.slice(1))];\n}\nfunction mergeHeaders(...headers) {\n  const merged = {};\n  for (const header of headers) {\n    if (!header) continue;\n    for (const [key, value] of Object.entries(header)) {\n      if (value !== void 0 && value !== null) {\n        merged[key] = value;\n      }\n    }\n  }\n  return merged;\n}\n\n// pkg/http.ts\nvar HttpClient = class {\n  baseUrl;\n  headers;\n  options;\n  readYourWrites;\n  upstashSyncToken = \"\";\n  hasCredentials;\n  retry;\n  constructor(config) {\n    this.options = {\n      backend: config.options?.backend,\n      agent: config.agent,\n      responseEncoding: config.responseEncoding ?? \"base64\",\n      // default to base64\n      cache: config.cache,\n      signal: config.signal,\n      keepAlive: config.keepAlive ?? true\n    };\n    this.upstashSyncToken = \"\";\n    this.readYourWrites = config.readYourWrites ?? true;\n    this.baseUrl = (config.baseUrl || \"\").replace(/\\/$/, \"\");\n    const urlRegex = /^https?:\\/\\/[^\\s#$./?].\\S*$/;\n    if (this.baseUrl && !urlRegex.test(this.baseUrl)) {\n      throw new UrlError(this.baseUrl);\n    }\n    this.headers = {\n      \"Content-Type\": \"application/json\",\n      ...config.headers\n    };\n    this.hasCredentials = Boolean(this.baseUrl && this.headers.authorization.split(\" \")[1]);\n    if (this.options.responseEncoding === \"base64\") {\n      this.headers[\"Upstash-Encoding\"] = \"base64\";\n    }\n    this.retry = typeof config.retry === \"boolean\" && !config.retry ? {\n      attempts: 1,\n      backoff: () => 0\n    } : {\n      attempts: config.retry?.retries ?? 5,\n      backoff: config.retry?.backoff ?? ((retryCount) => Math.exp(retryCount) * 50)\n    };\n  }\n  mergeTelemetry(telemetry) {\n    this.headers = merge(this.headers, \"Upstash-Telemetry-Runtime\", telemetry.runtime);\n    this.headers = merge(this.headers, \"Upstash-Telemetry-Platform\", telemetry.platform);\n    this.headers = merge(this.headers, \"Upstash-Telemetry-Sdk\", telemetry.sdk);\n  }\n  async request(req) {\n    const requestHeaders = mergeHeaders(this.headers, req.headers ?? {});\n    const requestUrl = [this.baseUrl, ...req.path ?? []].join(\"/\");\n    const isEventStream = requestHeaders.Accept === \"text/event-stream\";\n    const requestOptions = {\n      //@ts-expect-error this should throw due to bun regression\n      cache: this.options.cache,\n      method: \"POST\",\n      headers: requestHeaders,\n      body: JSON.stringify(req.body),\n      keepalive: this.options.keepAlive,\n      agent: this.options.agent,\n      signal: req.signal ?? this.options.signal,\n      /**\n       * Fastly specific\n       */\n      backend: this.options.backend\n    };\n    if (!this.hasCredentials) {\n      console.warn(\n        \"[Upstash Redis] Redis client was initialized without url or token. Failed to execute command.\"\n      );\n    }\n    if (this.readYourWrites) {\n      const newHeader = this.upstashSyncToken;\n      this.headers[\"upstash-sync-token\"] = newHeader;\n    }\n    let res = null;\n    let error = null;\n    for (let i = 0; i <= this.retry.attempts; i++) {\n      try {\n        res = await fetch(requestUrl, requestOptions);\n        break;\n      } catch (error_) {\n        if (this.options.signal?.aborted) {\n          const myBlob = new Blob([\n            JSON.stringify({ result: this.options.signal.reason ?? \"Aborted\" })\n          ]);\n          const myOptions = {\n            status: 200,\n            statusText: this.options.signal.reason ?? \"Aborted\"\n          };\n          res = new Response(myBlob, myOptions);\n          break;\n        }\n        error = error_;\n        if (i < this.retry.attempts) {\n          await new Promise((r) => setTimeout(r, this.retry.backoff(i)));\n        }\n      }\n    }\n    if (!res) {\n      throw error ?? new Error(\"Exhausted all retries\");\n    }\n    if (!res.ok) {\n      const body2 = await res.json();\n      throw new UpstashError(`${body2.error}, command was: ${JSON.stringify(req.body)}`);\n    }\n    if (this.readYourWrites) {\n      const headers = res.headers;\n      this.upstashSyncToken = headers.get(\"upstash-sync-token\") ?? \"\";\n    }\n    if (isEventStream && req && req.onMessage && res.body) {\n      const reader = res.body.getReader();\n      const decoder = new TextDecoder();\n      (async () => {\n        try {\n          while (true) {\n            const { value, done } = await reader.read();\n            if (done) break;\n            const chunk = decoder.decode(value);\n            const lines = chunk.split(\"\\n\");\n            for (const line of lines) {\n              if (line.startsWith(\"data: \")) {\n                const data = line.slice(6);\n                req.onMessage?.(data);\n              }\n            }\n          }\n        } catch (error2) {\n          if (error2 instanceof Error && error2.name === \"AbortError\") {\n          } else {\n            console.error(\"Stream reading error:\", error2);\n          }\n        } finally {\n          try {\n            await reader.cancel();\n          } catch {\n          }\n        }\n      })();\n      return { result: 1 };\n    }\n    const body = await res.json();\n    if (this.readYourWrites) {\n      const headers = res.headers;\n      this.upstashSyncToken = headers.get(\"upstash-sync-token\") ?? \"\";\n    }\n    if (this.options.responseEncoding === \"base64\") {\n      if (Array.isArray(body)) {\n        return body.map(({ result: result2, error: error2 }) => ({\n          result: decode(result2),\n          error: error2\n        }));\n      }\n      const result = decode(body.result);\n      return { result, error: body.error };\n    }\n    return body;\n  }\n};\nfunction base64decode(b64) {\n  let dec = \"\";\n  try {\n    const binString = atob(b64);\n    const size = binString.length;\n    const bytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n      bytes[i] = binString.charCodeAt(i);\n    }\n    dec = new TextDecoder().decode(bytes);\n  } catch {\n    dec = b64;\n  }\n  return dec;\n}\nfunction decode(raw) {\n  let result = void 0;\n  switch (typeof raw) {\n    case \"undefined\": {\n      return raw;\n    }\n    case \"number\": {\n      result = raw;\n      break;\n    }\n    case \"object\": {\n      if (Array.isArray(raw)) {\n        result = raw.map(\n          (v) => typeof v === \"string\" ? base64decode(v) : Array.isArray(v) ? v.map((element) => decode(element)) : v\n        );\n      } else {\n        result = null;\n      }\n      break;\n    }\n    case \"string\": {\n      result = raw === \"OK\" ? \"OK\" : base64decode(raw);\n      break;\n    }\n    default: {\n      break;\n    }\n  }\n  return result;\n}\nfunction merge(obj, key, value) {\n  if (!value) {\n    return obj;\n  }\n  obj[key] = obj[key] ? [obj[key], value].join(\",\") : value;\n  return obj;\n}\n\n// pkg/commands/command.ts\nvar defaultSerializer = (c) => {\n  switch (typeof c) {\n    case \"string\":\n    case \"number\":\n    case \"boolean\": {\n      return c;\n    }\n    default: {\n      return JSON.stringify(c);\n    }\n  }\n};\nvar Command = class {\n  command;\n  serialize;\n  deserialize;\n  headers;\n  path;\n  onMessage;\n  isStreaming;\n  signal;\n  /**\n   * Create a new command instance.\n   *\n   * You can define a custom `deserialize` function. By default we try to deserialize as json.\n   */\n  constructor(command, opts) {\n    this.serialize = defaultSerializer;\n    this.deserialize = opts?.automaticDeserialization === void 0 || opts.automaticDeserialization ? opts?.deserialize ?? parseResponse : (x) => x;\n    this.command = command.map((c) => this.serialize(c));\n    this.headers = opts?.headers;\n    this.path = opts?.path;\n    this.onMessage = opts?.streamOptions?.onMessage;\n    this.isStreaming = opts?.streamOptions?.isStreaming ?? false;\n    this.signal = opts?.streamOptions?.signal;\n    if (opts?.latencyLogging) {\n      const originalExec = this.exec.bind(this);\n      this.exec = async (client) => {\n        const start = performance.now();\n        const result = await originalExec(client);\n        const end = performance.now();\n        const loggerResult = (end - start).toFixed(2);\n        console.log(\n          `Latency for \\x1B[38;2;19;185;39m${this.command[0].toString().toUpperCase()}\\x1B[0m: \\x1B[38;2;0;255;255m${loggerResult} ms\\x1B[0m`\n        );\n        return result;\n      };\n    }\n  }\n  /**\n   * Execute the command using a client.\n   */\n  async exec(client) {\n    const { result, error } = await client.request({\n      body: this.command,\n      path: this.path,\n      upstashSyncToken: client.upstashSyncToken,\n      headers: this.headers,\n      onMessage: this.onMessage,\n      isStreaming: this.isStreaming,\n      signal: this.signal\n    });\n    if (error) {\n      throw new UpstashError(error);\n    }\n    if (result === void 0) {\n      throw new TypeError(\"Request did not return a result\");\n    }\n    return this.deserialize(result);\n  }\n};\n\n// pkg/commands/hrandfield.ts\nfunction deserialize(result) {\n  if (result.length === 0) {\n    return null;\n  }\n  const obj = {};\n  for (let i = 0; i < result.length; i += 2) {\n    const key = result[i];\n    const value = result[i + 1];\n    try {\n      obj[key] = JSON.parse(value);\n    } catch {\n      obj[key] = value;\n    }\n  }\n  return obj;\n}\nvar HRandFieldCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"hrandfield\", cmd[0]];\n    if (typeof cmd[1] === \"number\") {\n      command.push(cmd[1]);\n    }\n    if (cmd[2]) {\n      command.push(\"WITHVALUES\");\n    }\n    super(command, {\n      // @ts-expect-error to silence compiler\n      deserialize: cmd[2] ? (result) => deserialize(result) : opts?.deserialize,\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/append.ts\nvar AppendCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"append\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/bitcount.ts\nvar BitCountCommand = class extends Command {\n  constructor([key, start, end], opts) {\n    const command = [\"bitcount\", key];\n    if (typeof start === \"number\") {\n      command.push(start);\n    }\n    if (typeof end === \"number\") {\n      command.push(end);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/bitfield.ts\nvar BitFieldCommand = class {\n  constructor(args, client, opts, execOperation = (command) => command.exec(this.client)) {\n    this.client = client;\n    this.opts = opts;\n    this.execOperation = execOperation;\n    this.command = [\"bitfield\", ...args];\n  }\n  command;\n  chain(...args) {\n    this.command.push(...args);\n    return this;\n  }\n  get(...args) {\n    return this.chain(\"get\", ...args);\n  }\n  set(...args) {\n    return this.chain(\"set\", ...args);\n  }\n  incrby(...args) {\n    return this.chain(\"incrby\", ...args);\n  }\n  overflow(overflow) {\n    return this.chain(\"overflow\", overflow);\n  }\n  exec() {\n    const command = new Command(this.command, this.opts);\n    return this.execOperation(command);\n  }\n};\n\n// pkg/commands/bitop.ts\nvar BitOpCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"bitop\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/bitpos.ts\nvar BitPosCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"bitpos\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/copy.ts\nvar CopyCommand = class extends Command {\n  constructor([key, destinationKey, opts], commandOptions) {\n    super([\"COPY\", key, destinationKey, ...opts?.replace ? [\"REPLACE\"] : []], {\n      ...commandOptions,\n      deserialize(result) {\n        if (result > 0) {\n          return \"COPIED\";\n        }\n        return \"NOT_COPIED\";\n      }\n    });\n  }\n};\n\n// pkg/commands/dbsize.ts\nvar DBSizeCommand = class extends Command {\n  constructor(opts) {\n    super([\"dbsize\"], opts);\n  }\n};\n\n// pkg/commands/decr.ts\nvar DecrCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"decr\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/decrby.ts\nvar DecrByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"decrby\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/del.ts\nvar DelCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"del\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/echo.ts\nvar EchoCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"echo\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/evalRo.ts\nvar EvalROCommand = class extends Command {\n  constructor([script, keys, args], opts) {\n    super([\"eval_ro\", script, keys.length, ...keys, ...args ?? []], opts);\n  }\n};\n\n// pkg/commands/eval.ts\nvar EvalCommand = class extends Command {\n  constructor([script, keys, args], opts) {\n    super([\"eval\", script, keys.length, ...keys, ...args ?? []], opts);\n  }\n};\n\n// pkg/commands/evalshaRo.ts\nvar EvalshaROCommand = class extends Command {\n  constructor([sha, keys, args], opts) {\n    super([\"evalsha_ro\", sha, keys.length, ...keys, ...args ?? []], opts);\n  }\n};\n\n// pkg/commands/evalsha.ts\nvar EvalshaCommand = class extends Command {\n  constructor([sha, keys, args], opts) {\n    super([\"evalsha\", sha, keys.length, ...keys, ...args ?? []], opts);\n  }\n};\n\n// pkg/commands/exec.ts\nvar ExecCommand = class extends Command {\n  constructor(cmd, opts) {\n    const normalizedCmd = cmd.map((arg) => typeof arg === \"string\" ? arg : String(arg));\n    super(normalizedCmd, opts);\n  }\n};\n\n// pkg/commands/exists.ts\nvar ExistsCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"exists\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/expire.ts\nvar ExpireCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"expire\", ...cmd.filter(Boolean)], opts);\n  }\n};\n\n// pkg/commands/expireat.ts\nvar ExpireAtCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"expireat\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/flushall.ts\nvar FlushAllCommand = class extends Command {\n  constructor(args, opts) {\n    const command = [\"flushall\"];\n    if (args && args.length > 0 && args[0].async) {\n      command.push(\"async\");\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/flushdb.ts\nvar FlushDBCommand = class extends Command {\n  constructor([opts], cmdOpts) {\n    const command = [\"flushdb\"];\n    if (opts?.async) {\n      command.push(\"async\");\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/geo_add.ts\nvar GeoAddCommand = class extends Command {\n  constructor([key, arg1, ...arg2], opts) {\n    const command = [\"geoadd\", key];\n    if (\"nx\" in arg1 && arg1.nx) {\n      command.push(\"nx\");\n    } else if (\"xx\" in arg1 && arg1.xx) {\n      command.push(\"xx\");\n    }\n    if (\"ch\" in arg1 && arg1.ch) {\n      command.push(\"ch\");\n    }\n    if (\"latitude\" in arg1 && arg1.latitude) {\n      command.push(arg1.longitude, arg1.latitude, arg1.member);\n    }\n    command.push(\n      ...arg2.flatMap(({ latitude, longitude, member }) => [longitude, latitude, member])\n    );\n    super(command, opts);\n  }\n};\n\n// pkg/commands/geo_dist.ts\nvar GeoDistCommand = class extends Command {\n  constructor([key, member1, member2, unit = \"M\"], opts) {\n    super([\"GEODIST\", key, member1, member2, unit], opts);\n  }\n};\n\n// pkg/commands/geo_hash.ts\nvar GeoHashCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key] = cmd;\n    const members = Array.isArray(cmd[1]) ? cmd[1] : cmd.slice(1);\n    super([\"GEOHASH\", key, ...members], opts);\n  }\n};\n\n// pkg/commands/geo_pos.ts\nvar GeoPosCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key] = cmd;\n    const members = Array.isArray(cmd[1]) ? cmd[1] : cmd.slice(1);\n    super([\"GEOPOS\", key, ...members], {\n      deserialize: (result) => transform(result),\n      ...opts\n    });\n  }\n};\nfunction transform(result) {\n  const final = [];\n  for (const pos of result) {\n    if (!pos?.[0] || !pos?.[1]) {\n      continue;\n    }\n    final.push({ lng: Number.parseFloat(pos[0]), lat: Number.parseFloat(pos[1]) });\n  }\n  return final;\n}\n\n// pkg/commands/geo_search.ts\nvar GeoSearchCommand = class extends Command {\n  constructor([key, centerPoint, shape, order, opts], commandOptions) {\n    const command = [\"GEOSEARCH\", key];\n    if (centerPoint.type === \"FROMMEMBER\" || centerPoint.type === \"frommember\") {\n      command.push(centerPoint.type, centerPoint.member);\n    }\n    if (centerPoint.type === \"FROMLONLAT\" || centerPoint.type === \"fromlonlat\") {\n      command.push(centerPoint.type, centerPoint.coordinate.lon, centerPoint.coordinate.lat);\n    }\n    if (shape.type === \"BYRADIUS\" || shape.type === \"byradius\") {\n      command.push(shape.type, shape.radius, shape.radiusType);\n    }\n    if (shape.type === \"BYBOX\" || shape.type === \"bybox\") {\n      command.push(shape.type, shape.rect.width, shape.rect.height, shape.rectType);\n    }\n    command.push(order);\n    if (opts?.count) {\n      command.push(\"COUNT\", opts.count.limit, ...opts.count.any ? [\"ANY\"] : []);\n    }\n    const transform2 = (result) => {\n      if (!opts?.withCoord && !opts?.withDist && !opts?.withHash) {\n        return result.map((member) => {\n          try {\n            return { member: JSON.parse(member) };\n          } catch {\n            return { member };\n          }\n        });\n      }\n      return result.map((members) => {\n        let counter = 1;\n        const obj = {};\n        try {\n          obj.member = JSON.parse(members[0]);\n        } catch {\n          obj.member = members[0];\n        }\n        if (opts.withDist) {\n          obj.dist = Number.parseFloat(members[counter++]);\n        }\n        if (opts.withHash) {\n          obj.hash = members[counter++].toString();\n        }\n        if (opts.withCoord) {\n          obj.coord = {\n            long: Number.parseFloat(members[counter][0]),\n            lat: Number.parseFloat(members[counter][1])\n          };\n        }\n        return obj;\n      });\n    };\n    super(\n      [\n        ...command,\n        ...opts?.withCoord ? [\"WITHCOORD\"] : [],\n        ...opts?.withDist ? [\"WITHDIST\"] : [],\n        ...opts?.withHash ? [\"WITHHASH\"] : []\n      ],\n      {\n        deserialize: transform2,\n        ...commandOptions\n      }\n    );\n  }\n};\n\n// pkg/commands/geo_search_store.ts\nvar GeoSearchStoreCommand = class extends Command {\n  constructor([destination, key, centerPoint, shape, order, opts], commandOptions) {\n    const command = [\"GEOSEARCHSTORE\", destination, key];\n    if (centerPoint.type === \"FROMMEMBER\" || centerPoint.type === \"frommember\") {\n      command.push(centerPoint.type, centerPoint.member);\n    }\n    if (centerPoint.type === \"FROMLONLAT\" || centerPoint.type === \"fromlonlat\") {\n      command.push(centerPoint.type, centerPoint.coordinate.lon, centerPoint.coordinate.lat);\n    }\n    if (shape.type === \"BYRADIUS\" || shape.type === \"byradius\") {\n      command.push(shape.type, shape.radius, shape.radiusType);\n    }\n    if (shape.type === \"BYBOX\" || shape.type === \"bybox\") {\n      command.push(shape.type, shape.rect.width, shape.rect.height, shape.rectType);\n    }\n    command.push(order);\n    if (opts?.count) {\n      command.push(\"COUNT\", opts.count.limit, ...opts.count.any ? [\"ANY\"] : []);\n    }\n    super([...command, ...opts?.storeDist ? [\"STOREDIST\"] : []], commandOptions);\n  }\n};\n\n// pkg/commands/get.ts\nvar GetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"get\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/getbit.ts\nvar GetBitCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"getbit\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/getdel.ts\nvar GetDelCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"getdel\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/getex.ts\nvar GetExCommand = class extends Command {\n  constructor([key, opts], cmdOpts) {\n    const command = [\"getex\", key];\n    if (opts) {\n      if (\"ex\" in opts && typeof opts.ex === \"number\") {\n        command.push(\"ex\", opts.ex);\n      } else if (\"px\" in opts && typeof opts.px === \"number\") {\n        command.push(\"px\", opts.px);\n      } else if (\"exat\" in opts && typeof opts.exat === \"number\") {\n        command.push(\"exat\", opts.exat);\n      } else if (\"pxat\" in opts && typeof opts.pxat === \"number\") {\n        command.push(\"pxat\", opts.pxat);\n      } else if (\"persist\" in opts && opts.persist) {\n        command.push(\"persist\");\n      }\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/getrange.ts\nvar GetRangeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"getrange\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/getset.ts\nvar GetSetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"getset\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hdel.ts\nvar HDelCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hdel\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hexists.ts\nvar HExistsCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hexists\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hexpire.ts\nvar HExpireCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields, seconds, option] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super(\n      [\n        \"hexpire\",\n        key,\n        seconds,\n        ...option ? [option] : [],\n        \"FIELDS\",\n        fieldArray.length,\n        ...fieldArray\n      ],\n      opts\n    );\n  }\n};\n\n// pkg/commands/hexpireat.ts\nvar HExpireAtCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields, timestamp, option] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super(\n      [\n        \"hexpireat\",\n        key,\n        timestamp,\n        ...option ? [option] : [],\n        \"FIELDS\",\n        fieldArray.length,\n        ...fieldArray\n      ],\n      opts\n    );\n  }\n};\n\n// pkg/commands/hexpiretime.ts\nvar HExpireTimeCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super([\"hexpiretime\", key, \"FIELDS\", fieldArray.length, ...fieldArray], opts);\n  }\n};\n\n// pkg/commands/hpersist.ts\nvar HPersistCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super([\"hpersist\", key, \"FIELDS\", fieldArray.length, ...fieldArray], opts);\n  }\n};\n\n// pkg/commands/hpexpire.ts\nvar HPExpireCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields, milliseconds, option] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super(\n      [\n        \"hpexpire\",\n        key,\n        milliseconds,\n        ...option ? [option] : [],\n        \"FIELDS\",\n        fieldArray.length,\n        ...fieldArray\n      ],\n      opts\n    );\n  }\n};\n\n// pkg/commands/hpexpireat.ts\nvar HPExpireAtCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields, timestamp, option] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super(\n      [\n        \"hpexpireat\",\n        key,\n        timestamp,\n        ...option ? [option] : [],\n        \"FIELDS\",\n        fieldArray.length,\n        ...fieldArray\n      ],\n      opts\n    );\n  }\n};\n\n// pkg/commands/hpexpiretime.ts\nvar HPExpireTimeCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super([\"hpexpiretime\", key, \"FIELDS\", fieldArray.length, ...fieldArray], opts);\n  }\n};\n\n// pkg/commands/hpttl.ts\nvar HPTtlCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super([\"hpttl\", key, \"FIELDS\", fieldArray.length, ...fieldArray], opts);\n  }\n};\n\n// pkg/commands/hget.ts\nvar HGetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hget\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hgetall.ts\nfunction deserialize2(result) {\n  if (result.length === 0) {\n    return null;\n  }\n  const obj = {};\n  for (let i = 0; i < result.length; i += 2) {\n    const key = result[i];\n    const value = result[i + 1];\n    try {\n      const valueIsNumberAndNotSafeInteger = !Number.isNaN(Number(value)) && !Number.isSafeInteger(Number(value));\n      obj[key] = valueIsNumberAndNotSafeInteger ? value : JSON.parse(value);\n    } catch {\n      obj[key] = value;\n    }\n  }\n  return obj;\n}\nvar HGetAllCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hgetall\", ...cmd], {\n      deserialize: (result) => deserialize2(result),\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/hincrby.ts\nvar HIncrByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hincrby\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hincrbyfloat.ts\nvar HIncrByFloatCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hincrbyfloat\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hkeys.ts\nvar HKeysCommand = class extends Command {\n  constructor([key], opts) {\n    super([\"hkeys\", key], opts);\n  }\n};\n\n// pkg/commands/hlen.ts\nvar HLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hlen\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hmget.ts\nfunction deserialize3(fields, result) {\n  if (result.every((field) => field === null)) {\n    return null;\n  }\n  const obj = {};\n  for (const [i, field] of fields.entries()) {\n    try {\n      obj[field] = JSON.parse(result[i]);\n    } catch {\n      obj[field] = result[i];\n    }\n  }\n  return obj;\n}\nvar HMGetCommand = class extends Command {\n  constructor([key, ...fields], opts) {\n    super([\"hmget\", key, ...fields], {\n      deserialize: (result) => deserialize3(fields, result),\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/hmset.ts\nvar HMSetCommand = class extends Command {\n  constructor([key, kv], opts) {\n    super([\"hmset\", key, ...Object.entries(kv).flatMap(([field, value]) => [field, value])], opts);\n  }\n};\n\n// pkg/commands/hscan.ts\nvar HScanCommand = class extends Command {\n  constructor([key, cursor, cmdOpts], opts) {\n    const command = [\"hscan\", key, cursor];\n    if (cmdOpts?.match) {\n      command.push(\"match\", cmdOpts.match);\n    }\n    if (typeof cmdOpts?.count === \"number\") {\n      command.push(\"count\", cmdOpts.count);\n    }\n    super(command, {\n      deserialize: deserializeScanResponse,\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/hset.ts\nvar HSetCommand = class extends Command {\n  constructor([key, kv], opts) {\n    super([\"hset\", key, ...Object.entries(kv).flatMap(([field, value]) => [field, value])], opts);\n  }\n};\n\n// pkg/commands/hsetnx.ts\nvar HSetNXCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hsetnx\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/hstrlen.ts\nvar HStrLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hstrlen\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/httl.ts\nvar HTtlCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, fields] = cmd;\n    const fieldArray = Array.isArray(fields) ? fields : [fields];\n    super([\"httl\", key, \"FIELDS\", fieldArray.length, ...fieldArray], opts);\n  }\n};\n\n// pkg/commands/hvals.ts\nvar HValsCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"hvals\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/incr.ts\nvar IncrCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"incr\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/incrby.ts\nvar IncrByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"incrby\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/incrbyfloat.ts\nvar IncrByFloatCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"incrbyfloat\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_arrappend.ts\nvar JsonArrAppendCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.ARRAPPEND\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_arrindex.ts\nvar JsonArrIndexCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.ARRINDEX\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_arrinsert.ts\nvar JsonArrInsertCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.ARRINSERT\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_arrlen.ts\nvar JsonArrLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.ARRLEN\", cmd[0], cmd[1] ?? \"$\"], opts);\n  }\n};\n\n// pkg/commands/json_arrpop.ts\nvar JsonArrPopCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.ARRPOP\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_arrtrim.ts\nvar JsonArrTrimCommand = class extends Command {\n  constructor(cmd, opts) {\n    const path = cmd[1] ?? \"$\";\n    const start = cmd[2] ?? 0;\n    const stop = cmd[3] ?? 0;\n    super([\"JSON.ARRTRIM\", cmd[0], path, start, stop], opts);\n  }\n};\n\n// pkg/commands/json_clear.ts\nvar JsonClearCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.CLEAR\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_del.ts\nvar JsonDelCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.DEL\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_forget.ts\nvar JsonForgetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.FORGET\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_get.ts\nvar JsonGetCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"JSON.GET\"];\n    if (typeof cmd[1] === \"string\") {\n      command.push(...cmd);\n    } else {\n      command.push(cmd[0]);\n      if (cmd[1]) {\n        if (cmd[1].indent) {\n          command.push(\"INDENT\", cmd[1].indent);\n        }\n        if (cmd[1].newline) {\n          command.push(\"NEWLINE\", cmd[1].newline);\n        }\n        if (cmd[1].space) {\n          command.push(\"SPACE\", cmd[1].space);\n        }\n      }\n      command.push(...cmd.slice(2));\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/json_merge.ts\nvar JsonMergeCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"JSON.MERGE\", ...cmd];\n    super(command, opts);\n  }\n};\n\n// pkg/commands/json_mget.ts\nvar JsonMGetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.MGET\", ...cmd[0], cmd[1]], opts);\n  }\n};\n\n// pkg/commands/json_mset.ts\nvar JsonMSetCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"JSON.MSET\"];\n    for (const c of cmd) {\n      command.push(c.key, c.path, c.value);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/json_numincrby.ts\nvar JsonNumIncrByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.NUMINCRBY\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_nummultby.ts\nvar JsonNumMultByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.NUMMULTBY\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_objkeys.ts\nvar JsonObjKeysCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.OBJKEYS\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_objlen.ts\nvar JsonObjLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.OBJLEN\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_resp.ts\nvar JsonRespCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.RESP\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_set.ts\nvar JsonSetCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"JSON.SET\", cmd[0], cmd[1], cmd[2]];\n    if (cmd[3]) {\n      if (cmd[3].nx) {\n        command.push(\"NX\");\n      } else if (cmd[3].xx) {\n        command.push(\"XX\");\n      }\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/json_strappend.ts\nvar JsonStrAppendCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.STRAPPEND\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_strlen.ts\nvar JsonStrLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.STRLEN\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_toggle.ts\nvar JsonToggleCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.TOGGLE\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/json_type.ts\nvar JsonTypeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"JSON.TYPE\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/keys.ts\nvar KeysCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"keys\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lindex.ts\nvar LIndexCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lindex\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/linsert.ts\nvar LInsertCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"linsert\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/llen.ts\nvar LLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"llen\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lmove.ts\nvar LMoveCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lmove\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lmpop.ts\nvar LmPopCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [numkeys, keys, direction, count] = cmd;\n    super([\"LMPOP\", numkeys, ...keys, direction, ...count ? [\"COUNT\", count] : []], opts);\n  }\n};\n\n// pkg/commands/lpop.ts\nvar LPopCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lpop\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lpos.ts\nvar LPosCommand = class extends Command {\n  constructor(cmd, opts) {\n    const args = [\"lpos\", cmd[0], cmd[1]];\n    if (typeof cmd[2]?.rank === \"number\") {\n      args.push(\"rank\", cmd[2].rank);\n    }\n    if (typeof cmd[2]?.count === \"number\") {\n      args.push(\"count\", cmd[2].count);\n    }\n    if (typeof cmd[2]?.maxLen === \"number\") {\n      args.push(\"maxLen\", cmd[2].maxLen);\n    }\n    super(args, opts);\n  }\n};\n\n// pkg/commands/lpush.ts\nvar LPushCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lpush\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lpushx.ts\nvar LPushXCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lpushx\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lrange.ts\nvar LRangeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lrange\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lrem.ts\nvar LRemCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lrem\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/lset.ts\nvar LSetCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"lset\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/ltrim.ts\nvar LTrimCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"ltrim\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/mget.ts\nvar MGetCommand = class extends Command {\n  constructor(cmd, opts) {\n    const keys = Array.isArray(cmd[0]) ? cmd[0] : cmd;\n    super([\"mget\", ...keys], opts);\n  }\n};\n\n// pkg/commands/mset.ts\nvar MSetCommand = class extends Command {\n  constructor([kv], opts) {\n    super([\"mset\", ...Object.entries(kv).flatMap(([key, value]) => [key, value])], opts);\n  }\n};\n\n// pkg/commands/msetnx.ts\nvar MSetNXCommand = class extends Command {\n  constructor([kv], opts) {\n    super([\"msetnx\", ...Object.entries(kv).flat()], opts);\n  }\n};\n\n// pkg/commands/persist.ts\nvar PersistCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"persist\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pexpire.ts\nvar PExpireCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pexpire\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pexpireat.ts\nvar PExpireAtCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pexpireat\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pfadd.ts\nvar PfAddCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pfadd\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pfcount.ts\nvar PfCountCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pfcount\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pfmerge.ts\nvar PfMergeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pfmerge\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/ping.ts\nvar PingCommand = class extends Command {\n  constructor(cmd, opts) {\n    const command = [\"ping\"];\n    if (cmd?.[0] !== void 0) {\n      command.push(cmd[0]);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/psetex.ts\nvar PSetEXCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"psetex\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/pttl.ts\nvar PTtlCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"pttl\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/publish.ts\nvar PublishCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"publish\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/randomkey.ts\nvar RandomKeyCommand = class extends Command {\n  constructor(opts) {\n    super([\"randomkey\"], opts);\n  }\n};\n\n// pkg/commands/rename.ts\nvar RenameCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"rename\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/renamenx.ts\nvar RenameNXCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"renamenx\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/rpop.ts\nvar RPopCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"rpop\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/rpush.ts\nvar RPushCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"rpush\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/rpushx.ts\nvar RPushXCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"rpushx\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sadd.ts\nvar SAddCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sadd\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/scan.ts\nvar ScanCommand = class extends Command {\n  constructor([cursor, opts], cmdOpts) {\n    const command = [\"scan\", cursor];\n    if (opts?.match) {\n      command.push(\"match\", opts.match);\n    }\n    if (typeof opts?.count === \"number\") {\n      command.push(\"count\", opts.count);\n    }\n    if (opts?.type && opts.type.length > 0) {\n      command.push(\"type\", opts.type);\n    }\n    super(command, {\n      deserialize: deserializeScanResponse,\n      ...cmdOpts\n    });\n  }\n};\n\n// pkg/commands/scard.ts\nvar SCardCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"scard\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/script_exists.ts\nvar ScriptExistsCommand = class extends Command {\n  constructor(hashes, opts) {\n    super([\"script\", \"exists\", ...hashes], {\n      deserialize: (result) => result,\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/script_flush.ts\nvar ScriptFlushCommand = class extends Command {\n  constructor([opts], cmdOpts) {\n    const cmd = [\"script\", \"flush\"];\n    if (opts?.sync) {\n      cmd.push(\"sync\");\n    } else if (opts?.async) {\n      cmd.push(\"async\");\n    }\n    super(cmd, cmdOpts);\n  }\n};\n\n// pkg/commands/script_load.ts\nvar ScriptLoadCommand = class extends Command {\n  constructor(args, opts) {\n    super([\"script\", \"load\", ...args], opts);\n  }\n};\n\n// pkg/commands/sdiff.ts\nvar SDiffCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sdiff\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sdiffstore.ts\nvar SDiffStoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sdiffstore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/set.ts\nvar SetCommand = class extends Command {\n  constructor([key, value, opts], cmdOpts) {\n    const command = [\"set\", key, value];\n    if (opts) {\n      if (\"nx\" in opts && opts.nx) {\n        command.push(\"nx\");\n      } else if (\"xx\" in opts && opts.xx) {\n        command.push(\"xx\");\n      }\n      if (\"get\" in opts && opts.get) {\n        command.push(\"get\");\n      }\n      if (\"ex\" in opts && typeof opts.ex === \"number\") {\n        command.push(\"ex\", opts.ex);\n      } else if (\"px\" in opts && typeof opts.px === \"number\") {\n        command.push(\"px\", opts.px);\n      } else if (\"exat\" in opts && typeof opts.exat === \"number\") {\n        command.push(\"exat\", opts.exat);\n      } else if (\"pxat\" in opts && typeof opts.pxat === \"number\") {\n        command.push(\"pxat\", opts.pxat);\n      } else if (\"keepTtl\" in opts && opts.keepTtl) {\n        command.push(\"keepTtl\");\n      }\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/setbit.ts\nvar SetBitCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"setbit\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/setex.ts\nvar SetExCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"setex\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/setnx.ts\nvar SetNxCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"setnx\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/setrange.ts\nvar SetRangeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"setrange\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sinter.ts\nvar SInterCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sinter\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sinterstore.ts\nvar SInterStoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sinterstore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sismember.ts\nvar SIsMemberCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sismember\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/smembers.ts\nvar SMembersCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"smembers\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/smismember.ts\nvar SMIsMemberCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"smismember\", cmd[0], ...cmd[1]], opts);\n  }\n};\n\n// pkg/commands/smove.ts\nvar SMoveCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"smove\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/spop.ts\nvar SPopCommand = class extends Command {\n  constructor([key, count], opts) {\n    const command = [\"spop\", key];\n    if (typeof count === \"number\") {\n      command.push(count);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/srandmember.ts\nvar SRandMemberCommand = class extends Command {\n  constructor([key, count], opts) {\n    const command = [\"srandmember\", key];\n    if (typeof count === \"number\") {\n      command.push(count);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/srem.ts\nvar SRemCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"srem\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sscan.ts\nvar SScanCommand = class extends Command {\n  constructor([key, cursor, opts], cmdOpts) {\n    const command = [\"sscan\", key, cursor];\n    if (opts?.match) {\n      command.push(\"match\", opts.match);\n    }\n    if (typeof opts?.count === \"number\") {\n      command.push(\"count\", opts.count);\n    }\n    super(command, {\n      deserialize: deserializeScanResponse,\n      ...cmdOpts\n    });\n  }\n};\n\n// pkg/commands/strlen.ts\nvar StrLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"strlen\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sunion.ts\nvar SUnionCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sunion\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/sunionstore.ts\nvar SUnionStoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"sunionstore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/time.ts\nvar TimeCommand = class extends Command {\n  constructor(opts) {\n    super([\"time\"], opts);\n  }\n};\n\n// pkg/commands/touch.ts\nvar TouchCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"touch\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/ttl.ts\nvar TtlCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"ttl\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/type.ts\nvar TypeCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"type\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/unlink.ts\nvar UnlinkCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"unlink\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/xack.ts\nvar XAckCommand = class extends Command {\n  constructor([key, group, id], opts) {\n    const ids = Array.isArray(id) ? [...id] : [id];\n    super([\"XACK\", key, group, ...ids], opts);\n  }\n};\n\n// pkg/commands/xadd.ts\nvar XAddCommand = class extends Command {\n  constructor([key, id, entries, opts], commandOptions) {\n    const command = [\"XADD\", key];\n    if (opts) {\n      if (opts.nomkStream) {\n        command.push(\"NOMKSTREAM\");\n      }\n      if (opts.trim) {\n        command.push(opts.trim.type, opts.trim.comparison, opts.trim.threshold);\n        if (opts.trim.limit !== void 0) {\n          command.push(\"LIMIT\", opts.trim.limit);\n        }\n      }\n    }\n    command.push(id);\n    for (const [k, v] of Object.entries(entries)) {\n      command.push(k, v);\n    }\n    super(command, commandOptions);\n  }\n};\n\n// pkg/commands/xautoclaim.ts\nvar XAutoClaim = class extends Command {\n  constructor([key, group, consumer, minIdleTime, start, options], opts) {\n    const commands = [];\n    if (options?.count) {\n      commands.push(\"COUNT\", options.count);\n    }\n    if (options?.justId) {\n      commands.push(\"JUSTID\");\n    }\n    super([\"XAUTOCLAIM\", key, group, consumer, minIdleTime, start, ...commands], opts);\n  }\n};\n\n// pkg/commands/xclaim.ts\nvar XClaimCommand = class extends Command {\n  constructor([key, group, consumer, minIdleTime, id, options], opts) {\n    const ids = Array.isArray(id) ? [...id] : [id];\n    const commands = [];\n    if (options?.idleMS) {\n      commands.push(\"IDLE\", options.idleMS);\n    }\n    if (options?.idleMS) {\n      commands.push(\"TIME\", options.timeMS);\n    }\n    if (options?.retryCount) {\n      commands.push(\"RETRYCOUNT\", options.retryCount);\n    }\n    if (options?.force) {\n      commands.push(\"FORCE\");\n    }\n    if (options?.justId) {\n      commands.push(\"JUSTID\");\n    }\n    if (options?.lastId) {\n      commands.push(\"LASTID\", options.lastId);\n    }\n    super([\"XCLAIM\", key, group, consumer, minIdleTime, ...ids, ...commands], opts);\n  }\n};\n\n// pkg/commands/xdel.ts\nvar XDelCommand = class extends Command {\n  constructor([key, ids], opts) {\n    const cmds = Array.isArray(ids) ? [...ids] : [ids];\n    super([\"XDEL\", key, ...cmds], opts);\n  }\n};\n\n// pkg/commands/xgroup.ts\nvar XGroupCommand = class extends Command {\n  constructor([key, opts], commandOptions) {\n    const command = [\"XGROUP\"];\n    switch (opts.type) {\n      case \"CREATE\": {\n        command.push(\"CREATE\", key, opts.group, opts.id);\n        if (opts.options) {\n          if (opts.options.MKSTREAM) {\n            command.push(\"MKSTREAM\");\n          }\n          if (opts.options.ENTRIESREAD !== void 0) {\n            command.push(\"ENTRIESREAD\", opts.options.ENTRIESREAD.toString());\n          }\n        }\n        break;\n      }\n      case \"CREATECONSUMER\": {\n        command.push(\"CREATECONSUMER\", key, opts.group, opts.consumer);\n        break;\n      }\n      case \"DELCONSUMER\": {\n        command.push(\"DELCONSUMER\", key, opts.group, opts.consumer);\n        break;\n      }\n      case \"DESTROY\": {\n        command.push(\"DESTROY\", key, opts.group);\n        break;\n      }\n      case \"SETID\": {\n        command.push(\"SETID\", key, opts.group, opts.id);\n        if (opts.options?.ENTRIESREAD !== void 0) {\n          command.push(\"ENTRIESREAD\", opts.options.ENTRIESREAD.toString());\n        }\n        break;\n      }\n      default: {\n        throw new Error(\"Invalid XGROUP\");\n      }\n    }\n    super(command, commandOptions);\n  }\n};\n\n// pkg/commands/xinfo.ts\nvar XInfoCommand = class extends Command {\n  constructor([key, options], opts) {\n    const cmds = [];\n    if (options.type === \"CONSUMERS\") {\n      cmds.push(\"CONSUMERS\", key, options.group);\n    } else {\n      cmds.push(\"GROUPS\", key);\n    }\n    super([\"XINFO\", ...cmds], opts);\n  }\n};\n\n// pkg/commands/xlen.ts\nvar XLenCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"XLEN\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/xpending.ts\nvar XPendingCommand = class extends Command {\n  constructor([key, group, start, end, count, options], opts) {\n    const consumers = options?.consumer === void 0 ? [] : Array.isArray(options.consumer) ? [...options.consumer] : [options.consumer];\n    super(\n      [\n        \"XPENDING\",\n        key,\n        group,\n        ...options?.idleTime ? [\"IDLE\", options.idleTime] : [],\n        start,\n        end,\n        count,\n        ...consumers\n      ],\n      opts\n    );\n  }\n};\n\n// pkg/commands/xrange.ts\nfunction deserialize4(result) {\n  const obj = {};\n  for (const e of result) {\n    for (let i = 0; i < e.length; i += 2) {\n      const streamId = e[i];\n      const entries = e[i + 1];\n      if (!(streamId in obj)) {\n        obj[streamId] = {};\n      }\n      for (let j = 0; j < entries.length; j += 2) {\n        const field = entries[j];\n        const value = entries[j + 1];\n        try {\n          obj[streamId][field] = JSON.parse(value);\n        } catch {\n          obj[streamId][field] = value;\n        }\n      }\n    }\n  }\n  return obj;\n}\nvar XRangeCommand = class extends Command {\n  constructor([key, start, end, count], opts) {\n    const command = [\"XRANGE\", key, start, end];\n    if (typeof count === \"number\") {\n      command.push(\"COUNT\", count);\n    }\n    super(command, {\n      deserialize: (result) => deserialize4(result),\n      ...opts\n    });\n  }\n};\n\n// pkg/commands/xread.ts\nvar UNBALANCED_XREAD_ERR = \"ERR Unbalanced XREAD list of streams: for each stream key an ID or '$' must be specified\";\nvar XReadCommand = class extends Command {\n  constructor([key, id, options], opts) {\n    if (Array.isArray(key) && Array.isArray(id) && key.length !== id.length) {\n      throw new Error(UNBALANCED_XREAD_ERR);\n    }\n    const commands = [];\n    if (typeof options?.count === \"number\") {\n      commands.push(\"COUNT\", options.count);\n    }\n    if (typeof options?.blockMS === \"number\") {\n      commands.push(\"BLOCK\", options.blockMS);\n    }\n    commands.push(\n      \"STREAMS\",\n      ...Array.isArray(key) ? [...key] : [key],\n      ...Array.isArray(id) ? [...id] : [id]\n    );\n    super([\"XREAD\", ...commands], opts);\n  }\n};\n\n// pkg/commands/xreadgroup.ts\nvar UNBALANCED_XREADGROUP_ERR = \"ERR Unbalanced XREADGROUP list of streams: for each stream key an ID or '$' must be specified\";\nvar XReadGroupCommand = class extends Command {\n  constructor([group, consumer, key, id, options], opts) {\n    if (Array.isArray(key) && Array.isArray(id) && key.length !== id.length) {\n      throw new Error(UNBALANCED_XREADGROUP_ERR);\n    }\n    const commands = [];\n    if (typeof options?.count === \"number\") {\n      commands.push(\"COUNT\", options.count);\n    }\n    if (typeof options?.blockMS === \"number\") {\n      commands.push(\"BLOCK\", options.blockMS);\n    }\n    if (typeof options?.NOACK === \"boolean\" && options.NOACK) {\n      commands.push(\"NOACK\");\n    }\n    commands.push(\n      \"STREAMS\",\n      ...Array.isArray(key) ? [...key] : [key],\n      ...Array.isArray(id) ? [...id] : [id]\n    );\n    super([\"XREADGROUP\", \"GROUP\", group, consumer, ...commands], opts);\n  }\n};\n\n// pkg/commands/xrevrange.ts\nvar XRevRangeCommand = class extends Command {\n  constructor([key, end, start, count], opts) {\n    const command = [\"XREVRANGE\", key, end, start];\n    if (typeof count === \"number\") {\n      command.push(\"COUNT\", count);\n    }\n    super(command, {\n      deserialize: (result) => deserialize5(result),\n      ...opts\n    });\n  }\n};\nfunction deserialize5(result) {\n  const obj = {};\n  for (const e of result) {\n    for (let i = 0; i < e.length; i += 2) {\n      const streamId = e[i];\n      const entries = e[i + 1];\n      if (!(streamId in obj)) {\n        obj[streamId] = {};\n      }\n      for (let j = 0; j < entries.length; j += 2) {\n        const field = entries[j];\n        const value = entries[j + 1];\n        try {\n          obj[streamId][field] = JSON.parse(value);\n        } catch {\n          obj[streamId][field] = value;\n        }\n      }\n    }\n  }\n  return obj;\n}\n\n// pkg/commands/xtrim.ts\nvar XTrimCommand = class extends Command {\n  constructor([key, options], opts) {\n    const { limit, strategy, threshold, exactness = \"~\" } = options;\n    super([\"XTRIM\", key, strategy, exactness, threshold, ...limit ? [\"LIMIT\", limit] : []], opts);\n  }\n};\n\n// pkg/commands/zadd.ts\nvar ZAddCommand = class extends Command {\n  constructor([key, arg1, ...arg2], opts) {\n    const command = [\"zadd\", key];\n    if (\"nx\" in arg1 && arg1.nx) {\n      command.push(\"nx\");\n    } else if (\"xx\" in arg1 && arg1.xx) {\n      command.push(\"xx\");\n    }\n    if (\"ch\" in arg1 && arg1.ch) {\n      command.push(\"ch\");\n    }\n    if (\"incr\" in arg1 && arg1.incr) {\n      command.push(\"incr\");\n    }\n    if (\"lt\" in arg1 && arg1.lt) {\n      command.push(\"lt\");\n    } else if (\"gt\" in arg1 && arg1.gt) {\n      command.push(\"gt\");\n    }\n    if (\"score\" in arg1 && \"member\" in arg1) {\n      command.push(arg1.score, arg1.member);\n    }\n    command.push(...arg2.flatMap(({ score, member }) => [score, member]));\n    super(command, opts);\n  }\n};\n\n// pkg/commands/zcard.ts\nvar ZCardCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zcard\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zcount.ts\nvar ZCountCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zcount\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zincrby.ts\nvar ZIncrByCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zincrby\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zinterstore.ts\nvar ZInterStoreCommand = class extends Command {\n  constructor([destination, numKeys, keyOrKeys, opts], cmdOpts) {\n    const command = [\"zinterstore\", destination, numKeys];\n    if (Array.isArray(keyOrKeys)) {\n      command.push(...keyOrKeys);\n    } else {\n      command.push(keyOrKeys);\n    }\n    if (opts) {\n      if (\"weights\" in opts && opts.weights) {\n        command.push(\"weights\", ...opts.weights);\n      } else if (\"weight\" in opts && typeof opts.weight === \"number\") {\n        command.push(\"weights\", opts.weight);\n      }\n      if (\"aggregate\" in opts) {\n        command.push(\"aggregate\", opts.aggregate);\n      }\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/zlexcount.ts\nvar ZLexCountCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zlexcount\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zpopmax.ts\nvar ZPopMaxCommand = class extends Command {\n  constructor([key, count], opts) {\n    const command = [\"zpopmax\", key];\n    if (typeof count === \"number\") {\n      command.push(count);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/zpopmin.ts\nvar ZPopMinCommand = class extends Command {\n  constructor([key, count], opts) {\n    const command = [\"zpopmin\", key];\n    if (typeof count === \"number\") {\n      command.push(count);\n    }\n    super(command, opts);\n  }\n};\n\n// pkg/commands/zrange.ts\nvar ZRangeCommand = class extends Command {\n  constructor([key, min, max, opts], cmdOpts) {\n    const command = [\"zrange\", key, min, max];\n    if (opts?.byScore) {\n      command.push(\"byscore\");\n    }\n    if (opts?.byLex) {\n      command.push(\"bylex\");\n    }\n    if (opts?.rev) {\n      command.push(\"rev\");\n    }\n    if (opts?.count !== void 0 && opts.offset !== void 0) {\n      command.push(\"limit\", opts.offset, opts.count);\n    }\n    if (opts?.withScores) {\n      command.push(\"withscores\");\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/zrank.ts\nvar ZRankCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zrank\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zrem.ts\nvar ZRemCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zrem\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zremrangebylex.ts\nvar ZRemRangeByLexCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zremrangebylex\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zremrangebyrank.ts\nvar ZRemRangeByRankCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zremrangebyrank\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zremrangebyscore.ts\nvar ZRemRangeByScoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zremrangebyscore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zrevrank.ts\nvar ZRevRankCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zrevrank\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zscan.ts\nvar ZScanCommand = class extends Command {\n  constructor([key, cursor, opts], cmdOpts) {\n    const command = [\"zscan\", key, cursor];\n    if (opts?.match) {\n      command.push(\"match\", opts.match);\n    }\n    if (typeof opts?.count === \"number\") {\n      command.push(\"count\", opts.count);\n    }\n    super(command, {\n      deserialize: deserializeScanResponse,\n      ...cmdOpts\n    });\n  }\n};\n\n// pkg/commands/zscore.ts\nvar ZScoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zscore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zunion.ts\nvar ZUnionCommand = class extends Command {\n  constructor([numKeys, keyOrKeys, opts], cmdOpts) {\n    const command = [\"zunion\", numKeys];\n    if (Array.isArray(keyOrKeys)) {\n      command.push(...keyOrKeys);\n    } else {\n      command.push(keyOrKeys);\n    }\n    if (opts) {\n      if (\"weights\" in opts && opts.weights) {\n        command.push(\"weights\", ...opts.weights);\n      } else if (\"weight\" in opts && typeof opts.weight === \"number\") {\n        command.push(\"weights\", opts.weight);\n      }\n      if (\"aggregate\" in opts) {\n        command.push(\"aggregate\", opts.aggregate);\n      }\n      if (opts.withScores) {\n        command.push(\"withscores\");\n      }\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/zunionstore.ts\nvar ZUnionStoreCommand = class extends Command {\n  constructor([destination, numKeys, keyOrKeys, opts], cmdOpts) {\n    const command = [\"zunionstore\", destination, numKeys];\n    if (Array.isArray(keyOrKeys)) {\n      command.push(...keyOrKeys);\n    } else {\n      command.push(keyOrKeys);\n    }\n    if (opts) {\n      if (\"weights\" in opts && opts.weights) {\n        command.push(\"weights\", ...opts.weights);\n      } else if (\"weight\" in opts && typeof opts.weight === \"number\") {\n        command.push(\"weights\", opts.weight);\n      }\n      if (\"aggregate\" in opts) {\n        command.push(\"aggregate\", opts.aggregate);\n      }\n    }\n    super(command, cmdOpts);\n  }\n};\n\n// pkg/commands/zdiffstore.ts\nvar ZDiffStoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    super([\"zdiffstore\", ...cmd], opts);\n  }\n};\n\n// pkg/commands/zmscore.ts\nvar ZMScoreCommand = class extends Command {\n  constructor(cmd, opts) {\n    const [key, members] = cmd;\n    super([\"zmscore\", key, ...members], opts);\n  }\n};\n\n// pkg/pipeline.ts\nvar Pipeline = class {\n  client;\n  commands;\n  commandOptions;\n  multiExec;\n  constructor(opts) {\n    this.client = opts.client;\n    this.commands = [];\n    this.commandOptions = opts.commandOptions;\n    this.multiExec = opts.multiExec ?? false;\n    if (this.commandOptions?.latencyLogging) {\n      const originalExec = this.exec.bind(this);\n      this.exec = async (options) => {\n        const start = performance.now();\n        const result = await (options ? originalExec(options) : originalExec());\n        const end = performance.now();\n        const loggerResult = (end - start).toFixed(2);\n        console.log(\n          `Latency for \\x1B[38;2;19;185;39m${this.multiExec ? [\"MULTI-EXEC\"] : [\"PIPELINE\"].toString().toUpperCase()}\\x1B[0m: \\x1B[38;2;0;255;255m${loggerResult} ms\\x1B[0m`\n        );\n        return result;\n      };\n    }\n  }\n  exec = async (options) => {\n    if (this.commands.length === 0) {\n      throw new Error(\"Pipeline is empty\");\n    }\n    const path = this.multiExec ? [\"multi-exec\"] : [\"pipeline\"];\n    const res = await this.client.request({\n      path,\n      body: Object.values(this.commands).map((c) => c.command)\n    });\n    return options?.keepErrors ? res.map(({ error, result }, i) => {\n      return {\n        error,\n        result: this.commands[i].deserialize(result)\n      };\n    }) : res.map(({ error, result }, i) => {\n      if (error) {\n        throw new UpstashError(\n          `Command ${i + 1} [ ${this.commands[i].command[0]} ] failed: ${error}`\n        );\n      }\n      return this.commands[i].deserialize(result);\n    });\n  };\n  /**\n   * Returns the length of pipeline before the execution\n   */\n  length() {\n    return this.commands.length;\n  }\n  /**\n   * Pushes a command into the pipeline and returns a chainable instance of the\n   * pipeline\n   */\n  chain(command) {\n    this.commands.push(command);\n    return this;\n  }\n  /**\n   * @see https://redis.io/commands/append\n   */\n  append = (...args) => this.chain(new AppendCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/bitcount\n   */\n  bitcount = (...args) => this.chain(new BitCountCommand(args, this.commandOptions));\n  /**\n   * Returns an instance that can be used to execute `BITFIELD` commands on one key.\n   *\n   * @example\n   * ```typescript\n   * redis.set(\"mykey\", 0);\n   * const result = await redis.pipeline()\n   *   .bitfield(\"mykey\")\n   *   .set(\"u4\", 0, 16)\n   *   .incr(\"u4\", \"#1\", 1)\n   *   .exec();\n   * console.log(result); // [[0, 1]]\n   * ```\n   *\n   * @see https://redis.io/commands/bitfield\n   */\n  bitfield = (...args) => new BitFieldCommand(args, this.client, this.commandOptions, this.chain.bind(this));\n  /**\n   * @see https://redis.io/commands/bitop\n   */\n  bitop = (op, destinationKey, sourceKey, ...sourceKeys) => this.chain(\n    new BitOpCommand([op, destinationKey, sourceKey, ...sourceKeys], this.commandOptions)\n  );\n  /**\n   * @see https://redis.io/commands/bitpos\n   */\n  bitpos = (...args) => this.chain(new BitPosCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/copy\n   */\n  copy = (...args) => this.chain(new CopyCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zdiffstore\n   */\n  zdiffstore = (...args) => this.chain(new ZDiffStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/dbsize\n   */\n  dbsize = () => this.chain(new DBSizeCommand(this.commandOptions));\n  /**\n   * @see https://redis.io/commands/decr\n   */\n  decr = (...args) => this.chain(new DecrCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/decrby\n   */\n  decrby = (...args) => this.chain(new DecrByCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/del\n   */\n  del = (...args) => this.chain(new DelCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/echo\n   */\n  echo = (...args) => this.chain(new EchoCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/eval_ro\n   */\n  evalRo = (...args) => this.chain(new EvalROCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/eval\n   */\n  eval = (...args) => this.chain(new EvalCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/evalsha_ro\n   */\n  evalshaRo = (...args) => this.chain(new EvalshaROCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/evalsha\n   */\n  evalsha = (...args) => this.chain(new EvalshaCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/exists\n   */\n  exists = (...args) => this.chain(new ExistsCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/expire\n   */\n  expire = (...args) => this.chain(new ExpireCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/expireat\n   */\n  expireat = (...args) => this.chain(new ExpireAtCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/flushall\n   */\n  flushall = (args) => this.chain(new FlushAllCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/flushdb\n   */\n  flushdb = (...args) => this.chain(new FlushDBCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geoadd\n   */\n  geoadd = (...args) => this.chain(new GeoAddCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geodist\n   */\n  geodist = (...args) => this.chain(new GeoDistCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geopos\n   */\n  geopos = (...args) => this.chain(new GeoPosCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geohash\n   */\n  geohash = (...args) => this.chain(new GeoHashCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geosearch\n   */\n  geosearch = (...args) => this.chain(new GeoSearchCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/geosearchstore\n   */\n  geosearchstore = (...args) => this.chain(new GeoSearchStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/get\n   */\n  get = (...args) => this.chain(new GetCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/getbit\n   */\n  getbit = (...args) => this.chain(new GetBitCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/getdel\n   */\n  getdel = (...args) => this.chain(new GetDelCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/getex\n   */\n  getex = (...args) => this.chain(new GetExCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/getrange\n   */\n  getrange = (...args) => this.chain(new GetRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/getset\n   */\n  getset = (key, value) => this.chain(new GetSetCommand([key, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hdel\n   */\n  hdel = (...args) => this.chain(new HDelCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hexists\n   */\n  hexists = (...args) => this.chain(new HExistsCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hexpire\n   */\n  hexpire = (...args) => this.chain(new HExpireCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hexpireat\n   */\n  hexpireat = (...args) => this.chain(new HExpireAtCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hexpiretime\n   */\n  hexpiretime = (...args) => this.chain(new HExpireTimeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/httl\n   */\n  httl = (...args) => this.chain(new HTtlCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hpexpire\n   */\n  hpexpire = (...args) => this.chain(new HPExpireCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hpexpireat\n   */\n  hpexpireat = (...args) => this.chain(new HPExpireAtCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hpexpiretime\n   */\n  hpexpiretime = (...args) => this.chain(new HPExpireTimeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hpttl\n   */\n  hpttl = (...args) => this.chain(new HPTtlCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hpersist\n   */\n  hpersist = (...args) => this.chain(new HPersistCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hget\n   */\n  hget = (...args) => this.chain(new HGetCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hgetall\n   */\n  hgetall = (...args) => this.chain(new HGetAllCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hincrby\n   */\n  hincrby = (...args) => this.chain(new HIncrByCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hincrbyfloat\n   */\n  hincrbyfloat = (...args) => this.chain(new HIncrByFloatCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hkeys\n   */\n  hkeys = (...args) => this.chain(new HKeysCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hlen\n   */\n  hlen = (...args) => this.chain(new HLenCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hmget\n   */\n  hmget = (...args) => this.chain(new HMGetCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hmset\n   */\n  hmset = (key, kv) => this.chain(new HMSetCommand([key, kv], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hrandfield\n   */\n  hrandfield = (key, count, withValues) => this.chain(new HRandFieldCommand([key, count, withValues], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hscan\n   */\n  hscan = (...args) => this.chain(new HScanCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hset\n   */\n  hset = (key, kv) => this.chain(new HSetCommand([key, kv], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hsetnx\n   */\n  hsetnx = (key, field, value) => this.chain(new HSetNXCommand([key, field, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hstrlen\n   */\n  hstrlen = (...args) => this.chain(new HStrLenCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/hvals\n   */\n  hvals = (...args) => this.chain(new HValsCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/incr\n   */\n  incr = (...args) => this.chain(new IncrCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/incrby\n   */\n  incrby = (...args) => this.chain(new IncrByCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/incrbyfloat\n   */\n  incrbyfloat = (...args) => this.chain(new IncrByFloatCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/keys\n   */\n  keys = (...args) => this.chain(new KeysCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lindex\n   */\n  lindex = (...args) => this.chain(new LIndexCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/linsert\n   */\n  linsert = (key, direction, pivot, value) => this.chain(new LInsertCommand([key, direction, pivot, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/llen\n   */\n  llen = (...args) => this.chain(new LLenCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lmove\n   */\n  lmove = (...args) => this.chain(new LMoveCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lpop\n   */\n  lpop = (...args) => this.chain(new LPopCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lmpop\n   */\n  lmpop = (...args) => this.chain(new LmPopCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lpos\n   */\n  lpos = (...args) => this.chain(new LPosCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lpush\n   */\n  lpush = (key, ...elements) => this.chain(new LPushCommand([key, ...elements], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lpushx\n   */\n  lpushx = (key, ...elements) => this.chain(new LPushXCommand([key, ...elements], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lrange\n   */\n  lrange = (...args) => this.chain(new LRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lrem\n   */\n  lrem = (key, count, value) => this.chain(new LRemCommand([key, count, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/lset\n   */\n  lset = (key, index, value) => this.chain(new LSetCommand([key, index, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/ltrim\n   */\n  ltrim = (...args) => this.chain(new LTrimCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/mget\n   */\n  mget = (...args) => this.chain(new MGetCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/mset\n   */\n  mset = (kv) => this.chain(new MSetCommand([kv], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/msetnx\n   */\n  msetnx = (kv) => this.chain(new MSetNXCommand([kv], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/persist\n   */\n  persist = (...args) => this.chain(new PersistCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pexpire\n   */\n  pexpire = (...args) => this.chain(new PExpireCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pexpireat\n   */\n  pexpireat = (...args) => this.chain(new PExpireAtCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pfadd\n   */\n  pfadd = (...args) => this.chain(new PfAddCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pfcount\n   */\n  pfcount = (...args) => this.chain(new PfCountCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pfmerge\n   */\n  pfmerge = (...args) => this.chain(new PfMergeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/ping\n   */\n  ping = (args) => this.chain(new PingCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/psetex\n   */\n  psetex = (key, ttl, value) => this.chain(new PSetEXCommand([key, ttl, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/pttl\n   */\n  pttl = (...args) => this.chain(new PTtlCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/publish\n   */\n  publish = (...args) => this.chain(new PublishCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/randomkey\n   */\n  randomkey = () => this.chain(new RandomKeyCommand(this.commandOptions));\n  /**\n   * @see https://redis.io/commands/rename\n   */\n  rename = (...args) => this.chain(new RenameCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/renamenx\n   */\n  renamenx = (...args) => this.chain(new RenameNXCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/rpop\n   */\n  rpop = (...args) => this.chain(new RPopCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/rpush\n   */\n  rpush = (key, ...elements) => this.chain(new RPushCommand([key, ...elements], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/rpushx\n   */\n  rpushx = (key, ...elements) => this.chain(new RPushXCommand([key, ...elements], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sadd\n   */\n  sadd = (key, member, ...members) => this.chain(new SAddCommand([key, member, ...members], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/scan\n   */\n  scan = (...args) => this.chain(new ScanCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/scard\n   */\n  scard = (...args) => this.chain(new SCardCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/script-exists\n   */\n  scriptExists = (...args) => this.chain(new ScriptExistsCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/script-flush\n   */\n  scriptFlush = (...args) => this.chain(new ScriptFlushCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/script-load\n   */\n  scriptLoad = (...args) => this.chain(new ScriptLoadCommand(args, this.commandOptions));\n  /*)*\n   * @see https://redis.io/commands/sdiff\n   */\n  sdiff = (...args) => this.chain(new SDiffCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sdiffstore\n   */\n  sdiffstore = (...args) => this.chain(new SDiffStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/set\n   */\n  set = (key, value, opts) => this.chain(new SetCommand([key, value, opts], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/setbit\n   */\n  setbit = (...args) => this.chain(new SetBitCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/setex\n   */\n  setex = (key, ttl, value) => this.chain(new SetExCommand([key, ttl, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/setnx\n   */\n  setnx = (key, value) => this.chain(new SetNxCommand([key, value], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/setrange\n   */\n  setrange = (...args) => this.chain(new SetRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sinter\n   */\n  sinter = (...args) => this.chain(new SInterCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sinterstore\n   */\n  sinterstore = (...args) => this.chain(new SInterStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sismember\n   */\n  sismember = (key, member) => this.chain(new SIsMemberCommand([key, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/smembers\n   */\n  smembers = (...args) => this.chain(new SMembersCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/smismember\n   */\n  smismember = (key, members) => this.chain(new SMIsMemberCommand([key, members], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/smove\n   */\n  smove = (source, destination, member) => this.chain(new SMoveCommand([source, destination, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/spop\n   */\n  spop = (...args) => this.chain(new SPopCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/srandmember\n   */\n  srandmember = (...args) => this.chain(new SRandMemberCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/srem\n   */\n  srem = (key, ...members) => this.chain(new SRemCommand([key, ...members], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sscan\n   */\n  sscan = (...args) => this.chain(new SScanCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/strlen\n   */\n  strlen = (...args) => this.chain(new StrLenCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sunion\n   */\n  sunion = (...args) => this.chain(new SUnionCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/sunionstore\n   */\n  sunionstore = (...args) => this.chain(new SUnionStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/time\n   */\n  time = () => this.chain(new TimeCommand(this.commandOptions));\n  /**\n   * @see https://redis.io/commands/touch\n   */\n  touch = (...args) => this.chain(new TouchCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/ttl\n   */\n  ttl = (...args) => this.chain(new TtlCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/type\n   */\n  type = (...args) => this.chain(new TypeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/unlink\n   */\n  unlink = (...args) => this.chain(new UnlinkCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zadd\n   */\n  zadd = (...args) => {\n    if (\"score\" in args[1]) {\n      return this.chain(\n        new ZAddCommand([args[0], args[1], ...args.slice(2)], this.commandOptions)\n      );\n    }\n    return this.chain(\n      new ZAddCommand(\n        [args[0], args[1], ...args.slice(2)],\n        this.commandOptions\n      )\n    );\n  };\n  /**\n   * @see https://redis.io/commands/xadd\n   */\n  xadd = (...args) => this.chain(new XAddCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xack\n   */\n  xack = (...args) => this.chain(new XAckCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xdel\n   */\n  xdel = (...args) => this.chain(new XDelCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xgroup\n   */\n  xgroup = (...args) => this.chain(new XGroupCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xread\n   */\n  xread = (...args) => this.chain(new XReadCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xreadgroup\n   */\n  xreadgroup = (...args) => this.chain(new XReadGroupCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xinfo\n   */\n  xinfo = (...args) => this.chain(new XInfoCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xlen\n   */\n  xlen = (...args) => this.chain(new XLenCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xpending\n   */\n  xpending = (...args) => this.chain(new XPendingCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xclaim\n   */\n  xclaim = (...args) => this.chain(new XClaimCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xautoclaim\n   */\n  xautoclaim = (...args) => this.chain(new XAutoClaim(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xtrim\n   */\n  xtrim = (...args) => this.chain(new XTrimCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xrange\n   */\n  xrange = (...args) => this.chain(new XRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/xrevrange\n   */\n  xrevrange = (...args) => this.chain(new XRevRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zcard\n   */\n  zcard = (...args) => this.chain(new ZCardCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zcount\n   */\n  zcount = (...args) => this.chain(new ZCountCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zincrby\n   */\n  zincrby = (key, increment, member) => this.chain(new ZIncrByCommand([key, increment, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zinterstore\n   */\n  zinterstore = (...args) => this.chain(new ZInterStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zlexcount\n   */\n  zlexcount = (...args) => this.chain(new ZLexCountCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zmscore\n   */\n  zmscore = (...args) => this.chain(new ZMScoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zpopmax\n   */\n  zpopmax = (...args) => this.chain(new ZPopMaxCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zpopmin\n   */\n  zpopmin = (...args) => this.chain(new ZPopMinCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zrange\n   */\n  zrange = (...args) => this.chain(new ZRangeCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zrank\n   */\n  zrank = (key, member) => this.chain(new ZRankCommand([key, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zrem\n   */\n  zrem = (key, ...members) => this.chain(new ZRemCommand([key, ...members], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zremrangebylex\n   */\n  zremrangebylex = (...args) => this.chain(new ZRemRangeByLexCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zremrangebyrank\n   */\n  zremrangebyrank = (...args) => this.chain(new ZRemRangeByRankCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zremrangebyscore\n   */\n  zremrangebyscore = (...args) => this.chain(new ZRemRangeByScoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zrevrank\n   */\n  zrevrank = (key, member) => this.chain(new ZRevRankCommand([key, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zscan\n   */\n  zscan = (...args) => this.chain(new ZScanCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zscore\n   */\n  zscore = (key, member) => this.chain(new ZScoreCommand([key, member], this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zunionstore\n   */\n  zunionstore = (...args) => this.chain(new ZUnionStoreCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/zunion\n   */\n  zunion = (...args) => this.chain(new ZUnionCommand(args, this.commandOptions));\n  /**\n   * @see https://redis.io/commands/?group=json\n   */\n  get json() {\n    return {\n      /**\n       * @see https://redis.io/commands/json.arrappend\n       */\n      arrappend: (...args) => this.chain(new JsonArrAppendCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.arrindex\n       */\n      arrindex: (...args) => this.chain(new JsonArrIndexCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.arrinsert\n       */\n      arrinsert: (...args) => this.chain(new JsonArrInsertCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.arrlen\n       */\n      arrlen: (...args) => this.chain(new JsonArrLenCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.arrpop\n       */\n      arrpop: (...args) => this.chain(new JsonArrPopCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.arrtrim\n       */\n      arrtrim: (...args) => this.chain(new JsonArrTrimCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.clear\n       */\n      clear: (...args) => this.chain(new JsonClearCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.del\n       */\n      del: (...args) => this.chain(new JsonDelCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.forget\n       */\n      forget: (...args) => this.chain(new JsonForgetCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.get\n       */\n      get: (...args) => this.chain(new JsonGetCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.merge\n       */\n      merge: (...args) => this.chain(new JsonMergeCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.mget\n       */\n      mget: (...args) => this.chain(new JsonMGetCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.mset\n       */\n      mset: (...args) => this.chain(new JsonMSetCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.numincrby\n       */\n      numincrby: (...args) => this.chain(new JsonNumIncrByCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.nummultby\n       */\n      nummultby: (...args) => this.chain(new JsonNumMultByCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.objkeys\n       */\n      objkeys: (...args) => this.chain(new JsonObjKeysCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.objlen\n       */\n      objlen: (...args) => this.chain(new JsonObjLenCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.resp\n       */\n      resp: (...args) => this.chain(new JsonRespCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.set\n       */\n      set: (...args) => this.chain(new JsonSetCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.strappend\n       */\n      strappend: (...args) => this.chain(new JsonStrAppendCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.strlen\n       */\n      strlen: (...args) => this.chain(new JsonStrLenCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.toggle\n       */\n      toggle: (...args) => this.chain(new JsonToggleCommand(args, this.commandOptions)),\n      /**\n       * @see https://redis.io/commands/json.type\n       */\n      type: (...args) => this.chain(new JsonTypeCommand(args, this.commandOptions))\n    };\n  }\n};\n\n// pkg/auto-pipeline.ts\nfunction createAutoPipelineProxy(_redis, json) {\n  const redis = _redis;\n  if (!redis.autoPipelineExecutor) {\n    redis.autoPipelineExecutor = new AutoPipelineExecutor(redis);\n  }\n  return new Proxy(redis, {\n    get: (redis2, command) => {\n      if (command === \"pipelineCounter\") {\n        return redis2.autoPipelineExecutor.pipelineCounter;\n      }\n      if (command === \"json\") {\n        return createAutoPipelineProxy(redis2, true);\n      }\n      const commandInRedisButNotPipeline = command in redis2 && !(command in redis2.autoPipelineExecutor.pipeline);\n      if (commandInRedisButNotPipeline) {\n        return redis2[command];\n      }\n      const isFunction = json ? typeof redis2.autoPipelineExecutor.pipeline.json[command] === \"function\" : typeof redis2.autoPipelineExecutor.pipeline[command] === \"function\";\n      if (isFunction) {\n        return (...args) => {\n          return redis2.autoPipelineExecutor.withAutoPipeline((pipeline) => {\n            if (json) {\n              pipeline.json[command](\n                ...args\n              );\n            } else {\n              pipeline[command](...args);\n            }\n          });\n        };\n      }\n      return redis2.autoPipelineExecutor.pipeline[command];\n    }\n  });\n}\nvar AutoPipelineExecutor = class {\n  pipelinePromises = /* @__PURE__ */ new WeakMap();\n  activePipeline = null;\n  indexInCurrentPipeline = 0;\n  redis;\n  pipeline;\n  // only to make sure that proxy can work\n  pipelineCounter = 0;\n  // to keep track of how many times a pipeline was executed\n  constructor(redis) {\n    this.redis = redis;\n    this.pipeline = redis.pipeline();\n  }\n  async withAutoPipeline(executeWithPipeline) {\n    const pipeline = this.activePipeline ?? this.redis.pipeline();\n    if (!this.activePipeline) {\n      this.activePipeline = pipeline;\n      this.indexInCurrentPipeline = 0;\n    }\n    const index = this.indexInCurrentPipeline++;\n    executeWithPipeline(pipeline);\n    const pipelineDone = this.deferExecution().then(() => {\n      if (!this.pipelinePromises.has(pipeline)) {\n        const pipelinePromise = pipeline.exec({ keepErrors: true });\n        this.pipelineCounter += 1;\n        this.pipelinePromises.set(pipeline, pipelinePromise);\n        this.activePipeline = null;\n      }\n      return this.pipelinePromises.get(pipeline);\n    });\n    const results = await pipelineDone;\n    const commandResult = results[index];\n    if (commandResult.error) {\n      throw new UpstashError(`Command failed: ${commandResult.error}`);\n    }\n    return commandResult.result;\n  }\n  async deferExecution() {\n    await Promise.resolve();\n    await Promise.resolve();\n  }\n};\n\n// pkg/commands/psubscribe.ts\nvar PSubscribeCommand = class extends Command {\n  constructor(cmd, opts) {\n    const sseHeaders = {\n      Accept: \"text/event-stream\",\n      \"Cache-Control\": \"no-cache\",\n      Connection: \"keep-alive\"\n    };\n    super([], {\n      ...opts,\n      headers: sseHeaders,\n      path: [\"psubscribe\", ...cmd],\n      streamOptions: {\n        isStreaming: true,\n        onMessage: opts?.streamOptions?.onMessage,\n        signal: opts?.streamOptions?.signal\n      }\n    });\n  }\n};\n\n// pkg/commands/subscribe.ts\nvar Subscriber = class extends EventTarget {\n  subscriptions;\n  client;\n  listeners;\n  constructor(client, channels, isPattern = false) {\n    super();\n    this.client = client;\n    this.subscriptions = /* @__PURE__ */ new Map();\n    this.listeners = /* @__PURE__ */ new Map();\n    for (const channel of channels) {\n      if (isPattern) {\n        this.subscribeToPattern(channel);\n      } else {\n        this.subscribeToChannel(channel);\n      }\n    }\n  }\n  subscribeToChannel(channel) {\n    const controller = new AbortController();\n    const command = new SubscribeCommand([channel], {\n      streamOptions: {\n        signal: controller.signal,\n        onMessage: (data) => this.handleMessage(data, false)\n      }\n    });\n    command.exec(this.client).catch((error) => {\n      if (error.name !== \"AbortError\") {\n        this.dispatchToListeners(\"error\", error);\n      }\n    });\n    this.subscriptions.set(channel, {\n      command,\n      controller,\n      isPattern: false\n    });\n  }\n  subscribeToPattern(pattern) {\n    const controller = new AbortController();\n    const command = new PSubscribeCommand([pattern], {\n      streamOptions: {\n        signal: controller.signal,\n        onMessage: (data) => this.handleMessage(data, true)\n      }\n    });\n    command.exec(this.client).catch((error) => {\n      if (error.name !== \"AbortError\") {\n        this.dispatchToListeners(\"error\", error);\n      }\n    });\n    this.subscriptions.set(pattern, {\n      command,\n      controller,\n      isPattern: true\n    });\n  }\n  handleMessage(data, isPattern) {\n    const messageData = data.replace(/^data:\\s*/, \"\");\n    const firstCommaIndex = messageData.indexOf(\",\");\n    const secondCommaIndex = messageData.indexOf(\",\", firstCommaIndex + 1);\n    const thirdCommaIndex = isPattern ? messageData.indexOf(\",\", secondCommaIndex + 1) : -1;\n    if (firstCommaIndex !== -1 && secondCommaIndex !== -1) {\n      const type = messageData.slice(0, firstCommaIndex);\n      if (isPattern && type === \"pmessage\" && thirdCommaIndex !== -1) {\n        const pattern = messageData.slice(firstCommaIndex + 1, secondCommaIndex);\n        const channel = messageData.slice(secondCommaIndex + 1, thirdCommaIndex);\n        const messageStr = messageData.slice(thirdCommaIndex + 1);\n        try {\n          const message = JSON.parse(messageStr);\n          this.dispatchToListeners(\"pmessage\", { pattern, channel, message });\n          this.dispatchToListeners(`pmessage:${pattern}`, { pattern, channel, message });\n        } catch (error) {\n          this.dispatchToListeners(\"error\", new Error(`Failed to parse message: ${error}`));\n        }\n      } else {\n        const channel = messageData.slice(firstCommaIndex + 1, secondCommaIndex);\n        const messageStr = messageData.slice(secondCommaIndex + 1);\n        try {\n          if (type === \"subscribe\" || type === \"psubscribe\" || type === \"unsubscribe\" || type === \"punsubscribe\") {\n            const count = Number.parseInt(messageStr);\n            this.dispatchToListeners(type, count);\n          } else {\n            const message = JSON.parse(messageStr);\n            this.dispatchToListeners(type, { channel, message });\n            this.dispatchToListeners(`${type}:${channel}`, { channel, message });\n          }\n        } catch (error) {\n          this.dispatchToListeners(\"error\", new Error(`Failed to parse message: ${error}`));\n        }\n      }\n    }\n  }\n  dispatchToListeners(type, data) {\n    const listeners = this.listeners.get(type);\n    if (listeners) {\n      for (const listener of listeners) {\n        listener(data);\n      }\n    }\n  }\n  on(type, listener) {\n    if (!this.listeners.has(type)) {\n      this.listeners.set(type, /* @__PURE__ */ new Set());\n    }\n    this.listeners.get(type)?.add(listener);\n  }\n  removeAllListeners() {\n    this.listeners.clear();\n  }\n  async unsubscribe(channels) {\n    if (channels) {\n      for (const channel of channels) {\n        const subscription = this.subscriptions.get(channel);\n        if (subscription) {\n          try {\n            subscription.controller.abort();\n          } catch {\n          }\n          this.subscriptions.delete(channel);\n        }\n      }\n    } else {\n      for (const subscription of this.subscriptions.values()) {\n        try {\n          subscription.controller.abort();\n        } catch {\n        }\n      }\n      this.subscriptions.clear();\n      this.removeAllListeners();\n    }\n  }\n  getSubscribedChannels() {\n    return [...this.subscriptions.keys()];\n  }\n};\nvar SubscribeCommand = class extends Command {\n  constructor(cmd, opts) {\n    const sseHeaders = {\n      Accept: \"text/event-stream\",\n      \"Cache-Control\": \"no-cache\",\n      Connection: \"keep-alive\"\n    };\n    super([], {\n      ...opts,\n      headers: sseHeaders,\n      path: [\"subscribe\", ...cmd],\n      streamOptions: {\n        isStreaming: true,\n        onMessage: opts?.streamOptions?.onMessage,\n        signal: opts?.streamOptions?.signal\n      }\n    });\n  }\n};\n\n// pkg/script.ts\n\n\nvar Script = class {\n  script;\n  sha1;\n  redis;\n  constructor(redis, script) {\n    this.redis = redis;\n    this.sha1 = this.digest(script);\n    this.script = script;\n  }\n  /**\n   * Send an `EVAL` command to redis.\n   */\n  async eval(keys, args) {\n    return await this.redis.eval(this.script, keys, args);\n  }\n  /**\n   * Calculates the sha1 hash of the script and then calls `EVALSHA`.\n   */\n  async evalsha(keys, args) {\n    return await this.redis.evalsha(this.sha1, keys, args);\n  }\n  /**\n   * Optimistically try to run `EVALSHA` first.\n   * If the script is not loaded in redis, it will fall back and try again with `EVAL`.\n   *\n   * Following calls will be able to use the cached script\n   */\n  async exec(keys, args) {\n    const res = await this.redis.evalsha(this.sha1, keys, args).catch(async (error) => {\n      if (error instanceof Error && error.message.toLowerCase().includes(\"noscript\")) {\n        return await this.redis.eval(this.script, keys, args);\n      }\n      throw error;\n    });\n    return res;\n  }\n  /**\n   * Compute the sha1 hash of the script and return its hex representation.\n   */\n  digest(s) {\n    return crypto_js_enc_hex_js__WEBPACK_IMPORTED_MODULE_0__.stringify(crypto_js_sha1_js__WEBPACK_IMPORTED_MODULE_1__(s));\n  }\n};\n\n// pkg/scriptRo.ts\n\n\nvar ScriptRO = class {\n  script;\n  sha1;\n  redis;\n  constructor(redis, script) {\n    this.redis = redis;\n    this.sha1 = this.digest(script);\n    this.script = script;\n  }\n  /**\n   * Send an `EVAL_RO` command to redis.\n   */\n  async evalRo(keys, args) {\n    return await this.redis.evalRo(this.script, keys, args);\n  }\n  /**\n   * Calculates the sha1 hash of the script and then calls `EVALSHA_RO`.\n   */\n  async evalshaRo(keys, args) {\n    return await this.redis.evalshaRo(this.sha1, keys, args);\n  }\n  /**\n   * Optimistically try to run `EVALSHA_RO` first.\n   * If the script is not loaded in redis, it will fall back and try again with `EVAL_RO`.\n   *\n   * Following calls will be able to use the cached script\n   */\n  async exec(keys, args) {\n    const res = await this.redis.evalshaRo(this.sha1, keys, args).catch(async (error) => {\n      if (error instanceof Error && error.message.toLowerCase().includes(\"noscript\")) {\n        return await this.redis.evalRo(this.script, keys, args);\n      }\n      throw error;\n    });\n    return res;\n  }\n  /**\n   * Compute the sha1 hash of the script and return its hex representation.\n   */\n  digest(s) {\n    return crypto_js_enc_hex_js__WEBPACK_IMPORTED_MODULE_0__.stringify(crypto_js_sha1_js__WEBPACK_IMPORTED_MODULE_1__(s));\n  }\n};\n\n// pkg/redis.ts\nvar Redis = class {\n  client;\n  opts;\n  enableTelemetry;\n  enableAutoPipelining;\n  /**\n   * Create a new redis client\n   *\n   * @example\n   * ```typescript\n   * const redis = new Redis({\n   *  url: \"<UPSTASH_REDIS_REST_URL>\",\n   *  token: \"<UPSTASH_REDIS_REST_TOKEN>\",\n   * });\n   * ```\n   */\n  constructor(client, opts) {\n    this.client = client;\n    this.opts = opts;\n    this.enableTelemetry = opts?.enableTelemetry ?? true;\n    if (opts?.readYourWrites === false) {\n      this.client.readYourWrites = false;\n    }\n    this.enableAutoPipelining = opts?.enableAutoPipelining ?? true;\n  }\n  get readYourWritesSyncToken() {\n    return this.client.upstashSyncToken;\n  }\n  set readYourWritesSyncToken(session) {\n    this.client.upstashSyncToken = session;\n  }\n  get json() {\n    return {\n      /**\n       * @see https://redis.io/commands/json.arrappend\n       */\n      arrappend: (...args) => new JsonArrAppendCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.arrindex\n       */\n      arrindex: (...args) => new JsonArrIndexCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.arrinsert\n       */\n      arrinsert: (...args) => new JsonArrInsertCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.arrlen\n       */\n      arrlen: (...args) => new JsonArrLenCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.arrpop\n       */\n      arrpop: (...args) => new JsonArrPopCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.arrtrim\n       */\n      arrtrim: (...args) => new JsonArrTrimCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.clear\n       */\n      clear: (...args) => new JsonClearCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.del\n       */\n      del: (...args) => new JsonDelCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.forget\n       */\n      forget: (...args) => new JsonForgetCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.get\n       */\n      get: (...args) => new JsonGetCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.merge\n       */\n      merge: (...args) => new JsonMergeCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.mget\n       */\n      mget: (...args) => new JsonMGetCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.mset\n       */\n      mset: (...args) => new JsonMSetCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.numincrby\n       */\n      numincrby: (...args) => new JsonNumIncrByCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.nummultby\n       */\n      nummultby: (...args) => new JsonNumMultByCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.objkeys\n       */\n      objkeys: (...args) => new JsonObjKeysCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.objlen\n       */\n      objlen: (...args) => new JsonObjLenCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.resp\n       */\n      resp: (...args) => new JsonRespCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.set\n       */\n      set: (...args) => new JsonSetCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.strappend\n       */\n      strappend: (...args) => new JsonStrAppendCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.strlen\n       */\n      strlen: (...args) => new JsonStrLenCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.toggle\n       */\n      toggle: (...args) => new JsonToggleCommand(args, this.opts).exec(this.client),\n      /**\n       * @see https://redis.io/commands/json.type\n       */\n      type: (...args) => new JsonTypeCommand(args, this.opts).exec(this.client)\n    };\n  }\n  /**\n   * Wrap a new middleware around the HTTP client.\n   */\n  use = (middleware) => {\n    const makeRequest = this.client.request.bind(this.client);\n    this.client.request = (req) => middleware(req, makeRequest);\n  };\n  /**\n   * Technically this is not private, we can hide it from intellisense by doing this\n   */\n  addTelemetry = (telemetry) => {\n    if (!this.enableTelemetry) {\n      return;\n    }\n    try {\n      this.client.mergeTelemetry(telemetry);\n    } catch {\n    }\n  };\n  /**\n   * Creates a new script.\n   *\n   * Scripts offer the ability to optimistically try to execute a script without having to send the\n   * entire script to the server. If the script is loaded on the server, it tries again by sending\n   * the entire script. Afterwards, the script is cached on the server.\n   *\n   * @param script - The script to create\n   * @param opts - Optional options to pass to the script `{ readonly?: boolean }`\n   * @returns A new script\n   *\n   * @example\n   * ```ts\n   * const redis = new Redis({...})\n   *\n   * const script = redis.createScript<string>(\"return ARGV[1];\")\n   * const arg1 = await script.eval([], [\"Hello World\"])\n   * expect(arg1, \"Hello World\")\n   * ```\n   * @example\n   * ```ts\n   * const redis = new Redis({...})\n   *\n   * const script = redis.createScript<string>(\"return ARGV[1];\", { readonly: true })\n   * const arg1 = await script.evalRo([], [\"Hello World\"])\n   * expect(arg1, \"Hello World\")\n   * ```\n   */\n  createScript(script, opts) {\n    return opts?.readonly ? new ScriptRO(this, script) : new Script(this, script);\n  }\n  /**\n   * Create a new pipeline that allows you to send requests in bulk.\n   *\n   * @see {@link Pipeline}\n   */\n  pipeline = () => new Pipeline({\n    client: this.client,\n    commandOptions: this.opts,\n    multiExec: false\n  });\n  autoPipeline = () => {\n    return createAutoPipelineProxy(this);\n  };\n  /**\n   * Create a new transaction to allow executing multiple steps atomically.\n   *\n   * All the commands in a transaction are serialized and executed sequentially. A request sent by\n   * another client will never be served in the middle of the execution of a Redis Transaction. This\n   * guarantees that the commands are executed as a single isolated operation.\n   *\n   * @see {@link Pipeline}\n   */\n  multi = () => new Pipeline({\n    client: this.client,\n    commandOptions: this.opts,\n    multiExec: true\n  });\n  /**\n   * Returns an instance that can be used to execute `BITFIELD` commands on one key.\n   *\n   * @example\n   * ```typescript\n   * redis.set(\"mykey\", 0);\n   * const result = await redis.bitfield(\"mykey\")\n   *   .set(\"u4\", 0, 16)\n   *   .incr(\"u4\", \"#1\", 1)\n   *   .exec();\n   * console.log(result); // [0, 1]\n   * ```\n   *\n   * @see https://redis.io/commands/bitfield\n   */\n  bitfield = (...args) => new BitFieldCommand(args, this.client, this.opts);\n  /**\n   * @see https://redis.io/commands/append\n   */\n  append = (...args) => new AppendCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/bitcount\n   */\n  bitcount = (...args) => new BitCountCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/bitop\n   */\n  bitop = (op, destinationKey, sourceKey, ...sourceKeys) => new BitOpCommand([op, destinationKey, sourceKey, ...sourceKeys], this.opts).exec(\n    this.client\n  );\n  /**\n   * @see https://redis.io/commands/bitpos\n   */\n  bitpos = (...args) => new BitPosCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/copy\n   */\n  copy = (...args) => new CopyCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/dbsize\n   */\n  dbsize = () => new DBSizeCommand(this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/decr\n   */\n  decr = (...args) => new DecrCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/decrby\n   */\n  decrby = (...args) => new DecrByCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/del\n   */\n  del = (...args) => new DelCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/echo\n   */\n  echo = (...args) => new EchoCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/eval_ro\n   */\n  evalRo = (...args) => new EvalROCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/eval\n   */\n  eval = (...args) => new EvalCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/evalsha_ro\n   */\n  evalshaRo = (...args) => new EvalshaROCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/evalsha\n   */\n  evalsha = (...args) => new EvalshaCommand(args, this.opts).exec(this.client);\n  /**\n   * Generic method to execute any Redis command.\n   */\n  exec = (args) => new ExecCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/exists\n   */\n  exists = (...args) => new ExistsCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/expire\n   */\n  expire = (...args) => new ExpireCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/expireat\n   */\n  expireat = (...args) => new ExpireAtCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/flushall\n   */\n  flushall = (args) => new FlushAllCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/flushdb\n   */\n  flushdb = (...args) => new FlushDBCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geoadd\n   */\n  geoadd = (...args) => new GeoAddCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geopos\n   */\n  geopos = (...args) => new GeoPosCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geodist\n   */\n  geodist = (...args) => new GeoDistCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geohash\n   */\n  geohash = (...args) => new GeoHashCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geosearch\n   */\n  geosearch = (...args) => new GeoSearchCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/geosearchstore\n   */\n  geosearchstore = (...args) => new GeoSearchStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/get\n   */\n  get = (...args) => new GetCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/getbit\n   */\n  getbit = (...args) => new GetBitCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/getdel\n   */\n  getdel = (...args) => new GetDelCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/getex\n   */\n  getex = (...args) => new GetExCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/getrange\n   */\n  getrange = (...args) => new GetRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/getset\n   */\n  getset = (key, value) => new GetSetCommand([key, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hdel\n   */\n  hdel = (...args) => new HDelCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hexists\n   */\n  hexists = (...args) => new HExistsCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hexpire\n   */\n  hexpire = (...args) => new HExpireCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hexpireat\n   */\n  hexpireat = (...args) => new HExpireAtCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hexpiretime\n   */\n  hexpiretime = (...args) => new HExpireTimeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/httl\n   */\n  httl = (...args) => new HTtlCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hpexpire\n   */\n  hpexpire = (...args) => new HPExpireCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hpexpireat\n   */\n  hpexpireat = (...args) => new HPExpireAtCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hpexpiretime\n   */\n  hpexpiretime = (...args) => new HPExpireTimeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hpttl\n   */\n  hpttl = (...args) => new HPTtlCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hpersist\n   */\n  hpersist = (...args) => new HPersistCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hget\n   */\n  hget = (...args) => new HGetCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hgetall\n   */\n  hgetall = (...args) => new HGetAllCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hincrby\n   */\n  hincrby = (...args) => new HIncrByCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hincrbyfloat\n   */\n  hincrbyfloat = (...args) => new HIncrByFloatCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hkeys\n   */\n  hkeys = (...args) => new HKeysCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hlen\n   */\n  hlen = (...args) => new HLenCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hmget\n   */\n  hmget = (...args) => new HMGetCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hmset\n   */\n  hmset = (key, kv) => new HMSetCommand([key, kv], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hrandfield\n   */\n  hrandfield = (key, count, withValues) => new HRandFieldCommand([key, count, withValues], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hscan\n   */\n  hscan = (...args) => new HScanCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hset\n   */\n  hset = (key, kv) => new HSetCommand([key, kv], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hsetnx\n   */\n  hsetnx = (key, field, value) => new HSetNXCommand([key, field, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hstrlen\n   */\n  hstrlen = (...args) => new HStrLenCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/hvals\n   */\n  hvals = (...args) => new HValsCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/incr\n   */\n  incr = (...args) => new IncrCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/incrby\n   */\n  incrby = (...args) => new IncrByCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/incrbyfloat\n   */\n  incrbyfloat = (...args) => new IncrByFloatCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/keys\n   */\n  keys = (...args) => new KeysCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lindex\n   */\n  lindex = (...args) => new LIndexCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/linsert\n   */\n  linsert = (key, direction, pivot, value) => new LInsertCommand([key, direction, pivot, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/llen\n   */\n  llen = (...args) => new LLenCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lmove\n   */\n  lmove = (...args) => new LMoveCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lpop\n   */\n  lpop = (...args) => new LPopCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lmpop\n   */\n  lmpop = (...args) => new LmPopCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lpos\n   */\n  lpos = (...args) => new LPosCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lpush\n   */\n  lpush = (key, ...elements) => new LPushCommand([key, ...elements], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lpushx\n   */\n  lpushx = (key, ...elements) => new LPushXCommand([key, ...elements], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lrange\n   */\n  lrange = (...args) => new LRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lrem\n   */\n  lrem = (key, count, value) => new LRemCommand([key, count, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/lset\n   */\n  lset = (key, index, value) => new LSetCommand([key, index, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/ltrim\n   */\n  ltrim = (...args) => new LTrimCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/mget\n   */\n  mget = (...args) => new MGetCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/mset\n   */\n  mset = (kv) => new MSetCommand([kv], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/msetnx\n   */\n  msetnx = (kv) => new MSetNXCommand([kv], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/persist\n   */\n  persist = (...args) => new PersistCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/pexpire\n   */\n  pexpire = (...args) => new PExpireCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/pexpireat\n   */\n  pexpireat = (...args) => new PExpireAtCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/pfadd\n   */\n  pfadd = (...args) => new PfAddCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/pfcount\n   */\n  pfcount = (...args) => new PfCountCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/pfmerge\n   */\n  pfmerge = (...args) => new PfMergeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/ping\n   */\n  ping = (args) => new PingCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/psetex\n   */\n  psetex = (key, ttl, value) => new PSetEXCommand([key, ttl, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/psubscribe\n   */\n  psubscribe = (patterns) => {\n    const patternArray = Array.isArray(patterns) ? patterns : [patterns];\n    return new Subscriber(this.client, patternArray, true);\n  };\n  /**\n   * @see https://redis.io/commands/pttl\n   */\n  pttl = (...args) => new PTtlCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/publish\n   */\n  publish = (...args) => new PublishCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/randomkey\n   */\n  randomkey = () => new RandomKeyCommand().exec(this.client);\n  /**\n   * @see https://redis.io/commands/rename\n   */\n  rename = (...args) => new RenameCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/renamenx\n   */\n  renamenx = (...args) => new RenameNXCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/rpop\n   */\n  rpop = (...args) => new RPopCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/rpush\n   */\n  rpush = (key, ...elements) => new RPushCommand([key, ...elements], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/rpushx\n   */\n  rpushx = (key, ...elements) => new RPushXCommand([key, ...elements], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sadd\n   */\n  sadd = (key, member, ...members) => new SAddCommand([key, member, ...members], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/scan\n   */\n  scan = (...args) => new ScanCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/scard\n   */\n  scard = (...args) => new SCardCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/script-exists\n   */\n  scriptExists = (...args) => new ScriptExistsCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/script-flush\n   */\n  scriptFlush = (...args) => new ScriptFlushCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/script-load\n   */\n  scriptLoad = (...args) => new ScriptLoadCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sdiff\n   */\n  sdiff = (...args) => new SDiffCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sdiffstore\n   */\n  sdiffstore = (...args) => new SDiffStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/set\n   */\n  set = (key, value, opts) => new SetCommand([key, value, opts], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/setbit\n   */\n  setbit = (...args) => new SetBitCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/setex\n   */\n  setex = (key, ttl, value) => new SetExCommand([key, ttl, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/setnx\n   */\n  setnx = (key, value) => new SetNxCommand([key, value], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/setrange\n   */\n  setrange = (...args) => new SetRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sinter\n   */\n  sinter = (...args) => new SInterCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sinterstore\n   */\n  sinterstore = (...args) => new SInterStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sismember\n   */\n  sismember = (key, member) => new SIsMemberCommand([key, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/smismember\n   */\n  smismember = (key, members) => new SMIsMemberCommand([key, members], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/smembers\n   */\n  smembers = (...args) => new SMembersCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/smove\n   */\n  smove = (source, destination, member) => new SMoveCommand([source, destination, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/spop\n   */\n  spop = (...args) => new SPopCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/srandmember\n   */\n  srandmember = (...args) => new SRandMemberCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/srem\n   */\n  srem = (key, ...members) => new SRemCommand([key, ...members], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sscan\n   */\n  sscan = (...args) => new SScanCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/strlen\n   */\n  strlen = (...args) => new StrLenCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/subscribe\n   */\n  subscribe = (channels) => {\n    const channelArray = Array.isArray(channels) ? channels : [channels];\n    return new Subscriber(this.client, channelArray);\n  };\n  /**\n   * @see https://redis.io/commands/sunion\n   */\n  sunion = (...args) => new SUnionCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/sunionstore\n   */\n  sunionstore = (...args) => new SUnionStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/time\n   */\n  time = () => new TimeCommand().exec(this.client);\n  /**\n   * @see https://redis.io/commands/touch\n   */\n  touch = (...args) => new TouchCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/ttl\n   */\n  ttl = (...args) => new TtlCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/type\n   */\n  type = (...args) => new TypeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/unlink\n   */\n  unlink = (...args) => new UnlinkCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xadd\n   */\n  xadd = (...args) => new XAddCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xack\n   */\n  xack = (...args) => new XAckCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xdel\n   */\n  xdel = (...args) => new XDelCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xgroup\n   */\n  xgroup = (...args) => new XGroupCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xread\n   */\n  xread = (...args) => new XReadCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xreadgroup\n   */\n  xreadgroup = (...args) => new XReadGroupCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xinfo\n   */\n  xinfo = (...args) => new XInfoCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xlen\n   */\n  xlen = (...args) => new XLenCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xpending\n   */\n  xpending = (...args) => new XPendingCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xclaim\n   */\n  xclaim = (...args) => new XClaimCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xautoclaim\n   */\n  xautoclaim = (...args) => new XAutoClaim(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xtrim\n   */\n  xtrim = (...args) => new XTrimCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xrange\n   */\n  xrange = (...args) => new XRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/xrevrange\n   */\n  xrevrange = (...args) => new XRevRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zadd\n   */\n  zadd = (...args) => {\n    if (\"score\" in args[1]) {\n      return new ZAddCommand([args[0], args[1], ...args.slice(2)], this.opts).exec(\n        this.client\n      );\n    }\n    return new ZAddCommand(\n      [args[0], args[1], ...args.slice(2)],\n      this.opts\n    ).exec(this.client);\n  };\n  /**\n   * @see https://redis.io/commands/zcard\n   */\n  zcard = (...args) => new ZCardCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zcount\n   */\n  zcount = (...args) => new ZCountCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zdiffstore\n   */\n  zdiffstore = (...args) => new ZDiffStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zincrby\n   */\n  zincrby = (key, increment, member) => new ZIncrByCommand([key, increment, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zinterstore\n   */\n  zinterstore = (...args) => new ZInterStoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zlexcount\n   */\n  zlexcount = (...args) => new ZLexCountCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zmscore\n   */\n  zmscore = (...args) => new ZMScoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zpopmax\n   */\n  zpopmax = (...args) => new ZPopMaxCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zpopmin\n   */\n  zpopmin = (...args) => new ZPopMinCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zrange\n   */\n  zrange = (...args) => new ZRangeCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zrank\n   */\n  zrank = (key, member) => new ZRankCommand([key, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zrem\n   */\n  zrem = (key, ...members) => new ZRemCommand([key, ...members], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zremrangebylex\n   */\n  zremrangebylex = (...args) => new ZRemRangeByLexCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zremrangebyrank\n   */\n  zremrangebyrank = (...args) => new ZRemRangeByRankCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zremrangebyscore\n   */\n  zremrangebyscore = (...args) => new ZRemRangeByScoreCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zrevrank\n   */\n  zrevrank = (key, member) => new ZRevRankCommand([key, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zscan\n   */\n  zscan = (...args) => new ZScanCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zscore\n   */\n  zscore = (key, member) => new ZScoreCommand([key, member], this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zunion\n   */\n  zunion = (...args) => new ZUnionCommand(args, this.opts).exec(this.client);\n  /**\n   * @see https://redis.io/commands/zunionstore\n   */\n  zunionstore = (...args) => new ZUnionStoreCommand(args, this.opts).exec(this.client);\n};\n\n// version.ts\nvar VERSION = \"v1.34.8\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@upstash/redis/chunk-5XANP4AV.mjs\n"));

/***/ })

}]);