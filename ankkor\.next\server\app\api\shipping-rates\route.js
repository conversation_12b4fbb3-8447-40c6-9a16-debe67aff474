"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/shipping-rates/route";
exports.ids = ["app/api/shipping-rates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshipping-rates%2Froute&page=%2Fapi%2Fshipping-rates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshipping-rates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshipping-rates%2Froute&page=%2Fapi%2Fshipping-rates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshipping-rates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_shipping_rates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/shipping-rates/route.ts */ \"(rsc)/./src/app/api/shipping-rates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/shipping-rates/route\",\n        pathname: \"/api/shipping-rates\",\n        filename: \"route\",\n        bundlePath: \"app/api/shipping-rates/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\shipping-rates\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_shipping_rates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/shipping-rates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshipping-rates%2Froute&page=%2Fapi%2Fshipping-rates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshipping-rates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/shipping-rates/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/shipping-rates/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { pincode, cartItems } = body;\n        // Validate input\n        if (!pincode || !cartItems || !Array.isArray(cartItems)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid request data\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate pincode format (6 digits for India)\n        if (!/^[0-9]{6}$/.test(pincode)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid pincode format\"\n            }, {\n                status: 400\n            });\n        }\n        // Get shipping provider from environment\n        const shippingProvider = process.env.SHIPPING_PROVIDER || \"woocommerce\";\n        let shippingRates = [];\n        if (shippingProvider === \"woocommerce\") {\n            shippingRates = await getWooCommerceShippingRates(pincode, cartItems);\n        } else if (shippingProvider === \"delhivery\") {\n            shippingRates = await getDelhiveryShippingRates(pincode, cartItems);\n        } else {\n            // Fallback to basic calculation\n            shippingRates = await getBasicShippingRates(pincode, cartItems);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(shippingRates);\n    } catch (error) {\n        console.error(\"Shipping rates error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Failed to calculate shipping rates\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getWooCommerceShippingRates(pincode, cartItems) {\n    try {\n        const wooUrl = \"https://maroon-lapwing-781450.hostingersite.com\";\n        const consumerKey = process.env.WOOCOMMERCE_CONSUMER_KEY;\n        const consumerSecret = process.env.WOOCOMMERCE_CONSUMER_SECRET;\n        if (!wooUrl || !consumerKey || !consumerSecret) {\n            throw new Error(\"WooCommerce credentials not configured\");\n        }\n        // Calculate cart totals\n        const subtotal = cartItems.reduce((sum, item)=>{\n            const price = typeof item.price === \"string\" ? parseFloat(item.price) : item.price;\n            return sum + price * item.quantity;\n        }, 0);\n        const totalWeight = cartItems.reduce((sum, item)=>{\n            // Assume 0.5kg per item if weight not specified\n            return sum + (item.weight || 0.5) * item.quantity;\n        }, 0);\n        // Get shipping zones from WooCommerce\n        const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString(\"base64\");\n        const zonesResponse = await fetch(`${wooUrl}/wp-json/wc/v3/shipping/zones`, {\n            headers: {\n                \"Authorization\": `Basic ${auth}`\n            }\n        });\n        if (!zonesResponse.ok) {\n            throw new Error(\"Failed to fetch shipping zones\");\n        }\n        const zones = await zonesResponse.json();\n        const shippingRates = [];\n        // Find applicable zone based on pincode\n        for (const zone of zones){\n            if (zone.id === 0) continue; // Skip \"Rest of the World\" zone\n            // Get zone methods\n            const methodsResponse = await fetch(`${wooUrl}/wp-json/wc/v3/shipping/zones/${zone.id}/methods`, {\n                headers: {\n                    \"Authorization\": `Basic ${auth}`\n                }\n            });\n            if (methodsResponse.ok) {\n                const methods = await methodsResponse.json();\n                for (const method of methods){\n                    if (method.enabled) {\n                        let cost = 0;\n                        // Calculate cost based on method type\n                        if (method.method_id === \"flat_rate\") {\n                            cost = parseFloat(method.settings?.cost?.value || \"0\");\n                        } else if (method.method_id === \"free_shipping\") {\n                            const minAmount = parseFloat(method.settings?.min_amount?.value || \"0\");\n                            cost = subtotal >= minAmount ? 0 : parseFloat(method.settings?.cost?.value || \"50\");\n                        } else if (method.method_id === \"local_pickup\") {\n                            cost = parseFloat(method.settings?.cost?.value || \"0\");\n                        }\n                        shippingRates.push({\n                            id: `${zone.id}_${method.instance_id}`,\n                            name: method.title,\n                            cost: cost,\n                            description: method.settings?.description?.value || \"\",\n                            estimatedDays: getEstimatedDays(method.method_id, pincode)\n                        });\n                    }\n                }\n            }\n        }\n        // If no rates found, provide default rates\n        if (shippingRates.length === 0) {\n            return getBasicShippingRates(pincode, cartItems);\n        }\n        return shippingRates;\n    } catch (error) {\n        console.error(\"WooCommerce shipping error:\", error);\n        // Fallback to basic rates\n        return getBasicShippingRates(pincode, cartItems);\n    }\n}\nasync function getDelhiveryShippingRates(pincode, cartItems) {\n    try {\n        // This would integrate with Delhivery API\n        // For now, return basic rates with Delhivery-like options\n        const totalWeight = cartItems.reduce((sum, item)=>{\n            return sum + (item.weight || 0.5) * item.quantity;\n        }, 0);\n        const shippingRates = [\n            {\n                id: \"delhivery_surface\",\n                name: \"Delhivery Surface\",\n                cost: Math.max(50, totalWeight * 10),\n                description: \"Standard delivery via Delhivery\",\n                estimatedDays: \"5-7 days\"\n            },\n            {\n                id: \"delhivery_express\",\n                name: \"Delhivery Express\",\n                cost: Math.max(100, totalWeight * 20),\n                description: \"Express delivery via Delhivery\",\n                estimatedDays: \"2-3 days\"\n            }\n        ];\n        return shippingRates;\n    } catch (error) {\n        console.error(\"Delhivery shipping error:\", error);\n        return getBasicShippingRates(pincode, cartItems);\n    }\n}\nasync function getBasicShippingRates(pincode, cartItems) {\n    const totalValue = cartItems.reduce((sum, item)=>{\n        const price = typeof item.price === \"string\" ? parseFloat(item.price) : item.price;\n        return sum + price * item.quantity;\n    }, 0);\n    const shippingRates = [];\n    // Standard shipping (always available)\n    shippingRates.push({\n        id: \"standard\",\n        name: \"Standard Shipping\",\n        cost: totalValue > 1000 ? 0 : 50,\n        description: \"Delivered in 5-7 business days\",\n        estimatedDays: \"5-7 days\"\n    });\n    // Express shipping (available for most pincodes)\n    const metroAreas = [\n        \"110001\",\n        \"400001\",\n        \"560001\",\n        \"600001\",\n        \"700001\"\n    ];\n    if (metroAreas.includes(pincode)) {\n        shippingRates.push({\n            id: \"express\",\n            name: \"Express Shipping\",\n            cost: 150,\n            description: \"Delivered in 2-3 business days\",\n            estimatedDays: \"2-3 days\"\n        });\n        // Same day delivery for metro areas\n        shippingRates.push({\n            id: \"same_day\",\n            name: \"Same Day Delivery\",\n            cost: 300,\n            description: \"Delivered today before 9 PM\",\n            estimatedDays: \"Today\"\n        });\n    } else {\n        // Express for non-metro areas\n        shippingRates.push({\n            id: \"express\",\n            name: \"Express Shipping\",\n            cost: 200,\n            description: \"Delivered in 3-4 business days\",\n            estimatedDays: \"3-4 days\"\n        });\n    }\n    return shippingRates;\n}\nfunction getEstimatedDays(methodId, pincode) {\n    const metroAreas = [\n        \"110001\",\n        \"400001\",\n        \"560001\",\n        \"600001\",\n        \"700001\"\n    ];\n    const isMetro = metroAreas.includes(pincode);\n    switch(methodId){\n        case \"free_shipping\":\n        case \"flat_rate\":\n            return isMetro ? \"3-5 days\" : \"5-7 days\";\n        case \"local_pickup\":\n            return \"Same day\";\n        default:\n            return \"5-7 days\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shipping-rates/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshipping-rates%2Froute&page=%2Fapi%2Fshipping-rates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshipping-rates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();