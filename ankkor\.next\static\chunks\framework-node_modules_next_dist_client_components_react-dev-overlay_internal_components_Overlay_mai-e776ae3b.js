"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/maintain--tab-focus.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/maintain--tab-focus.js ***!
  \***********************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* eslint-disable */ // @ts-nocheck\n// Copied from https://github.com/medialize/ally.js\n// License: MIT\n// Copyright (c) 2015 Rodney Rehm\n//\n// Entrypoint: ally.js/maintain/tab-focus\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _platform = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/platform */ \"(app-pages-browser)/./node_modules/next/dist/compiled/platform/platform.js\"));\nconst _cssescape = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/css.escape */ \"(app-pages-browser)/./node_modules/next/dist/compiled/css.escape/css.escape.js\"));\n// input may be undefined, selector-tring, Node, NodeList, HTMLCollection, array of Nodes\n// yes, to some extent this is a bad replica of jQuery's constructor function\nfunction nodeArray(input) {\n    if (!input) {\n        return [];\n    }\n    if (Array.isArray(input)) {\n        return input;\n    }\n    // instanceof Node - does not work with iframes\n    if (input.nodeType !== undefined) {\n        return [\n            input\n        ];\n    }\n    if (typeof input === \"string\") {\n        input = document.querySelectorAll(input);\n    }\n    if (input.length !== undefined) {\n        return [].slice.call(input, 0);\n    }\n    throw new TypeError(\"unexpected input \" + String(input));\n}\nfunction contextToElement(_ref) {\n    var context = _ref.context, _ref$label = _ref.label, label = _ref$label === undefined ? \"context-to-element\" : _ref$label, resolveDocument = _ref.resolveDocument, defaultToDocument = _ref.defaultToDocument;\n    var element = nodeArray(context)[0];\n    if (resolveDocument && element && element.nodeType === Node.DOCUMENT_NODE) {\n        element = element.documentElement;\n    }\n    if (!element && defaultToDocument) {\n        return document.documentElement;\n    }\n    if (!element) {\n        throw new TypeError(label + \" requires valid options.context\");\n    }\n    if (element.nodeType !== Node.ELEMENT_NODE && element.nodeType !== Node.DOCUMENT_FRAGMENT_NODE) {\n        throw new TypeError(label + \" requires options.context to be an Element\");\n    }\n    return element;\n}\nfunction getShadowHost() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context;\n    var element = contextToElement({\n        label: \"get/shadow-host\",\n        context: context\n    });\n    // walk up to the root\n    var container = null;\n    while(element){\n        container = element;\n        element = element.parentNode;\n    }\n    // https://developer.mozilla.org/docs/Web/API/Node.nodeType\n    // NOTE: Firefox 34 does not expose ShadowRoot.host (but 37 does)\n    if (container.nodeType === container.DOCUMENT_FRAGMENT_NODE && container.host) {\n        // the root is attached to a fragment node that has a host\n        return container.host;\n    }\n    return null;\n}\nfunction getDocument(node) {\n    if (!node) {\n        return document;\n    }\n    if (node.nodeType === Node.DOCUMENT_NODE) {\n        return node;\n    }\n    return node.ownerDocument || document;\n}\nfunction isActiveElement(context) {\n    var element = contextToElement({\n        label: \"is/active-element\",\n        resolveDocument: true,\n        context: context\n    });\n    var _document = getDocument(element);\n    if (_document.activeElement === element) {\n        return true;\n    }\n    var shadowHost = getShadowHost({\n        context: element\n    });\n    if (shadowHost && shadowHost.shadowRoot.activeElement === element) {\n        return true;\n    }\n    return false;\n}\n// [elem, elem.parent, elem.parent.parent, …, html]\n// will not contain the shadowRoot (DOCUMENT_FRAGMENT_NODE) and shadowHost\nfunction getParents() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context;\n    var list = [];\n    var element = contextToElement({\n        label: \"get/parents\",\n        context: context\n    });\n    while(element){\n        list.push(element);\n        // IE does know support parentElement on SVGElement\n        element = element.parentNode;\n        if (element && element.nodeType !== Node.ELEMENT_NODE) {\n            element = null;\n        }\n    }\n    return list;\n}\n// Element.prototype.matches may be available at a different name\n// https://developer.mozilla.org/en/docs/Web/API/Element/matches\nvar names = [\n    \"matches\",\n    \"webkitMatchesSelector\",\n    \"mozMatchesSelector\",\n    \"msMatchesSelector\"\n];\nvar name = null;\nfunction findMethodName(element) {\n    names.some(function(_name) {\n        if (!element[_name]) {\n            return false;\n        }\n        name = _name;\n        return true;\n    });\n}\nfunction elementMatches(element, selector) {\n    if (!name) {\n        findMethodName(element);\n    }\n    return element[name](selector);\n}\n// deep clone of original platform\nvar platform = JSON.parse(JSON.stringify(_platform.default));\n// operating system\nvar os = platform.os.family || \"\";\nvar ANDROID = os === \"Android\";\nvar WINDOWS = os.slice(0, 7) === \"Windows\";\nvar OSX = os === \"OS X\";\nvar IOS = os === \"iOS\";\n// layout\nvar BLINK = platform.layout === \"Blink\";\nvar GECKO = platform.layout === \"Gecko\";\nvar TRIDENT = platform.layout === \"Trident\";\nvar EDGE = platform.layout === \"EdgeHTML\";\nvar WEBKIT = platform.layout === \"WebKit\";\n// browser version (not layout engine version!)\nvar version = parseFloat(platform.version);\nvar majorVersion = Math.floor(version);\nplatform.majorVersion = majorVersion;\nplatform.is = {\n    // operating system\n    ANDROID: ANDROID,\n    WINDOWS: WINDOWS,\n    OSX: OSX,\n    IOS: IOS,\n    // layout\n    BLINK: BLINK,\n    GECKO: GECKO,\n    TRIDENT: TRIDENT,\n    EDGE: EDGE,\n    WEBKIT: WEBKIT,\n    // INTERNET EXPLORERS\n    IE9: TRIDENT && majorVersion === 9,\n    IE10: TRIDENT && majorVersion === 10,\n    IE11: TRIDENT && majorVersion === 11\n};\nfunction before() {\n    var data = {\n        // remember what had focus to restore after test\n        activeElement: document.activeElement,\n        // remember scroll positions to restore after test\n        windowScrollTop: window.scrollTop,\n        windowScrollLeft: window.scrollLeft,\n        bodyScrollTop: document.body.scrollTop,\n        bodyScrollLeft: document.body.scrollLeft\n    };\n    // wrap tests in an element hidden from screen readers to prevent them\n    // from announcing focus, which can be quite irritating to the user\n    var iframe = document.createElement(\"iframe\");\n    iframe.setAttribute(\"style\", \"position:absolute; position:fixed; top:0; left:-2px; width:1px; height:1px; overflow:hidden;\");\n    iframe.setAttribute(\"aria-live\", \"off\");\n    iframe.setAttribute(\"aria-busy\", \"true\");\n    iframe.setAttribute(\"aria-hidden\", \"true\");\n    document.body.appendChild(iframe);\n    var _window = iframe.contentWindow;\n    var _document = _window.document;\n    _document.open();\n    _document.close();\n    var wrapper = _document.createElement(\"div\");\n    _document.body.appendChild(wrapper);\n    data.iframe = iframe;\n    data.wrapper = wrapper;\n    data.window = _window;\n    data.document = _document;\n    return data;\n}\n// options.element:\n//  {string} element name\n//  {function} callback(wrapper, document) to generate an element\n// options.mutate: (optional)\n//  {function} callback(element, wrapper, document) to manipulate element prior to focus-test.\n//             Can return DOMElement to define focus target (default: element)\n// options.validate: (optional)\n//  {function} callback(element, focusTarget, document) to manipulate test-result\nfunction test(data, options) {\n    // make sure we operate on a clean slate\n    data.wrapper.innerHTML = \"\";\n    // create dummy element to test focusability of\n    var element = typeof options.element === \"string\" ? data.document.createElement(options.element) : options.element(data.wrapper, data.document);\n    // allow callback to further specify dummy element\n    // and optionally define element to focus\n    var focus = options.mutate && options.mutate(element, data.wrapper, data.document);\n    if (!focus && focus !== false) {\n        focus = element;\n    }\n    // element needs to be part of the DOM to be focusable\n    !element.parentNode && data.wrapper.appendChild(element);\n    // test if the element with invalid tabindex can be focused\n    focus && focus.focus && focus.focus();\n    // validate test's result\n    return options.validate ? options.validate(element, focus, data.document) : data.document.activeElement === focus;\n}\nfunction after(data) {\n    // restore focus to what it was before test and cleanup\n    if (data.activeElement === document.body) {\n        document.activeElement && document.activeElement.blur && document.activeElement.blur();\n        if (platform.is.IE10) {\n            // IE10 does not redirect focus to <body> when the activeElement is removed\n            document.body.focus();\n        }\n    } else {\n        data.activeElement && data.activeElement.focus && data.activeElement.focus();\n    }\n    document.body.removeChild(data.iframe);\n    // restore scroll position\n    window.scrollTop = data.windowScrollTop;\n    window.scrollLeft = data.windowScrollLeft;\n    document.body.scrollTop = data.bodyScrollTop;\n    document.body.scrollLeft = data.bodyScrollLeft;\n}\nfunction detectFocus(tests) {\n    var data = before();\n    var results = {};\n    Object.keys(tests).map(function(key) {\n        results[key] = test(data, tests[key]);\n    });\n    after(data);\n    return results;\n}\n// this file is overwritten by `npm run build:pre`\nvar version$1 = \"1.4.1\";\n/*\n    Facility to cache test results in localStorage.\n\n    USAGE:\n      cache.get('key');\n      cache.set('key', 'value');\n */ function readLocalStorage(key) {\n    // allow reading from storage to retrieve previous support results\n    // even while the document does not have focus\n    var data = void 0;\n    try {\n        data = window.localStorage && window.localStorage.getItem(key);\n        data = data ? JSON.parse(data) : {};\n    } catch (e) {\n        data = {};\n    }\n    return data;\n}\nfunction writeLocalStorage(key, value) {\n    if (!document.hasFocus()) {\n        // if the document does not have focus when tests are executed, focus() may\n        // not be handled properly and events may not be dispatched immediately.\n        // This can happen when a document is reloaded while Developer Tools have focus.\n        try {\n            window.localStorage && window.localStorage.removeItem(key);\n        } catch (e) {\n        // ignore\n        }\n        return;\n    }\n    try {\n        window.localStorage && window.localStorage.setItem(key, JSON.stringify(value));\n    } catch (e) {\n    // ignore\n    }\n}\nvar userAgent = typeof window !== \"undefined\" && window.navigator.userAgent || \"\";\nvar cacheKey = \"ally-supports-cache\";\nvar cache = readLocalStorage(cacheKey);\n// update the cache if ally or the user agent changed (newer version, etc)\nif (cache.userAgent !== userAgent || cache.version !== version$1) {\n    cache = {};\n}\ncache.userAgent = userAgent;\ncache.version = version$1;\nvar cache$1 = {\n    get: function get() {\n        return cache;\n    },\n    set: function set(values) {\n        Object.keys(values).forEach(function(key) {\n            cache[key] = values[key];\n        });\n        cache.time = new Date().toISOString();\n        writeLocalStorage(cacheKey, cache);\n    }\n};\nfunction cssShadowPiercingDeepCombinator() {\n    var combinator = void 0;\n    // see https://dev.w3.org/csswg/css-scoping-1/#deep-combinator\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1117572\n    // https://code.google.com/p/chromium/issues/detail?id=446051\n    try {\n        document.querySelector(\"html >>> :first-child\");\n        combinator = \">>>\";\n    } catch (noArrowArrowArrow) {\n        try {\n            // old syntax supported at least up to Chrome 41\n            // https://code.google.com/p/chromium/issues/detail?id=446051\n            document.querySelector(\"html /deep/ :first-child\");\n            combinator = \"/deep/\";\n        } catch (noDeep) {\n            combinator = \"\";\n        }\n    }\n    return combinator;\n}\nvar gif = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaImgTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"image-map-tabindex-test\">' + '<area shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#image-map-tabindex-test\" tabindex=\"-1\" alt=\"\" src=\"' + gif + '\">';\n        return element.querySelector(\"area\");\n    }\n};\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"image-map-tabindex-test\">' + '<area href=\"#void\" tabindex=\"-1\" shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#image-map-tabindex-test\" alt=\"\" src=\"' + gif + '\">';\n        return false;\n    },\n    validate: function validate(element, focusTarget, _document) {\n        if (platform.is.GECKO) {\n            // fixes https://github.com/medialize/ally.js/issues/35\n            // Firefox loads the DataURI asynchronously, causing a false-negative\n            return true;\n        }\n        var focus = element.querySelector(\"area\");\n        focus.focus();\n        return _document.activeElement === focus;\n    }\n};\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaWithoutHref = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"image-map-area-href-test\">' + '<area shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#image-map-area-href-test\" alt=\"\" src=\"' + gif + '\">';\n        return element.querySelector(\"area\");\n    },\n    validate: function validate(element, focusTarget, _document) {\n        if (platform.is.GECKO) {\n            // fixes https://github.com/medialize/ally.js/issues/35\n            // Firefox loads the DataURI asynchronously, causing a false-negative\n            return true;\n        }\n        return _document.activeElement === focusTarget;\n    }\n};\nvar focusAudioWithoutControls = {\n    name: \"can-focus-audio-without-controls\",\n    element: \"audio\",\n    mutate: function mutate(element) {\n        try {\n            // invalid media file can trigger warning in console, data-uri to prevent HTTP request\n            element.setAttribute(\"src\", gif);\n        } catch (e) {\n        // IE9 may throw \"Error: Not implemented\"\n        }\n    }\n};\nvar invalidGif = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ\";\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusBrokenImageMap = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"broken-image-map-test\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#broken-image-map-test\" alt=\"\" src=\"' + invalidGif + '\">';\n        return element.querySelector(\"area\");\n    }\n};\n// Children of focusable elements with display:flex are focusable in IE10-11\nvar focusChildrenOfFocusableFlexbox = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", \"-1\");\n        element.setAttribute(\"style\", \"display: -webkit-flex; display: -ms-flexbox; display: flex;\");\n        element.innerHTML = '<span style=\"display: block;\">hello</span>';\n        return element.querySelector(\"span\");\n    }\n};\n// fieldset[tabindex=0][disabled] should not be focusable, but Blink and WebKit disagree\n// @specification https://www.w3.org/TR/html5/disabled-elements.html#concept-element-disabled\n// @browser-issue Chromium https://crbug.com/453847\n// @browser-issue WebKit https://bugs.webkit.org/show_bug.cgi?id=141086\nvar focusFieldsetDisabled = {\n    element: \"fieldset\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", 0);\n        element.setAttribute(\"disabled\", \"disabled\");\n    }\n};\nvar focusFieldset = {\n    element: \"fieldset\",\n    mutate: function mutate(element) {\n        element.innerHTML = \"<legend>legend</legend><p>content</p>\";\n    }\n};\n// elements with display:flex are focusable in IE10-11\nvar focusFlexboxContainer = {\n    element: \"span\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"style\", \"display: -webkit-flex; display: -ms-flexbox; display: flex;\");\n        element.innerHTML = '<span style=\"display: block;\">hello</span>';\n    }\n};\n// form[tabindex=0][disabled] should be focusable as the\n// specification doesn't know the disabled attribute on the form element\n// @specification https://www.w3.org/TR/html5/forms.html#the-form-element\nvar focusFormDisabled = {\n    element: \"form\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", 0);\n        element.setAttribute(\"disabled\", \"disabled\");\n    }\n};\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// fixes https://github.com/medialize/ally.js/issues/20\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-ismap\nvar focusImgIsmap = {\n    element: \"a\",\n    mutate: function mutate(element) {\n        element.href = \"#void\";\n        element.innerHTML = '<img ismap src=\"' + gif + '\" alt=\"\">';\n        return element.querySelector(\"img\");\n    }\n};\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusImgUsemapTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"image-map-tabindex-test\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#image-map-tabindex-test\" tabindex=\"-1\" alt=\"\" ' + 'src=\"' + gif + '\">';\n        return element.querySelector(\"img\");\n    }\n};\nvar focusInHiddenIframe = {\n    element: function element(wrapper, _document) {\n        var iframe = _document.createElement(\"iframe\");\n        // iframe must be part of the DOM before accessing the contentWindow is possible\n        wrapper.appendChild(iframe);\n        // create the iframe's default document (<html><head></head><body></body></html>)\n        var iframeDocument = iframe.contentWindow.document;\n        iframeDocument.open();\n        iframeDocument.close();\n        return iframe;\n    },\n    mutate: function mutate(iframe) {\n        iframe.style.visibility = \"hidden\";\n        var iframeDocument = iframe.contentWindow.document;\n        var input = iframeDocument.createElement(\"input\");\n        iframeDocument.body.appendChild(input);\n        return input;\n    },\n    validate: function validate(iframe) {\n        var iframeDocument = iframe.contentWindow.document;\n        var focus = iframeDocument.querySelector(\"input\");\n        return iframeDocument.activeElement === focus;\n    }\n};\nvar result = !platform.is.WEBKIT;\nfunction focusInZeroDimensionObject() {\n    return result;\n}\n// Firefox allows *any* value and treats invalid values like tabindex=\"-1\"\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\nvar focusInvalidTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", \"invalid-value\");\n    }\n};\nvar focusLabelTabindex = {\n    element: \"label\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", \"-1\");\n    },\n    validate: function validate(element, focusTarget, _document) {\n        // force layout in Chrome 49, otherwise the element won't be focusable\n        /* eslint-disable no-unused-vars */ var variableToPreventDeadCodeElimination = element.offsetHeight;\n        /* eslint-enable no-unused-vars */ element.focus();\n        return _document.activeElement === element;\n    }\n};\nvar svg = \"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtb\" + \"G5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBpZD0ic3ZnIj48dGV4dCB4PSIxMCIgeT0iMjAiIGlkPSJ\" + \"zdmctbGluay10ZXh0Ij50ZXh0PC90ZXh0Pjwvc3ZnPg==\";\n// Note: IE10 on BrowserStack does not like this test\nvar focusObjectSvgHidden = {\n    element: \"object\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"type\", \"image/svg+xml\");\n        element.setAttribute(\"data\", svg);\n        element.setAttribute(\"width\", \"200\");\n        element.setAttribute(\"height\", \"50\");\n        element.style.visibility = \"hidden\";\n    }\n};\n// Note: IE10 on BrowserStack does not like this test\nvar focusObjectSvg = {\n    name: \"can-focus-object-svg\",\n    element: \"object\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"type\", \"image/svg+xml\");\n        element.setAttribute(\"data\", svg);\n        element.setAttribute(\"width\", \"200\");\n        element.setAttribute(\"height\", \"50\");\n    },\n    validate: function validate(element, focusTarget, _document) {\n        if (platform.is.GECKO) {\n            // Firefox seems to be handling the object creation asynchronously and thereby produces a false negative test result.\n            // Because we know Firefox is able to focus object elements referencing SVGs, we simply cheat by sniffing the user agent string\n            return true;\n        }\n        return _document.activeElement === element;\n    }\n};\n// Every Environment except IE9 considers SWF objects focusable\nvar result$1 = !platform.is.IE9;\nfunction focusObjectSwf() {\n    return result$1;\n}\nvar focusRedirectImgUsemap = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<map name=\"focus-redirect-img-usemap\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' + '<img usemap=\"#focus-redirect-img-usemap\" alt=\"\" ' + 'src=\"' + gif + '\">';\n        // focus the <img>, not the <div>\n        return element.querySelector(\"img\");\n    },\n    validate: function validate(element, focusTarget, _document) {\n        var target = element.querySelector(\"area\");\n        return _document.activeElement === target;\n    }\n};\n// see https://jsbin.com/nenirisage/edit?html,js,console,output\nvar focusRedirectLegend = {\n    element: \"fieldset\",\n    mutate: function mutate(element) {\n        element.innerHTML = '<legend>legend</legend><input tabindex=\"-1\"><input tabindex=\"0\">';\n        // take care of focus in validate();\n        return false;\n    },\n    validate: function validate(element, focusTarget, _document) {\n        var focusable = element.querySelector('input[tabindex=\"-1\"]');\n        var tabbable = element.querySelector('input[tabindex=\"0\"]');\n        // Firefox requires this test to focus the <fieldset> first, while this is not necessary in\n        // https://jsbin.com/nenirisage/edit?html,js,console,output\n        element.focus();\n        element.querySelector(\"legend\").focus();\n        return _document.activeElement === focusable && \"focusable\" || _document.activeElement === tabbable && \"tabbable\" || \"\";\n    }\n};\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollBody = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"style\", \"width: 100px; height: 50px; overflow: auto;\");\n        element.innerHTML = '<div style=\"width: 500px; height: 40px;\">scrollable content</div>';\n        return element.querySelector(\"div\");\n    }\n};\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollContainerWithoutOverflow = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"style\", \"width: 100px; height: 50px;\");\n        element.innerHTML = '<div style=\"width: 500px; height: 40px;\">scrollable content</div>';\n    }\n};\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollContainer = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"style\", \"width: 100px; height: 50px; overflow: auto;\");\n        element.innerHTML = '<div style=\"width: 500px; height: 40px;\">scrollable content</div>';\n    }\n};\nvar focusSummary = {\n    element: \"details\",\n    mutate: function mutate(element) {\n        element.innerHTML = \"<summary>foo</summary><p>content</p>\";\n        return element.firstElementChild;\n    }\n};\nfunction makeFocusableForeignObject() {\n    // Constructs <foreignObject width=\"30\" height=\"30\"><input type=\"text\"/></foreignObject>\n    // without raising a Trusted Types violation\n    var foreignObject = document.createElementNS(\"http://www.w3.org/2000/svg\", \"foreignObject\");\n    foreignObject.width.baseVal.value = 30;\n    foreignObject.height.baseVal.value = 30;\n    foreignObject.appendChild(document.createElement(\"input\"));\n    foreignObject.lastChild.type = \"text\";\n    return foreignObject;\n}\nfunction focusSvgForeignObjectHack(element) {\n    // Edge13, Edge14: foreignObject focus hack\n    // https://jsbin.com/kunehinugi/edit?html,js,output\n    // https://jsbin.com/fajagi/3/edit?html,js,output\n    var isSvgElement = element.ownerSVGElement || element.nodeName.toLowerCase() === \"svg\";\n    if (!isSvgElement) {\n        return false;\n    }\n    // inject and focus an <input> element into the SVG element to receive focus\n    var foreignObject = makeFocusableForeignObject();\n    element.appendChild(foreignObject);\n    var input = foreignObject.querySelector(\"input\");\n    input.focus();\n    // upon disabling the activeElement, IE and Edge\n    // will not shift focus to <body> like all the other\n    // browsers, but instead find the first focusable\n    // ancestor and shift focus to that\n    input.disabled = true;\n    // clean up\n    element.removeChild(foreignObject);\n    return true;\n}\nfunction generate(element) {\n    return '<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">' + element + \"</svg>\";\n}\nfunction focus(element) {\n    if (element.focus) {\n        return;\n    }\n    try {\n        HTMLElement.prototype.focus.call(element);\n    } catch (e) {\n        focusSvgForeignObjectHack(element);\n    }\n}\nfunction validate(element, focusTarget, _document) {\n    focus(focusTarget);\n    return _document.activeElement === focusTarget;\n}\nvar focusSvgFocusableAttribute = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate('<text focusable=\"true\">a</text>');\n        return element.querySelector(\"text\");\n    },\n    validate: validate\n};\nvar focusSvgTabindexAttribute = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate('<text tabindex=\"0\">a</text>');\n        return element.querySelector(\"text\");\n    },\n    validate: validate\n};\nvar focusSvgNegativeTabindexAttribute = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate('<text tabindex=\"-1\">a</text>');\n        return element.querySelector(\"text\");\n    },\n    validate: validate\n};\nvar focusSvgUseTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate([\n            '<g id=\"ally-test-target\"><a xlink:href=\"#void\"><text>link</text></a></g>',\n            '<use xlink:href=\"#ally-test-target\" x=\"0\" y=\"0\" tabindex=\"-1\" />'\n        ].join(\"\"));\n        return element.querySelector(\"use\");\n    },\n    validate: validate\n};\nvar focusSvgForeignobjectTabindex = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate('<foreignObject tabindex=\"-1\"><input type=\"text\" /></foreignObject>');\n        // Safari 8's querySelector() can't identify foreignObject, but getElementsByTagName() can\n        return element.querySelector(\"foreignObject\") || element.getElementsByTagName(\"foreignObject\")[0];\n    },\n    validate: validate\n};\n// Firefox seems to be handling the SVG-document-in-iframe creation asynchronously\n// and thereby produces a false negative test result. Thus the test is pointless\n// and we resort to UA sniffing once again.\n// see http://jsbin.com/vunadohoko/1/edit?js,console,output\nvar result$2 = Boolean(platform.is.GECKO && typeof SVGElement !== \"undefined\" && SVGElement.prototype.focus);\nfunction focusSvgInIframe() {\n    return result$2;\n}\nvar focusSvg = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.innerHTML = generate(\"\");\n        return element.firstChild;\n    },\n    validate: validate\n};\n// Firefox allows *any* value and treats invalid values like tabindex=\"-1\"\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\nvar focusTabindexTrailingCharacters = {\n    element: \"div\",\n    mutate: function mutate(element) {\n        element.setAttribute(\"tabindex\", \"3x\");\n    }\n};\nvar focusTable = {\n    element: \"table\",\n    mutate: function mutate(element, wrapper, _document) {\n        // IE9 has a problem replacing TBODY contents with innerHTML.\n        // https://stackoverflow.com/a/8097055/515124\n        // element.innerHTML = '<tr><td>cell</td></tr>';\n        var fragment = _document.createDocumentFragment();\n        fragment.innerHTML = \"<tr><td>cell</td></tr>\";\n        element.appendChild(fragment);\n    }\n};\nvar focusVideoWithoutControls = {\n    element: \"video\",\n    mutate: function mutate(element) {\n        try {\n            // invalid media file can trigger warning in console, data-uri to prevent HTTP request\n            element.setAttribute(\"src\", gif);\n        } catch (e) {\n        // IE9 may throw \"Error: Not implemented\"\n        }\n    }\n};\n// https://jsbin.com/vafaba/3/edit?html,js,console,output\nvar result$3 = platform.is.GECKO || platform.is.TRIDENT || platform.is.EDGE;\nfunction tabsequenceAreaAtImgPosition() {\n    return result$3;\n}\nvar testCallbacks = {\n    cssShadowPiercingDeepCombinator: cssShadowPiercingDeepCombinator,\n    focusInZeroDimensionObject: focusInZeroDimensionObject,\n    focusObjectSwf: focusObjectSwf,\n    focusSvgInIframe: focusSvgInIframe,\n    tabsequenceAreaAtImgPosition: tabsequenceAreaAtImgPosition\n};\nvar testDescriptions = {\n    focusAreaImgTabindex: focusAreaImgTabindex,\n    focusAreaTabindex: focusAreaTabindex,\n    focusAreaWithoutHref: focusAreaWithoutHref,\n    focusAudioWithoutControls: focusAudioWithoutControls,\n    focusBrokenImageMap: focusBrokenImageMap,\n    focusChildrenOfFocusableFlexbox: focusChildrenOfFocusableFlexbox,\n    focusFieldsetDisabled: focusFieldsetDisabled,\n    focusFieldset: focusFieldset,\n    focusFlexboxContainer: focusFlexboxContainer,\n    focusFormDisabled: focusFormDisabled,\n    focusImgIsmap: focusImgIsmap,\n    focusImgUsemapTabindex: focusImgUsemapTabindex,\n    focusInHiddenIframe: focusInHiddenIframe,\n    focusInvalidTabindex: focusInvalidTabindex,\n    focusLabelTabindex: focusLabelTabindex,\n    focusObjectSvg: focusObjectSvg,\n    focusObjectSvgHidden: focusObjectSvgHidden,\n    focusRedirectImgUsemap: focusRedirectImgUsemap,\n    focusRedirectLegend: focusRedirectLegend,\n    focusScrollBody: focusScrollBody,\n    focusScrollContainerWithoutOverflow: focusScrollContainerWithoutOverflow,\n    focusScrollContainer: focusScrollContainer,\n    focusSummary: focusSummary,\n    focusSvgFocusableAttribute: focusSvgFocusableAttribute,\n    focusSvgTabindexAttribute: focusSvgTabindexAttribute,\n    focusSvgNegativeTabindexAttribute: focusSvgNegativeTabindexAttribute,\n    focusSvgUseTabindex: focusSvgUseTabindex,\n    focusSvgForeignobjectTabindex: focusSvgForeignobjectTabindex,\n    focusSvg: focusSvg,\n    focusTabindexTrailingCharacters: focusTabindexTrailingCharacters,\n    focusTable: focusTable,\n    focusVideoWithoutControls: focusVideoWithoutControls\n};\nfunction executeTests() {\n    var results = detectFocus(testDescriptions);\n    Object.keys(testCallbacks).forEach(function(key) {\n        results[key] = testCallbacks[key]();\n    });\n    return results;\n}\nvar supportsCache = null;\nfunction _supports() {\n    if (supportsCache) {\n        return supportsCache;\n    }\n    supportsCache = cache$1.get();\n    if (!supportsCache.time) {\n        cache$1.set(executeTests());\n        supportsCache = cache$1.get();\n    }\n    return supportsCache;\n}\nvar supports = void 0;\n// https://www.w3.org/TR/html5/infrastructure.html#rules-for-parsing-integers\n// NOTE: all browsers agree to allow trailing spaces as well\nvar validIntegerPatternNoTrailing = /^\\s*(-|\\+)?[0-9]+\\s*$/;\nvar validIntegerPatternWithTrailing = /^\\s*(-|\\+)?[0-9]+.*$/;\nfunction isValidTabindex(context) {\n    if (!supports) {\n        supports = _supports();\n    }\n    var validIntegerPattern = supports.focusTabindexTrailingCharacters ? validIntegerPatternWithTrailing : validIntegerPatternNoTrailing;\n    var element = contextToElement({\n        label: \"is/valid-tabindex\",\n        resolveDocument: true,\n        context: context\n    });\n    // Edge 14 has a capitalization problem on SVG elements,\n    // see https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/9282058/\n    var hasTabindex = element.hasAttribute(\"tabindex\");\n    var hasTabIndex = element.hasAttribute(\"tabIndex\");\n    if (!hasTabindex && !hasTabIndex) {\n        return false;\n    }\n    // older Firefox and Internet Explorer don't support tabindex on SVG elements\n    var isSvgElement = element.ownerSVGElement || element.nodeName.toLowerCase() === \"svg\";\n    if (isSvgElement && !supports.focusSvgTabindexAttribute) {\n        return false;\n    }\n    // @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    if (supports.focusInvalidTabindex) {\n        return true;\n    }\n    // an element matches the tabindex selector even if its value is invalid\n    var tabindex = element.getAttribute(hasTabindex ? \"tabindex\" : \"tabIndex\");\n    // IE11 parses tabindex=\"\" as the value \"-32768\"\n    // @browser-issue Trident https://connect.microsoft.com/IE/feedback/details/1072965\n    if (tabindex === \"-32768\") {\n        return false;\n    }\n    return Boolean(tabindex && validIntegerPattern.test(tabindex));\n}\nfunction tabindexValue(element) {\n    if (!isValidTabindex(element)) {\n        return null;\n    }\n    // Edge 14 has a capitalization problem on SVG elements,\n    // see https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/9282058/\n    var hasTabindex = element.hasAttribute(\"tabindex\");\n    var attributeName = hasTabindex ? \"tabindex\" : \"tabIndex\";\n    // @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    var tabindex = parseInt(element.getAttribute(attributeName), 10);\n    return isNaN(tabindex) ? -1 : tabindex;\n}\n// this is a shared utility file for focus-relevant.js and tabbable.js\n// separate testing of this file's functions is not necessary,\n// as they're implicitly tested by way of the consumers\nfunction isUserModifyWritable(style) {\n    // https://www.w3.org/TR/1999/WD-css3-userint-19990916#user-modify\n    // https://github.com/medialize/ally.js/issues/17\n    var userModify = style.webkitUserModify || \"\";\n    return Boolean(userModify && userModify.indexOf(\"write\") !== -1);\n}\nfunction hasCssOverflowScroll(style) {\n    return [\n        style.getPropertyValue(\"overflow\"),\n        style.getPropertyValue(\"overflow-x\"),\n        style.getPropertyValue(\"overflow-y\")\n    ].some(function(overflow) {\n        return overflow === \"auto\" || overflow === \"scroll\";\n    });\n}\nfunction hasCssDisplayFlex(style) {\n    return style.display.indexOf(\"flex\") > -1;\n}\nfunction isScrollableContainer(element, nodeName, parentNodeName, parentStyle) {\n    if (nodeName !== \"div\" && nodeName !== \"span\") {\n        // Internet Explorer advances scrollable containers and bodies to focusable\n        // only if the scrollable container is <div> or <span> - this does *not*\n        // happen for <section>, <article>, …\n        return false;\n    }\n    if (parentNodeName && parentNodeName !== \"div\" && parentNodeName !== \"span\" && !hasCssOverflowScroll(parentStyle)) {\n        return false;\n    }\n    return element.offsetHeight < element.scrollHeight || element.offsetWidth < element.scrollWidth;\n}\nvar supports$1 = void 0;\nfunction isFocusRelevantRules() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, _ref$except = _ref.except, except = _ref$except === undefined ? {\n        flexbox: false,\n        scrollable: false,\n        shadow: false\n    } : _ref$except;\n    if (!supports$1) {\n        supports$1 = _supports();\n    }\n    var element = contextToElement({\n        label: \"is/focus-relevant\",\n        resolveDocument: true,\n        context: context\n    });\n    if (!except.shadow && element.shadowRoot) {\n        // a ShadowDOM host receives focus when the focus moves to its content\n        return true;\n    }\n    var nodeName = element.nodeName.toLowerCase();\n    if (nodeName === \"input\" && element.type === \"hidden\") {\n        // input[type=\"hidden\"] supports.cannot be focused\n        return false;\n    }\n    if (nodeName === \"input\" || nodeName === \"select\" || nodeName === \"button\" || nodeName === \"textarea\") {\n        return true;\n    }\n    if (nodeName === \"legend\" && supports$1.focusRedirectLegend) {\n        // specifics filtered in is/focusable\n        return true;\n    }\n    if (nodeName === \"label\") {\n        // specifics filtered in is/focusable\n        return true;\n    }\n    if (nodeName === \"area\") {\n        // specifics filtered in is/focusable\n        return true;\n    }\n    if (nodeName === \"a\" && element.hasAttribute(\"href\")) {\n        return true;\n    }\n    if (nodeName === \"object\" && element.hasAttribute(\"usemap\")) {\n        // object[usemap] is not focusable in any browser\n        return false;\n    }\n    if (nodeName === \"object\") {\n        var svgType = element.getAttribute(\"type\");\n        if (!supports$1.focusObjectSvg && svgType === \"image/svg+xml\") {\n            // object[type=\"image/svg+xml\"] is not focusable in Internet Explorer\n            return false;\n        } else if (!supports$1.focusObjectSwf && svgType === \"application/x-shockwave-flash\") {\n            // object[type=\"application/x-shockwave-flash\"] is not focusable in Internet Explorer 9\n            return false;\n        }\n    }\n    if (nodeName === \"iframe\" || nodeName === \"object\") {\n        // browsing context containers\n        return true;\n    }\n    if (nodeName === \"embed\" || nodeName === \"keygen\") {\n        // embed is considered focus-relevant but not focusable\n        // see https://github.com/medialize/ally.js/issues/82\n        return true;\n    }\n    if (element.hasAttribute(\"contenteditable\")) {\n        // also see CSS property user-modify below\n        return true;\n    }\n    if (nodeName === \"audio\" && (supports$1.focusAudioWithoutControls || element.hasAttribute(\"controls\"))) {\n        return true;\n    }\n    if (nodeName === \"video\" && (supports$1.focusVideoWithoutControls || element.hasAttribute(\"controls\"))) {\n        return true;\n    }\n    if (supports$1.focusSummary && nodeName === \"summary\") {\n        return true;\n    }\n    var validTabindex = isValidTabindex(element);\n    if (nodeName === \"img\" && element.hasAttribute(\"usemap\")) {\n        // Gecko, Trident and Edge do not allow an image with an image map and tabindex to be focused,\n        // it appears the tabindex is overruled so focus is still forwarded to the <map>\n        return validTabindex && supports$1.focusImgUsemapTabindex || supports$1.focusRedirectImgUsemap;\n    }\n    if (supports$1.focusTable && (nodeName === \"table\" || nodeName === \"td\")) {\n        // IE10-11 supports.can focus <table> and <td>\n        return true;\n    }\n    if (supports$1.focusFieldset && nodeName === \"fieldset\") {\n        // IE10-11 supports.can focus <fieldset>\n        return true;\n    }\n    var isSvgElement = nodeName === \"svg\";\n    var isSvgContent = element.ownerSVGElement;\n    var focusableAttribute = element.getAttribute(\"focusable\");\n    var tabindex = tabindexValue(element);\n    if (nodeName === \"use\" && tabindex !== null && !supports$1.focusSvgUseTabindex) {\n        // <use> cannot be made focusable by adding a tabindex attribute anywhere but Blink and WebKit\n        return false;\n    }\n    if (nodeName === \"foreignobject\") {\n        // <use> can only be made focusable in Blink and WebKit\n        return tabindex !== null && supports$1.focusSvgForeignobjectTabindex;\n    }\n    if (elementMatches(element, \"svg a\") && element.hasAttribute(\"xlink:href\")) {\n        return true;\n    }\n    if ((isSvgElement || isSvgContent) && element.focus && !supports$1.focusSvgNegativeTabindexAttribute && tabindex < 0) {\n        // Firefox 51 and 52 treat any natively tabbable SVG element with\n        // tabindex=\"-1\" as tabbable and everything else as inert\n        // see https://bugzilla.mozilla.org/show_bug.cgi?id=1302340\n        return false;\n    }\n    if (isSvgElement) {\n        return validTabindex || supports$1.focusSvg || supports$1.focusSvgInIframe || // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n        Boolean(supports$1.focusSvgFocusableAttribute && focusableAttribute && focusableAttribute === \"true\");\n    }\n    if (isSvgContent) {\n        if (supports$1.focusSvgTabindexAttribute && validTabindex) {\n            return true;\n        }\n        if (supports$1.focusSvgFocusableAttribute) {\n            // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n            return focusableAttribute === \"true\";\n        }\n    }\n    // https://www.w3.org/TR/html5/editing.html#sequential-focus-navigation-and-the-tabindex-attribute\n    if (validTabindex) {\n        return true;\n    }\n    var style = window.getComputedStyle(element, null);\n    if (isUserModifyWritable(style)) {\n        return true;\n    }\n    if (supports$1.focusImgIsmap && nodeName === \"img\" && element.hasAttribute(\"ismap\")) {\n        // IE10-11 considers the <img> in <a href><img ismap> focusable\n        // https://github.com/medialize/ally.js/issues/20\n        var hasLinkParent = getParents({\n            context: element\n        }).some(function(parent) {\n            return parent.nodeName.toLowerCase() === \"a\" && parent.hasAttribute(\"href\");\n        });\n        if (hasLinkParent) {\n            return true;\n        }\n    }\n    // https://github.com/medialize/ally.js/issues/21\n    if (!except.scrollable && supports$1.focusScrollContainer) {\n        if (supports$1.focusScrollContainerWithoutOverflow) {\n            // Internet Explorer does will consider the scrollable area focusable\n            // if the element is a <div> or a <span> and it is in fact scrollable,\n            // regardless of the CSS overflow property\n            if (isScrollableContainer(element, nodeName)) {\n                return true;\n            }\n        } else if (hasCssOverflowScroll(style)) {\n            // Firefox requires proper overflow setting, IE does not necessarily\n            // https://developer.mozilla.org/docs/Web/CSS/overflow\n            return true;\n        }\n    }\n    if (!except.flexbox && supports$1.focusFlexboxContainer && hasCssDisplayFlex(style)) {\n        // elements with display:flex are focusable in IE10-11\n        return true;\n    }\n    var parent = element.parentElement;\n    if (!except.scrollable && parent) {\n        var parentNodeName = parent.nodeName.toLowerCase();\n        var parentStyle = window.getComputedStyle(parent, null);\n        if (supports$1.focusScrollBody && isScrollableContainer(parent, nodeName, parentNodeName, parentStyle)) {\n            // scrollable bodies are focusable Internet Explorer\n            // https://github.com/medialize/ally.js/issues/21\n            return true;\n        }\n        // Children of focusable elements with display:flex are focusable in IE10-11\n        if (supports$1.focusChildrenOfFocusableFlexbox) {\n            if (hasCssDisplayFlex(parentStyle)) {\n                return true;\n            }\n        }\n    }\n    // NOTE: elements marked as inert are not focusable,\n    // but that property is not exposed to the DOM\n    // https://www.w3.org/TR/html5/editing.html#inert\n    return false;\n}\n// bind exceptions to an iterator callback\nisFocusRelevantRules.except = function() {\n    var except = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var isFocusRelevant = function isFocusRelevant(context) {\n        return isFocusRelevantRules({\n            context: context,\n            except: except\n        });\n    };\n    isFocusRelevant.rules = isFocusRelevantRules;\n    return isFocusRelevant;\n};\n// provide isFocusRelevant(context) as default iterator callback\nvar isFocusRelevant = isFocusRelevantRules.except({});\nfunction findIndex(array, callback) {\n    // attempt to use native or polyfilled Array#findIndex first\n    if (array.findIndex) {\n        return array.findIndex(callback);\n    }\n    var length = array.length;\n    // shortcut if the array is empty\n    if (length === 0) {\n        return -1;\n    }\n    // otherwise loop over array\n    for(var i = 0; i < length; i++){\n        if (callback(array[i], i, array)) {\n            return i;\n        }\n    }\n    return -1;\n}\nfunction getContentDocument(node) {\n    try {\n        // works on <object> and <iframe>\n        return node.contentDocument || // works on <object> and <iframe>\n        node.contentWindow && node.contentWindow.document || // works on <object> and <iframe> that contain SVG\n        node.getSVGDocument && node.getSVGDocument() || null;\n    } catch (e) {\n        // SecurityError: Failed to read the 'contentDocument' property from 'HTMLObjectElement'\n        // also IE may throw member not found exception e.g. on <object type=\"image/png\">\n        return null;\n    }\n}\nfunction getWindow(node) {\n    var _document = getDocument(node);\n    return _document.defaultView || window;\n}\nvar shadowPrefix = void 0;\nfunction selectInShadows(selector) {\n    if (typeof shadowPrefix !== \"string\") {\n        var operator = cssShadowPiercingDeepCombinator();\n        if (operator) {\n            shadowPrefix = \", html \" + operator + \" \";\n        }\n    }\n    if (!shadowPrefix) {\n        return selector;\n    }\n    return selector + shadowPrefix + selector.replace(/\\s*,\\s*/g, \",\").split(\",\").join(shadowPrefix);\n}\nvar selector = void 0;\nfunction findDocumentHostElement(_window) {\n    if (!selector) {\n        selector = selectInShadows(\"object, iframe\");\n    }\n    if (_window._frameElement !== undefined) {\n        return _window._frameElement;\n    }\n    _window._frameElement = null;\n    var potentialHosts = _window.parent.document.querySelectorAll(selector);\n    [].some.call(potentialHosts, function(element) {\n        var _document = getContentDocument(element);\n        if (_document !== _window.document) {\n            return false;\n        }\n        _window._frameElement = element;\n        return true;\n    });\n    return _window._frameElement;\n}\nfunction getFrameElement(element) {\n    var _window = getWindow(element);\n    if (!_window.parent || _window.parent === _window) {\n        // if there is no parent browsing context,\n        // we're not going to get a frameElement either way\n        return null;\n    }\n    try {\n        // see https://developer.mozilla.org/docs/Web/API/Window/frameElement\n        // does not work within <embed> anywhere, and not within in <object> in IE\n        return _window.frameElement || findDocumentHostElement(_window);\n    } catch (e) {\n        return null;\n    }\n}\n// https://www.w3.org/TR/html5/rendering.html#being-rendered\n// <area> is not rendered, but we *consider* it visible to simplfiy this function's usage\nvar notRenderedElementsPattern = /^(area)$/;\nfunction computedStyle(element, property) {\n    return window.getComputedStyle(element, null).getPropertyValue(property);\n}\nfunction notDisplayed(_path) {\n    return _path.some(function(element) {\n        // display:none is not visible (optimized away at layout)\n        return computedStyle(element, \"display\") === \"none\";\n    });\n}\nfunction notVisible(_path) {\n    // https://github.com/jquery/jquery-ui/blob/master/ui/core.js#L109-L114\n    // NOTE: a nested element can reverse visibility:hidden|collapse by explicitly setting visibility:visible\n    // NOTE: visibility can be [\"\", \"visible\", \"hidden\", \"collapse\"]\n    var hidden = findIndex(_path, function(element) {\n        var visibility = computedStyle(element, \"visibility\");\n        return visibility === \"hidden\" || visibility === \"collapse\";\n    });\n    if (hidden === -1) {\n        // there is no hidden element\n        return false;\n    }\n    var visible = findIndex(_path, function(element) {\n        return computedStyle(element, \"visibility\") === \"visible\";\n    });\n    if (visible === -1) {\n        // there is no visible element (but a hidden element)\n        return true;\n    }\n    if (hidden < visible) {\n        // there is a hidden element and it's closer than the first visible element\n        return true;\n    }\n    // there may be a hidden element, but the closest element is visible\n    return false;\n}\nfunction collapsedParent(_path) {\n    var offset = 1;\n    if (_path[0].nodeName.toLowerCase() === \"summary\") {\n        offset = 2;\n    }\n    return _path.slice(offset).some(function(element) {\n        // \"content children\" of a closed details element are not visible\n        return element.nodeName.toLowerCase() === \"details\" && element.open === false;\n    });\n}\nfunction isVisibleRules() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, _ref$except = _ref.except, except = _ref$except === undefined ? {\n        notRendered: false,\n        cssDisplay: false,\n        cssVisibility: false,\n        detailsElement: false,\n        browsingContext: false\n    } : _ref$except;\n    var element = contextToElement({\n        label: \"is/visible\",\n        resolveDocument: true,\n        context: context\n    });\n    var nodeName = element.nodeName.toLowerCase();\n    if (!except.notRendered && notRenderedElementsPattern.test(nodeName)) {\n        return true;\n    }\n    var _path = getParents({\n        context: element\n    });\n    // in Internet Explorer <audio> has a default display: none, where others have display: inline\n    // but IE allows focusing <audio style=\"display:none\">, but not <div display:none><audio>\n    // this is irrelevant to other browsers, as the controls attribute is required to make <audio> focusable\n    var isAudioWithoutControls = nodeName === \"audio\" && !element.hasAttribute(\"controls\");\n    if (!except.cssDisplay && notDisplayed(isAudioWithoutControls ? _path.slice(1) : _path)) {\n        return false;\n    }\n    if (!except.cssVisibility && notVisible(_path)) {\n        return false;\n    }\n    if (!except.detailsElement && collapsedParent(_path)) {\n        return false;\n    }\n    if (!except.browsingContext) {\n        // elements within a browsing context are affected by the\n        // browsing context host element's visibility and tabindex\n        var frameElement = getFrameElement(element);\n        var _isVisible = isVisibleRules.except(except);\n        if (frameElement && !_isVisible(frameElement)) {\n            return false;\n        }\n    }\n    return true;\n}\n// bind exceptions to an iterator callback\nisVisibleRules.except = function() {\n    var except = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var isVisible = function isVisible(context) {\n        return isVisibleRules({\n            context: context,\n            except: except\n        });\n    };\n    isVisible.rules = isVisibleRules;\n    return isVisible;\n};\n// provide isVisible(context) as default iterator callback\nvar isVisible = isVisibleRules.except({});\nfunction getMapByName(name, _document) {\n    // apparently getElementsByName() also considers id attribute in IE & opera\n    // https://developer.mozilla.org/docs/Web/API/Document/getElementsByName\n    var map = _document.querySelector('map[name=\"' + (0, _cssescape.default)(name) + '\"]');\n    return map || null;\n}\nfunction getImageOfArea(element) {\n    var map = element.parentElement;\n    if (!map.name || map.nodeName.toLowerCase() !== \"map\") {\n        return null;\n    }\n    // NOTE: image maps can also be applied to <object> with image content,\n    // but no browser supports this at the moment\n    // HTML5 specifies HTMLMapElement.images to be an HTMLCollection of all\n    // <img> and <object> referencing the <map> element, but no browser implements this\n    //   https://www.w3.org/TR/html5/embedded-content-0.html#the-map-element\n    //   https://developer.mozilla.org/docs/Web/API/HTMLMapElement\n    // the image must be valid and loaded for the map to take effect\n    var _document = getDocument(element);\n    return _document.querySelector('img[usemap=\"#' + (0, _cssescape.default)(map.name) + '\"]') || null;\n}\nvar supports$2 = void 0;\n// https://developer.mozilla.org/docs/Web/HTML/Element/map\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\n// https://github.com/jquery/jquery-ui/blob/master/ui/core.js#L88-L107\nfunction isValidArea(context) {\n    if (!supports$2) {\n        supports$2 = _supports();\n    }\n    var element = contextToElement({\n        label: \"is/valid-area\",\n        context: context\n    });\n    var nodeName = element.nodeName.toLowerCase();\n    if (nodeName !== \"area\") {\n        return false;\n    }\n    var hasTabindex = element.hasAttribute(\"tabindex\");\n    if (!supports$2.focusAreaTabindex && hasTabindex) {\n        // Blink and WebKit do not consider <area tabindex=\"-1\" href=\"#void\"> focusable\n        return false;\n    }\n    var img = getImageOfArea(element);\n    if (!img || !isVisible(img)) {\n        return false;\n    }\n    // Firefox only allows fully loaded images to reference image maps\n    // https://stereochro.me/ideas/detecting-broken-images-js\n    if (!supports$2.focusBrokenImageMap && (!img.complete || !img.naturalHeight || img.offsetWidth <= 0 || img.offsetHeight <= 0)) {\n        return false;\n    }\n    // Firefox supports.can focus area elements even if they don't have an href attribute\n    if (!supports$2.focusAreaWithoutHref && !element.href) {\n        // Internet explorer supports.can focus area elements without href if either\n        // the area element or the image element has a tabindex attribute\n        return supports$2.focusAreaTabindex && hasTabindex || supports$2.focusAreaImgTabindex && img.hasAttribute(\"tabindex\");\n    }\n    // https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\n    var childOfInteractive = getParents({\n        context: img\n    }).slice(1).some(function(_element) {\n        var name = _element.nodeName.toLowerCase();\n        return name === \"button\" || name === \"a\";\n    });\n    if (childOfInteractive) {\n        return false;\n    }\n    return true;\n}\nvar supports$3 = void 0;\n// https://www.w3.org/TR/html5/disabled-elements.html#concept-element-disabled\nvar disabledElementsPattern = void 0;\nvar disabledElements = {\n    input: true,\n    select: true,\n    textarea: true,\n    button: true,\n    fieldset: true,\n    form: true\n};\nfunction isNativeDisabledSupported(context) {\n    if (!supports$3) {\n        supports$3 = _supports();\n        if (supports$3.focusFieldsetDisabled) {\n            delete disabledElements.fieldset;\n        }\n        if (supports$3.focusFormDisabled) {\n            delete disabledElements.form;\n        }\n        disabledElementsPattern = new RegExp(\"^(\" + Object.keys(disabledElements).join(\"|\") + \")$\");\n    }\n    var element = contextToElement({\n        label: \"is/native-disabled-supported\",\n        context: context\n    });\n    var nodeName = element.nodeName.toLowerCase();\n    return Boolean(disabledElementsPattern.test(nodeName));\n}\nvar supports$4 = void 0;\nfunction isDisabledFieldset(element) {\n    var nodeName = element.nodeName.toLowerCase();\n    return nodeName === \"fieldset\" && element.disabled;\n}\nfunction isDisabledForm(element) {\n    var nodeName = element.nodeName.toLowerCase();\n    return nodeName === \"form\" && element.disabled;\n}\nfunction isDisabled(context) {\n    if (!supports$4) {\n        supports$4 = _supports();\n    }\n    var element = contextToElement({\n        label: \"is/disabled\",\n        context: context\n    });\n    if (element.hasAttribute(\"data-ally-disabled\")) {\n        // treat ally's element/disabled like the DOM native element.disabled\n        return true;\n    }\n    if (!isNativeDisabledSupported(element)) {\n        // non-form elements do not support the disabled attribute\n        return false;\n    }\n    if (element.disabled) {\n        // the element itself is disabled\n        return true;\n    }\n    var parents = getParents({\n        context: element\n    });\n    if (parents.some(isDisabledFieldset)) {\n        // a parental <fieldset> is disabld and inherits the state onto this element\n        return true;\n    }\n    if (!supports$4.focusFormDisabled && parents.some(isDisabledForm)) {\n        // a parental <form> is disabld and inherits the state onto this element\n        return true;\n    }\n    return false;\n}\nfunction isOnlyTabbableRules() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, _ref$except = _ref.except, except = _ref$except === undefined ? {\n        onlyFocusableBrowsingContext: false,\n        visible: false\n    } : _ref$except;\n    var element = contextToElement({\n        label: \"is/only-tabbable\",\n        resolveDocument: true,\n        context: context\n    });\n    if (!except.visible && !isVisible(element)) {\n        return false;\n    }\n    if (!except.onlyFocusableBrowsingContext && (platform.is.GECKO || platform.is.TRIDENT || platform.is.EDGE)) {\n        var frameElement = getFrameElement(element);\n        if (frameElement) {\n            if (tabindexValue(frameElement) < 0) {\n                // iframe[tabindex=\"-1\"] and object[tabindex=\"-1\"] inherit the\n                // tabbable demotion onto elements of their browsing contexts\n                return false;\n            }\n        }\n    }\n    var nodeName = element.nodeName.toLowerCase();\n    var tabindex = tabindexValue(element);\n    if (nodeName === \"label\" && platform.is.GECKO) {\n        // Firefox cannot focus, but tab to: label[tabindex=0]\n        return tabindex !== null && tabindex >= 0;\n    }\n    // SVG Elements were keyboard focusable but not script focusable before Firefox 51.\n    // Firefox 51 added the focus management DOM API (.focus and .blur) to SVGElement,\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=778654\n    if (platform.is.GECKO && element.ownerSVGElement && !element.focus) {\n        if (nodeName === \"a\" && element.hasAttribute(\"xlink:href\")) {\n            // any focusable child of <svg> cannot be focused, but tabbed to\n            if (platform.is.GECKO) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n// bind exceptions to an iterator callback\nisOnlyTabbableRules.except = function() {\n    var except = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var isOnlyTabbable = function isOnlyTabbable(context) {\n        return isOnlyTabbableRules({\n            context: context,\n            except: except\n        });\n    };\n    isOnlyTabbable.rules = isOnlyTabbableRules;\n    return isOnlyTabbable;\n};\n// provide isOnlyTabbable(context) as default iterator callback\nvar isOnlyTabbable = isOnlyTabbableRules.except({});\nvar supports$5 = void 0;\nfunction isOnlyFocusRelevant(element) {\n    var nodeName = element.nodeName.toLowerCase();\n    if (nodeName === \"embed\" || nodeName === \"keygen\") {\n        // embed is considered focus-relevant but not focusable\n        // see https://github.com/medialize/ally.js/issues/82\n        return true;\n    }\n    var _tabindex = tabindexValue(element);\n    if (element.shadowRoot && _tabindex === null) {\n        // ShadowDOM host elements *may* receive focus\n        // even though they are not considered focuable\n        return true;\n    }\n    if (nodeName === \"label\") {\n        // <label tabindex=\"0\"> is only tabbable in Firefox, not script-focusable\n        // there's no way to make an element focusable other than by adding a tabindex,\n        // and focus behavior of the label element seems hard-wired to ignore tabindex\n        // in some browsers (like Gecko, Blink and WebKit)\n        return !supports$5.focusLabelTabindex || _tabindex === null;\n    }\n    if (nodeName === \"legend\") {\n        return _tabindex === null;\n    }\n    if (supports$5.focusSvgFocusableAttribute && (element.ownerSVGElement || nodeName === \"svg\")) {\n        // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n        var focusableAttribute = element.getAttribute(\"focusable\");\n        return focusableAttribute && focusableAttribute === \"false\";\n    }\n    if (nodeName === \"img\" && element.hasAttribute(\"usemap\")) {\n        // Gecko, Trident and Edge do not allow an image with an image map and tabindex to be focused,\n        // it appears the tabindex is overruled so focus is still forwarded to the <map>\n        return _tabindex === null || !supports$5.focusImgUsemapTabindex;\n    }\n    if (nodeName === \"area\") {\n        // all <area>s are considered relevant,\n        // but only the valid <area>s are focusable\n        return !isValidArea(element);\n    }\n    return false;\n}\nfunction isFocusableRules() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, _ref$except = _ref.except, except = _ref$except === undefined ? {\n        disabled: false,\n        visible: false,\n        onlyTabbable: false\n    } : _ref$except;\n    if (!supports$5) {\n        supports$5 = _supports();\n    }\n    var _isOnlyTabbable = isOnlyTabbable.rules.except({\n        onlyFocusableBrowsingContext: true,\n        visible: except.visible\n    });\n    var element = contextToElement({\n        label: \"is/focusable\",\n        resolveDocument: true,\n        context: context\n    });\n    var focusRelevant = isFocusRelevant.rules({\n        context: element,\n        except: except\n    });\n    if (!focusRelevant || isOnlyFocusRelevant(element)) {\n        return false;\n    }\n    if (!except.disabled && isDisabled(element)) {\n        return false;\n    }\n    if (!except.onlyTabbable && _isOnlyTabbable(element)) {\n        // some elements may be keyboard focusable, but not script focusable\n        return false;\n    }\n    // elements that are not rendered, cannot be focused\n    if (!except.visible) {\n        var visibilityOptions = {\n            context: element,\n            except: {}\n        };\n        if (supports$5.focusInHiddenIframe) {\n            // WebKit and Blink can focus content in hidden <iframe> and <object>\n            visibilityOptions.except.browsingContext = true;\n        }\n        if (supports$5.focusObjectSvgHidden) {\n            // Blink allows focusing the object element, even if it has visibility: hidden;\n            // @browser-issue Blink https://code.google.com/p/chromium/issues/detail?id=586191\n            var _nodeName2 = element.nodeName.toLowerCase();\n            if (_nodeName2 === \"object\") {\n                visibilityOptions.except.cssVisibility = true;\n            }\n        }\n        if (!isVisible.rules(visibilityOptions)) {\n            return false;\n        }\n    }\n    var frameElement = getFrameElement(element);\n    if (frameElement) {\n        var _nodeName = frameElement.nodeName.toLowerCase();\n        if (_nodeName === \"object\" && !supports$5.focusInZeroDimensionObject) {\n            if (!frameElement.offsetWidth || !frameElement.offsetHeight) {\n                // WebKit can not focus content in <object> if it doesn't have dimensions\n                return false;\n            }\n        }\n    }\n    var nodeName = element.nodeName.toLowerCase();\n    if (nodeName === \"svg\" && supports$5.focusSvgInIframe && !frameElement && element.getAttribute(\"tabindex\") === null) {\n        return false;\n    }\n    return true;\n}\n// bind exceptions to an iterator callback\nisFocusableRules.except = function() {\n    var except = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var isFocusable = function isFocusable(context) {\n        return isFocusableRules({\n            context: context,\n            except: except\n        });\n    };\n    isFocusable.rules = isFocusableRules;\n    return isFocusable;\n};\n// provide isFocusRelevant(context) as default iterator callback\nvar isFocusable = isFocusableRules.except({});\nfunction createFilter(condition) {\n    // see https://developer.mozilla.org/docs/Web/API/NodeFilter\n    var filter = function filter(node) {\n        if (node.shadowRoot) {\n            // return ShadowRoot elements regardless of them being focusable,\n            // so they can be walked recursively later\n            return NodeFilter.FILTER_ACCEPT;\n        }\n        if (condition(node)) {\n            // finds elements that could have been found by document.querySelectorAll()\n            return NodeFilter.FILTER_ACCEPT;\n        }\n        return NodeFilter.FILTER_SKIP;\n    };\n    // IE requires a function, Browsers require {acceptNode: function}\n    // see http://www.bennadel.com/blog/2607-finding-html-comment-nodes-in-the-dom-using-treewalker.htm\n    filter.acceptNode = filter;\n    return filter;\n}\nvar PossiblyFocusableFilter = createFilter(isFocusRelevant);\nfunction queryFocusableStrict() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, includeContext = _ref.includeContext, includeOnlyTabbable = _ref.includeOnlyTabbable, strategy = _ref.strategy;\n    if (!context) {\n        context = document.documentElement;\n    }\n    var _isFocusable = isFocusable.rules.except({\n        onlyTabbable: includeOnlyTabbable\n    });\n    var _document = getDocument(context);\n    // see https://developer.mozilla.org/docs/Web/API/Document/createTreeWalker\n    var walker = _document.createTreeWalker(context, NodeFilter.SHOW_ELEMENT, strategy === \"all\" ? PossiblyFocusableFilter : createFilter(_isFocusable), false);\n    var list = [];\n    while(walker.nextNode()){\n        if (walker.currentNode.shadowRoot) {\n            if (_isFocusable(walker.currentNode)) {\n                list.push(walker.currentNode);\n            }\n            list = list.concat(queryFocusableStrict({\n                context: walker.currentNode.shadowRoot,\n                includeOnlyTabbable: includeOnlyTabbable,\n                strategy: strategy\n            }));\n        } else {\n            list.push(walker.currentNode);\n        }\n    }\n    // add context if requested and focusable\n    if (includeContext) {\n        if (strategy === \"all\") {\n            if (isFocusRelevant(context)) {\n                list.unshift(context);\n            }\n        } else if (_isFocusable(context)) {\n            list.unshift(context);\n        }\n    }\n    return list;\n}\n// NOTE: this selector MUST *never* be used directly,\nvar supports$6 = void 0;\nvar selector$1 = void 0;\nfunction selector$2() {\n    if (!supports$6) {\n        supports$6 = _supports();\n    }\n    if (typeof selector$1 === \"string\") {\n        return selector$1;\n    }\n    // https://www.w3.org/TR/html5/editing.html#sequential-focus-navigation-and-the-tabindex-attribute\n    selector$1 = \"\" + // IE11 supports.can focus <table> and <td>\n    (supports$6.focusTable ? \"table, td,\" : \"\") + // IE11 supports.can focus <fieldset>\n    (supports$6.focusFieldset ? \"fieldset,\" : \"\") + // Namespace problems of [xlink:href] explained in https://stackoverflow.com/a/23047888/515124\n    // svg a[*|href] does not match in IE9, but since we're filtering\n    // through is/focusable we can include all <a> from SVG\n    \"svg a,\" + // may behave as 'svg, svg *,' in chrome as *every* svg element with a focus event listener is focusable\n    // navigational elements\n    \"a[href],\" + // validity determined by is/valid-area.js\n    \"area[href],\" + // validity determined by is/disabled.js\n    \"input, select, textarea, button,\" + // browsing context containers\n    \"iframe, object, embed,\" + // interactive content\n    \"keygen,\" + (supports$6.focusAudioWithoutControls ? \"audio,\" : \"audio[controls],\") + (supports$6.focusVideoWithoutControls ? \"video,\" : \"video[controls],\") + (supports$6.focusSummary ? \"summary,\" : \"\") + // validity determined by is/valid-tabindex.js\n    \"[tabindex],\" + // editing hosts\n    \"[contenteditable]\";\n    // where ShadowDOM is supported, we also want the shadowed focusable elements (via \">>>\" or \"/deep/\")\n    selector$1 = selectInShadows(selector$1);\n    return selector$1;\n}\nfunction queryFocusableQuick() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, includeContext = _ref.includeContext, includeOnlyTabbable = _ref.includeOnlyTabbable;\n    var _selector = selector$2();\n    var elements = context.querySelectorAll(_selector);\n    // the selector potentially matches more than really is focusable\n    var _isFocusable = isFocusable.rules.except({\n        onlyTabbable: includeOnlyTabbable\n    });\n    var result = [].filter.call(elements, _isFocusable);\n    // add context if requested and focusable\n    if (includeContext && _isFocusable(context)) {\n        result.unshift(context);\n    }\n    return result;\n}\nfunction queryFocusable() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, includeContext = _ref.includeContext, includeOnlyTabbable = _ref.includeOnlyTabbable, _ref$strategy = _ref.strategy, strategy = _ref$strategy === undefined ? \"quick\" : _ref$strategy;\n    var element = contextToElement({\n        label: \"query/focusable\",\n        resolveDocument: true,\n        defaultToDocument: true,\n        context: context\n    });\n    var options = {\n        context: element,\n        includeContext: includeContext,\n        includeOnlyTabbable: includeOnlyTabbable,\n        strategy: strategy\n    };\n    if (strategy === \"quick\") {\n        return queryFocusableQuick(options);\n    } else if (strategy === \"strict\" || strategy === \"all\") {\n        return queryFocusableStrict(options);\n    }\n    throw new TypeError('query/focusable requires option.strategy to be one of [\"quick\", \"strict\", \"all\"]');\n}\nvar supports$7 = void 0;\n// Internet Explorer 11 considers fieldset, table, td focusable, but not tabbable\n// Internet Explorer 11 considers body to have [tabindex=0], but does not allow tabbing to it\nvar focusableElementsPattern = /^(fieldset|table|td|body)$/;\nfunction isTabbableRules() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, _ref$except = _ref.except, except = _ref$except === undefined ? {\n        flexbox: false,\n        scrollable: false,\n        shadow: false,\n        visible: false,\n        onlyTabbable: false\n    } : _ref$except;\n    if (!supports$7) {\n        supports$7 = _supports();\n    }\n    var element = contextToElement({\n        label: \"is/tabbable\",\n        resolveDocument: true,\n        context: context\n    });\n    if (platform.is.BLINK && platform.is.ANDROID && platform.majorVersion > 42) {\n        // External keyboard support worked fine in CHrome 42, but stopped working in Chrome 45.\n        // The on-screen keyboard does not provide a way to focus the next input element (like iOS does).\n        // That leaves us with no option to advance focus by keyboard, ergo nothing is tabbable (keyboard focusable).\n        return false;\n    }\n    var frameElement = getFrameElement(element);\n    if (frameElement) {\n        if (platform.is.WEBKIT && platform.is.IOS) {\n            // iOS only does not consider anything from another browsing context keyboard focusable\n            return false;\n        }\n        // iframe[tabindex=\"-1\"] and object[tabindex=\"-1\"] inherit the\n        // tabbable demotion onto elements of their browsing contexts\n        if (tabindexValue(frameElement) < 0) {\n            return false;\n        }\n        if (!except.visible && (platform.is.BLINK || platform.is.WEBKIT) && !isVisible(frameElement)) {\n            // Blink and WebKit consider elements in hidden browsing contexts focusable, but not tabbable\n            return false;\n        }\n        // Webkit and Blink don't consider anything in <object> tabbable\n        // Blink fixed that fixed in Chrome 54, Opera 41\n        var frameNodeName = frameElement.nodeName.toLowerCase();\n        if (frameNodeName === \"object\") {\n            var isFixedBlink = platform.name === \"Chrome\" && platform.majorVersion >= 54 || platform.name === \"Opera\" && platform.majorVersion >= 41;\n            if (platform.is.WEBKIT || platform.is.BLINK && !isFixedBlink) {\n                return false;\n            }\n        }\n    }\n    var nodeName = element.nodeName.toLowerCase();\n    var _tabindex = tabindexValue(element);\n    var tabindex = _tabindex === null ? null : _tabindex >= 0;\n    if (platform.is.EDGE && platform.majorVersion >= 14 && frameElement && element.ownerSVGElement && _tabindex < 0) {\n        // Edge 14+ considers <a xlink:href=\"…\" tabindex=\"-1\"> keyboard focusable\n        // if the element is in a nested browsing context\n        return true;\n    }\n    var hasTabbableTabindexOrNone = tabindex !== false;\n    var hasTabbableTabindex = _tabindex !== null && _tabindex >= 0;\n    // NOTE: Firefox 31 considers [contenteditable] to have [tabindex=-1], but allows tabbing to it\n    // fixed in Firefox 40 the latest - https://bugzilla.mozilla.org/show_bug.cgi?id=1185657\n    if (element.hasAttribute(\"contenteditable\")) {\n        // tabbing can still be disabled by explicitly providing [tabindex=\"-1\"]\n        return hasTabbableTabindexOrNone;\n    }\n    if (focusableElementsPattern.test(nodeName) && tabindex !== true) {\n        return false;\n    }\n    if (platform.is.WEBKIT && platform.is.IOS) {\n        // iOS only considers a hand full of elements tabbable (keyboard focusable)\n        // this holds true even with external keyboards\n        var potentiallyTabbable = nodeName === \"input\" && element.type === \"text\" || element.type === \"password\" || nodeName === \"select\" || nodeName === \"textarea\" || element.hasAttribute(\"contenteditable\");\n        if (!potentiallyTabbable) {\n            var style = window.getComputedStyle(element, null);\n            potentiallyTabbable = isUserModifyWritable(style);\n        }\n        if (!potentiallyTabbable) {\n            return false;\n        }\n    }\n    if (nodeName === \"use\" && _tabindex !== null) {\n        if (platform.is.BLINK || platform.is.WEBKIT && platform.majorVersion === 9) {\n            // In Chrome and Safari 9 the <use> element is keyboard focusable even for tabindex=\"-1\"\n            return true;\n        }\n    }\n    if (elementMatches(element, \"svg a\") && element.hasAttribute(\"xlink:href\")) {\n        if (hasTabbableTabindexOrNone) {\n            // in Trident and Gecko SVGElement does not handle the tabIndex property properly\n            return true;\n        }\n        if (element.focus && !supports$7.focusSvgNegativeTabindexAttribute) {\n            // Firefox 51 and 52 treat any natively tabbable SVG element with\n            // tabindex=\"-1\" as tabbable and everything else as inert\n            // see https://bugzilla.mozilla.org/show_bug.cgi?id=1302340\n            return true;\n        }\n    }\n    if (nodeName === \"svg\" && supports$7.focusSvgInIframe && hasTabbableTabindexOrNone) {\n        return true;\n    }\n    if (platform.is.TRIDENT || platform.is.EDGE) {\n        if (nodeName === \"svg\") {\n            if (supports$7.focusSvg) {\n                // older Internet Explorers consider <svg> keyboard focusable\n                // unless they have focsable=\"false\", but then they wouldn't\n                // be focusable and thus not even reach this filter\n                return true;\n            }\n            // elements that have [focusable] are automatically keyboard focusable regardless of the attribute's value\n            return element.hasAttribute(\"focusable\") || hasTabbableTabindex;\n        }\n        if (element.ownerSVGElement) {\n            if (supports$7.focusSvgTabindexAttribute && hasTabbableTabindex) {\n                return true;\n            }\n            // elements that have [focusable] are automatically keyboard focusable regardless of the attribute's value\n            return element.hasAttribute(\"focusable\");\n        }\n    }\n    if (element.tabIndex === undefined) {\n        return Boolean(except.onlyTabbable);\n    }\n    if (nodeName === \"audio\") {\n        if (!element.hasAttribute(\"controls\")) {\n            // In Internet Explorer the <audio> element is focusable, but not tabbable, and tabIndex property is wrong\n            return false;\n        } else if (platform.is.BLINK) {\n            // In Chrome <audio controls tabindex=\"-1\"> remains keyboard focusable\n            return true;\n        }\n    }\n    if (nodeName === \"video\") {\n        if (!element.hasAttribute(\"controls\")) {\n            if (platform.is.TRIDENT || platform.is.EDGE) {\n                // In Internet Explorer and Edge the <video> element is focusable, but not tabbable, and tabIndex property is wrong\n                return false;\n            }\n        } else if (platform.is.BLINK || platform.is.GECKO) {\n            // In Chrome and Firefox <video controls tabindex=\"-1\"> remains keyboard focusable\n            return true;\n        }\n    }\n    if (nodeName === \"object\") {\n        if (platform.is.BLINK || platform.is.WEBKIT) {\n            // In all Blink and WebKit based browsers <embed> and <object> are never keyboard focusable, even with tabindex=\"0\" set\n            return false;\n        }\n    }\n    if (nodeName === \"iframe\") {\n        // In Internet Explorer all iframes are only focusable\n        // In WebKit, Blink and Gecko iframes may be tabbable depending on content.\n        // Since we can't reliably investigate iframe documents because of the\n        // SameOriginPolicy, we're declaring everything only focusable.\n        return false;\n    }\n    if (!except.scrollable && platform.is.GECKO) {\n        // Firefox considers scrollable containers keyboard focusable,\n        // even though their tabIndex property is -1\n        var _style = window.getComputedStyle(element, null);\n        if (hasCssOverflowScroll(_style)) {\n            return hasTabbableTabindexOrNone;\n        }\n    }\n    if (platform.is.TRIDENT || platform.is.EDGE) {\n        // IE and Edge degrade <area> to script focusable, if the image\n        // using the <map> has been given tabindex=\"-1\"\n        if (nodeName === \"area\") {\n            var img = getImageOfArea(element);\n            if (img && tabindexValue(img) < 0) {\n                return false;\n            }\n        }\n        var _style2 = window.getComputedStyle(element, null);\n        if (isUserModifyWritable(_style2)) {\n            // prevent being swallowed by the overzealous isScrollableContainer() below\n            return element.tabIndex >= 0;\n        }\n        if (!except.flexbox && hasCssDisplayFlex(_style2)) {\n            if (_tabindex !== null) {\n                return hasTabbableTabindex;\n            }\n            return isFocusRelevantWithoutFlexbox(element) && isTabbableWithoutFlexbox(element);\n        }\n        // IE considers scrollable containers script focusable only,\n        // even though their tabIndex property is 0\n        if (isScrollableContainer(element, nodeName)) {\n            return false;\n        }\n        var parent = element.parentElement;\n        if (parent) {\n            var parentNodeName = parent.nodeName.toLowerCase();\n            var parentStyle = window.getComputedStyle(parent, null);\n            // IE considers scrollable bodies script focusable only,\n            if (isScrollableContainer(parent, nodeName, parentNodeName, parentStyle)) {\n                return false;\n            }\n            // Children of focusable elements with display:flex are focusable in IE10-11,\n            // even though their tabIndex property suggests otherwise\n            if (hasCssDisplayFlex(parentStyle)) {\n                // value of tabindex takes precedence\n                return hasTabbableTabindex;\n            }\n        }\n    }\n    // https://www.w3.org/WAI/PF/aria-practices/#focus_tabindex\n    return element.tabIndex >= 0;\n}\n// bind exceptions to an iterator callback\nisTabbableRules.except = function() {\n    var except = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var isTabbable = function isTabbable(context) {\n        return isTabbableRules({\n            context: context,\n            except: except\n        });\n    };\n    isTabbable.rules = isTabbableRules;\n    return isTabbable;\n};\nvar isFocusRelevantWithoutFlexbox = isFocusRelevant.rules.except({\n    flexbox: true\n});\nvar isTabbableWithoutFlexbox = isTabbableRules.except({\n    flexbox: true\n});\n// provide isTabbable(context) as default iterator callback\nvar isTabbable = isTabbableRules.except({});\nfunction queryTabbable() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, includeContext = _ref.includeContext, includeOnlyTabbable = _ref.includeOnlyTabbable, strategy = _ref.strategy;\n    var _isTabbable = isTabbable.rules.except({\n        onlyTabbable: includeOnlyTabbable\n    });\n    return queryFocusable({\n        context: context,\n        includeContext: includeContext,\n        includeOnlyTabbable: includeOnlyTabbable,\n        strategy: strategy\n    }).filter(_isTabbable);\n}\n// sorts a list of elements according to their order in the DOM\nfunction compareDomPosition(a, b) {\n    return a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n}\nfunction sortDomOrder(elements) {\n    return elements.sort(compareDomPosition);\n}\nfunction getFirstSuccessorOffset(list, target) {\n    // find the first element that comes AFTER the target element\n    return findIndex(list, function(element) {\n        return target.compareDocumentPosition(element) & Node.DOCUMENT_POSITION_FOLLOWING;\n    });\n}\nfunction findInsertionOffsets(list, elements, resolveElement) {\n    // instead of mutating the elements list directly, remember position and map\n    // to inject later, when we can do this more efficiently\n    var insertions = [];\n    elements.forEach(function(element) {\n        var replace = true;\n        var offset = list.indexOf(element);\n        if (offset === -1) {\n            // element is not in target list\n            offset = getFirstSuccessorOffset(list, element);\n            replace = false;\n        }\n        if (offset === -1) {\n            // there is no successor in the tabsequence,\n            // meaning the image must be the last element\n            offset = list.length;\n        }\n        // allow the consumer to replace the injected element\n        var injections = nodeArray(resolveElement ? resolveElement(element) : element);\n        if (!injections.length) {\n            // we can't inject zero elements\n            return;\n        }\n        insertions.push({\n            offset: offset,\n            replace: replace,\n            elements: injections\n        });\n    });\n    return insertions;\n}\nfunction insertElementsAtOffsets(list, insertions) {\n    // remember the number of elements we have already injected\n    // so we account for the caused index offset\n    var inserted = 0;\n    // make sure that we insert the elements in sequence,\n    // otherwise the offset compensation won't work\n    insertions.sort(function(a, b) {\n        return a.offset - b.offset;\n    });\n    insertions.forEach(function(insertion) {\n        // array.splice has an annoying function signature :(\n        var remove = insertion.replace ? 1 : 0;\n        var args = [\n            insertion.offset + inserted,\n            remove\n        ].concat(insertion.elements);\n        list.splice.apply(list, args);\n        inserted += insertion.elements.length - remove;\n    });\n}\nfunction mergeInDomOrder() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, list = _ref.list, elements = _ref.elements, resolveElement = _ref.resolveElement;\n    // operate on a copy so we don't mutate the original array\n    var _list = list.slice(0);\n    // make sure the elements we're injecting are provided in DOM order\n    var _elements = nodeArray(elements).slice(0);\n    sortDomOrder(_elements);\n    // find the offsets within the target array (list) at which to inject\n    // each individual element (from elements)\n    var insertions = findInsertionOffsets(_list, _elements, resolveElement);\n    // actually inject the elements into the target array at the identified positions\n    insertElementsAtOffsets(_list, insertions);\n    return _list;\n}\nvar _createClass = function() {\n    function defineProperties(target, props) {\n        for(var i = 0; i < props.length; i++){\n            var descriptor = props[i];\n            descriptor.enumerable = descriptor.enumerable || false;\n            descriptor.configurable = true;\n            if (\"value\" in descriptor) descriptor.writable = true;\n            Object.defineProperty(target, descriptor.key, descriptor);\n        }\n    }\n    return function(Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n    };\n}();\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nvar Maps = function() {\n    function Maps(context) {\n        _classCallCheck(this, Maps);\n        this._document = getDocument(context);\n        this.maps = {};\n    }\n    _createClass(Maps, [\n        {\n            key: \"getAreasFor\",\n            value: function getAreasFor(name) {\n                if (!this.maps[name]) {\n                    // the map is not defined within the context, so we\n                    // have to go find it elsewhere in the document\n                    this.addMapByName(name);\n                }\n                return this.maps[name];\n            }\n        },\n        {\n            key: \"addMapByName\",\n            value: function addMapByName(name) {\n                var map = getMapByName(name, this._document);\n                if (!map) {\n                    // if there is no map, the img[usemap] wasn't doing anything anyway\n                    return;\n                }\n                this.maps[map.name] = queryTabbable({\n                    context: map\n                });\n            }\n        },\n        {\n            key: \"extractAreasFromList\",\n            value: function extractAreasFromList(elements) {\n                // remove all <area> elements from the elements list,\n                // but put them the map for later retrieval\n                return elements.filter(function(element) {\n                    var nodeName = element.nodeName.toLowerCase();\n                    if (nodeName !== \"area\") {\n                        return true;\n                    }\n                    var map = element.parentNode;\n                    if (!this.maps[map.name]) {\n                        this.maps[map.name] = [];\n                    }\n                    this.maps[map.name].push(element);\n                    return false;\n                }, this);\n            }\n        }\n    ]);\n    return Maps;\n}();\nfunction sortArea(elements, context) {\n    // images - unless they are focusable themselves, likely not\n    // part of the elements list, so we'll have to find them and\n    // sort them into the elements list manually\n    var usemaps = context.querySelectorAll(\"img[usemap]\");\n    var maps = new Maps(context);\n    // remove all <area> elements from the elements list,\n    // but put them the map for later retrieval\n    var _elements = maps.extractAreasFromList(elements);\n    if (!usemaps.length) {\n        // the context does not contain any <area>s so no need\n        // to replace anything, just remove any maps\n        return _elements;\n    }\n    return mergeInDomOrder({\n        list: _elements,\n        elements: usemaps,\n        resolveElement: function resolveElement(image) {\n            var name = image.getAttribute(\"usemap\").slice(1);\n            return maps.getAreasFor(name);\n        }\n    });\n}\nvar _createClass$1 = function() {\n    function defineProperties(target, props) {\n        for(var i = 0; i < props.length; i++){\n            var descriptor = props[i];\n            descriptor.enumerable = descriptor.enumerable || false;\n            descriptor.configurable = true;\n            if (\"value\" in descriptor) descriptor.writable = true;\n            Object.defineProperty(target, descriptor.key, descriptor);\n        }\n    }\n    return function(Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n    };\n}();\nfunction _classCallCheck$1(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nvar Shadows = function() {\n    function Shadows(context, sortElements) {\n        _classCallCheck$1(this, Shadows);\n        // document context we're working with\n        this.context = context;\n        // callback that sorts an array of elements\n        this.sortElements = sortElements;\n        // reference to create unique IDs for each ShadowHost\n        this.hostCounter = 1;\n        // reference map for child-ShadowHosts of a ShadowHost\n        this.inHost = {};\n        // reference map for child-ShadowHost of the document\n        this.inDocument = [];\n        // reference map for ShadowHosts\n        this.hosts = {};\n        // reference map for tabbable elements of a ShadowHost\n        this.elements = {};\n    }\n    // remember which hosts we have to sort within later\n    _createClass$1(Shadows, [\n        {\n            key: \"_registerHost\",\n            value: function _registerHost(host) {\n                if (host._sortingId) {\n                    return;\n                }\n                // make the ShadowHost identifiable (see cleanup() for undo)\n                host._sortingId = \"shadow-\" + this.hostCounter++;\n                this.hosts[host._sortingId] = host;\n                // hosts may contain other hosts\n                var parentHost = getShadowHost({\n                    context: host\n                });\n                if (parentHost) {\n                    this._registerHost(parentHost);\n                    this._registerHostParent(host, parentHost);\n                } else {\n                    this.inDocument.push(host);\n                }\n            }\n        },\n        {\n            key: \"_registerHostParent\",\n            value: function _registerHostParent(host, parent) {\n                if (!this.inHost[parent._sortingId]) {\n                    this.inHost[parent._sortingId] = [];\n                }\n                this.inHost[parent._sortingId].push(host);\n            }\n        },\n        {\n            key: \"_registerElement\",\n            value: function _registerElement(element, host) {\n                if (!this.elements[host._sortingId]) {\n                    this.elements[host._sortingId] = [];\n                }\n                this.elements[host._sortingId].push(element);\n            }\n        },\n        {\n            key: \"extractElements\",\n            value: function extractElements(elements) {\n                return elements.filter(function(element) {\n                    var host = getShadowHost({\n                        context: element\n                    });\n                    if (!host) {\n                        return true;\n                    }\n                    this._registerHost(host);\n                    this._registerElement(element, host);\n                    return false;\n                }, this);\n            }\n        },\n        {\n            key: \"sort\",\n            value: function sort(elements) {\n                var _elements = this._injectHosts(elements);\n                _elements = this._replaceHosts(_elements);\n                this._cleanup();\n                return _elements;\n            }\n        },\n        {\n            key: \"_injectHosts\",\n            value: function _injectHosts(elements) {\n                Object.keys(this.hosts).forEach(function(_sortingId) {\n                    var _list = this.elements[_sortingId];\n                    var _elements = this.inHost[_sortingId];\n                    var _context = this.hosts[_sortingId].shadowRoot;\n                    this.elements[_sortingId] = this._merge(_list, _elements, _context);\n                }, this);\n                return this._merge(elements, this.inDocument, this.context);\n            }\n        },\n        {\n            key: \"_merge\",\n            value: function _merge(list, elements, context) {\n                var merged = mergeInDomOrder({\n                    list: list,\n                    elements: elements\n                });\n                return this.sortElements(merged, context);\n            }\n        },\n        {\n            key: \"_replaceHosts\",\n            value: function _replaceHosts(elements) {\n                return mergeInDomOrder({\n                    list: elements,\n                    elements: this.inDocument,\n                    resolveElement: this._resolveHostElement.bind(this)\n                });\n            }\n        },\n        {\n            key: \"_resolveHostElement\",\n            value: function _resolveHostElement(host) {\n                var merged = mergeInDomOrder({\n                    list: this.elements[host._sortingId],\n                    elements: this.inHost[host._sortingId],\n                    resolveElement: this._resolveHostElement.bind(this)\n                });\n                var _tabindex = tabindexValue(host);\n                if (_tabindex !== null && _tabindex > -1) {\n                    return [\n                        host\n                    ].concat(merged);\n                }\n                return merged;\n            }\n        },\n        {\n            key: \"_cleanup\",\n            value: function _cleanup() {\n                // remove those identifers we put on the ShadowHost to avoid using Map()\n                Object.keys(this.hosts).forEach(function(key) {\n                    delete this.hosts[key]._sortingId;\n                }, this);\n            }\n        }\n    ]);\n    return Shadows;\n}();\nfunction sortShadowed(elements, context, sortElements) {\n    var shadows = new Shadows(context, sortElements);\n    var _elements = shadows.extractElements(elements);\n    if (_elements.length === elements.length) {\n        // no shadowed content found, no need to continue\n        return sortElements(elements);\n    }\n    return shadows.sort(_elements);\n}\nfunction sortTabindex(elements) {\n    // https://developer.mozilla.org/docs/Web/API/HTMLElement.tabIndex\n    // elements with tabIndex \"0\" (including tabbableElements without tabIndex) should be navigated in the order they appear.\n    // elements with a positive tabIndex:\n    //   Elements that have identical tabIndexes should be navigated in the order they appear.\n    //   Navigation proceeds from the lowest tabIndex to the highest tabIndex.\n    // NOTE: sort implementation may be unstable and thus mess up DOM order,\n    // that's why we build a map that's being sorted instead. If we were able to rely\n    // on a stable sorting algorithm, sortTabindex() could be as simple as\n    // elements.sort(function(a, b) { return a.tabIndex - b.tabIndex; });\n    // at this time Chrome does not use a stable sorting algorithm\n    // see http://blog.rodneyrehm.de/archives/14-Sorting-Were-Doing-It-Wrong.html#stability\n    // NOTE: compareDocumentPosition seemed like more overhead than just sorting this with buckets\n    // https://developer.mozilla.org/docs/Web/API/Node.compareDocumentPosition\n    var map = {};\n    var indexes = [];\n    var normal = elements.filter(function(element) {\n        // in Trident and Gecko SVGElement does not know about the tabIndex property\n        var tabIndex = element.tabIndex;\n        if (tabIndex === undefined) {\n            tabIndex = tabindexValue(element);\n        }\n        // extract elements that don't need sorting\n        if (tabIndex <= 0 || tabIndex === null || tabIndex === undefined) {\n            return true;\n        }\n        if (!map[tabIndex]) {\n            // create sortable bucket for dom-order-preservation of elements with the same tabIndex\n            map[tabIndex] = [];\n            // maintain a list of unique tabIndexes\n            indexes.push(tabIndex);\n        }\n        // sort element into the proper bucket\n        map[tabIndex].push(element);\n        // element moved to sorting map, so not \"normal\" anymore\n        return false;\n    });\n    // sort the tabindex ascending,\n    // then resolve them to their appropriate buckets,\n    // then flatten the array of arrays to an array\n    var _elements = indexes.sort().map(function(tabIndex) {\n        return map[tabIndex];\n    }).reduceRight(function(previous, current) {\n        return current.concat(previous);\n    }, normal);\n    return _elements;\n}\nvar supports$8 = void 0;\nfunction moveContextToBeginning(elements, context) {\n    var pos = elements.indexOf(context);\n    if (pos > 0) {\n        var tmp = elements.splice(pos, 1);\n        return tmp.concat(elements);\n    }\n    return elements;\n}\nfunction sortElements(elements, _context) {\n    if (supports$8.tabsequenceAreaAtImgPosition) {\n        // Some browsers sort <area> in DOM order, some place the <area>s\n        // where the <img> referecing them would've been in DOM order.\n        // https://github.com/medialize/ally.js/issues/5\n        elements = sortArea(elements, _context);\n    }\n    elements = sortTabindex(elements);\n    return elements;\n}\nfunction queryTabsequence() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, context = _ref.context, includeContext = _ref.includeContext, includeOnlyTabbable = _ref.includeOnlyTabbable, strategy = _ref.strategy;\n    if (!supports$8) {\n        supports$8 = _supports();\n    }\n    var _context = nodeArray(context)[0] || document.documentElement;\n    var elements = queryTabbable({\n        context: _context,\n        includeContext: includeContext,\n        includeOnlyTabbable: includeOnlyTabbable,\n        strategy: strategy\n    });\n    if (document.body.createShadowRoot && platform.is.BLINK) {\n        // sort tabindex localized to shadow dom\n        // see https://github.com/medialize/ally.js/issues/6\n        elements = sortShadowed(elements, _context, sortElements);\n    } else {\n        elements = sortElements(elements, _context);\n    }\n    if (includeContext) {\n        // if we include the context itself, it has to be the first\n        // element of the sequence\n        elements = moveContextToBeginning(elements, _context);\n    }\n    return elements;\n}\n// codes mostly cloned from https://github.com/keithamus/jwerty/blob/master/jwerty.js\n// deliberately not exposing characters like <,.-#* because they vary *wildly*\n// across keyboard layouts and may cause various problems\n// (e.g. \"*\" is \"Shift +\" on a German Mac keyboard)\n// (e.g. \"@\" is \"Alt L\" on a German Mac keyboard)\nvar keycode = {\n    // Element Focus\n    tab: 9,\n    // Navigation\n    left: 37,\n    up: 38,\n    right: 39,\n    down: 40,\n    pageUp: 33,\n    \"page-up\": 33,\n    pageDown: 34,\n    \"page-down\": 34,\n    end: 35,\n    home: 36,\n    // Action\n    enter: 13,\n    escape: 27,\n    space: 32,\n    // Modifier\n    shift: 16,\n    capsLock: 20,\n    \"caps-lock\": 20,\n    ctrl: 17,\n    alt: 18,\n    meta: 91,\n    // in firefox: 224\n    // on mac (chrome): meta-left=91, meta-right=93\n    // on win (IE11): meta-left=91, meta-right=92\n    pause: 19,\n    // Content Manipulation\n    insert: 45,\n    delete: 46,\n    backspace: 8,\n    // the same logical key may be identified through different keyCodes\n    _alias: {\n        91: [\n            92,\n            93,\n            224\n        ]\n    }\n};\n// Function keys (112 - 137)\n// NOTE: not every keyboard knows F13+\nfor(var n = 1; n < 26; n++){\n    keycode[\"f\" + n] = n + 111;\n}\n// Number keys (48-57, numpad 96-105)\n// NOTE: not every keyboard knows num-0+\nfor(var _n = 0; _n < 10; _n++){\n    var code = _n + 48;\n    var numCode = _n + 96;\n    keycode[_n] = code;\n    keycode[\"num-\" + _n] = numCode;\n    keycode._alias[code] = [\n        numCode\n    ];\n}\n// Latin characters (65 - 90)\nfor(var _n2 = 0; _n2 < 26; _n2++){\n    var _code = _n2 + 65;\n    var name$1 = String.fromCharCode(_code).toLowerCase();\n    keycode[name$1] = _code;\n}\nvar modifier = {\n    alt: \"altKey\",\n    ctrl: \"ctrlKey\",\n    meta: \"metaKey\",\n    shift: \"shiftKey\"\n};\nvar modifierSequence = Object.keys(modifier).map(function(name) {\n    return modifier[name];\n});\nfunction createExpectedModifiers(ignoreModifiers) {\n    var value = ignoreModifiers ? null : false;\n    return {\n        altKey: value,\n        ctrlKey: value,\n        metaKey: value,\n        shiftKey: value\n    };\n}\nfunction resolveModifiers(modifiers) {\n    var ignoreModifiers = modifiers.indexOf(\"*\") !== -1;\n    var expected = createExpectedModifiers(ignoreModifiers);\n    modifiers.forEach(function(token) {\n        if (token === \"*\") {\n            // we've already covered the all-in operator\n            return;\n        }\n        // we want the modifier pressed\n        var value = true;\n        var operator = token.slice(0, 1);\n        if (operator === \"?\") {\n            // we don't care if the modifier is pressed\n            value = null;\n        } else if (operator === \"!\") {\n            // we do not want the modifier pressed\n            value = false;\n        }\n        if (value !== true) {\n            // compensate for the modifier's operator\n            token = token.slice(1);\n        }\n        var propertyName = modifier[token];\n        if (!propertyName) {\n            throw new TypeError('Unknown modifier \"' + token + '\"');\n        }\n        expected[propertyName] = value;\n    });\n    return expected;\n}\nfunction resolveKey(key) {\n    var code = keycode[key] || parseInt(key, 10);\n    if (!code || typeof code !== \"number\" || isNaN(code)) {\n        throw new TypeError('Unknown key \"' + key + '\"');\n    }\n    return [\n        code\n    ].concat(keycode._alias[code] || []);\n}\nfunction matchModifiers(expected, event) {\n    // returns true on match\n    return !modifierSequence.some(function(prop) {\n        // returns true on mismatch\n        return typeof expected[prop] === \"boolean\" && Boolean(event[prop]) !== expected[prop];\n    });\n}\nfunction keyBinding(text) {\n    return text.split(/\\s+/).map(function(_text) {\n        var tokens = _text.split(\"+\");\n        var _modifiers = resolveModifiers(tokens.slice(0, -1));\n        var _keyCodes = resolveKey(tokens.slice(-1));\n        return {\n            keyCodes: _keyCodes,\n            modifiers: _modifiers,\n            matchModifiers: matchModifiers.bind(null, _modifiers)\n        };\n    });\n}\n// Node.compareDocumentPosition is available since IE9\n// see https://developer.mozilla.org/docs/Web/API/Node.compareDocumentPosition\n// callback returns true when element is contained by parent or is the parent suited for use with Array.some()\n/*\n  USAGE:\n    var isChildOf = getParentComparator({parent: someNode});\n    listOfElements.some(isChildOf)\n*/ function getParentComparator() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, parent = _ref.parent, element = _ref.element, includeSelf = _ref.includeSelf;\n    if (parent) {\n        return function isChildOf(node) {\n            return Boolean(includeSelf && node === parent || parent.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY);\n        };\n    } else if (element) {\n        return function isParentOf(node) {\n            return Boolean(includeSelf && element === node || node.compareDocumentPosition(element) & Node.DOCUMENT_POSITION_CONTAINED_BY);\n        };\n    }\n    throw new TypeError(\"util/compare-position#getParentComparator required either options.parent or options.element\");\n}\n// Bug 286933 - Key events in the autocomplete popup should be hidden from page scripts\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=286933\nfunction whenKey() {\n    var map = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var bindings = {};\n    var context = nodeArray(map.context)[0] || document.documentElement;\n    delete map.context;\n    var filter = nodeArray(map.filter);\n    delete map.filter;\n    var mapKeys = Object.keys(map);\n    if (!mapKeys.length) {\n        throw new TypeError(\"when/key requires at least one option key\");\n    }\n    var registerBinding = function registerBinding(event) {\n        event.keyCodes.forEach(function(code) {\n            if (!bindings[code]) {\n                bindings[code] = [];\n            }\n            bindings[code].push(event);\n        });\n    };\n    mapKeys.forEach(function(text) {\n        if (typeof map[text] !== \"function\") {\n            throw new TypeError('when/key requires option[\"' + text + '\"] to be a function');\n        }\n        var addCallback = function addCallback(event) {\n            event.callback = map[text];\n            return event;\n        };\n        keyBinding(text).map(addCallback).forEach(registerBinding);\n    });\n    var handleKeyDown = function handleKeyDown(event) {\n        if (event.defaultPrevented) {\n            return;\n        }\n        if (filter.length) {\n            // ignore elements within the exempted sub-trees\n            var isParentOfElement = getParentComparator({\n                element: event.target,\n                includeSelf: true\n            });\n            if (filter.some(isParentOfElement)) {\n                return;\n            }\n        }\n        var key = event.keyCode || event.which;\n        if (!bindings[key]) {\n            return;\n        }\n        bindings[key].forEach(function(_event) {\n            if (!_event.matchModifiers(event)) {\n                return;\n            }\n            _event.callback.call(context, event, disengage);\n        });\n    };\n    context.addEventListener(\"keydown\", handleKeyDown, false);\n    var disengage = function disengage() {\n        context.removeEventListener(\"keydown\", handleKeyDown, false);\n    };\n    return {\n        disengage: disengage\n    };\n}\nfunction _default(param) {\n    let { context } = param === void 0 ? {} : param;\n    if (!context) {\n        context = document.documentElement;\n    }\n    // Make sure the supports tests are run before intercepting the Tab key,\n    // or IE10 and IE11 will fail to process the first Tab key event. Not\n    // limiting this warm-up to IE because it may be a problem elsewhere, too.\n    queryTabsequence();\n    return whenKey({\n        // Safari on OSX may require ALT+TAB to reach links,\n        // see https://github.com/medialize/ally.js/issues/146\n        \"?alt+?shift+tab\": function altShiftTab(event) {\n            // we're completely taking over the Tab key handling\n            event.preventDefault();\n            var sequence = queryTabsequence({\n                context: context\n            });\n            var backward = event.shiftKey;\n            var first = sequence[0];\n            var last = sequence[sequence.length - 1];\n            // wrap around first to last, last to first\n            var source = backward ? first : last;\n            var target = backward ? last : first;\n            if (isActiveElement(source)) {\n                target.focus();\n                return;\n            }\n            // find current position in tabsequence\n            var currentIndex = void 0;\n            var found = sequence.some(function(element, index) {\n                if (!isActiveElement(element)) {\n                    return false;\n                }\n                currentIndex = index;\n                return true;\n            });\n            if (!found) {\n                // redirect to first as we're not in our tabsequence\n                first.focus();\n                return;\n            }\n            // shift focus to previous/next element in the sequence\n            var offset = backward ? -1 : 1;\n            sequence[currentIndex + offset].focus();\n        }\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=maintain--tab-focus.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Overlay/maintain--tab-focus.js\n"));

/***/ })

}]);