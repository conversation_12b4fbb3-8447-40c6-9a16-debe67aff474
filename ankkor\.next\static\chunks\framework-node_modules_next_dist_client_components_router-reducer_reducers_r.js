"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_router-reducer_reducers_r"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js ***!
  \*********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"refreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return refreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ../fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ../refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nfunction refreshReducer(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    let currentTree = state.tree;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), [\n        currentTree[0],\n        currentTree[1],\n        currentTree[2],\n        \"refetch\"\n    ], includeNextUrl ? state.nextUrl : null, state.buildId);\n    return cache.lazyData.then(async (param)=>{\n        let [flightData, canonicalUrlOverride] = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === \"string\") {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        for (const flightDataPath of flightData){\n            // FlightDataPath with more than two items means unexpected Flight data was returned\n            if (flightDataPath.length !== 3) {\n                // TODO-APP: handle this case better\n                console.log(\"REFRESH FAILED\");\n                return state;\n            }\n            // Given the path can only have two items the items are only the router state and rsc for the root.\n            const [treePatch] = flightDataPath;\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                \"\"\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            // The one before last item is the router state tree patch\n            const [cacheNodeSeedData, head] = flightDataPath.slice(-2);\n            // Handles case where prefetch only returns the router tree patch without rendered components.\n            if (cacheNodeSeedData !== null) {\n                const rsc = cacheNodeSeedData[2];\n                cache.rsc = rsc;\n                cache.prefetchRsc = null;\n                (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, treePatch, cacheNodeSeedData, head);\n                mutable.prefetchCache = new Map();\n            }\n            await (0, _refetchinactiveparallelsegments.refreshInactiveParallelSegments)({\n                state,\n                updatedTree: newTree,\n                updatedCache: cache,\n                includeNextUrl,\n                canonicalUrl: mutable.canonicalUrl || state.canonicalUrl\n            });\n            mutable.cache = cache;\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvcmVmcmVzaC1yZWR1Y2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7a0RBbUJnQkE7OztlQUFBQTs7O2lEQW5Cb0I7K0NBQ0Y7eURBQ1U7eURBQ0E7NkNBT1Y7MkNBQ0o7MkRBRWdCO3VDQUNUO21EQUNDOytEQUNZOzZEQUNGO0FBRXpDLFNBQVNBLGVBQ2RDLEtBQTJCLEVBQzNCQyxNQUFxQjtJQUVyQixNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHRDtJQUNuQixNQUFNRSxVQUFtQixDQUFDO0lBQzFCLE1BQU1DLE9BQU9KLE1BQU1LLFlBQVk7SUFFL0IsSUFBSUMsY0FBY04sTUFBTU8sSUFBSTtJQUU1QkosUUFBUUssMEJBQTBCLEdBQUc7SUFFckMsTUFBTUMsUUFBbUJDLENBQUFBLEdBQUFBLFdBQUFBLG9CQUFvQjtJQUU3QyxzRkFBc0Y7SUFDdEYsc0hBQXNIO0lBQ3RILE1BQU1DLGlCQUFpQkMsQ0FBQUEsR0FBQUEsbUNBQUFBLGlDQUFpQyxFQUFDWixNQUFNTyxJQUFJO0lBRW5FLHVEQUF1RDtJQUN2RCx3Q0FBd0M7SUFDeENFLE1BQU1JLFFBQVEsR0FBR0MsQ0FBQUEsR0FBQUEscUJBQUFBLG1CQUFtQixFQUNsQyxJQUFJQyxJQUFJWCxNQUFNRixTQUNkO1FBQUNJLFdBQVcsQ0FBQyxFQUFFO1FBQUVBLFdBQVcsQ0FBQyxFQUFFO1FBQUVBLFdBQVcsQ0FBQyxFQUFFO1FBQUU7S0FBVSxFQUMzREssaUJBQWlCWCxNQUFNZ0IsT0FBTyxHQUFHLE1BQ2pDaEIsTUFBTWlCLE9BQU87SUFHZixPQUFPUixNQUFNSSxRQUFRLENBQUNLLElBQUksQ0FDeEIsT0FBQUM7WUFBTyxDQUFDQyxZQUFZQyxxQkFBcUIsR0FBQUY7UUFDdkMsNERBQTREO1FBQzVELElBQUksT0FBT0MsZUFBZSxVQUFVO1lBQ2xDLE9BQU9FLENBQUFBLEdBQUFBLGlCQUFBQSxpQkFBaUIsRUFDdEJ0QixPQUNBRyxTQUNBaUIsWUFDQXBCLE1BQU11QixPQUFPLENBQUNDLFdBQVc7UUFFN0I7UUFFQSwrREFBK0Q7UUFDL0RmLE1BQU1JLFFBQVEsR0FBRztRQUVqQixLQUFLLE1BQU1ZLGtCQUFrQkwsV0FBWTtZQUN2QyxvRkFBb0Y7WUFDcEYsSUFBSUssZUFBZUMsTUFBTSxLQUFLLEdBQUc7Z0JBQy9CLG9DQUFvQztnQkFDcENDLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPNUI7WUFDVDtZQUVBLG1HQUFtRztZQUNuRyxNQUFNLENBQUM2QixVQUFVLEdBQUdKO1lBQ3BCLE1BQU1LLFVBQVVDLENBQUFBLEdBQUFBLDZCQUFBQSwyQkFBMkIsRUFFekM7Z0JBQUM7YUFBRyxFQUNKekIsYUFDQXVCLFdBQ0E3QixNQUFNSyxZQUFZO1lBR3BCLElBQUl5QixZQUFZLE1BQU07Z0JBQ3BCLE9BQU9FLENBQUFBLEdBQUFBLHVCQUFBQSxxQkFBcUIsRUFBQ2hDLE9BQU9DLFFBQVE0QjtZQUM5QztZQUVBLElBQUlJLENBQUFBLEdBQUFBLDZCQUFBQSwyQkFBMkIsRUFBQzNCLGFBQWF3QixVQUFVO2dCQUNyRCxPQUFPUixDQUFBQSxHQUFBQSxpQkFBQUEsaUJBQWlCLEVBQ3RCdEIsT0FDQUcsU0FDQUMsTUFDQUosTUFBTXVCLE9BQU8sQ0FBQ0MsV0FBVztZQUU3QjtZQUVBLE1BQU1VLDJCQUEyQmIsdUJBQzdCYyxDQUFBQSxHQUFBQSxtQkFBQUEsaUJBQWlCLEVBQUNkLHdCQUNsQmU7WUFFSixJQUFJZixzQkFBc0I7Z0JBQ3hCbEIsUUFBUUUsWUFBWSxHQUFHNkI7WUFDekI7WUFFQSwwREFBMEQ7WUFDMUQsTUFBTSxDQUFDRyxtQkFBbUJDLEtBQUssR0FBR2IsZUFBZWMsS0FBSyxDQUFDLENBQUM7WUFFeEQsOEZBQThGO1lBQzlGLElBQUlGLHNCQUFzQixNQUFNO2dCQUM5QixNQUFNRyxNQUFNSCxpQkFBaUIsQ0FBQyxFQUFFO2dCQUNoQzVCLE1BQU0rQixHQUFHLEdBQUdBO2dCQUNaL0IsTUFBTWdDLFdBQVcsR0FBRztnQkFDcEJDLENBQUFBLEdBQUFBLCtCQUFBQSw2QkFBNkIsRUFDM0JqQyxPQUVBMkIsV0FDQVAsV0FDQVEsbUJBQ0FDO2dCQUVGbkMsUUFBUXdDLGFBQWEsR0FBRyxJQUFJQztZQUM5QjtZQUVBLE1BQU1DLENBQUFBLEdBQUFBLGlDQUFBQSwrQkFBK0IsRUFBQztnQkFDcEM3QztnQkFDQThDLGFBQWFoQjtnQkFDYmlCLGNBQWN0QztnQkFDZEU7Z0JBQ0FOLGNBQWNGLFFBQVFFLFlBQVksSUFBSUwsTUFBTUssWUFBWTtZQUMxRDtZQUVBRixRQUFRTSxLQUFLLEdBQUdBO1lBQ2hCTixRQUFRNkMsV0FBVyxHQUFHbEI7WUFDdEIzQixRQUFRRSxZQUFZLEdBQUdEO1lBRXZCRSxjQUFjd0I7UUFDaEI7UUFFQSxPQUFPbUIsQ0FBQUEsR0FBQUEsZUFBQUEsYUFBYSxFQUFDakQsT0FBT0c7SUFDOUIsR0FDQSxJQUFNSDtBQUVWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvcmVmcmVzaC1yZWR1Y2VyLnRzPzg4YTkiXSwibmFtZXMiOlsicmVmcmVzaFJlZHVjZXIiLCJzdGF0ZSIsImFjdGlvbiIsIm9yaWdpbiIsIm11dGFibGUiLCJocmVmIiwiY2Fub25pY2FsVXJsIiwiY3VycmVudFRyZWUiLCJ0cmVlIiwicHJlc2VydmVDdXN0b21IaXN0b3J5U3RhdGUiLCJjYWNoZSIsImNyZWF0ZUVtcHR5Q2FjaGVOb2RlIiwiaW5jbHVkZU5leHRVcmwiLCJoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUiLCJsYXp5RGF0YSIsImZldGNoU2VydmVyUmVzcG9uc2UiLCJVUkwiLCJuZXh0VXJsIiwiYnVpbGRJZCIsInRoZW4iLCJwYXJhbSIsImZsaWdodERhdGEiLCJjYW5vbmljYWxVcmxPdmVycmlkZSIsImhhbmRsZUV4dGVybmFsVXJsIiwicHVzaFJlZiIsInBlbmRpbmdQdXNoIiwiZmxpZ2h0RGF0YVBhdGgiLCJsZW5ndGgiLCJjb25zb2xlIiwibG9nIiwidHJlZVBhdGNoIiwibmV3VHJlZSIsImFwcGx5Um91dGVyU3RhdGVQYXRjaFRvVHJlZSIsImhhbmRsZVNlZ21lbnRNaXNtYXRjaCIsImlzTmF2aWdhdGluZ1RvTmV3Um9vdExheW91dCIsImNhbm9uaWNhbFVybE92ZXJyaWRlSHJlZiIsImNyZWF0ZUhyZWZGcm9tVXJsIiwidW5kZWZpbmVkIiwiY2FjaGVOb2RlU2VlZERhdGEiLCJoZWFkIiwic2xpY2UiLCJyc2MiLCJwcmVmZXRjaFJzYyIsImZpbGxMYXp5SXRlbXNUaWxsTGVhZldpdGhIZWFkIiwicHJlZmV0Y2hDYWNoZSIsIk1hcCIsInJlZnJlc2hJbmFjdGl2ZVBhcmFsbGVsU2VnbWVudHMiLCJ1cGRhdGVkVHJlZSIsInVwZGF0ZWRDYWNoZSIsInBhdGNoZWRUcmVlIiwiaGFuZGxlTXV0YWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js ***!
  \*********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"restoreReducer\", ({\n    enumerable: true,\n    get: function() {\n        return restoreReducer;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _computechangedpath = __webpack_require__(/*! ../compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _pprnavigations = __webpack_require__(/*! ../ppr-navigations */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\");\nfunction restoreReducer(state, action) {\n    const { url, tree } = action;\n    const href = (0, _createhreffromurl.createHrefFromUrl)(url);\n    // This action is used to restore the router state from the history state.\n    // However, it's possible that the history state no longer contains the `FlightRouterState`.\n    // We will copy over the internal state on pushState/replaceState events, but if a history entry\n    // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n    // the history state will not contain the `FlightRouterState`.\n    // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n    const treeToRestore = tree || state.tree;\n    const oldCache = state.cache;\n    const newCache =  false ? // prevents an unnecessary flash back to PPR state during a\n    // back/forward navigation.\n    0 : oldCache;\n    var _extractPathFromFlightRouterState;\n    return {\n        buildId: state.buildId,\n        // Set canonical url\n        canonicalUrl: href,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // Ensures that the custom history state that was set is preserved when applying this update.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: state.focusAndScrollRef,\n        cache: newCache,\n        prefetchCache: state.prefetchCache,\n        // Restore provided tree\n        tree: treeToRestore,\n        nextUrl: (_extractPathFromFlightRouterState = (0, _computechangedpath.extractPathFromFlightRouterState)(treeToRestore)) != null ? _extractPathFromFlightRouterState : url.pathname\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=restore-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvcmVzdG9yZS1yZWR1Y2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7a0RBU2dCQTs7O2VBQUFBOzs7K0NBVGtCO2dEQU1lOzRDQUNJO0FBRTlDLFNBQVNBLGVBQ2RDLEtBQTJCLEVBQzNCQyxNQUFxQjtJQUVyQixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsSUFBSSxFQUFFLEdBQUdGO0lBQ3RCLE1BQU1HLE9BQU9DLENBQUFBLEdBQUFBLG1CQUFBQSxpQkFBaUIsRUFBQ0g7SUFDL0IsMEVBQTBFO0lBQzFFLDRGQUE0RjtJQUM1RixnR0FBZ0c7SUFDaEcsNkZBQTZGO0lBQzdGLDhEQUE4RDtJQUM5RCx5R0FBeUc7SUFDekcsTUFBTUksZ0JBQWdCSCxRQUFRSCxNQUFNRyxJQUFJO0lBRXhDLE1BQU1JLFdBQVdQLE1BQU1RLEtBQUs7SUFDNUIsTUFBTUMsV0FBV0MsTUFBc0IsR0FLbkNHLDJEQUYyRDtJQUMzRCwyQkFBMkI7SUFDM0JBLENBQStDUCxHQUMvQ0M7UUFpQk9PO0lBZlgsT0FBTztRQUNMQyxTQUFTZixNQUFNZSxPQUFPO1FBQ3RCLG9CQUFvQjtRQUNwQkMsY0FBY1o7UUFDZGEsU0FBUztZQUNQQyxhQUFhO1lBQ2JDLGVBQWU7WUFDZiw2RkFBNkY7WUFDN0ZDLDRCQUE0QjtRQUM5QjtRQUNBQyxtQkFBbUJyQixNQUFNcUIsaUJBQWlCO1FBQzFDYixPQUFPQztRQUNQYSxlQUFldEIsTUFBTXNCLGFBQWE7UUFDbEMsd0JBQXdCO1FBQ3hCbkIsTUFBTUc7UUFDTmlCLFNBQVNULENBQUFBLG9DQUFBQSxDQUFBQSxHQUFBQSxvQkFBQUEsZ0NBQWdDLEVBQUNSLGNBQUFBLEtBQUFBLE9BQWpDUSxvQ0FBbURaLElBQUlzQixRQUFRO0lBQzFFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yb3V0ZXItcmVkdWNlci9yZWR1Y2Vycy9yZXN0b3JlLXJlZHVjZXIudHM/MDY3MSJdLCJuYW1lcyI6WyJyZXN0b3JlUmVkdWNlciIsInN0YXRlIiwiYWN0aW9uIiwidXJsIiwidHJlZSIsImhyZWYiLCJjcmVhdGVIcmVmRnJvbVVybCIsInRyZWVUb1Jlc3RvcmUiLCJvbGRDYWNoZSIsImNhY2hlIiwibmV3Q2FjaGUiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1BQUiIsInVwZGF0ZUNhY2hlTm9kZU9uUG9wc3RhdGVSZXN0b3JhdGlvbiIsImV4dHJhY3RQYXRoRnJvbUZsaWdodFJvdXRlclN0YXRlIiwiYnVpbGRJZCIsImNhbm9uaWNhbFVybCIsInB1c2hSZWYiLCJwZW5kaW5nUHVzaCIsIm1wYU5hdmlnYXRpb24iLCJwcmVzZXJ2ZUN1c3RvbUhpc3RvcnlTdGF0ZSIsImZvY3VzQW5kU2Nyb2xsUmVmIiwicHJlZmV0Y2hDYWNoZSIsIm5leHRVcmwiLCJwYXRobmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js ***!
  \***************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"serverActionReducer\", ({\n    enumerable: true,\n    get: function() {\n        return serverActionReducer;\n    }\n}));\nconst _appcallserver = __webpack_require__(/*! ../../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _approuterheaders = __webpack_require__(/*! ../../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ../fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ../refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, encodeReply } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nasync function fetchServerAction(state, nextUrl, param) {\n    let { actionId, actionArgs } = param;\n    const body = await encodeReply(actionArgs);\n    const res = await fetch(\"\", {\n        method: \"POST\",\n        headers: {\n            Accept: _approuterheaders.RSC_CONTENT_TYPE_HEADER,\n            [_approuterheaders.ACTION]: actionId,\n            [_approuterheaders.NEXT_ROUTER_STATE_TREE]: encodeURIComponent(JSON.stringify(state.tree)),\n            ... false ? 0 : {},\n            ...nextUrl ? {\n                [_approuterheaders.NEXT_URL]: nextUrl\n            } : {}\n        },\n        body\n    });\n    const location = res.headers.get(\"x-action-redirect\");\n    let revalidatedParts;\n    try {\n        const revalidatedHeader = JSON.parse(res.headers.get(\"x-action-revalidated\") || \"[[],0,0]\");\n        revalidatedParts = {\n            paths: revalidatedHeader[0] || [],\n            tag: !!revalidatedHeader[1],\n            cookie: revalidatedHeader[2]\n        };\n    } catch (e) {\n        revalidatedParts = {\n            paths: [],\n            tag: false,\n            cookie: false\n        };\n    }\n    const redirectLocation = location ? new URL((0, _addbasepath.addBasePath)(location), new URL(state.canonicalUrl, window.location.href)) : undefined;\n    let isFlightResponse = res.headers.get(\"content-type\") === _approuterheaders.RSC_CONTENT_TYPE_HEADER;\n    if (isFlightResponse) {\n        const response = await createFromFetch(Promise.resolve(res), {\n            callServer: _appcallserver.callServer\n        });\n        if (location) {\n            // if it was a redirection, then result is just a regular RSC payload\n            const [, actionFlightData] = response != null ? response : [];\n            return {\n                actionFlightData: actionFlightData,\n                redirectLocation,\n                revalidatedParts\n            };\n        }\n        // otherwise it's a tuple of [actionResult, actionFlightData]\n        const [actionResult, [, actionFlightData]] = response != null ? response : [];\n        return {\n            actionResult,\n            actionFlightData,\n            redirectLocation,\n            revalidatedParts\n        };\n    }\n    return {\n        redirectLocation,\n        revalidatedParts\n    };\n}\nfunction serverActionReducer(state, action) {\n    const { resolve, reject } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    let currentTree = state.tree;\n    mutable.preserveCustomHistoryState = false;\n    // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n    // If the route has been intercepted, the action should be as well.\n    // Otherwise the server action might be intercepted with the wrong action id\n    // (ie, one that corresponds with the intercepted route)\n    const nextUrl = state.nextUrl && (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree) ? state.nextUrl : null;\n    mutable.inFlightServerAction = fetchServerAction(state, nextUrl, action);\n    return mutable.inFlightServerAction.then(async (param)=>{\n        let { actionResult, actionFlightData: flightData, redirectLocation } = param;\n        // Make sure the redirection is a push instead of a replace.\n        // Issue: https://github.com/vercel/next.js/issues/53911\n        if (redirectLocation) {\n            state.pushRef.pendingPush = true;\n            mutable.pendingPush = true;\n        }\n        if (!flightData) {\n            resolve(actionResult);\n            // If there is a redirect but no flight data we need to do a mpaNavigation.\n            if (redirectLocation) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, redirectLocation.href, state.pushRef.pendingPush);\n            }\n            return state;\n        }\n        if (typeof flightData === \"string\") {\n            // Handle case when navigating to page in `pages` from `app`\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.data as it has been resolved at this point.\n        mutable.inFlightServerAction = null;\n        if (redirectLocation) {\n            const newHref = (0, _createhreffromurl.createHrefFromUrl)(redirectLocation, false);\n            mutable.canonicalUrl = newHref;\n        }\n        for (const flightDataPath of flightData){\n            // FlightDataPath with more than two items means unexpected Flight data was returned\n            if (flightDataPath.length !== 3) {\n                // TODO-APP: handle this case better\n                console.log(\"SERVER ACTION APPLY FAILED\");\n                return state;\n            }\n            // Given the path can only have two items the items are only the router state and rsc for the root.\n            const [treePatch] = flightDataPath;\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                \"\"\n            ], currentTree, treePatch, redirectLocation ? (0, _createhreffromurl.createHrefFromUrl)(redirectLocation) : state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            // The one before last item is the router state tree patch\n            const [cacheNodeSeedData, head] = flightDataPath.slice(-2);\n            const rsc = cacheNodeSeedData !== null ? cacheNodeSeedData[2] : null;\n            // Handles case where prefetch only returns the router tree patch without rendered components.\n            if (rsc !== null) {\n                const cache = (0, _approuter.createEmptyCacheNode)();\n                cache.rsc = rsc;\n                cache.prefetchRsc = null;\n                (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, treePatch, cacheNodeSeedData, head);\n                await (0, _refetchinactiveparallelsegments.refreshInactiveParallelSegments)({\n                    state,\n                    updatedTree: newTree,\n                    updatedCache: cache,\n                    includeNextUrl: Boolean(nextUrl),\n                    canonicalUrl: mutable.canonicalUrl || state.canonicalUrl\n                });\n                mutable.cache = cache;\n                mutable.prefetchCache = new Map();\n            }\n            mutable.patchedTree = newTree;\n            currentTree = newTree;\n        }\n        resolve(actionResult);\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, (e)=>{\n        // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n        reject(e);\n        return state;\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=server-action-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js ***!
  \**************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"serverPatchReducer\", ({\n    enumerable: true,\n    get: function() {\n        return serverPatchReducer;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nfunction serverPatchReducer(state, action) {\n    const { serverResponse } = action;\n    const [flightData, overrideCanonicalUrl] = serverResponse;\n    const mutable = {};\n    mutable.preserveCustomHistoryState = false;\n    // Handle case when navigating to page in `pages` from `app`\n    if (typeof flightData === \"string\") {\n        return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n    }\n    let currentTree = state.tree;\n    let currentCache = state.cache;\n    for (const flightDataPath of flightData){\n        // Slices off the last segment (which is at -4) as it doesn't exist in the tree yet\n        const flightSegmentPath = flightDataPath.slice(0, -4);\n        const [treePatch] = flightDataPath.slice(-3, -2);\n        const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n            \"\",\n            ...flightSegmentPath\n        ], currentTree, treePatch, state.canonicalUrl);\n        if (newTree === null) {\n            return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n        }\n        if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, state.canonicalUrl, state.pushRef.pendingPush);\n        }\n        const canonicalUrlOverrideHref = overrideCanonicalUrl ? (0, _createhreffromurl.createHrefFromUrl)(overrideCanonicalUrl) : undefined;\n        if (canonicalUrlOverrideHref) {\n            mutable.canonicalUrl = canonicalUrlOverrideHref;\n        }\n        const cache = (0, _approuter.createEmptyCacheNode)();\n        (0, _applyflightdata.applyFlightData)(currentCache, cache, flightDataPath);\n        mutable.patchedTree = newTree;\n        mutable.cache = cache;\n        currentCache = cache;\n        currentTree = newTree;\n    }\n    return (0, _handlemutable.handleMutable)(state, mutable);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=server-patch-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js\n"));

/***/ })

}]);