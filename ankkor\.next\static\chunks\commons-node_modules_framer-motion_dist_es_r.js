"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-node_modules_framer-motion_dist_es_r"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SVGVisualElement: function() { return /* binding */ SVGVisualElement; }\n/* harmony export */ });\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom/DOMVisualElement.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\");\n/* harmony import */ var _utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/build-attrs.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\");\n/* harmony import */ var _dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom/utils/camel-to-dash.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\");\n/* harmony import */ var _utils_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/camel-case-attrs.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\");\n/* harmony import */ var _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n/* harmony import */ var _utils_render_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/render.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/render.mjs\");\n/* harmony import */ var _dom_value_types_defaults_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dom/value-types/defaults.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/is-svg-tag.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nclass SVGVisualElement extends _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (_html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.transformProps.has(key)) {\n            const defaultType = (0,_dom_value_types_defaults_mjs__WEBPACK_IMPORTED_MODULE_2__.getDefaultValueType)(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !_utils_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_3__.camelCaseAttributes.has(key) ? (0,_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_4__.camelToDash)(key) : key;\n        return instance.getAttribute(key);\n    }\n    measureInstanceViewportBox() {\n        return (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_5__.createBox)();\n    }\n    scrapeMotionValuesFromProps(props, prevProps) {\n        return (0,_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_6__.scrapeMotionValuesFromProps)(props, prevProps);\n    }\n    build(renderState, latestValues, options, props) {\n        (0,_utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_7__.buildSVGAttrs)(renderState, latestValues, options, this.isSVGTag, props.transformTemplate);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        (0,_utils_render_mjs__WEBPACK_IMPORTED_MODULE_8__.renderSVG)(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = (0,_utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_9__.isSVGTag)(instance.tagName);\n        super.mount(instance);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/config-motion.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/config-motion.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgMotionConfig: function() { return /* binding */ svgMotionConfig; }\n/* harmony export */ });\n/* harmony import */ var _utils_render_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/render.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/render.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/utils/use-visual-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\");\n/* harmony import */ var _utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/build-attrs.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\");\n/* harmony import */ var _utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/is-svg-tag.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\nconst svgMotionConfig = {\n    useVisualState: (0,_motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__.makeUseVisualState)({\n        scrapeMotionValuesFromProps: _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__.scrapeMotionValuesFromProps,\n        createRenderState: _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__.createSvgRenderState,\n        onMount: (props, instance, { renderState, latestValues }) => {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.read(() => {\n                try {\n                    renderState.dimensions =\n                        typeof instance.getBBox ===\n                            \"function\"\n                            ? instance.getBBox()\n                            : instance.getBoundingClientRect();\n                }\n                catch (e) {\n                    // Most likely trying to measure an unrendered element under Firefox\n                    renderState.dimensions = {\n                        x: 0,\n                        y: 0,\n                        width: 0,\n                        height: 0,\n                    };\n                }\n            });\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.render(() => {\n                (0,_utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_4__.buildSVGAttrs)(renderState, latestValues, { enableHardwareAcceleration: false }, (0,_utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_5__.isSVGTag)(instance.tagName), props.transformTemplate);\n                (0,_utils_render_mjs__WEBPACK_IMPORTED_MODULE_6__.renderSVG)(instance, renderState);\n            });\n        },\n    }),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/config-motion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lowercaseSVGElements: function() { return /* binding */ lowercaseSVGElements; }\n/* harmony export */ });\n/**\n * We keep these listed seperately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\n    \"animate\",\n    \"circle\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"g\",\n    \"image\",\n    \"line\",\n    \"filter\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"rect\",\n    \"stop\",\n    \"switch\",\n    \"symbol\",\n    \"svg\",\n    \"text\",\n    \"tspan\",\n    \"use\",\n    \"view\",\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy9sb3dlcmNhc2UtZWxlbWVudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvc3ZnL2xvd2VyY2FzZS1lbGVtZW50cy5tanM/ZjJkNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFdlIGtlZXAgdGhlc2UgbGlzdGVkIHNlcGVyYXRlbHkgYXMgd2UgdXNlIHRoZSBsb3dlcmNhc2UgdGFnIG5hbWVzIGFzIHBhcnRcbiAqIG9mIHRoZSBydW50aW1lIGJ1bmRsZSB0byBkZXRlY3QgU1ZHIGNvbXBvbmVudHNcbiAqL1xuY29uc3QgbG93ZXJjYXNlU1ZHRWxlbWVudHMgPSBbXG4gICAgXCJhbmltYXRlXCIsXG4gICAgXCJjaXJjbGVcIixcbiAgICBcImRlZnNcIixcbiAgICBcImRlc2NcIixcbiAgICBcImVsbGlwc2VcIixcbiAgICBcImdcIixcbiAgICBcImltYWdlXCIsXG4gICAgXCJsaW5lXCIsXG4gICAgXCJmaWx0ZXJcIixcbiAgICBcIm1hcmtlclwiLFxuICAgIFwibWFza1wiLFxuICAgIFwibWV0YWRhdGFcIixcbiAgICBcInBhdGhcIixcbiAgICBcInBhdHRlcm5cIixcbiAgICBcInBvbHlnb25cIixcbiAgICBcInBvbHlsaW5lXCIsXG4gICAgXCJyZWN0XCIsXG4gICAgXCJzdG9wXCIsXG4gICAgXCJzd2l0Y2hcIixcbiAgICBcInN5bWJvbFwiLFxuICAgIFwic3ZnXCIsXG4gICAgXCJ0ZXh0XCIsXG4gICAgXCJ0c3BhblwiLFxuICAgIFwidXNlXCIsXG4gICAgXCJ2aWV3XCIsXG5dO1xuXG5leHBvcnQgeyBsb3dlcmNhc2VTVkdFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/use-props.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/use-props.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSVGProps: function() { return /* binding */ useSVGProps; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../html/use-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/use-props.mjs\");\n/* harmony import */ var _utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/build-attrs.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\");\n/* harmony import */ var _utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/is-svg-tag.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\");\n\n\n\n\n\n\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n    const visualProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        const state = (0,_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_1__.createSvgRenderState)();\n        (0,_utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_2__.buildSVGAttrs)(state, visualState, { enableHardwareAcceleration: false }, (0,_utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_3__.isSVGTag)(Component), props.transformTemplate);\n        return {\n            ...state.attrs,\n            style: { ...state.style },\n        };\n    }, [visualState]);\n    if (props.style) {\n        const rawStyles = {};\n        (0,_html_use_props_mjs__WEBPACK_IMPORTED_MODULE_4__.copyRawValuesOnly)(rawStyles, props.style, props);\n        visualProps.style = { ...rawStyles, ...visualProps.style };\n    }\n    return visualProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91c2UtcHJvcHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFnQztBQUMwQjtBQUNGO0FBQ2U7QUFDckI7O0FBRWxEO0FBQ0Esd0JBQXdCLDhDQUFPO0FBQy9CLHNCQUFzQixvRkFBb0I7QUFDMUMsUUFBUSxxRUFBYSx1QkFBdUIsbUNBQW1DLEVBQUUsK0RBQVE7QUFDekY7QUFDQTtBQUNBLHFCQUFxQixnQkFBZ0I7QUFDckM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFFBQVEsc0VBQWlCO0FBQ3pCLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91c2UtcHJvcHMubWpzPzE1ZmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNvcHlSYXdWYWx1ZXNPbmx5IH0gZnJvbSAnLi4vaHRtbC91c2UtcHJvcHMubWpzJztcbmltcG9ydCB7IGJ1aWxkU1ZHQXR0cnMgfSBmcm9tICcuL3V0aWxzL2J1aWxkLWF0dHJzLm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVTdmdSZW5kZXJTdGF0ZSB9IGZyb20gJy4vdXRpbHMvY3JlYXRlLXJlbmRlci1zdGF0ZS5tanMnO1xuaW1wb3J0IHsgaXNTVkdUYWcgfSBmcm9tICcuL3V0aWxzL2lzLXN2Zy10YWcubWpzJztcblxuZnVuY3Rpb24gdXNlU1ZHUHJvcHMocHJvcHMsIHZpc3VhbFN0YXRlLCBfaXNTdGF0aWMsIENvbXBvbmVudCkge1xuICAgIGNvbnN0IHZpc3VhbFByb3BzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gY3JlYXRlU3ZnUmVuZGVyU3RhdGUoKTtcbiAgICAgICAgYnVpbGRTVkdBdHRycyhzdGF0ZSwgdmlzdWFsU3RhdGUsIHsgZW5hYmxlSGFyZHdhcmVBY2NlbGVyYXRpb246IGZhbHNlIH0sIGlzU1ZHVGFnKENvbXBvbmVudCksIHByb3BzLnRyYW5zZm9ybVRlbXBsYXRlKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLmF0dHJzLFxuICAgICAgICAgICAgc3R5bGU6IHsgLi4uc3RhdGUuc3R5bGUgfSxcbiAgICAgICAgfTtcbiAgICB9LCBbdmlzdWFsU3RhdGVdKTtcbiAgICBpZiAocHJvcHMuc3R5bGUpIHtcbiAgICAgICAgY29uc3QgcmF3U3R5bGVzID0ge307XG4gICAgICAgIGNvcHlSYXdWYWx1ZXNPbmx5KHJhd1N0eWxlcywgcHJvcHMuc3R5bGUsIHByb3BzKTtcbiAgICAgICAgdmlzdWFsUHJvcHMuc3R5bGUgPSB7IC4uLnJhd1N0eWxlcywgLi4udmlzdWFsUHJvcHMuc3R5bGUgfTtcbiAgICB9XG4gICAgcmV0dXJuIHZpc3VhbFByb3BzO1xufVxuXG5leHBvcnQgeyB1c2VTVkdQcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/use-props.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildSVGAttrs: function() { return /* binding */ buildSVGAttrs; }\n/* harmony export */ });\n/* harmony import */ var _html_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/build-styles.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _transform_origin_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transform-origin.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs\");\n/* harmony import */ var _path_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./path.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/path.mjs\");\n\n\n\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, originX, originY, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, options, isSVGTag, transformTemplate) {\n    (0,_html_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_0__.buildHTMLStyles)(state, latest, options, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style, dimensions } = state;\n    /**\n     * However, we apply transforms as CSS transforms. So if we detect a transform we take it from attrs\n     * and copy it into style.\n     */\n    if (attrs.transform) {\n        if (dimensions)\n            style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    // Parse transformOrigin\n    if (dimensions &&\n        (originX !== undefined || originY !== undefined || style.transform)) {\n        style.transformOrigin = (0,_transform_origin_mjs__WEBPACK_IMPORTED_MODULE_1__.calcSVGTransformOrigin)(dimensions, originX !== undefined ? originX : 0.5, originY !== undefined ? originY : 0.5);\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        (0,_path_mjs__WEBPACK_IMPORTED_MODULE_2__.buildSVGPath)(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelCaseAttributes: function() { return /* binding */ camelCaseAttributes; }\n/* harmony export */ });\n/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9jYW1lbC1jYXNlLWF0dHJzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRStCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9jYW1lbC1jYXNlLWF0dHJzLm1qcz8yMzhhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSBzZXQgb2YgYXR0cmlidXRlIG5hbWVzIHRoYXQgYXJlIGFsd2F5cyByZWFkL3dyaXR0ZW4gYXMgY2FtZWwgY2FzZS5cbiAqL1xuY29uc3QgY2FtZWxDYXNlQXR0cmlidXRlcyA9IG5ldyBTZXQoW1xuICAgIFwiYmFzZUZyZXF1ZW5jeVwiLFxuICAgIFwiZGlmZnVzZUNvbnN0YW50XCIsXG4gICAgXCJrZXJuZWxNYXRyaXhcIixcbiAgICBcImtlcm5lbFVuaXRMZW5ndGhcIixcbiAgICBcImtleVNwbGluZXNcIixcbiAgICBcImtleVRpbWVzXCIsXG4gICAgXCJsaW1pdGluZ0NvbmVBbmdsZVwiLFxuICAgIFwibWFya2VySGVpZ2h0XCIsXG4gICAgXCJtYXJrZXJXaWR0aFwiLFxuICAgIFwibnVtT2N0YXZlc1wiLFxuICAgIFwidGFyZ2V0WFwiLFxuICAgIFwidGFyZ2V0WVwiLFxuICAgIFwic3VyZmFjZVNjYWxlXCIsXG4gICAgXCJzcGVjdWxhckNvbnN0YW50XCIsXG4gICAgXCJzcGVjdWxhckV4cG9uZW50XCIsXG4gICAgXCJzdGREZXZpYXRpb25cIixcbiAgICBcInRhYmxlVmFsdWVzXCIsXG4gICAgXCJ2aWV3Qm94XCIsXG4gICAgXCJncmFkaWVudFRyYW5zZm9ybVwiLFxuICAgIFwicGF0aExlbmd0aFwiLFxuICAgIFwic3RhcnRPZmZzZXRcIixcbiAgICBcInRleHRMZW5ndGhcIixcbiAgICBcImxlbmd0aEFkanVzdFwiLFxuXSk7XG5cbmV4cG9ydCB7IGNhbWVsQ2FzZUF0dHJpYnV0ZXMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSvgRenderState: function() { return /* binding */ createSvgRenderState; }\n/* harmony export */ });\n/* harmony import */ var _html_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/create-render-state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\nconst createSvgRenderState = () => ({\n    ...(0,_html_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_0__.createHtmlRenderState)(),\n    attrs: {},\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9jcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRjs7QUFFakY7QUFDQSxPQUFPLDBGQUFxQjtBQUM1QixhQUFhO0FBQ2IsQ0FBQzs7QUFFK0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvc3ZnL3V0aWxzL2NyZWF0ZS1yZW5kZXItc3RhdGUubWpzPzdjYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlSHRtbFJlbmRlclN0YXRlIH0gZnJvbSAnLi4vLi4vaHRtbC91dGlscy9jcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyc7XG5cbmNvbnN0IGNyZWF0ZVN2Z1JlbmRlclN0YXRlID0gKCkgPT4gKHtcbiAgICAuLi5jcmVhdGVIdG1sUmVuZGVyU3RhdGUoKSxcbiAgICBhdHRyczoge30sXG59KTtcblxuZXhwb3J0IHsgY3JlYXRlU3ZnUmVuZGVyU3RhdGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSVGTag: function() { return /* binding */ isSVGTag; }\n/* harmony export */ });\nconst isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9pcy1zdmctdGFnLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9pcy1zdmctdGFnLm1qcz9lMGQxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzU1ZHVGFnID0gKHRhZykgPT4gdHlwZW9mIHRhZyA9PT0gXCJzdHJpbmdcIiAmJiB0YWcudG9Mb3dlckNhc2UoKSA9PT0gXCJzdmdcIjtcblxuZXhwb3J0IHsgaXNTVkdUYWcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/path.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/path.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildSVGPath: function() { return /* binding */ buildSVGPath; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.transform(-offset);\n    // Build the dash array\n    const pathLength = _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.transform(length);\n    const pathSpacing = _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/path.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/render.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/render.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderSVG: function() { return /* binding */ renderSVG; }\n/* harmony export */ });\n/* harmony import */ var _dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../dom/utils/camel-to-dash.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\");\n/* harmony import */ var _html_utils_render_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/render.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/render.mjs\");\n/* harmony import */ var _camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./camel-case-attrs.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\");\n\n\n\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    (0,_html_utils_render_mjs__WEBPACK_IMPORTED_MODULE_0__.renderHTML)(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_1__.camelCaseAttributes.has(key) ? (0,_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_2__.camelToDash)(key) : key, renderState.attrs[key]);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9yZW5kZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0U7QUFDUDtBQUNJOztBQUU3RDtBQUNBLElBQUksa0VBQVU7QUFDZDtBQUNBLDhCQUE4QixzRUFBbUIsWUFBWSx5RUFBVztBQUN4RTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9zdmcvdXRpbHMvcmVuZGVyLm1qcz83NGNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhbWVsVG9EYXNoIH0gZnJvbSAnLi4vLi4vZG9tL3V0aWxzL2NhbWVsLXRvLWRhc2gubWpzJztcbmltcG9ydCB7IHJlbmRlckhUTUwgfSBmcm9tICcuLi8uLi9odG1sL3V0aWxzL3JlbmRlci5tanMnO1xuaW1wb3J0IHsgY2FtZWxDYXNlQXR0cmlidXRlcyB9IGZyb20gJy4vY2FtZWwtY2FzZS1hdHRycy5tanMnO1xuXG5mdW5jdGlvbiByZW5kZXJTVkcoZWxlbWVudCwgcmVuZGVyU3RhdGUsIF9zdHlsZVByb3AsIHByb2plY3Rpb24pIHtcbiAgICByZW5kZXJIVE1MKGVsZW1lbnQsIHJlbmRlclN0YXRlLCB1bmRlZmluZWQsIHByb2plY3Rpb24pO1xuICAgIGZvciAoY29uc3Qga2V5IGluIHJlbmRlclN0YXRlLmF0dHJzKSB7XG4gICAgICAgIGVsZW1lbnQuc2V0QXR0cmlidXRlKCFjYW1lbENhc2VBdHRyaWJ1dGVzLmhhcyhrZXkpID8gY2FtZWxUb0Rhc2goa2V5KSA6IGtleSwgcmVuZGVyU3RhdGUuYXR0cnNba2V5XSk7XG4gICAgfVxufVxuXG5leHBvcnQgeyByZW5kZXJTVkcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/render.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrapeMotionValuesFromProps: function() { return /* binding */ scrapeMotionValuesFromProps; }\n/* harmony export */ });\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _html_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/scrape-motion-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../html/utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/html/utils/transform.mjs\");\n\n\n\n\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n    const newValues = (0,_html_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_0__.scrapeMotionValuesFromProps)(props, prevProps);\n    for (const key in props) {\n        if ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(props[key]) || (0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(prevProps[key])) {\n            const targetKey = _html_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_2__.transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9zY3JhcGUtbW90aW9uLXZhbHVlcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5RTtBQUNnRDtBQUNyRDs7QUFFcEU7QUFDQSxzQkFBc0IsaUdBQTZCO0FBQ25EO0FBQ0EsWUFBWSwrRUFBYSxnQkFBZ0IsK0VBQWE7QUFDdEQsOEJBQThCLHlFQUFrQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvc3ZnL3V0aWxzL3NjcmFwZS1tb3Rpb24tdmFsdWVzLm1qcz9kOThmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzTW90aW9uVmFsdWUgfSBmcm9tICcuLi8uLi8uLi92YWx1ZS91dGlscy9pcy1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyBhcyBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHMkMSB9IGZyb20gJy4uLy4uL2h0bWwvdXRpbHMvc2NyYXBlLW1vdGlvbi12YWx1ZXMubWpzJztcbmltcG9ydCB7IHRyYW5zZm9ybVByb3BPcmRlciB9IGZyb20gJy4uLy4uL2h0bWwvdXRpbHMvdHJhbnNmb3JtLm1qcyc7XG5cbmZ1bmN0aW9uIHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyhwcm9wcywgcHJldlByb3BzKSB7XG4gICAgY29uc3QgbmV3VmFsdWVzID0gc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzJDEocHJvcHMsIHByZXZQcm9wcyk7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gcHJvcHMpIHtcbiAgICAgICAgaWYgKGlzTW90aW9uVmFsdWUocHJvcHNba2V5XSkgfHwgaXNNb3Rpb25WYWx1ZShwcmV2UHJvcHNba2V5XSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHRhcmdldEtleSA9IHRyYW5zZm9ybVByb3BPcmRlci5pbmRleE9mKGtleSkgIT09IC0xXG4gICAgICAgICAgICAgICAgPyBcImF0dHJcIiArIGtleS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGtleS5zdWJzdHJpbmcoMSlcbiAgICAgICAgICAgICAgICA6IGtleTtcbiAgICAgICAgICAgIG5ld1ZhbHVlc1t0YXJnZXRLZXldID0gcHJvcHNba2V5XTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbmV3VmFsdWVzO1xufVxuXG5leHBvcnQgeyBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcSVGTransformOrigin: function() { return /* binding */ calcSVGTransformOrigin; }\n/* harmony export */ });\n/* harmony import */ var _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../value/types/numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n\n\nfunction calcOrigin(origin, offset, size) {\n    return typeof origin === \"string\"\n        ? origin\n        : _value_types_numbers_units_mjs__WEBPACK_IMPORTED_MODULE_0__.px.transform(offset + size * origin);\n}\n/**\n * The SVG transform origin defaults are different to CSS and is less intuitive,\n * so we use the measured dimensions of the SVG to reconcile these.\n */\nfunction calcSVGTransformOrigin(dimensions, originX, originY) {\n    const pxOriginX = calcOrigin(originX, dimensions.x, dimensions.width);\n    const pxOriginY = calcOrigin(originY, dimensions.y, dimensions.height);\n    return `${pxOriginX} ${pxOriginY}`;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy90cmFuc2Zvcm0tb3JpZ2luLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDs7QUFFNUQ7QUFDQTtBQUNBO0FBQ0EsVUFBVSw4REFBRTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFdBQVcsRUFBRSxVQUFVO0FBQ3JDOztBQUVrQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9zdmcvdXRpbHMvdHJhbnNmb3JtLW9yaWdpbi5tanM/M2EwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBweCB9IGZyb20gJy4uLy4uLy4uL3ZhbHVlL3R5cGVzL251bWJlcnMvdW5pdHMubWpzJztcblxuZnVuY3Rpb24gY2FsY09yaWdpbihvcmlnaW4sIG9mZnNldCwgc2l6ZSkge1xuICAgIHJldHVybiB0eXBlb2Ygb3JpZ2luID09PSBcInN0cmluZ1wiXG4gICAgICAgID8gb3JpZ2luXG4gICAgICAgIDogcHgudHJhbnNmb3JtKG9mZnNldCArIHNpemUgKiBvcmlnaW4pO1xufVxuLyoqXG4gKiBUaGUgU1ZHIHRyYW5zZm9ybSBvcmlnaW4gZGVmYXVsdHMgYXJlIGRpZmZlcmVudCB0byBDU1MgYW5kIGlzIGxlc3MgaW50dWl0aXZlLFxuICogc28gd2UgdXNlIHRoZSBtZWFzdXJlZCBkaW1lbnNpb25zIG9mIHRoZSBTVkcgdG8gcmVjb25jaWxlIHRoZXNlLlxuICovXG5mdW5jdGlvbiBjYWxjU1ZHVHJhbnNmb3JtT3JpZ2luKGRpbWVuc2lvbnMsIG9yaWdpblgsIG9yaWdpblkpIHtcbiAgICBjb25zdCBweE9yaWdpblggPSBjYWxjT3JpZ2luKG9yaWdpblgsIGRpbWVuc2lvbnMueCwgZGltZW5zaW9ucy53aWR0aCk7XG4gICAgY29uc3QgcHhPcmlnaW5ZID0gY2FsY09yaWdpbihvcmlnaW5ZLCBkaW1lbnNpb25zLnksIGRpbWVuc2lvbnMuaGVpZ2h0KTtcbiAgICByZXR1cm4gYCR7cHhPcmlnaW5YfSAke3B4T3JpZ2luWX1gO1xufVxuXG5leHBvcnQgeyBjYWxjU1ZHVHJhbnNmb3JtT3JpZ2luIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/animation-state.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/animation-state.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkVariantsDidChange: function() { return /* binding */ checkVariantsDidChange; },\n/* harmony export */   createAnimationState: function() { return /* binding */ createAnimationState; }\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../animation/utils/is-keyframes-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\");\n/* harmony import */ var _utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/shallow-compare.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/shallow-compare.mjs\");\n/* harmony import */ var _is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is-variant-label.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolve-dynamic-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n/* harmony import */ var _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variant-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n/* harmony import */ var _animation_interfaces_visual_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../animation/interfaces/visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs\");\n\n\n\n\n\n\n\n\nconst reversePriorityOrder = [..._variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantPriorityOrder].reverse();\nconst numAnimationTypes = _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => (0,_animation_interfaces_visual_element_mjs__WEBPACK_IMPORTED_MODULE_1__.animateVisualElement)(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    const state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (acc, definition) => {\n        const resolved = (0,_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveVariant)(visualElement, definition);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(options, changedActiveType) {\n        const props = visualElement.getProps();\n        const context = visualElement.getVariantContext(true) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined ? props[type] : context[type];\n            const propIsVariant = (0,_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_3__.isVariantLabel)(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] && prop !== props[type] && propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                (0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_4__.isAnimationControls)(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues, {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if ((0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_5__.isKeyframesTarget)(next) && (0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_5__.isKeyframesTarget)(prev)) {\n                    valueHasChanged = !(0,_utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_6__.shallowCompare)(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to hard-block animations\n             */\n            if (shouldAnimateType && (!isInherited || handledRemovedValues)) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type, ...options },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                if (fallbackTarget !== undefined) {\n                    fallbackAnimation[key] = fallbackTarget;\n                }\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive, options) {\n        var _a;\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        (_a = visualElement.variantChildren) === null || _a === void 0 ? void 0 : _a.forEach((child) => { var _a; return (_a = child.animationState) === null || _a === void 0 ? void 0 : _a.setActive(type, isActive); });\n        state[type].isActive = isActive;\n        const animations = animateChanges(options, type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !(0,_utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_6__.shallowCompare)(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/animation-state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compareByDepth: function() { return /* binding */ compareByDepth; }\n/* harmony export */ });\nconst compareByDepth = (a, b) => a.depth - b.depth;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2NvbXBhcmUtYnktZGVwdGgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFMEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvdXRpbHMvY29tcGFyZS1ieS1kZXB0aC5tanM/MzFjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjb21wYXJlQnlEZXB0aCA9IChhLCBiKSA9PiBhLmRlcHRoIC0gYi5kZXB0aDtcblxuZXhwb3J0IHsgY29tcGFyZUJ5RGVwdGggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlatTree: function() { return /* binding */ FlatTree; }\n/* harmony export */ });\n/* harmony import */ var _utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/array.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\");\n/* harmony import */ var _compare_by_depth_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compare-by-depth.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs\");\n\n\n\nclass FlatTree {\n    constructor() {\n        this.children = [];\n        this.isDirty = false;\n    }\n    add(child) {\n        (0,_utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.children, child);\n        this.isDirty = true;\n    }\n    remove(child) {\n        (0,_utils_array_mjs__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.children, child);\n        this.isDirty = true;\n    }\n    forEach(callback) {\n        this.isDirty && this.children.sort(_compare_by_depth_mjs__WEBPACK_IMPORTED_MODULE_1__.compareByDepth);\n        this.isDirty = false;\n        this.children.forEach(callback);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2ZsYXQtdHJlZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBQ1Y7O0FBRXhEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsK0RBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0EsUUFBUSw0REFBVTtBQUNsQjtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsaUVBQWM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2ZsYXQtdHJlZS5tanM/MzA4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhZGRVbmlxdWVJdGVtLCByZW1vdmVJdGVtIH0gZnJvbSAnLi4vLi4vdXRpbHMvYXJyYXkubWpzJztcbmltcG9ydCB7IGNvbXBhcmVCeURlcHRoIH0gZnJvbSAnLi9jb21wYXJlLWJ5LWRlcHRoLm1qcyc7XG5cbmNsYXNzIEZsYXRUcmVlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5jaGlsZHJlbiA9IFtdO1xuICAgICAgICB0aGlzLmlzRGlydHkgPSBmYWxzZTtcbiAgICB9XG4gICAgYWRkKGNoaWxkKSB7XG4gICAgICAgIGFkZFVuaXF1ZUl0ZW0odGhpcy5jaGlsZHJlbiwgY2hpbGQpO1xuICAgICAgICB0aGlzLmlzRGlydHkgPSB0cnVlO1xuICAgIH1cbiAgICByZW1vdmUoY2hpbGQpIHtcbiAgICAgICAgcmVtb3ZlSXRlbSh0aGlzLmNoaWxkcmVuLCBjaGlsZCk7XG4gICAgICAgIHRoaXMuaXNEaXJ0eSA9IHRydWU7XG4gICAgfVxuICAgIGZvckVhY2goY2FsbGJhY2spIHtcbiAgICAgICAgdGhpcy5pc0RpcnR5ICYmIHRoaXMuY2hpbGRyZW4uc29ydChjb21wYXJlQnlEZXB0aCk7XG4gICAgICAgIHRoaXMuaXNEaXJ0eSA9IGZhbHNlO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLmZvckVhY2goY2FsbGJhY2spO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgRmxhdFRyZWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isControllingVariants: function() { return /* binding */ isControllingVariants; },\n/* harmony export */   isVariantNode: function() { return /* binding */ isVariantNode; }\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-variant-label.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./variant-props.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n\n\n\n\nfunction isControllingVariants(props) {\n    return ((0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_0__.isAnimationControls)(props.animate) ||\n        _variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__.variantProps.some((name) => (0,_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_2__.isVariantLabel)(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2lzLWNvbnRyb2xsaW5nLXZhcmlhbnRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzRjtBQUM5QjtBQUNMOztBQUVuRDtBQUNBLFlBQVksK0ZBQW1CO0FBQy9CLFFBQVEsNERBQVksZ0JBQWdCLHFFQUFjO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBOztBQUVnRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci91dGlscy9pcy1jb250cm9sbGluZy12YXJpYW50cy5tanM/NjEzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0FuaW1hdGlvbkNvbnRyb2xzIH0gZnJvbSAnLi4vLi4vYW5pbWF0aW9uL3V0aWxzL2lzLWFuaW1hdGlvbi1jb250cm9scy5tanMnO1xuaW1wb3J0IHsgaXNWYXJpYW50TGFiZWwgfSBmcm9tICcuL2lzLXZhcmlhbnQtbGFiZWwubWpzJztcbmltcG9ydCB7IHZhcmlhbnRQcm9wcyB9IGZyb20gJy4vdmFyaWFudC1wcm9wcy5tanMnO1xuXG5mdW5jdGlvbiBpc0NvbnRyb2xsaW5nVmFyaWFudHMocHJvcHMpIHtcbiAgICByZXR1cm4gKGlzQW5pbWF0aW9uQ29udHJvbHMocHJvcHMuYW5pbWF0ZSkgfHxcbiAgICAgICAgdmFyaWFudFByb3BzLnNvbWUoKG5hbWUpID0+IGlzVmFyaWFudExhYmVsKHByb3BzW25hbWVdKSkpO1xufVxuZnVuY3Rpb24gaXNWYXJpYW50Tm9kZShwcm9wcykge1xuICAgIHJldHVybiBCb29sZWFuKGlzQ29udHJvbGxpbmdWYXJpYW50cyhwcm9wcykgfHwgcHJvcHMudmFyaWFudHMpO1xufVxuXG5leHBvcnQgeyBpc0NvbnRyb2xsaW5nVmFyaWFudHMsIGlzVmFyaWFudE5vZGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isVariantLabel: function() { return /* binding */ isVariantLabel; }\n/* harmony export */ });\n/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2lzLXZhcmlhbnQtbGFiZWwubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2lzLXZhcmlhbnQtbGFiZWwubWpzP2Q5ZjQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEZWNpZGVzIGlmIHRoZSBzdXBwbGllZCB2YXJpYWJsZSBpcyB2YXJpYW50IGxhYmVsXG4gKi9cbmZ1bmN0aW9uIGlzVmFyaWFudExhYmVsKHYpIHtcbiAgICByZXR1cm4gdHlwZW9mIHYgPT09IFwic3RyaW5nXCIgfHwgQXJyYXkuaXNBcnJheSh2KTtcbn1cblxuZXhwb3J0IHsgaXNWYXJpYW50TGFiZWwgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/motion-values.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/motion-values.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateMotionValuesFromProps: function() { return /* binding */ updateMotionValuesFromProps; }\n/* harmony export */ });\n/* harmony import */ var _value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../value/use-will-change/is.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-will-change/is.mjs\");\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _value_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../value/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n\n\n\n\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    const { willChange } = next;\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n            if ((0,_value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_1__.isWillChangeMotionValue)(willChange)) {\n                willChange.add(key);\n            }\n            /**\n             * Check the version of the incoming motion value with this version\n             * and warn against mismatches.\n             */\n            if (true) {\n                (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(nextValue.version === \"10.18.0\", `Attempting to mix Framer Motion versions ${nextValue.version} with 10.18.0 may not work as expected.`);\n            }\n        }\n        else if ((0,_value_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_3__.motionValue)(nextValue, { owner: element }));\n            if ((0,_value_use_will_change_is_mjs__WEBPACK_IMPORTED_MODULE_1__.isWillChangeMotionValue)(willChange)) {\n                willChange.remove(key);\n            }\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                // TODO: Only update values that aren't being animated or even looked at\n                !existingValue.hasAnimated && existingValue.set(nextValue);\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_3__.motionValue)(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/motion-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveVariant: function() { return /* binding */ resolveVariant; }\n/* harmony export */ });\n/* harmony import */ var _resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./resolve-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n\n\n/**\n * Creates an object containing the latest state of every MotionValue on a VisualElement\n */\nfunction getCurrent(visualElement) {\n    const current = {};\n    visualElement.values.forEach((value, key) => (current[key] = value.get()));\n    return current;\n}\n/**\n * Creates an object containing the latest velocity of every MotionValue on a VisualElement\n */\nfunction getVelocity(visualElement) {\n    const velocity = {};\n    visualElement.values.forEach((value, key) => (velocity[key] = value.getVelocity()));\n    return velocity;\n}\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return (0,_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveVariantFromProps)(props, definition, custom !== undefined ? custom : props.custom, getCurrent(visualElement), getVelocity(visualElement));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3Jlc29sdmUtZHluYW1pYy12YXJpYW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUU7O0FBRWpFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsOEVBQXVCO0FBQ2xDOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci91dGlscy9yZXNvbHZlLWR5bmFtaWMtdmFyaWFudHMubWpzP2EwOGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZVZhcmlhbnRGcm9tUHJvcHMgfSBmcm9tICcuL3Jlc29sdmUtdmFyaWFudHMubWpzJztcblxuLyoqXG4gKiBDcmVhdGVzIGFuIG9iamVjdCBjb250YWluaW5nIHRoZSBsYXRlc3Qgc3RhdGUgb2YgZXZlcnkgTW90aW9uVmFsdWUgb24gYSBWaXN1YWxFbGVtZW50XG4gKi9cbmZ1bmN0aW9uIGdldEN1cnJlbnQodmlzdWFsRWxlbWVudCkge1xuICAgIGNvbnN0IGN1cnJlbnQgPSB7fTtcbiAgICB2aXN1YWxFbGVtZW50LnZhbHVlcy5mb3JFYWNoKCh2YWx1ZSwga2V5KSA9PiAoY3VycmVudFtrZXldID0gdmFsdWUuZ2V0KCkpKTtcbiAgICByZXR1cm4gY3VycmVudDtcbn1cbi8qKlxuICogQ3JlYXRlcyBhbiBvYmplY3QgY29udGFpbmluZyB0aGUgbGF0ZXN0IHZlbG9jaXR5IG9mIGV2ZXJ5IE1vdGlvblZhbHVlIG9uIGEgVmlzdWFsRWxlbWVudFxuICovXG5mdW5jdGlvbiBnZXRWZWxvY2l0eSh2aXN1YWxFbGVtZW50KSB7XG4gICAgY29uc3QgdmVsb2NpdHkgPSB7fTtcbiAgICB2aXN1YWxFbGVtZW50LnZhbHVlcy5mb3JFYWNoKCh2YWx1ZSwga2V5KSA9PiAodmVsb2NpdHlba2V5XSA9IHZhbHVlLmdldFZlbG9jaXR5KCkpKTtcbiAgICByZXR1cm4gdmVsb2NpdHk7XG59XG5mdW5jdGlvbiByZXNvbHZlVmFyaWFudCh2aXN1YWxFbGVtZW50LCBkZWZpbml0aW9uLCBjdXN0b20pIHtcbiAgICBjb25zdCBwcm9wcyA9IHZpc3VhbEVsZW1lbnQuZ2V0UHJvcHMoKTtcbiAgICByZXR1cm4gcmVzb2x2ZVZhcmlhbnRGcm9tUHJvcHMocHJvcHMsIGRlZmluaXRpb24sIGN1c3RvbSAhPT0gdW5kZWZpbmVkID8gY3VzdG9tIDogcHJvcHMuY3VzdG9tLCBnZXRDdXJyZW50KHZpc3VhbEVsZW1lbnQpLCBnZXRWZWxvY2l0eSh2aXN1YWxFbGVtZW50KSk7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVWYXJpYW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveVariantFromProps: function() { return /* binding */ resolveVariantFromProps; }\n/* harmony export */ });\nfunction resolveVariantFromProps(props, definition, custom, currentValues = {}, currentVelocity = {}) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    return definition;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3Jlc29sdmUtdmFyaWFudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw4RUFBOEUsc0JBQXNCO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3Jlc29sdmUtdmFyaWFudHMubWpzPzJkOWEiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVzb2x2ZVZhcmlhbnRGcm9tUHJvcHMocHJvcHMsIGRlZmluaXRpb24sIGN1c3RvbSwgY3VycmVudFZhbHVlcyA9IHt9LCBjdXJyZW50VmVsb2NpdHkgPSB7fSkge1xuICAgIC8qKlxuICAgICAqIElmIHRoZSB2YXJpYW50IGRlZmluaXRpb24gaXMgYSBmdW5jdGlvbiwgcmVzb2x2ZS5cbiAgICAgKi9cbiAgICBpZiAodHlwZW9mIGRlZmluaXRpb24gPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICBkZWZpbml0aW9uID0gZGVmaW5pdGlvbihjdXN0b20gIT09IHVuZGVmaW5lZCA/IGN1c3RvbSA6IHByb3BzLmN1c3RvbSwgY3VycmVudFZhbHVlcywgY3VycmVudFZlbG9jaXR5KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSWYgdGhlIHZhcmlhbnQgZGVmaW5pdGlvbiBpcyBhIHZhcmlhbnQgbGFiZWwsIG9yXG4gICAgICogdGhlIGZ1bmN0aW9uIHJldHVybmVkIGEgdmFyaWFudCBsYWJlbCwgcmVzb2x2ZS5cbiAgICAgKi9cbiAgICBpZiAodHlwZW9mIGRlZmluaXRpb24gPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgZGVmaW5pdGlvbiA9IHByb3BzLnZhcmlhbnRzICYmIHByb3BzLnZhcmlhbnRzW2RlZmluaXRpb25dO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBdCB0aGlzIHBvaW50IHdlJ3ZlIHJlc29sdmVkIGJvdGggZnVuY3Rpb25zIGFuZCB2YXJpYW50IGxhYmVscyxcbiAgICAgKiBidXQgdGhlIHJlc29sdmVkIHZhcmlhbnQgbGFiZWwgbWlnaHQgaXRzZWxmIGhhdmUgYmVlbiBhIGZ1bmN0aW9uLlxuICAgICAqIElmIHNvLCByZXNvbHZlLiBUaGlzIGNhbiBvbmx5IGhhdmUgcmV0dXJuZWQgYSB2YWxpZCB0YXJnZXQgb2JqZWN0LlxuICAgICAqL1xuICAgIGlmICh0eXBlb2YgZGVmaW5pdGlvbiA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIGRlZmluaXRpb24gPSBkZWZpbml0aW9uKGN1c3RvbSAhPT0gdW5kZWZpbmVkID8gY3VzdG9tIDogcHJvcHMuY3VzdG9tLCBjdXJyZW50VmFsdWVzLCBjdXJyZW50VmVsb2NpdHkpO1xuICAgIH1cbiAgICByZXR1cm4gZGVmaW5pdGlvbjtcbn1cblxuZXhwb3J0IHsgcmVzb2x2ZVZhcmlhbnRGcm9tUHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/setters.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/setters.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTargetForNewValues: function() { return /* binding */ checkTargetForNewValues; },\n/* harmony export */   getOrigin: function() { return /* binding */ getOrigin; },\n/* harmony export */   getOriginFromTransition: function() { return /* binding */ getOriginFromTransition; },\n/* harmony export */   setTarget: function() { return /* binding */ setTarget; },\n/* harmony export */   setValues: function() { return /* binding */ setValues; }\n/* harmony export */ });\n/* harmony import */ var _utils_is_numerical_string_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/is-numerical-string.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs\");\n/* harmony import */ var _utils_is_zero_value_string_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/is-zero-value-string.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs\");\n/* harmony import */ var _utils_resolve_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/resolve-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/resolve-value.mjs\");\n/* harmony import */ var _value_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../value/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n/* harmony import */ var _dom_value_types_animatable_none_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../dom/value-types/animatable-none.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs\");\n/* harmony import */ var _dom_value_types_find_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom/value-types/find.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/value-types/find.mjs\");\n/* harmony import */ var _resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolve-dynamic-variants.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n\n\n\n\n\n\n\n\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_0__.motionValue)(value));\n    }\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = (0,_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_1__.resolveVariant)(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved ? visualElement.makeTargetAnimatable(resolved, false) : {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = (0,_utils_resolve_value_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveFinalValueInKeyframes)(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\nfunction setVariants(visualElement, variantLabels) {\n    const reversedLabels = [...variantLabels].reverse();\n    reversedLabels.forEach((key) => {\n        const variant = visualElement.getVariant(key);\n        variant && setTarget(visualElement, variant);\n        if (visualElement.variantChildren) {\n            visualElement.variantChildren.forEach((child) => {\n                setVariants(child, variantLabels);\n            });\n        }\n    });\n}\nfunction setValues(visualElement, definition) {\n    if (Array.isArray(definition)) {\n        return setVariants(visualElement, definition);\n    }\n    else if (typeof definition === \"string\") {\n        return setVariants(visualElement, [definition]);\n    }\n    else {\n        setTarget(visualElement, definition);\n    }\n}\nfunction checkTargetForNewValues(visualElement, target, origin) {\n    var _a, _b;\n    const newValueKeys = Object.keys(target).filter((key) => !visualElement.hasValue(key));\n    const numNewValues = newValueKeys.length;\n    if (!numNewValues)\n        return;\n    for (let i = 0; i < numNewValues; i++) {\n        const key = newValueKeys[i];\n        const targetValue = target[key];\n        let value = null;\n        /**\n         * If the target is a series of keyframes, we can use the first value\n         * in the array. If this first value is null, we'll still need to read from the DOM.\n         */\n        if (Array.isArray(targetValue)) {\n            value = targetValue[0];\n        }\n        /**\n         * If the target isn't keyframes, or the first keyframe was null, we need to\n         * first check if an origin value was explicitly defined in the transition as \"from\",\n         * if not read the value from the DOM. As an absolute fallback, take the defined target value.\n         */\n        if (value === null) {\n            value = (_b = (_a = origin[key]) !== null && _a !== void 0 ? _a : visualElement.readValue(key)) !== null && _b !== void 0 ? _b : target[key];\n        }\n        /**\n         * If value is still undefined or null, ignore it. Preferably this would throw,\n         * but this was causing issues in Framer.\n         */\n        if (value === undefined || value === null)\n            continue;\n        if (typeof value === \"string\" &&\n            ((0,_utils_is_numerical_string_mjs__WEBPACK_IMPORTED_MODULE_3__.isNumericalString)(value) || (0,_utils_is_zero_value_string_mjs__WEBPACK_IMPORTED_MODULE_4__.isZeroValueString)(value))) {\n            // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n            value = parseFloat(value);\n        }\n        else if (!(0,_dom_value_types_find_mjs__WEBPACK_IMPORTED_MODULE_5__.findValueType)(value) && _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__.complex.test(targetValue)) {\n            value = (0,_dom_value_types_animatable_none_mjs__WEBPACK_IMPORTED_MODULE_7__.getAnimatableNone)(key, targetValue);\n        }\n        visualElement.addValue(key, (0,_value_index_mjs__WEBPACK_IMPORTED_MODULE_0__.motionValue)(value, { owner: visualElement }));\n        if (origin[key] === undefined) {\n            origin[key] = value;\n        }\n        if (value !== null)\n            visualElement.setBaseTarget(key, value);\n    }\n}\nfunction getOriginFromTransition(key, transition) {\n    if (!transition)\n        return;\n    const valueTransition = transition[key] || transition[\"default\"] || transition;\n    return valueTransition.from;\n}\nfunction getOrigin(target, transition, visualElement) {\n    const origin = {};\n    for (const key in target) {\n        const transitionOrigin = getOriginFromTransition(key, transition);\n        if (transitionOrigin !== undefined) {\n            origin[key] = transitionOrigin;\n        }\n        else {\n            const value = visualElement.getValue(key);\n            if (value) {\n                origin[key] = value.get();\n            }\n        }\n    }\n    return origin;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/setters.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   variantPriorityOrder: function() { return /* binding */ variantPriorityOrder; },\n/* harmony export */   variantProps: function() { return /* binding */ variantProps; }\n/* harmony export */ });\nconst variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3ZhcmlhbnQtcHJvcHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3ZhcmlhbnQtcHJvcHMubWpzPzUzMzUiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdmFyaWFudFByaW9yaXR5T3JkZXIgPSBbXG4gICAgXCJhbmltYXRlXCIsXG4gICAgXCJ3aGlsZUluVmlld1wiLFxuICAgIFwid2hpbGVGb2N1c1wiLFxuICAgIFwid2hpbGVIb3ZlclwiLFxuICAgIFwid2hpbGVUYXBcIixcbiAgICBcIndoaWxlRHJhZ1wiLFxuICAgIFwiZXhpdFwiLFxuXTtcbmNvbnN0IHZhcmlhbnRQcm9wcyA9IFtcImluaXRpYWxcIiwgLi4udmFyaWFudFByaW9yaXR5T3JkZXJdO1xuXG5leHBvcnQgeyB2YXJpYW50UHJpb3JpdHlPcmRlciwgdmFyaWFudFByb3BzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionGlobalConfig: function() { return /* binding */ MotionGlobalConfig; }\n/* harmony export */ });\nconst MotionGlobalConfig = {\n    skipAnimations: false,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvR2xvYmFsQ29uZmlnLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUU4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL0dsb2JhbENvbmZpZy5tanM/YjA2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBNb3Rpb25HbG9iYWxDb25maWcgPSB7XG4gICAgc2tpcEFuaW1hdGlvbnM6IGZhbHNlLFxufTtcblxuZXhwb3J0IHsgTW90aW9uR2xvYmFsQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/array.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueItem: function() { return /* binding */ addUniqueItem; },\n/* harmony export */   moveItem: function() { return /* binding */ moveItem; },\n/* harmony export */   removeItem: function() { return /* binding */ removeItem; }\n/* harmony export */ });\nfunction addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvYXJyYXkubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUrQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2FycmF5Lm1qcz85MjRlIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGFkZFVuaXF1ZUl0ZW0oYXJyLCBpdGVtKSB7XG4gICAgaWYgKGFyci5pbmRleE9mKGl0ZW0pID09PSAtMSlcbiAgICAgICAgYXJyLnB1c2goaXRlbSk7XG59XG5mdW5jdGlvbiByZW1vdmVJdGVtKGFyciwgaXRlbSkge1xuICAgIGNvbnN0IGluZGV4ID0gYXJyLmluZGV4T2YoaXRlbSk7XG4gICAgaWYgKGluZGV4ID4gLTEpXG4gICAgICAgIGFyci5zcGxpY2UoaW5kZXgsIDEpO1xufVxuLy8gQWRhcHRlZCBmcm9tIGFycmF5LW1vdmVcbmZ1bmN0aW9uIG1vdmVJdGVtKFsuLi5hcnJdLCBmcm9tSW5kZXgsIHRvSW5kZXgpIHtcbiAgICBjb25zdCBzdGFydEluZGV4ID0gZnJvbUluZGV4IDwgMCA/IGFyci5sZW5ndGggKyBmcm9tSW5kZXggOiBmcm9tSW5kZXg7XG4gICAgaWYgKHN0YXJ0SW5kZXggPj0gMCAmJiBzdGFydEluZGV4IDwgYXJyLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBlbmRJbmRleCA9IHRvSW5kZXggPCAwID8gYXJyLmxlbmd0aCArIHRvSW5kZXggOiB0b0luZGV4O1xuICAgICAgICBjb25zdCBbaXRlbV0gPSBhcnIuc3BsaWNlKGZyb21JbmRleCwgMSk7XG4gICAgICAgIGFyci5zcGxpY2UoZW5kSW5kZXgsIDAsIGl0ZW0pO1xuICAgIH1cbiAgICByZXR1cm4gYXJyO1xufVxuXG5leHBvcnQgeyBhZGRVbmlxdWVJdGVtLCBtb3ZlSXRlbSwgcmVtb3ZlSXRlbSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/clamp.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: function() { return /* binding */ clamp; }\n/* harmony export */ });\nconst clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvY2xhbXAubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9jbGFtcC5tanM/ZGQyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjbGFtcCA9IChtaW4sIG1heCwgdikgPT4gTWF0aC5taW4oTWF0aC5tYXgodiwgbWluKSwgbWF4KTtcblxuZXhwb3J0IHsgY2xhbXAgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/delay.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/delay.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   delay: function() { return /* binding */ delay; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = performance.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.read(checkElapsed, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(checkElapsed);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvZGVsYXkubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTREOztBQUU1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFdBQVc7QUFDdkM7QUFDQTtBQUNBLFlBQVksaUVBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsSUFBSSx1REFBSztBQUNULGlCQUFpQixpRUFBVztBQUM1Qjs7QUFFaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9kZWxheS5tanM/YTdhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmcmFtZSwgY2FuY2VsRnJhbWUgfSBmcm9tICcuLi9mcmFtZWxvb3AvZnJhbWUubWpzJztcblxuLyoqXG4gKiBUaW1lb3V0IGRlZmluZWQgaW4gbXNcbiAqL1xuZnVuY3Rpb24gZGVsYXkoY2FsbGJhY2ssIHRpbWVvdXQpIHtcbiAgICBjb25zdCBzdGFydCA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgIGNvbnN0IGNoZWNrRWxhcHNlZCA9ICh7IHRpbWVzdGFtcCB9KSA9PiB7XG4gICAgICAgIGNvbnN0IGVsYXBzZWQgPSB0aW1lc3RhbXAgLSBzdGFydDtcbiAgICAgICAgaWYgKGVsYXBzZWQgPj0gdGltZW91dCkge1xuICAgICAgICAgICAgY2FuY2VsRnJhbWUoY2hlY2tFbGFwc2VkKTtcbiAgICAgICAgICAgIGNhbGxiYWNrKGVsYXBzZWQgLSB0aW1lb3V0KTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgZnJhbWUucmVhZChjaGVja0VsYXBzZWQsIHRydWUpO1xuICAgIHJldHVybiAoKSA9PiBjYW5jZWxGcmFtZShjaGVja0VsYXBzZWQpO1xufVxuXG5leHBvcnQgeyBkZWxheSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/delay.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/distance.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/distance.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   distance: function() { return /* binding */ distance; },\n/* harmony export */   distance2D: function() { return /* binding */ distance2D; }\n/* harmony export */ });\nconst distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n    // Multi-dimensional\n    const xDelta = distance(a.x, b.x);\n    const yDelta = distance(a.y, b.y);\n    return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvZGlzdGFuY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvZGlzdGFuY2UubWpzPzM2NTYiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZGlzdGFuY2UgPSAoYSwgYikgPT4gTWF0aC5hYnMoYSAtIGIpO1xuZnVuY3Rpb24gZGlzdGFuY2UyRChhLCBiKSB7XG4gICAgLy8gTXVsdGktZGltZW5zaW9uYWxcbiAgICBjb25zdCB4RGVsdGEgPSBkaXN0YW5jZShhLngsIGIueCk7XG4gICAgY29uc3QgeURlbHRhID0gZGlzdGFuY2UoYS55LCBiLnkpO1xuICAgIHJldHVybiBNYXRoLnNxcnQoeERlbHRhICoqIDIgKyB5RGVsdGEgKiogMik7XG59XG5cbmV4cG9ydCB7IGRpc3RhbmNlLCBkaXN0YW5jZTJEIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/distance.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/errors.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: function() { return /* binding */ invariant; },\n/* harmony export */   warning: function() { return /* binding */ warning; }\n/* harmony export */ });\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\nlet warning = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nlet invariant = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nif (true) {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvZXJyb3JzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7O0FBRWxDLGNBQWMsMkNBQUk7QUFDbEIsZ0JBQWdCLDJDQUFJO0FBQ3BCLElBQUksSUFBcUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9lcnJvcnMubWpzPzY1MTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9vcCB9IGZyb20gJy4vbm9vcC5tanMnO1xuXG5sZXQgd2FybmluZyA9IG5vb3A7XG5sZXQgaW52YXJpYW50ID0gbm9vcDtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICB3YXJuaW5nID0gKGNoZWNrLCBtZXNzYWdlKSA9PiB7XG4gICAgICAgIGlmICghY2hlY2sgJiYgdHlwZW9mIGNvbnNvbGUgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihtZXNzYWdlKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgaW52YXJpYW50ID0gKGNoZWNrLCBtZXNzYWdlKSA9PiB7XG4gICAgICAgIGlmICghY2hlY2spIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgICAgICAgfVxuICAgIH07XG59XG5cbmV4cG9ydCB7IGludmFyaWFudCwgd2FybmluZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/get-context-window.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/get-context-window.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getContextWindow: function() { return /* binding */ getContextWindow; }\n/* harmony export */ });\n// Fixes https://github.com/framer/motion/issues/2270\nconst getContextWindow = ({ current }) => {\n    return current ? current.ownerDocument.defaultView : null;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvZ2V0LWNvbnRleHQtd2luZG93Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSw0QkFBNEIsU0FBUztBQUNyQztBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2dldC1jb250ZXh0LXdpbmRvdy5tanM/Y2NiNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaXhlcyBodHRwczovL2dpdGh1Yi5jb20vZnJhbWVyL21vdGlvbi9pc3N1ZXMvMjI3MFxuY29uc3QgZ2V0Q29udGV4dFdpbmRvdyA9ICh7IGN1cnJlbnQgfSkgPT4ge1xuICAgIHJldHVybiBjdXJyZW50ID8gY3VycmVudC5vd25lckRvY3VtZW50LmRlZmF1bHRWaWV3IDogbnVsbDtcbn07XG5cbmV4cG9ydCB7IGdldENvbnRleHRXaW5kb3cgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/get-context-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hslaToRgba: function() { return /* binding */ hslaToRgba; }\n/* harmony export */ });\n// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/interpolate.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   interpolate: function() { return /* binding */ interpolate; }\n/* harmony export */ });\n/* harmony import */ var _errors_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../value/types/color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _clamp_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _mix_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _mix_color_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mix-color.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-color.mjs\");\n/* harmony import */ var _mix_complex_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mix-complex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-complex.mjs\");\n/* harmony import */ var _pipe_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var _progress_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst mixNumber = (from, to) => (p) => (0,_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(from, to, p);\nfunction detectMixerFactory(v) {\n    if (typeof v === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof v === \"string\") {\n        return _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color.test(v) ? _mix_color_mjs__WEBPACK_IMPORTED_MODULE_2__.mixColor : _mix_complex_mjs__WEBPACK_IMPORTED_MODULE_3__.mixComplex;\n    }\n    else if (Array.isArray(v)) {\n        return _mix_complex_mjs__WEBPACK_IMPORTED_MODULE_3__.mixArray;\n    }\n    else if (typeof v === \"object\") {\n        return _mix_complex_mjs__WEBPACK_IMPORTED_MODULE_3__.mixObject;\n    }\n    return mixNumber;\n}\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || detectMixerFactory(output[0]);\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || _noop_mjs__WEBPACK_IMPORTED_MODULE_4__.noop : ease;\n            mixer = (0,_pipe_mjs__WEBPACK_IMPORTED_MODULE_5__.pipe)(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    (0,_errors_mjs__WEBPACK_IMPORTED_MODULE_6__.invariant)(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = (0,_progress_mjs__WEBPACK_IMPORTED_MODULE_7__.progress)(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator((0,_clamp_mjs__WEBPACK_IMPORTED_MODULE_8__.clamp)(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/is-browser.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: function() { return /* binding */ isBrowser; }\n/* harmony export */ });\nconst isBrowser = typeof document !== \"undefined\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtYnJvd3Nlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2lzLWJyb3dzZXIubWpzPzFmYzciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNCcm93c2VyID0gdHlwZW9mIGRvY3VtZW50ICE9PSBcInVuZGVmaW5lZFwiO1xuXG5leHBvcnQgeyBpc0Jyb3dzZXIgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNumericalString: function() { return /* binding */ isNumericalString; }\n/* harmony export */ });\n/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^\\-?\\d*\\.?\\d+$/.test(v);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtbnVtZXJpY2FsLXN0cmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2lzLW51bWVyaWNhbC1zdHJpbmcubWpzP2Q1MjQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDaGVjayBpZiB2YWx1ZSBpcyBhIG51bWVyaWNhbCBzdHJpbmcsIGllIGEgc3RyaW5nIHRoYXQgaXMgcHVyZWx5IGEgbnVtYmVyIGVnIFwiMTAwXCIgb3IgXCItMTAwLjFcIlxuICovXG5jb25zdCBpc051bWVyaWNhbFN0cmluZyA9ICh2KSA9PiAvXlxcLT9cXGQqXFwuP1xcZCskLy50ZXN0KHYpO1xuXG5leHBvcnQgeyBpc051bWVyaWNhbFN0cmluZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRefObject: function() { return /* binding */ isRefObject; }\n/* harmony export */ });\nfunction isRefObject(ref) {\n    return (ref &&\n        typeof ref === \"object\" &&\n        Object.prototype.hasOwnProperty.call(ref, \"current\"));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtcmVmLW9iamVjdC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtcmVmLW9iamVjdC5tanM/MzM1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc1JlZk9iamVjdChyZWYpIHtcbiAgICByZXR1cm4gKHJlZiAmJlxuICAgICAgICB0eXBlb2YgcmVmID09PSBcIm9iamVjdFwiICYmXG4gICAgICAgIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChyZWYsIFwiY3VycmVudFwiKSk7XG59XG5cbmV4cG9ydCB7IGlzUmVmT2JqZWN0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isZeroValueString: function() { return /* binding */ isZeroValueString; }\n/* harmony export */ });\n/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/.test(v);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtemVyby12YWx1ZS1zdHJpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9pcy16ZXJvLXZhbHVlLXN0cmluZy5tanM/MDIxMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrIGlmIHRoZSB2YWx1ZSBpcyBhIHplcm8gdmFsdWUgc3RyaW5nIGxpa2UgXCIwcHhcIiBvciBcIjAlXCJcbiAqL1xuY29uc3QgaXNaZXJvVmFsdWVTdHJpbmcgPSAodikgPT4gL14wW14uXFxzXSskLy50ZXN0KHYpO1xuXG5leHBvcnQgeyBpc1plcm9WYWx1ZVN0cmluZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/memo.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/memo.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memo: function() { return /* binding */ memo; }\n/* harmony export */ });\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWVtby5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWVtby5tanM/ZjE5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBtZW1vKGNhbGxiYWNrKSB7XG4gICAgbGV0IHJlc3VsdDtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBpZiAocmVzdWx0ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICByZXN1bHQgPSBjYWxsYmFjaygpO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH07XG59XG5cbmV4cG9ydCB7IG1lbW8gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/memo.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-color.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix-color.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixColor: function() { return /* binding */ mixColor; },\n/* harmony export */   mixLinearColor: function() { return /* binding */ mixLinearColor; }\n/* harmony export */ });\n/* harmony import */ var _mix_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _errors_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _hsla_to_rgba_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hsla-to-rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs\");\n/* harmony import */ var _value_types_color_hex_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../value/types/color/hex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\");\n/* harmony import */ var _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../value/types/color/rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n/* harmony import */ var _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../value/types/color/hsla.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\");\n\n\n\n\n\n\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    return Math.sqrt(Math.max(0, v * (to * to - fromExpo) + fromExpo));\n};\nconst colorTypes = [_value_types_color_hex_mjs__WEBPACK_IMPORTED_MODULE_0__.hex, _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_1__.rgba, _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    (0,_errors_mjs__WEBPACK_IMPORTED_MODULE_3__.invariant)(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n    let model = type.parse(color);\n    if (type === _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla) {\n        // TODO Remove this cast - needed since Framer Motion's stricter typing\n        model = (0,_hsla_to_rgba_mjs__WEBPACK_IMPORTED_MODULE_4__.hslaToRgba)(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = (0,_mix_mjs__WEBPACK_IMPORTED_MODULE_5__.mix)(fromRGBA.alpha, toRGBA.alpha, v);\n        return _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_1__.rgba.transform(blended);\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-color.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-complex.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix-complex.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixArray: function() { return /* binding */ mixArray; },\n/* harmony export */   mixComplex: function() { return /* binding */ mixComplex; },\n/* harmony export */   mixObject: function() { return /* binding */ mixObject; }\n/* harmony export */ });\n/* harmony import */ var _mix_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _mix_color_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mix-color.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-color.mjs\");\n/* harmony import */ var _pipe_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var _errors_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../value/types/color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n\n\n\n\n\n\n\nconst mixImmediate = (origin, target) => (p) => `${p > 0 ? target : origin}`;\nfunction getMixer(origin, target) {\n    if (typeof origin === \"number\") {\n        return (v) => (0,_mix_mjs__WEBPACK_IMPORTED_MODULE_0__.mix)(origin, target, v);\n    }\n    else if (_value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_1__.color.test(origin)) {\n        return (0,_mix_color_mjs__WEBPACK_IMPORTED_MODULE_2__.mixColor)(origin, target);\n    }\n    else {\n        return origin.startsWith(\"var(\")\n            ? mixImmediate(origin, target)\n            : mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to) => {\n    const output = [...from];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n    return (v) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target) => {\n    const output = { ...origin, ...target };\n    const blendValue = {};\n    for (const key in output) {\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nconst mixComplex = (origin, target) => {\n    const template = _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_3__.complex.createTransformer(target);\n    const originStats = (0,_value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_3__.analyseComplexValue)(origin);\n    const targetStats = (0,_value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_3__.analyseComplexValue)(target);\n    const canInterpolate = originStats.numVars === targetStats.numVars &&\n        originStats.numColors === targetStats.numColors &&\n        originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return (0,_pipe_mjs__WEBPACK_IMPORTED_MODULE_4__.pipe)(mixArray(originStats.values, targetStats.values), template);\n    }\n    else {\n        (0,_errors_mjs__WEBPACK_IMPORTED_MODULE_5__.warning)(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return mixImmediate(origin, target);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix-complex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix.mjs ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mix: function() { return /* binding */ mix; }\n/* harmony export */ });\n/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mix = (from, to, progress) => -progress * from + progress * to + from;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWl4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBOztBQUVBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL21peC5tanM/Y2QxMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICBWYWx1ZSBpbiByYW5nZSBmcm9tIHByb2dyZXNzXG5cbiAgR2l2ZW4gYSBsb3dlciBsaW1pdCBhbmQgYW4gdXBwZXIgbGltaXQsIHdlIHJldHVybiB0aGUgdmFsdWUgd2l0aGluXG4gIHRoYXQgcmFuZ2UgYXMgZXhwcmVzc2VkIGJ5IHByb2dyZXNzICh1c3VhbGx5IGEgbnVtYmVyIGZyb20gMCB0byAxKVxuXG4gIFNvIHByb2dyZXNzID0gMC41IHdvdWxkIGNoYW5nZVxuXG4gIGZyb20gLS0tLS0tLS0gdG9cblxuICB0b1xuXG4gIGZyb20gLS0tLSB0b1xuXG4gIEUuZy4gZnJvbSA9IDEwLCB0byA9IDIwLCBwcm9ncmVzcyA9IDAuNSA9PiAxNVxuXG4gIEBwYXJhbSBbbnVtYmVyXTogTG93ZXIgbGltaXQgb2YgcmFuZ2VcbiAgQHBhcmFtIFtudW1iZXJdOiBVcHBlciBsaW1pdCBvZiByYW5nZVxuICBAcGFyYW0gW251bWJlcl06IFRoZSBwcm9ncmVzcyBiZXR3ZWVuIGxvd2VyIGFuZCB1cHBlciBsaW1pdHMgZXhwcmVzc2VkIDAtMVxuICBAcmV0dXJuIFtudW1iZXJdOiBWYWx1ZSBhcyBjYWxjdWxhdGVkIGZyb20gcHJvZ3Jlc3Mgd2l0aGluIHJhbmdlIChub3QgbGltaXRlZCB3aXRoaW4gcmFuZ2UpXG4qL1xuY29uc3QgbWl4ID0gKGZyb20sIHRvLCBwcm9ncmVzcykgPT4gLXByb2dyZXNzICogZnJvbSArIHByb2dyZXNzICogdG8gKyBmcm9tO1xuXG5leHBvcnQgeyBtaXggfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/noop.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: function() { return /* binding */ noop; }\n/* harmony export */ });\nconst noop = (any) => any;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbm9vcC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVnQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL25vb3AubWpzP2IyYzciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgbm9vcCA9IChhbnkpID0+IGFueTtcblxuZXhwb3J0IHsgbm9vcCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/offsets/default.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultOffset: function() { return /* binding */ defaultOffset; }\n/* harmony export */ });\n/* harmony import */ var _fill_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fill.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs\");\n\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    (0,_fill_mjs__WEBPACK_IMPORTED_MODULE_0__.fillOffset)(offset, arr.length - 1);\n    return offset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvb2Zmc2V0cy9kZWZhdWx0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3Qzs7QUFFeEM7QUFDQTtBQUNBLElBQUkscURBQVU7QUFDZDtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL29mZnNldHMvZGVmYXVsdC5tanM/ZDNiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmaWxsT2Zmc2V0IH0gZnJvbSAnLi9maWxsLm1qcyc7XG5cbmZ1bmN0aW9uIGRlZmF1bHRPZmZzZXQoYXJyKSB7XG4gICAgY29uc3Qgb2Zmc2V0ID0gWzBdO1xuICAgIGZpbGxPZmZzZXQob2Zmc2V0LCBhcnIubGVuZ3RoIC0gMSk7XG4gICAgcmV0dXJuIG9mZnNldDtcbn1cblxuZXhwb3J0IHsgZGVmYXVsdE9mZnNldCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fillOffset: function() { return /* binding */ fillOffset; }\n/* harmony export */ });\n/* harmony import */ var _mix_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../mix.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix.mjs\");\n/* harmony import */ var _progress_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n\n\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = (0,_progress_mjs__WEBPACK_IMPORTED_MODULE_0__.progress)(0, remaining, i);\n        offset.push((0,_mix_mjs__WEBPACK_IMPORTED_MODULE_1__.mix)(min, 1, offsetProgress));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvb2Zmc2V0cy9maWxsLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDVTs7QUFFM0M7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEMsK0JBQStCLHVEQUFRO0FBQ3ZDLG9CQUFvQiw2Q0FBRztBQUN2QjtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL29mZnNldHMvZmlsbC5tanM/YWRkYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtaXggfSBmcm9tICcuLi9taXgubWpzJztcbmltcG9ydCB7IHByb2dyZXNzIH0gZnJvbSAnLi4vcHJvZ3Jlc3MubWpzJztcblxuZnVuY3Rpb24gZmlsbE9mZnNldChvZmZzZXQsIHJlbWFpbmluZykge1xuICAgIGNvbnN0IG1pbiA9IG9mZnNldFtvZmZzZXQubGVuZ3RoIC0gMV07XG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gcmVtYWluaW5nOyBpKyspIHtcbiAgICAgICAgY29uc3Qgb2Zmc2V0UHJvZ3Jlc3MgPSBwcm9ncmVzcygwLCByZW1haW5pbmcsIGkpO1xuICAgICAgICBvZmZzZXQucHVzaChtaXgobWluLCAxLCBvZmZzZXRQcm9ncmVzcykpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgZmlsbE9mZnNldCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/time.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/offsets/time.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertOffsetToTimes: function() { return /* binding */ convertOffsetToTimes; }\n/* harmony export */ });\nfunction convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvb2Zmc2V0cy90aW1lLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL29mZnNldHMvdGltZS5tanM/YzMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjb252ZXJ0T2Zmc2V0VG9UaW1lcyhvZmZzZXQsIGR1cmF0aW9uKSB7XG4gICAgcmV0dXJuIG9mZnNldC5tYXAoKG8pID0+IG8gKiBkdXJhdGlvbik7XG59XG5cbmV4cG9ydCB7IGNvbnZlcnRPZmZzZXRUb1RpbWVzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/time.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/pipe.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pipe: function() { return /* binding */ pipe; }\n/* harmony export */ });\n/**\n * Pipe\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcGlwZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxjQUFjO0FBQzFCLFlBQVk7QUFDWjtBQUNBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcGlwZS5tanM/NWJkMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFBpcGVcbiAqIENvbXBvc2Ugb3RoZXIgdHJhbnNmb3JtZXJzIHRvIHJ1biBsaW5lYXJpbHlcbiAqIHBpcGUobWluKDIwKSwgbWF4KDQwKSlcbiAqIEBwYXJhbSAgey4uLmZ1bmN0aW9uc30gdHJhbnNmb3JtZXJzXG4gKiBAcmV0dXJuIHtmdW5jdGlvbn1cbiAqL1xuY29uc3QgY29tYmluZUZ1bmN0aW9ucyA9IChhLCBiKSA9PiAodikgPT4gYihhKHYpKTtcbmNvbnN0IHBpcGUgPSAoLi4udHJhbnNmb3JtZXJzKSA9PiB0cmFuc2Zvcm1lcnMucmVkdWNlKGNvbWJpbmVGdW5jdGlvbnMpO1xuXG5leHBvcnQgeyBwaXBlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/progress.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progress: function() { return /* binding */ progress; }\n/* harmony export */ });\n/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcHJvZ3Jlc3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcHJvZ3Jlc3MubWpzPzYxNjkiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAgUHJvZ3Jlc3Mgd2l0aGluIGdpdmVuIHJhbmdlXG5cbiAgR2l2ZW4gYSBsb3dlciBsaW1pdCBhbmQgYW4gdXBwZXIgbGltaXQsIHdlIHJldHVybiB0aGUgcHJvZ3Jlc3NcbiAgKGV4cHJlc3NlZCBhcyBhIG51bWJlciAwLTEpIHJlcHJlc2VudGVkIGJ5IHRoZSBnaXZlbiB2YWx1ZSwgYW5kXG4gIGxpbWl0IHRoYXQgcHJvZ3Jlc3MgdG8gd2l0aGluIDAtMS5cblxuICBAcGFyYW0gW251bWJlcl06IExvd2VyIGxpbWl0XG4gIEBwYXJhbSBbbnVtYmVyXTogVXBwZXIgbGltaXRcbiAgQHBhcmFtIFtudW1iZXJdOiBWYWx1ZSB0byBmaW5kIHByb2dyZXNzIHdpdGhpbiBnaXZlbiByYW5nZVxuICBAcmV0dXJuIFtudW1iZXJdOiBQcm9ncmVzcyBvZiB2YWx1ZSB3aXRoaW4gcmFuZ2UgYXMgZXhwcmVzc2VkIDAtMVxuKi9cbmNvbnN0IHByb2dyZXNzID0gKGZyb20sIHRvLCB2YWx1ZSkgPT4ge1xuICAgIGNvbnN0IHRvRnJvbURpZmZlcmVuY2UgPSB0byAtIGZyb207XG4gICAgcmV0dXJuIHRvRnJvbURpZmZlcmVuY2UgPT09IDAgPyAxIDogKHZhbHVlIC0gZnJvbSkgLyB0b0Zyb21EaWZmZXJlbmNlO1xufTtcblxuZXhwb3J0IHsgcHJvZ3Jlc3MgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initPrefersReducedMotion: function() { return /* binding */ initPrefersReducedMotion; }\n/* harmony export */ });\n/* harmony import */ var _is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs\");\n\n\n\nfunction initPrefersReducedMotion() {\n    _state_mjs__WEBPACK_IMPORTED_MODULE_0__.hasReducedMotionListener.current = true;\n    if (!_is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__.isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addListener(setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        _state_mjs__WEBPACK_IMPORTED_MODULE_0__.prefersReducedMotion.current = false;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcmVkdWNlZC1tb3Rpb24vaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUMrQjs7QUFFN0U7QUFDQSxJQUFJLGdFQUF3QjtBQUM1QixTQUFTLHNEQUFTO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCw0REFBb0I7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDREQUFvQjtBQUM1QjtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3JlZHVjZWQtbW90aW9uL2luZGV4Lm1qcz9hZGQ4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQnJvd3NlciB9IGZyb20gJy4uL2lzLWJyb3dzZXIubWpzJztcbmltcG9ydCB7IGhhc1JlZHVjZWRNb3Rpb25MaXN0ZW5lciwgcHJlZmVyc1JlZHVjZWRNb3Rpb24gfSBmcm9tICcuL3N0YXRlLm1qcyc7XG5cbmZ1bmN0aW9uIGluaXRQcmVmZXJzUmVkdWNlZE1vdGlvbigpIHtcbiAgICBoYXNSZWR1Y2VkTW90aW9uTGlzdGVuZXIuY3VycmVudCA9IHRydWU7XG4gICAgaWYgKCFpc0Jyb3dzZXIpXG4gICAgICAgIHJldHVybjtcbiAgICBpZiAod2luZG93Lm1hdGNoTWVkaWEpIHtcbiAgICAgICAgY29uc3QgbW90aW9uTWVkaWFRdWVyeSA9IHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtcmVkdWNlZC1tb3Rpb24pXCIpO1xuICAgICAgICBjb25zdCBzZXRSZWR1Y2VkTW90aW9uUHJlZmVyZW5jZXMgPSAoKSA9PiAocHJlZmVyc1JlZHVjZWRNb3Rpb24uY3VycmVudCA9IG1vdGlvbk1lZGlhUXVlcnkubWF0Y2hlcyk7XG4gICAgICAgIG1vdGlvbk1lZGlhUXVlcnkuYWRkTGlzdGVuZXIoc2V0UmVkdWNlZE1vdGlvblByZWZlcmVuY2VzKTtcbiAgICAgICAgc2V0UmVkdWNlZE1vdGlvblByZWZlcmVuY2VzKCk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBwcmVmZXJzUmVkdWNlZE1vdGlvbi5jdXJyZW50ID0gZmFsc2U7XG4gICAgfVxufVxuXG5leHBvcnQgeyBpbml0UHJlZmVyc1JlZHVjZWRNb3Rpb24gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasReducedMotionListener: function() { return /* binding */ hasReducedMotionListener; },\n/* harmony export */   prefersReducedMotion: function() { return /* binding */ prefersReducedMotion; }\n/* harmony export */ });\n// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcmVkdWNlZC1tb3Rpb24vc3RhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSwrQkFBK0I7QUFDL0IsbUNBQW1DOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3JlZHVjZWQtbW90aW9uL3N0YXRlLm1qcz9mZDZhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIERvZXMgdGhpcyBkZXZpY2UgcHJlZmVyIHJlZHVjZWQgbW90aW9uPyBSZXR1cm5zIGBudWxsYCBzZXJ2ZXItc2lkZS5cbmNvbnN0IHByZWZlcnNSZWR1Y2VkTW90aW9uID0geyBjdXJyZW50OiBudWxsIH07XG5jb25zdCBoYXNSZWR1Y2VkTW90aW9uTGlzdGVuZXIgPSB7IGN1cnJlbnQ6IGZhbHNlIH07XG5cbmV4cG9ydCB7IGhhc1JlZHVjZWRNb3Rpb25MaXN0ZW5lciwgcHJlZmVyc1JlZHVjZWRNb3Rpb24gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/resolve-value.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/resolve-value.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCustomValue: function() { return /* binding */ isCustomValue; },\n/* harmony export */   resolveFinalValueInKeyframes: function() { return /* binding */ resolveFinalValueInKeyframes; }\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../animation/utils/is-keyframes-target.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\");\n\n\nconst isCustomValue = (v) => {\n    return Boolean(v && typeof v === \"object\" && v.mix && v.toValue);\n};\nconst resolveFinalValueInKeyframes = (v) => {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return (0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_0__.isKeyframesTarget)(v) ? v[v.length - 1] || 0 : v;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcmVzb2x2ZS12YWx1ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStFOztBQUUvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywyRkFBaUI7QUFDNUI7O0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcmVzb2x2ZS12YWx1ZS5tanM/YmJhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0tleWZyYW1lc1RhcmdldCB9IGZyb20gJy4uL2FuaW1hdGlvbi91dGlscy9pcy1rZXlmcmFtZXMtdGFyZ2V0Lm1qcyc7XG5cbmNvbnN0IGlzQ3VzdG9tVmFsdWUgPSAodikgPT4ge1xuICAgIHJldHVybiBCb29sZWFuKHYgJiYgdHlwZW9mIHYgPT09IFwib2JqZWN0XCIgJiYgdi5taXggJiYgdi50b1ZhbHVlKTtcbn07XG5jb25zdCByZXNvbHZlRmluYWxWYWx1ZUluS2V5ZnJhbWVzID0gKHYpID0+IHtcbiAgICAvLyBUT0RPIG1heWJlIHRocm93IGlmIHYubGVuZ3RoIC0gMSBpcyBwbGFjZWhvbGRlciB0b2tlbj9cbiAgICByZXR1cm4gaXNLZXlmcmFtZXNUYXJnZXQodikgPyB2W3YubGVuZ3RoIC0gMV0gfHwgMCA6IHY7XG59O1xuXG5leHBvcnQgeyBpc0N1c3RvbVZhbHVlLCByZXNvbHZlRmluYWxWYWx1ZUluS2V5ZnJhbWVzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/resolve-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/shallow-compare.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/shallow-compare.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shallowCompare: function() { return /* binding */ shallowCompare; }\n/* harmony export */ });\nfunction shallowCompare(next, prev) {\n    if (!Array.isArray(prev))\n        return false;\n    const prevLength = prev.length;\n    if (prevLength !== next.length)\n        return false;\n    for (let i = 0; i < prevLength; i++) {\n        if (prev[i] !== next[i])\n            return false;\n    }\n    return true;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvc2hhbGxvdy1jb21wYXJlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3NoYWxsb3ctY29tcGFyZS5tanM/N2MzZCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBzaGFsbG93Q29tcGFyZShuZXh0LCBwcmV2KSB7XG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHByZXYpKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgY29uc3QgcHJldkxlbmd0aCA9IHByZXYubGVuZ3RoO1xuICAgIGlmIChwcmV2TGVuZ3RoICE9PSBuZXh0Lmxlbmd0aClcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcHJldkxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGlmIChwcmV2W2ldICE9PSBuZXh0W2ldKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cblxuZXhwb3J0IHsgc2hhbGxvd0NvbXBhcmUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/shallow-compare.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SubscriptionManager: function() { return /* binding */ SubscriptionManager; }\n/* harmony export */ });\n/* harmony import */ var _array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\");\n\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.subscriptions, handler);\n        return () => (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/time-conversion.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecondsToSeconds: function() { return /* binding */ millisecondsToSeconds; },\n/* harmony export */   secondsToMilliseconds: function() { return /* binding */ secondsToMilliseconds; }\n/* harmony export */ });\n/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdGltZS1jb252ZXJzaW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdGltZS1jb252ZXJzaW9uLm1qcz9iNjY2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29udmVydHMgc2Vjb25kcyB0byBtaWxsaXNlY29uZHNcbiAqXG4gKiBAcGFyYW0gc2Vjb25kcyAtIFRpbWUgaW4gc2Vjb25kcy5cbiAqIEByZXR1cm4gbWlsbGlzZWNvbmRzIC0gQ29udmVydGVkIHRpbWUgaW4gbWlsbGlzZWNvbmRzLlxuICovXG5jb25zdCBzZWNvbmRzVG9NaWxsaXNlY29uZHMgPSAoc2Vjb25kcykgPT4gc2Vjb25kcyAqIDEwMDA7XG5jb25zdCBtaWxsaXNlY29uZHNUb1NlY29uZHMgPSAobWlsbGlzZWNvbmRzKSA9PiBtaWxsaXNlY29uZHMgLyAxMDAwO1xuXG5leHBvcnQgeyBtaWxsaXNlY29uZHNUb1NlY29uZHMsIHNlY29uZHNUb01pbGxpc2Vjb25kcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-constant.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConstant: function() { return /* binding */ useConstant; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2Q0FBTTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS1jb25zdGFudC5tanM/ZTM0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQ3JlYXRlcyBhIGNvbnN0YW50IHZhbHVlIG92ZXIgdGhlIGxpZmVjeWNsZSBvZiBhIGNvbXBvbmVudC5cbiAqXG4gKiBFdmVuIGlmIGB1c2VNZW1vYCBpcyBwcm92aWRlZCBhbiBlbXB0eSBhcnJheSBhcyBpdHMgZmluYWwgYXJndW1lbnQsIGl0IGRvZXNuJ3Qgb2ZmZXJcbiAqIGEgZ3VhcmFudGVlIHRoYXQgaXQgd29uJ3QgcmUtcnVuIGZvciBwZXJmb3JtYW5jZSByZWFzb25zIGxhdGVyIG9uLiBCeSB1c2luZyBgdXNlQ29uc3RhbnRgXG4gKiB5b3UgY2FuIGVuc3VyZSB0aGF0IGluaXRpYWxpc2VycyBkb24ndCBleGVjdXRlIHR3aWNlIG9yIG1vcmUuXG4gKi9cbmZ1bmN0aW9uIHVzZUNvbnN0YW50KGluaXQpIHtcbiAgICBjb25zdCByZWYgPSB1c2VSZWYobnVsbCk7XG4gICAgaWYgKHJlZi5jdXJyZW50ID09PSBudWxsKSB7XG4gICAgICAgIHJlZi5jdXJyZW50ID0gaW5pdCgpO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59XG5cbmV4cG9ydCB7IHVzZUNvbnN0YW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-force-update.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useForceUpdate: function() { return /* binding */ useForceUpdate; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-is-mounted.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\nfunction useForceUpdate() {\n    const isMounted = (0,_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsMounted)();\n    const [forcedRenderCount, setForcedRenderCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const forceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWZvcmNlLXVwZGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QztBQUNNO0FBQ0w7O0FBRS9DO0FBQ0Esc0JBQXNCLGlFQUFZO0FBQ2xDLHNEQUFzRCwrQ0FBUTtBQUM5RCx3QkFBd0Isa0RBQVc7QUFDbkM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0RBQVcsT0FBTyx1REFBSztBQUN2RDtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS1mb3JjZS11cGRhdGUubWpzPzY5MTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlSXNNb3VudGVkIH0gZnJvbSAnLi91c2UtaXMtbW91bnRlZC5tanMnO1xuaW1wb3J0IHsgZnJhbWUgfSBmcm9tICcuLi9mcmFtZWxvb3AvZnJhbWUubWpzJztcblxuZnVuY3Rpb24gdXNlRm9yY2VVcGRhdGUoKSB7XG4gICAgY29uc3QgaXNNb3VudGVkID0gdXNlSXNNb3VudGVkKCk7XG4gICAgY29uc3QgW2ZvcmNlZFJlbmRlckNvdW50LCBzZXRGb3JjZWRSZW5kZXJDb3VudF0gPSB1c2VTdGF0ZSgwKTtcbiAgICBjb25zdCBmb3JjZVJlbmRlciA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgaXNNb3VudGVkLmN1cnJlbnQgJiYgc2V0Rm9yY2VkUmVuZGVyQ291bnQoZm9yY2VkUmVuZGVyQ291bnQgKyAxKTtcbiAgICB9LCBbZm9yY2VkUmVuZGVyQ291bnRdKTtcbiAgICAvKipcbiAgICAgKiBEZWZlciB0aGlzIHRvIHRoZSBlbmQgb2YgdGhlIG5leHQgYW5pbWF0aW9uIGZyYW1lIGluIGNhc2UgdGhlcmUgYXJlIG11bHRpcGxlXG4gICAgICogc3luY2hyb25vdXMgY2FsbHMuXG4gICAgICovXG4gICAgY29uc3QgZGVmZXJyZWRGb3JjZVJlbmRlciA9IHVzZUNhbGxiYWNrKCgpID0+IGZyYW1lLnBvc3RSZW5kZXIoZm9yY2VSZW5kZXIpLCBbZm9yY2VSZW5kZXJdKTtcbiAgICByZXR1cm4gW2RlZmVycmVkRm9yY2VSZW5kZXIsIGZvcmNlZFJlbmRlckNvdW50XTtcbn1cblxuZXhwb3J0IHsgdXNlRm9yY2VVcGRhdGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-instant-transition-state.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-instant-transition-state.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   instantAnimationState: function() { return /* binding */ instantAnimationState; }\n/* harmony export */ });\nconst instantAnimationState = {\n    current: false,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluc3RhbnQtdHJhbnNpdGlvbi1zdGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaW5zdGFudC10cmFuc2l0aW9uLXN0YXRlLm1qcz81YjljIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGluc3RhbnRBbmltYXRpb25TdGF0ZSA9IHtcbiAgICBjdXJyZW50OiBmYWxzZSxcbn07XG5cbmV4cG9ydCB7IGluc3RhbnRBbmltYXRpb25TdGF0ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-instant-transition-state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMounted: function() { return /* binding */ useIsMounted; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\nfunction useIsMounted() {\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        isMounted.current = true;\n        return () => {\n            isMounted.current = false;\n        };\n    }, []);\n    return isMounted;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWlzLW1vdW50ZWQubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUN5Qzs7QUFFeEU7QUFDQSxzQkFBc0IsNkNBQU07QUFDNUIsSUFBSSxxRkFBeUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaXMtbW91bnRlZC5tanM/NDhhMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IH0gZnJvbSAnLi91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzJztcblxuZnVuY3Rpb24gdXNlSXNNb3VudGVkKCkge1xuICAgIGNvbnN0IGlzTW91bnRlZCA9IHVzZVJlZihmYWxzZSk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIH07XG4gICAgfSwgW10pO1xuICAgIHJldHVybiBpc01vdW50ZWQ7XG59XG5cbmV4cG9ydCB7IHVzZUlzTW91bnRlZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsomorphicLayoutEffect: function() { return /* binding */ useIsomorphicLayoutEffect; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n\n\n\nconst useIsomorphicLayoutEffect = _is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWlzb21vcnBoaWMtZWZmZWN0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFDTjs7QUFFN0Msa0NBQWtDLHNEQUFTLEdBQUcsa0RBQWUsR0FBRyw0Q0FBUzs7QUFFcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzP2ZlMmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0LCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpc0Jyb3dzZXIgfSBmcm9tICcuL2lzLWJyb3dzZXIubWpzJztcblxuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGlzQnJvd3NlciA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcblxuZXhwb3J0IHsgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnmountEffect: function() { return /* binding */ useUnmountEffect; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nfunction useUnmountEffect(callback) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => callback(), []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLXVubW91bnQtZWZmZWN0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQzs7QUFFbEM7QUFDQSxXQUFXLGdEQUFTO0FBQ3BCOztBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS11bm1vdW50LWVmZmVjdC5tanM/Nzg1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZVVubW91bnRFZmZlY3QoY2FsbGJhY2spIHtcbiAgICByZXR1cm4gdXNlRWZmZWN0KCgpID0+ICgpID0+IGNhbGxiYWNrKCksIFtdKTtcbn1cblxuZXhwb3J0IHsgdXNlVW5tb3VudEVmZmVjdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   velocityPerSecond: function() { return /* binding */ velocityPerSecond; }\n/* harmony export */ });\n/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdmVsb2NpdHktcGVyLXNlY29uZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3ZlbG9jaXR5LXBlci1zZWNvbmQubWpzP2YzOWQiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAgQ29udmVydCB2ZWxvY2l0eSBpbnRvIHZlbG9jaXR5IHBlciBzZWNvbmRcblxuICBAcGFyYW0gW251bWJlcl06IFVuaXQgcGVyIGZyYW1lXG4gIEBwYXJhbSBbbnVtYmVyXTogRnJhbWUgZHVyYXRpb24gaW4gbXNcbiovXG5mdW5jdGlvbiB2ZWxvY2l0eVBlclNlY29uZCh2ZWxvY2l0eSwgZnJhbWVEdXJhdGlvbikge1xuICAgIHJldHVybiBmcmFtZUR1cmF0aW9uID8gdmVsb2NpdHkgKiAoMTAwMCAvIGZyYW1lRHVyYXRpb24pIDogMDtcbn1cblxuZXhwb3J0IHsgdmVsb2NpdHlQZXJTZWNvbmQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/warn-once.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   warnOnce: function() { return /* binding */ warnOnce; }\n/* harmony export */ });\nconst warned = new Set();\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvd2Fybi1vbmNlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3dhcm4tb25jZS5tanM/N2VkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3YXJuZWQgPSBuZXcgU2V0KCk7XG5mdW5jdGlvbiB3YXJuT25jZShjb25kaXRpb24sIG1lc3NhZ2UsIGVsZW1lbnQpIHtcbiAgICBpZiAoY29uZGl0aW9uIHx8IHdhcm5lZC5oYXMobWVzc2FnZSkpXG4gICAgICAgIHJldHVybjtcbiAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgaWYgKGVsZW1lbnQpXG4gICAgICAgIGNvbnNvbGUud2FybihlbGVtZW50KTtcbiAgICB3YXJuZWQuYWRkKG1lc3NhZ2UpO1xufVxuXG5leHBvcnQgeyB3YXJuT25jZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\n"));

/***/ })

}]);