"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_shared_lib_m"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/magic-identifier.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/magic-identifier.js ***!
  \***************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MAGIC_IDENTIFIER_REGEX: function() {\n        return MAGIC_IDENTIFIER_REGEX;\n    },\n    decodeMagicIdentifier: function() {\n        return decodeMagicIdentifier;\n    }\n});\nfunction decodeHex(hexStr) {\n    if (hexStr.trim() === \"\") {\n        throw new Error(\"can't decode empty hex\");\n    }\n    const num = parseInt(hexStr, 16);\n    if (isNaN(num)) {\n        throw new Error(\"invalid hex: `\" + hexStr + \"`\");\n    }\n    return String.fromCodePoint(num);\n}\nvar Mode;\nconst DECODE_REGEX = /^__TURBOPACK__([a-zA-Z0-9_$]+)__$/;\nfunction decodeMagicIdentifier(identifier) {\n    const matches = identifier.match(DECODE_REGEX);\n    if (!matches) {\n        return identifier;\n    }\n    const inner = matches[1];\n    let output = \"\";\n    let mode = 0;\n    let buffer = \"\";\n    for(let i = 0; i < inner.length; i++){\n        const char = inner[i];\n        if (mode === 0) {\n            if (char === \"_\") {\n                mode = 1;\n            } else if (char === \"$\") {\n                mode = 2;\n            } else {\n                output += char;\n            }\n        } else if (mode === 1) {\n            if (char === \"_\") {\n                output += \" \";\n                mode = 0;\n            } else if (char === \"$\") {\n                output += \"_\";\n                mode = 2;\n            } else {\n                output += char;\n                mode = 0;\n            }\n        } else if (mode === 2) {\n            if (buffer.length === 2) {\n                output += decodeHex(buffer);\n                buffer = \"\";\n            }\n            if (char === \"_\") {\n                if (buffer !== \"\") {\n                    throw new Error(\"invalid hex: `\" + buffer + \"`\");\n                }\n                mode = 3;\n            } else if (char === \"$\") {\n                if (buffer !== \"\") {\n                    throw new Error(\"invalid hex: `\" + buffer + \"`\");\n                }\n                mode = 0;\n            } else {\n                buffer += char;\n            }\n        } else if (mode === 3) {\n            if (char === \"_\") {\n                throw new Error(\"invalid hex: `\" + (buffer + char) + \"`\");\n            } else if (char === \"$\") {\n                output += decodeHex(buffer);\n                buffer = \"\";\n                mode = 0;\n            } else {\n                buffer += char;\n            }\n        }\n    }\n    return output;\n}\nconst MAGIC_IDENTIFIER_REGEX = /__TURBOPACK__[a-zA-Z0-9_$]+__/g; //# sourceMappingURL=magic-identifier.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/magic-identifier.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/match-local-pattern.js ***!
  \******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    hasLocalMatch: function() {\n        return hasLocalMatch;\n    },\n    matchLocalPattern: function() {\n        return matchLocalPattern;\n    }\n});\nconst _picomatch = __webpack_require__(/*! next/dist/compiled/picomatch */ \"(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js\");\nfunction matchLocalPattern(pattern, url) {\n    if (pattern.search !== undefined) {\n        if (pattern.search !== url.search) {\n            return false;\n        }\n    }\n    var _pattern_pathname;\n    if (!(0, _picomatch.makeRe)((_pattern_pathname = pattern.pathname) != null ? _pattern_pathname : \"**\", {\n        dot: true\n    }).test(url.pathname)) {\n        return false;\n    }\n    return true;\n}\nfunction hasLocalMatch(localPatterns, urlPathAndQuery) {\n    if (!localPatterns) {\n        // if the user didn't define \"localPatterns\", we allow all local images\n        return true;\n    }\n    const url = new URL(urlPathAndQuery, \"http://n\");\n    return localPatterns.some((p)=>matchLocalPattern(p, url));\n} //# sourceMappingURL=match-local-pattern.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9tYXRjaC1sb2NhbC1wYXR0ZXJuLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQWtCZ0JBLGVBQWE7ZUFBYkE7O0lBZEFDLG1CQUFpQjtlQUFqQkE7Ozt1Q0FITztBQUdoQixTQUFTQSxrQkFBa0JDLE9BQXFCLEVBQUVDLEdBQVE7SUFDL0QsSUFBSUQsUUFBUUUsTUFBTSxLQUFLQyxXQUFXO1FBQ2hDLElBQUlILFFBQVFFLE1BQU0sS0FBS0QsSUFBSUMsTUFBTSxFQUFFO1lBQ2pDLE9BQU87UUFDVDtJQUNGO1FBRVlGO0lBQVosSUFBSSxDQUFDSSxDQUFBQSxHQUFBQSxXQUFBQSxNQUFNLEVBQUNKLENBQUFBLG9CQUFBQSxRQUFRSyxRQUFRLFlBQWhCTCxvQkFBb0IsTUFBTTtRQUFFTSxLQUFLO0lBQUssR0FBR0MsSUFBSSxDQUFDTixJQUFJSSxRQUFRLEdBQUc7UUFDdkUsT0FBTztJQUNUO0lBRUEsT0FBTztBQUNUO0FBRU8sU0FBU1AsY0FDZFUsYUFBeUMsRUFDekNDLGVBQXVCO0lBRXZCLElBQUksQ0FBQ0QsZUFBZTtRQUNsQix1RUFBdUU7UUFDdkUsT0FBTztJQUNUO0lBQ0EsTUFBTVAsTUFBTSxJQUFJUyxJQUFJRCxpQkFBaUI7SUFDckMsT0FBT0QsY0FBY0csSUFBSSxDQUFDLENBQUNDLElBQU1iLGtCQUFrQmEsR0FBR1g7QUFDeEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL21hdGNoLWxvY2FsLXBhdHRlcm4udHM/ZDg1NCJdLCJuYW1lcyI6WyJoYXNMb2NhbE1hdGNoIiwibWF0Y2hMb2NhbFBhdHRlcm4iLCJwYXR0ZXJuIiwidXJsIiwic2VhcmNoIiwidW5kZWZpbmVkIiwibWFrZVJlIiwicGF0aG5hbWUiLCJkb3QiLCJ0ZXN0IiwibG9jYWxQYXR0ZXJucyIsInVybFBhdGhBbmRRdWVyeSIsIlVSTCIsInNvbWUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/match-remote-pattern.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    hasRemoteMatch: function() {\n        return hasRemoteMatch;\n    },\n    matchRemotePattern: function() {\n        return matchRemotePattern;\n    }\n});\nconst _picomatch = __webpack_require__(/*! next/dist/compiled/picomatch */ \"(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js\");\nfunction matchRemotePattern(pattern, url) {\n    if (pattern.protocol !== undefined) {\n        const actualProto = url.protocol.slice(0, -1);\n        if (pattern.protocol !== actualProto) {\n            return false;\n        }\n    }\n    if (pattern.port !== undefined) {\n        if (pattern.port !== url.port) {\n            return false;\n        }\n    }\n    if (pattern.hostname === undefined) {\n        throw new Error(\"Pattern should define hostname but found\\n\" + JSON.stringify(pattern));\n    } else {\n        if (!(0, _picomatch.makeRe)(pattern.hostname).test(url.hostname)) {\n            return false;\n        }\n    }\n    if (pattern.search !== undefined) {\n        if (pattern.search !== url.search) {\n            return false;\n        }\n    }\n    var _pattern_pathname;\n    // Should be the same as writeImagesManifest()\n    if (!(0, _picomatch.makeRe)((_pattern_pathname = pattern.pathname) != null ? _pattern_pathname : \"**\", {\n        dot: true\n    }).test(url.pathname)) {\n        return false;\n    }\n    return true;\n}\nfunction hasRemoteMatch(domains, remotePatterns, url) {\n    return domains.some((domain)=>url.hostname === domain) || remotePatterns.some((p)=>matchRemotePattern(p, url));\n} //# sourceMappingURL=match-remote-pattern.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9tYXRjaC1yZW1vdGUtcGF0dGVybi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF5Q2dCQSxnQkFBYztlQUFkQTs7SUFyQ0FDLG9CQUFrQjtlQUFsQkE7Ozt1Q0FITztBQUdoQixTQUFTQSxtQkFBbUJDLE9BQXNCLEVBQUVDLEdBQVE7SUFDakUsSUFBSUQsUUFBUUUsUUFBUSxLQUFLQyxXQUFXO1FBQ2xDLE1BQU1DLGNBQWNILElBQUlDLFFBQVEsQ0FBQ0csS0FBSyxDQUFDLEdBQUcsQ0FBQztRQUMzQyxJQUFJTCxRQUFRRSxRQUFRLEtBQUtFLGFBQWE7WUFDcEMsT0FBTztRQUNUO0lBQ0Y7SUFDQSxJQUFJSixRQUFRTSxJQUFJLEtBQUtILFdBQVc7UUFDOUIsSUFBSUgsUUFBUU0sSUFBSSxLQUFLTCxJQUFJSyxJQUFJLEVBQUU7WUFDN0IsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJTixRQUFRTyxRQUFRLEtBQUtKLFdBQVc7UUFDbEMsTUFBTSxJQUFJSyxNQUNSLCtDQUE2Q0MsS0FBS0MsU0FBUyxDQUFDVjtJQUVoRSxPQUFPO1FBQ0wsSUFBSSxDQUFDVyxDQUFBQSxHQUFBQSxXQUFBQSxNQUFNLEVBQUNYLFFBQVFPLFFBQVEsRUFBRUssSUFBSSxDQUFDWCxJQUFJTSxRQUFRLEdBQUc7WUFDaEQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJUCxRQUFRYSxNQUFNLEtBQUtWLFdBQVc7UUFDaEMsSUFBSUgsUUFBUWEsTUFBTSxLQUFLWixJQUFJWSxNQUFNLEVBQUU7WUFDakMsT0FBTztRQUNUO0lBQ0Y7UUFHWWI7SUFEWiw4Q0FBOEM7SUFDOUMsSUFBSSxDQUFDVyxDQUFBQSxHQUFBQSxXQUFBQSxNQUFNLEVBQUNYLENBQUFBLG9CQUFBQSxRQUFRYyxRQUFRLFlBQWhCZCxvQkFBb0IsTUFBTTtRQUFFZSxLQUFLO0lBQUssR0FBR0gsSUFBSSxDQUFDWCxJQUFJYSxRQUFRLEdBQUc7UUFDdkUsT0FBTztJQUNUO0lBRUEsT0FBTztBQUNUO0FBRU8sU0FBU2hCLGVBQ2RrQixPQUFpQixFQUNqQkMsY0FBK0IsRUFDL0JoQixHQUFRO0lBRVIsT0FDRWUsUUFBUUUsSUFBSSxDQUFDLENBQUNDLFNBQVdsQixJQUFJTSxRQUFRLEtBQUtZLFdBQzFDRixlQUFlQyxJQUFJLENBQUMsQ0FBQ0UsSUFBTXJCLG1CQUFtQnFCLEdBQUduQjtBQUVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL3NoYXJlZC9saWIvbWF0Y2gtcmVtb3RlLXBhdHRlcm4udHM/NTQ5NSJdLCJuYW1lcyI6WyJoYXNSZW1vdGVNYXRjaCIsIm1hdGNoUmVtb3RlUGF0dGVybiIsInBhdHRlcm4iLCJ1cmwiLCJwcm90b2NvbCIsInVuZGVmaW5lZCIsImFjdHVhbFByb3RvIiwic2xpY2UiLCJwb3J0IiwiaG9zdG5hbWUiLCJFcnJvciIsIkpTT04iLCJzdHJpbmdpZnkiLCJtYWtlUmUiLCJ0ZXN0Iiwic2VhcmNoIiwicGF0aG5hbWUiLCJkb3QiLCJkb21haW5zIiwicmVtb3RlUGF0dGVybnMiLCJzb21lIiwiZG9tYWluIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/normalized-asset-prefix.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"normalizedAssetPrefix\", ({\n    enumerable: true,\n    get: function() {\n        return normalizedAssetPrefix;\n    }\n}));\nfunction normalizedAssetPrefix(assetPrefix) {\n    // remove all leading slashes and trailing slashes\n    const escapedAssetPrefix = (assetPrefix == null ? void 0 : assetPrefix.replace(/^\\/+|\\/+$/g, \"\")) || false;\n    // if an assetPrefix was '/', we return empty string\n    // because it could be an unnecessary trailing slash\n    if (!escapedAssetPrefix) {\n        return \"\";\n    }\n    if (URL.canParse(escapedAssetPrefix)) {\n        const url = new URL(escapedAssetPrefix).toString();\n        return url.endsWith(\"/\") ? url.slice(0, -1) : url;\n    }\n    // assuming assetPrefix here is a pathname-style,\n    // restore the leading slash\n    return \"/\" + escapedAssetPrefix;\n} //# sourceMappingURL=normalized-asset-prefix.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ub3JtYWxpemVkLWFzc2V0LXByZWZpeC5qcyIsIm1hcHBpbmdzIjoiOzs7O3lEQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0Esc0JBQXNCQyxXQUErQjtJQUNuRSxrREFBa0Q7SUFDbEQsTUFBTUMscUJBQXFCRCxDQUFBQSxlQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxZQUFhRSxPQUFPLENBQUMsY0FBYyxRQUFPO0lBRXJFLG9EQUFvRDtJQUNwRCxvREFBb0Q7SUFDcEQsSUFBSSxDQUFDRCxvQkFBb0I7UUFDdkIsT0FBTztJQUNUO0lBRUEsSUFBSUUsSUFBSUMsUUFBUSxDQUFDSCxxQkFBcUI7UUFDcEMsTUFBTUksTUFBTSxJQUFJRixJQUFJRixvQkFBb0JLLFFBQVE7UUFDaEQsT0FBT0QsSUFBSUUsUUFBUSxDQUFDLE9BQU9GLElBQUlHLEtBQUssQ0FBQyxHQUFHLENBQUMsS0FBS0g7SUFDaEQ7SUFFQSxpREFBaUQ7SUFDakQsNEJBQTRCO0lBQzVCLE9BQU8sTUFBSUo7QUFDYiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL3NoYXJlZC9saWIvbm9ybWFsaXplZC1hc3NldC1wcmVmaXgudHM/MzRiYyJdLCJuYW1lcyI6WyJub3JtYWxpemVkQXNzZXRQcmVmaXgiLCJhc3NldFByZWZpeCIsImVzY2FwZWRBc3NldFByZWZpeCIsInJlcGxhY2UiLCJVUkwiLCJjYW5QYXJzZSIsInVybCIsInRvU3RyaW5nIiwiZW5kc1dpdGgiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js ***!
  \*****************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ensureLeadingSlash\", ({\n    enumerable: true,\n    get: function() {\n        return ensureLeadingSlash;\n    }\n}));\nfunction ensureLeadingSlash(path) {\n    return path.startsWith(\"/\") ? path : \"/\" + path;\n} //# sourceMappingURL=ensure-leading-slash.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9wYWdlLXBhdGgvZW5zdXJlLWxlYWRpbmctc2xhc2guanMiLCJtYXBwaW5ncyI6IkFBQUE7OztDQUdDOzs7O3NEQUNlQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSxtQkFBbUJDLElBQVk7SUFDN0MsT0FBT0EsS0FBS0MsVUFBVSxDQUFDLE9BQU9ELE9BQU8sTUFBSUE7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL3BhZ2UtcGF0aC9lbnN1cmUtbGVhZGluZy1zbGFzaC50cz81Zjg1Il0sIm5hbWVzIjpbImVuc3VyZUxlYWRpbmdTbGFzaCIsInBhdGgiLCJzdGFydHNXaXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\n"));

/***/ })

}]);