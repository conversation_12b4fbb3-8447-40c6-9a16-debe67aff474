"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_woocommerce_ts-ea0e4c9f"],{

/***/ "(app-pages-browser)/./src/lib/woocommerce.ts":
/*!********************************!*\
  !*** ./src/lib/woocommerce.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: function() { return /* binding */ ADD_TO_CART; },\n/* harmony export */   GET_CART: function() { return /* binding */ GET_CART; },\n/* harmony export */   MUTATION_LOGIN: function() { return /* binding */ MUTATION_LOGIN; },\n/* harmony export */   MUTATION_REMOVE_FROM_CART: function() { return /* binding */ MUTATION_REMOVE_FROM_CART; },\n/* harmony export */   QUERY_ALL_CATEGORIES: function() { return /* binding */ QUERY_ALL_CATEGORIES; },\n/* harmony export */   QUERY_ALL_PRODUCTS: function() { return /* binding */ QUERY_ALL_PRODUCTS; },\n/* harmony export */   QUERY_CATEGORY_PRODUCTS: function() { return /* binding */ QUERY_CATEGORY_PRODUCTS; },\n/* harmony export */   QUERY_GET_CART: function() { return /* binding */ QUERY_GET_CART; },\n/* harmony export */   QUERY_PAYMENT_GATEWAYS: function() { return /* binding */ QUERY_PAYMENT_GATEWAYS; },\n/* harmony export */   QUERY_SHIPPING_METHODS: function() { return /* binding */ QUERY_SHIPPING_METHODS; },\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   clearAuthToken: function() { return /* binding */ clearAuthToken; },\n/* harmony export */   createAddress: function() { return /* binding */ createAddress; },\n/* harmony export */   createCart: function() { return /* binding */ createCart; },\n/* harmony export */   createCustomer: function() { return /* binding */ createCustomer; },\n/* harmony export */   customerLogin: function() { return /* binding */ customerLogin; },\n/* harmony export */   deleteAddress: function() { return /* binding */ deleteAddress; },\n/* harmony export */   fetchFromWooCommerce: function() { return /* binding */ fetchFromWooCommerce; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   getAllCategories: function() { return /* binding */ getAllCategories; },\n/* harmony export */   getAllProducts: function() { return /* binding */ getAllProducts; },\n/* harmony export */   getCart: function() { return /* binding */ getCart; },\n/* harmony export */   getCategories: function() { return /* binding */ getCategories; },\n/* harmony export */   getCategoryProducts: function() { return /* binding */ getCategoryProducts; },\n/* harmony export */   getCategoryProductsWithTags: function() { return /* binding */ getCategoryProductsWithTags; },\n/* harmony export */   getCustomer: function() { return /* binding */ getCustomer; },\n/* harmony export */   getMetafield: function() { return /* binding */ getMetafield; },\n/* harmony export */   getProduct: function() { return /* binding */ getProduct; },\n/* harmony export */   getProductById: function() { return /* binding */ getProductById; },\n/* harmony export */   getProductBySlug: function() { return /* binding */ getProductBySlug; },\n/* harmony export */   getProductBySlugWithTags: function() { return /* binding */ getProductBySlugWithTags; },\n/* harmony export */   getProductVariations: function() { return /* binding */ getProductVariations; },\n/* harmony export */   getProducts: function() { return /* binding */ getProducts; },\n/* harmony export */   getProductsByCategory: function() { return /* binding */ getProductsByCategory; },\n/* harmony export */   getProductsByTag: function() { return /* binding */ getProductsByTag; },\n/* harmony export */   getSessionToken: function() { return /* binding */ getSessionToken; },\n/* harmony export */   getWooCommerceCheckoutUrl: function() { return /* binding */ getWooCommerceCheckoutUrl; },\n/* harmony export */   normalizeCart: function() { return /* binding */ normalizeCart; },\n/* harmony export */   normalizeCategory: function() { return /* binding */ normalizeCategory; },\n/* harmony export */   normalizeProduct: function() { return /* binding */ normalizeProduct; },\n/* harmony export */   normalizeProductImages: function() { return /* binding */ normalizeProductImages; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   searchProducts: function() { return /* binding */ searchProducts; },\n/* harmony export */   setAuthToken: function() { return /* binding */ setAuthToken; },\n/* harmony export */   setDefaultAddress: function() { return /* binding */ setDefaultAddress; },\n/* harmony export */   setSessionToken: function() { return /* binding */ setSessionToken; },\n/* harmony export */   testWooCommerceConnection: function() { return /* binding */ testWooCommerceConnection; },\n/* harmony export */   updateAddress: function() { return /* binding */ updateAddress; },\n/* harmony export */   updateCart: function() { return /* binding */ updateCart; },\n/* harmony export */   updateCustomer: function() { return /* binding */ updateCustomer; },\n/* harmony export */   wooConfig: function() { return /* binding */ wooConfig; },\n/* harmony export */   wooGraphQLFetch: function() { return /* binding */ wooGraphQLFetch; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-request */ \"(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\");\n/* harmony import */ var _wooInventoryMapping__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wooInventoryMapping */ \"(app-pages-browser)/./src/lib/wooInventoryMapping.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// WooCommerce GraphQL API integration - Fixed according to official documentation\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment ProductFields on Product {\\n    id\\n    databaseId\\n    name\\n    slug\\n    description\\n    shortDescription\\n    type\\n    image {\\n      sourceUrl\\n      altText\\n    }\\n    galleryImages {\\n      nodes {\\n        sourceUrl\\n        altText\\n      }\\n    }\\n    ... on SimpleProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n    }\\n    ... on VariableProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n      attributes {\\n        nodes {\\n          name\\n          options\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment VariableProductWithVariations on VariableProduct {\\n    attributes {\\n      nodes {\\n        name\\n        options\\n      }\\n    }\\n    variations {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        price\\n        regularPrice\\n        salePrice\\n        stockStatus\\n        stockQuantity\\n        attributes {\\n          nodes {\\n            name\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProducts(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductConnectionWhereArgs\\n  ) {\\n    products(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        ...ProductFields\\n        ... on VariableProduct {\\n          ...VariableProductWithVariations\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlug($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlugWithTags($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n      productTags {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n      productCategories {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCategories(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductCategoryConnectionWhereArgs\\n  ) {\\n    productCategories(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductVariations($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          ... on VariableProduct {\\n            variations {\\n              nodes {\\n                id\\n                databaseId\\n                name\\n                price\\n                regularPrice\\n                salePrice\\n                stockStatus\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllProducts($first: Int = 20) {\\n    products(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        productCategories {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n        ... on SimpleProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          stockQuantity\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          variations {\\n            nodes {\\n              stockStatus\\n              stockQuantity\\n            }\\n          }\\n        }\\n        image {\\n          id\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on VariableProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\\n    productCategory(id: $slug, idType: SLUG) {\\n      id\\n      name\\n      slug\\n      description\\n      products(first: $first) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          description\\n          shortDescription\\n          productCategories {\\n            nodes {\\n              id\\n              name\\n              slug\\n            }\\n          }\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            stockQuantity\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            variations {\\n              nodes {\\n                stockStatus\\n                stockQuantity\\n              }\\n            }\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n          galleryImages {\\n            nodes {\\n              id\\n              sourceUrl\\n              altText\\n            }\\n          }\\n          ... on VariableProduct {\\n            attributes {\\n              nodes {\\n                name\\n                options\\n              }\\n            }\\n          }\\n          ... on SimpleProduct {\\n            attributes {\\n              nodes {\\n                name\\n                options\\n              }\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllCategories($first: Int = 20) {\\n    productCategories(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        children {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n    }\\n  }\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation LoginUser($username: String!, $password: String!) {\\n    login(input: {\\n      clientMutationId: \"login\"\\n      username: $username\\n      password: $password\\n    }) {\\n      authToken\\n      refreshToken\\n      user {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        nicename\\n        nickname\\n        username\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n      contentsCount\\n    }\\n  }\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\\n    addToCart(\\n      input: {\\n        clientMutationId: \"addToCart\"\\n        productId: $productId\\n        variationId: $variationId\\n        quantity: $quantity\\n        extraData: $extraData\\n      }\\n    ) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n        contentsCount\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetShippingMethods {\\n    shippingMethods {\\n      nodes {\\n        id\\n        title\\n        description\\n        cost\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject16() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetPaymentGateways {\\n    paymentGateways {\\n      nodes {\\n        id\\n        title\\n        description\\n        enabled\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject16 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject17() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query TestConnection {\\n        generalSettings {\\n          title\\n          url\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject17 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject18() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n      mutation LoginUser($username: String!, $password: String!) {\\n        login(input: {\\n          clientMutationId: \"login\"\\n          username: $username\\n          password: $password\\n        }) {\\n          authToken\\n          refreshToken\\n          user {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n            nicename\\n            nickname\\n            username\\n          }\\n        }\\n      }\\n    '\n    ]);\n    _templateObject18 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject19() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      mutation RegisterUser($input: RegisterCustomerInput!) {\\n        registerCustomer(input: $input) {\\n          clientMutationId\\n          authToken\\n          refreshToken\\n          customer {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject19 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject20() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetCustomer {\\n        customer {\\n          id\\n          databaseId\\n          email\\n          firstName\\n          lastName\\n          displayName\\n          username\\n          role\\n          date\\n          modified\\n          isPayingCustomer\\n          orderCount\\n          totalSpent\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          orders(first: 50) {\\n            nodes {\\n              id\\n              databaseId\\n              date\\n              status\\n              total\\n              subtotal\\n              totalTax\\n              shippingTotal\\n              discountTotal\\n              paymentMethodTitle\\n              customerNote\\n              billing {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n                email\\n                phone\\n              }\\n              shipping {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n              }\\n              lineItems {\\n                nodes {\\n                  product {\\n                    node {\\n                      id\\n                      name\\n                      slug\\n                      image {\\n                        sourceUrl\\n                        altText\\n                      }\\n                    }\\n                  }\\n                  variation {\\n                    node {\\n                      id\\n                      name\\n                      attributes {\\n                        nodes {\\n                          name\\n                          value\\n                        }\\n                      }\\n                    }\\n                  }\\n                  quantity\\n                  total\\n                  subtotal\\n                  totalTax\\n                }\\n              }\\n              shippingLines {\\n                nodes {\\n                  methodTitle\\n                  total\\n                }\\n              }\\n              feeLines {\\n                nodes {\\n                  name\\n                  total\\n                }\\n              }\\n              couponLines {\\n                nodes {\\n                  code\\n                  discount\\n                }\\n              }\\n            }\\n          }\\n          downloadableItems {\\n            nodes {\\n              name\\n              downloadId\\n              downloadsRemaining\\n              accessExpires\\n              product {\\n                node {\\n                  id\\n                  name\\n                }\\n              }\\n            }\\n          }\\n          metaData {\\n            key\\n            value\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject20 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject21() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductById($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          id\\n          databaseId\\n          name\\n          slug\\n          description\\n          shortDescription\\n          productCategories {\\n            nodes {\\n              id\\n              name\\n              slug\\n            }\\n          }\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            stockQuantity\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            variations {\\n              nodes {\\n                stockStatus\\n                stockQuantity\\n              }\\n            }\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject21 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject22() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query SearchProducts($query: String!, $first: Int) {\\n      products(first: $first, where: { search: $query }) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          price\\n          image {\\n            sourceUrl\\n            altText\\n          }\\n          shortDescription\\n        }\\n        pageInfo {\\n          hasNextPage\\n          endCursor\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject22 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject23() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query GetProduct($id: ID!) {\\n      product(id: $id, idType: DATABASE_ID) {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        price\\n        regularPrice\\n        salePrice\\n        onSale\\n        stockStatus\\n        stockQuantity\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          price\\n          regularPrice\\n          salePrice\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          variations {\\n            nodes {\\n              id\\n              databaseId\\n              name\\n              price\\n              regularPrice\\n              salePrice\\n              stockStatus\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject23 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject24() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\\n    registerCustomer(input: $input) {\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n      }\\n      authToken\\n      refreshToken\\n    }\\n  }\\n\"\n    ]);\n    _templateObject24 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject25() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      clientMutationId\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n      customerUserErrors {\\n        field\\n        message\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject25 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject26() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCustomer {\\n    customer {\\n      id\\n      databaseId\\n      email\\n      firstName\\n      lastName\\n      displayName\\n      username\\n      role\\n      date\\n      modified\\n      isPayingCustomer\\n      orderCount\\n      totalSpent\\n      billing {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n        email\\n        phone\\n      }\\n      shipping {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n      }\\n      orders(first: 50) {\\n        nodes {\\n          id\\n          databaseId\\n          date\\n          status\\n          total\\n          subtotal\\n          totalTax\\n          shippingTotal\\n          discountTotal\\n          paymentMethodTitle\\n          customerNote\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          lineItems {\\n            nodes {\\n              product {\\n                node {\\n                  id\\n                  name\\n                  slug\\n                  image {\\n                    sourceUrl\\n                    altText\\n                  }\\n                }\\n              }\\n              variation {\\n                node {\\n                  id\\n                  name\\n                  attributes {\\n                    nodes {\\n                      name\\n                      value\\n                    }\\n                  }\\n                }\\n              }\\n              quantity\\n              total\\n              subtotal\\n              totalTax\\n            }\\n          }\\n          shippingLines {\\n            nodes {\\n              methodTitle\\n              total\\n            }\\n          }\\n          feeLines {\\n            nodes {\\n              name\\n              total\\n            }\\n          }\\n          couponLines {\\n            nodes {\\n              code\\n              discount\\n            }\\n          }\\n        }\\n      }\\n      downloadableItems {\\n        nodes {\\n          name\\n          downloadId\\n          downloadsRemaining\\n          accessExpires\\n          product {\\n            node {\\n              id\\n              name\\n            }\\n          }\\n        }\\n      }\\n      metaData {\\n        key\\n        value\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject26 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject27() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject27 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject28() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject28 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject29() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject29 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject30() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject30 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject31() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\\n    updateItemQuantities(input: $input) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                name\\n                price\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject31 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// WooCommerce store configuration\nconst wooConfig = {\n    storeUrl: \"https://maroon-lapwing-781450.hostingersite.com\" || 0,\n    graphqlUrl: process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\",\n    apiVersion: \"v1\"\n};\n// Session management for WooCommerce\nlet sessionToken = null;\nconst getSessionToken = ()=>{\n    if (true) {\n        return sessionStorage.getItem(\"woo-session-token\") || sessionToken;\n    }\n    return sessionToken;\n};\nconst setSessionToken = (token)=>{\n    sessionToken = token;\n    if (true) {\n        if (token) {\n            sessionStorage.setItem(\"woo-session-token\", token);\n        } else {\n            sessionStorage.removeItem(\"woo-session-token\");\n        }\n    }\n};\n// Check if code is running on client or server\nconst isClient = \"object\" !== \"undefined\";\n// Initialize GraphQL client with proper headers for CORS\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    }\n});\n// Set auth token for authenticated requests\nconst setAuthToken = (token)=>{\n    graphQLClient.setHeader(\"Authorization\", \"Bearer \".concat(token));\n};\n// Clear auth token for unauthenticated requests\nconst clearAuthToken = ()=>{\n    graphQLClient.setHeaders({\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    });\n};\n// GraphQL fragments\nconst PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\n// Define a separate fragment for variable products with variations\nconst VARIABLE_PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\n// Queries\nconst GET_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG_WITH_TAGS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject5());\n// Fetch functions\nasync function getProducts() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCTS, {\n            first: variables.first || 12,\n            after: variables.after || null,\n            where: variables.where || {}\n        }, [\n            \"products\"\n        ], 60);\n        return data.products;\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get variations for a variable product\n */ async function getProductVariations(productId) {\n    try {\n        var _response_product_variations, _response_product;\n        const query = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject6());\n        const response = await fetchFromWooCommerce(query, {\n            id: productId\n        }, [\n            \"product-\".concat(productId),\n            \"products\"\n        ], 60);\n        return ((_response_product = response.product) === null || _response_product === void 0 ? void 0 : (_response_product_variations = _response_product.variations) === null || _response_product_variations === void 0 ? void 0 : _response_product_variations.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching product variations:\", error);\n        return [];\n    }\n}\n/**\n * Get a product by its slug\n */ async function getProductBySlug(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        const product = data.product;\n        // If it's a variable product, fetch variations separately\n        if (product && product.type === \"VARIABLE\") {\n            const variations = await getProductVariations(product.databaseId);\n            return {\n                ...product,\n                variations: {\n                    nodes: variations\n                }\n            };\n        }\n        return product;\n    } catch (error) {\n        console.error(\"Error fetching product by slug:\", error);\n        return null;\n    }\n}\nasync function getProductBySlugWithTags(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG_WITH_TAGS, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product with slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// Categories functionality is now handled by the more comprehensive getAllCategories function\n// Helper function to format price\nfunction formatPrice(price) {\n    const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\n    return numericPrice.toFixed(2);\n}\n/**\n * Fetch data from WooCommerce GraphQL API with caching and revalidation\n */ async function fetchFromWooCommerce(query) {\n    let variables = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, tags = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], revalidate = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 60;\n    try {\n        // Use different approaches for client and server\n        if (isClient) {\n            // When on client, use our proxy API route to avoid CORS issues\n            const proxyEndpoint = \"/api/graphql\";\n            // Build the fetch options with session token\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            // Add session token if available\n            const sessionToken = getSessionToken();\n            if (sessionToken) {\n                headers[\"woocommerce-session\"] = \"Session \".concat(sessionToken);\n            }\n            const fetchOptions = {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    query,\n                    variables\n                })\n            };\n            // Make the fetch request through our proxy\n            const response = await fetch(proxyEndpoint, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"GraphQL API responded with status \".concat(response.status));\n            }\n            // Extract session token from response headers if available\n            const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n            if (responseSessionHeader) {\n                const token = responseSessionHeader.replace(\"Session \", \"\");\n                setSessionToken(token);\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        } else {\n            // Server-side code can directly access the WooCommerce GraphQL endpoint\n            // Build the fetch options with cache control\n            const fetchOptions = {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    variables\n                }),\n                next: {}\n            };\n            // Add cache tags if provided\n            if (tags && tags.length > 0) {\n                fetchOptions.next.tags = tags;\n            }\n            // Add revalidation if provided\n            if (revalidate !== undefined) {\n                fetchOptions.next.revalidate = revalidate;\n            }\n            // Make the fetch request\n            const response = await fetch(wooConfig.graphqlUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"WooCommerce GraphQL API responded with status \".concat(response.status));\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        }\n    } catch (error) {\n        console.error(\"Error fetching from WooCommerce:\", error);\n        throw error;\n    }\n}\n/**\n * Base implementation of WooCommerce fetch that can be used by other modules\n * This provides a standardized way to make WooGraphQL API requests with retry logic\n * \n * @param query GraphQL query to execute \n * @param variables Variables for the GraphQL query\n * @param retries Number of retries in case of failure\n * @param delay Delay between retries in milliseconds\n * @returns The fetched data\n */ async function wooGraphQLFetch(param) {\n    let { query, variables } = param, retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let attemptCount = 0;\n    let lastError = null;\n    while(attemptCount < retries){\n        try {\n            // Use fetchFromWooCommerce for the actual request, but ignore caching controls\n            // as this is the low-level function that might be used in different contexts\n            const data = await fetchFromWooCommerce(query, variables, [], 0);\n            return data;\n        } catch (error) {\n            lastError = error;\n            attemptCount++;\n            if (attemptCount < retries) {\n                console.log(\"Retrying request (\".concat(attemptCount, \"/\").concat(retries, \") after \").concat(delay, \"ms\"));\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // Exponential backoff\n                delay *= 2;\n            }\n        }\n    }\n    console.error(\"Failed after \".concat(retries, \" attempts:\"), lastError);\n    throw lastError;\n}\n/**\n * Get products by category with cache tags for efficient revalidation\n * \n * @param slug The category slug\n * @param first Number of products to fetch\n * @param revalidate Revalidation period in seconds (optional)\n * @returns The category with products\n */ async function getCategoryProductsWithTags(slug) {\n    let first = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, revalidate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60;\n    try {\n        // Define cache tags for this category\n        const tags = [\n            \"category-\".concat(slug),\n            \"categories\",\n            \"products\"\n        ];\n        // Fetch the category with tags\n        const data = await fetchFromWooCommerce(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        }, tags, revalidate);\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"Error fetching category with slug \".concat(slug, \":\"), error);\n        throw error;\n    }\n}\n// GraphQL query to fetch all products\nconst QUERY_ALL_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject7());\n// GraphQL query to fetch products by category\nconst QUERY_CATEGORY_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject8());\n// GraphQL query to fetch all categories\nconst QUERY_ALL_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject9());\n// GraphQL query to get cart contents - Updated for current WooGraphQL schema\nconst QUERY_GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject10());\n// Mutation for customer login\nconst MUTATION_LOGIN = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject11());\n// Get cart query - WooCommerce automatically creates a cart when needed\nconst GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject12());\n// Add to cart mutation - Updated for current WooGraphQL schema\nconst ADD_TO_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject13());\n// Remove from cart mutation - Updated for current WooGraphQL schema\nconst MUTATION_REMOVE_FROM_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject14());\n// Shipping and payment related queries\nconst QUERY_SHIPPING_METHODS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject15());\nconst QUERY_PAYMENT_GATEWAYS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject16());\n// Implement core API methods\n/**\n * Get all products with pagination\n */ async function getAllProducts() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_products;\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_PRODUCTS,\n            variables: {\n                first\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_products = data.products) === null || _data_products === void 0 ? void 0 : _data_products.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching all products:\", error);\n        return [];\n    }\n}\n/**\n * Get all categories with pagination\n */ async function getAllCategories() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_productCategories;\n        console.log(\"\\uD83D\\uDD0D Fetching all categories with first: \".concat(first));\n        console.log(\"\\uD83D\\uDCE1 Using GraphQL endpoint: \".concat(process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"));\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first\n            }\n        });\n        console.log(\"\\uD83D\\uDCCA Raw categories response:\", JSON.stringify(data, null, 2));\n        const categories = (data === null || data === void 0 ? void 0 : (_data_productCategories = data.productCategories) === null || _data_productCategories === void 0 ? void 0 : _data_productCategories.nodes) || [];\n        console.log(\"\\uD83D\\uDCC2 Found \".concat(categories.length, \" categories:\"), categories.map((cat)=>({\n                name: cat.name,\n                slug: cat.slug,\n                id: cat.id,\n                databaseId: cat.databaseId,\n                count: cat.count\n            })));\n        return categories;\n    } catch (error) {\n        console.error(\"❌ Error fetching all categories:\", error);\n        // Log more details about the error\n        if (error instanceof Error) {\n            console.error(\"Error message: \".concat(error.message));\n            console.error(\"Error stack: \".concat(error.stack));\n        }\n        return [];\n    }\n}\n/**\n * Test GraphQL connection and list available categories\n */ async function testWooCommerceConnection() {\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing WooCommerce GraphQL connection...\");\n        console.log(\"\\uD83D\\uDCE1 Endpoint: \".concat(process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"));\n        // Test basic connection with a simple query\n        const testQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject17());\n        const testResult = await wooGraphQLFetch({\n            query: testQuery,\n            variables: {}\n        });\n        console.log(\"✅ Basic connection test result:\", testResult);\n        // Test categories\n        const categories = await getAllCategories(50);\n        console.log(\"\\uD83D\\uDCC2 Available categories (\".concat(categories.length, \"):\"), categories);\n        // Test products\n        const products = await getAllProducts(10);\n        console.log(\"\\uD83D\\uDCE6 Available products (\".concat(products.length, \"):\"), products === null || products === void 0 ? void 0 : products.slice(0, 3));\n        return {\n            connectionWorking: !!testResult,\n            categoriesCount: categories.length,\n            productsCount: products.length,\n            categories: categories.map((cat)=>({\n                    name: cat.name,\n                    slug: cat.slug,\n                    count: cat.count\n                })),\n            sampleProducts: products === null || products === void 0 ? void 0 : products.slice(0, 3).map((prod)=>({\n                    name: prod.name,\n                    slug: prod.slug\n                }))\n        };\n    } catch (error) {\n        console.error(\"❌ WooCommerce connection test failed:\", error);\n        return {\n            connectionWorking: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\n/**\n * Get product categories with pagination and filtering\n * @param variables Object containing:\n *   - first: Number of categories to return (default: 20)\n *   - after: Cursor for pagination\n *   - where: Filter criteria (parent, search, etc.)\n * @returns Object containing categories and pagination info\n */ async function getCategories() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const result = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first: variables.first || 20,\n                after: variables.after || null,\n                where: variables.where || {}\n            }\n        });\n        return {\n            nodes: result.productCategories.nodes,\n            pageInfo: result.productCategories.pageInfo\n        };\n    } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Create a new cart by adding the first item - WooCommerce automatically creates cart\n */ async function createCart() {\n    let items = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    try {\n        if (items.length === 0) {\n            // Just return an empty cart structure - WooCommerce will create cart when first item is added\n            return {\n                contents: {\n                    nodes: []\n                },\n                subtotal: \"0\",\n                total: \"0\",\n                totalTax: \"0\",\n                isEmpty: true,\n                contentsCount: 0\n            };\n        }\n        // Add the first item to create the cart\n        const firstItem = items[0];\n        const cart = await addToCart(\"\", [\n            firstItem\n        ]);\n        // Add remaining items if any\n        if (items.length > 1) {\n            for(let i = 1; i < items.length; i++){\n                await addToCart(\"\", [\n                    items[i]\n                ]);\n            }\n            // Get the updated cart\n            return await getCart();\n        }\n        return cart;\n    } catch (error) {\n        console.error(\"Error creating cart:\", error);\n        throw error;\n    }\n}\n/**\n * Get cart contents - Updated for current WooGraphQL schema\n */ async function getCart() {\n    try {\n        const data = await wooGraphQLFetch({\n            query: GET_CART,\n            variables: {} // Cart query doesn't need parameters in current WooGraphQL\n        });\n        return (data === null || data === void 0 ? void 0 : data.cart) || null;\n    } catch (error) {\n        console.error(\"Error fetching cart:\", error);\n        return null;\n    }\n}\n/**\n * Add items to cart - Updated for current WooGraphQL schema\n */ async function addToCart(_cartId, items) {\n    try {\n        // WooCommerce GraphQL addToCart only accepts one item at a time\n        // So we'll add the first item and return the cart\n        if (items.length === 0) {\n            throw new Error(\"No items provided to add to cart\");\n        }\n        const item = items[0];\n        const variables = {\n            productId: parseInt(item.productId),\n            quantity: item.quantity || 1,\n            variationId: item.variationId ? parseInt(item.variationId) : null,\n            extraData: null\n        };\n        console.log(\"Adding to cart with variables:\", variables);\n        const response = await wooGraphQLFetch({\n            query: ADD_TO_CART,\n            variables\n        });\n        console.log(\"Add to cart response:\", response);\n        return response.addToCart.cart;\n    } catch (error) {\n        console.error(\"Error adding items to cart:\", error);\n        throw error;\n    }\n}\n/**\n * Remove items from cart - Updated for current WooGraphQL schema\n */ async function removeFromCart(cartId, keys) {\n    try {\n        var _data_removeItemsFromCart;\n        const data = await wooGraphQLFetch({\n            query: MUTATION_REMOVE_FROM_CART,\n            variables: {\n                keys,\n                all: false\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_removeItemsFromCart = data.removeItemsFromCart) === null || _data_removeItemsFromCart === void 0 ? void 0 : _data_removeItemsFromCart.cart) || null;\n    } catch (error) {\n        console.error(\"Error removing items from cart:\", error);\n        throw error;\n    }\n}\n/**\n * Customer login with WooCommerce GraphQL\n * \n * @param username User's email/username\n * @param password User's password\n * @returns Authentication token and user information\n */ async function customerLogin(username, password) {\n    try {\n        const LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject18());\n        const variables = {\n            username,\n            password\n        };\n        const result = await wooGraphQLFetch({\n            query: LOGIN_MUTATION,\n            variables\n        });\n        if (!result || !result.login || !result.login.authToken) {\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n        // Set the auth token for future requests\n        setAuthToken(result.login.authToken);\n        return {\n            authToken: result.login.authToken,\n            refreshToken: result.login.refreshToken,\n            user: result.login.user,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Format the error to match the expected structure\n        return {\n            authToken: null,\n            refreshToken: null,\n            user: null,\n            customerUserErrors: [\n                {\n                    code: \"LOGIN_FAILED\",\n                    message: error.message || \"Login failed. Please check your credentials.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Create customer (register) with WooCommerce GraphQL\n */ async function createCustomer(param) {\n    let { email, password, firstName, lastName, phone, acceptsMarketing = false } = param;\n    try {\n        const REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject19());\n        const variables = {\n            input: {\n                clientMutationId: \"registerCustomer\",\n                email,\n                password,\n                firstName,\n                lastName,\n                username: email\n            }\n        };\n        const result = await wooGraphQLFetch({\n            query: REGISTER_MUTATION,\n            variables\n        });\n        if (!result || !result.registerCustomer) {\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n        return {\n            customer: result.registerCustomer.customer,\n            authToken: result.registerCustomer.authToken,\n            refreshToken: result.registerCustomer.refreshToken,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Format the error to match the expected structure\n        return {\n            customer: null,\n            authToken: null,\n            refreshToken: null,\n            customerUserErrors: [\n                {\n                    code: \"REGISTRATION_FAILED\",\n                    message: error.message || \"Registration failed. Please try again.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Get customer data using JWT authentication\n * \n * @param token JWT auth token\n * @returns Customer data\n */ async function getCustomer(token) {\n    try {\n        if (token) {\n            setAuthToken(token);\n        }\n        const GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject20());\n        const result = await wooGraphQLFetch({\n            query: GET_CUSTOMER_QUERY\n        });\n        if (!result || !result.customer) {\n            throw new Error(\"Failed to get customer data\");\n        }\n        return result.customer;\n    } catch (error) {\n        console.error(\"Error getting customer data:\", error);\n        throw error;\n    } finally{\n        if (token) {\n            clearAuthToken();\n        }\n    }\n}\n/**\n * Normalize product data to match the existing frontend structure\n * This helps maintain compatibility with the existing components\n */ function normalizeProduct(product) {\n    var _product_variations_nodes, _product_variations, _product_variations_nodes1, _product_variations1, _product_variations_nodes2, _product_variations2, _product_attributes_nodes, _product_attributes, _product_productCategories_nodes, _product_productCategories;\n    if (!product) return null;\n    // Extract product type\n    const isVariable = Boolean((_product_variations = product.variations) === null || _product_variations === void 0 ? void 0 : (_product_variations_nodes = _product_variations.nodes) === null || _product_variations_nodes === void 0 ? void 0 : _product_variations_nodes.length);\n    // Extract pricing data\n    let priceRange = {\n        minVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\" // Default currency for the application\n        },\n        maxVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\"\n        }\n    };\n    // For variable products, calculate actual price range\n    if (isVariable && ((_product_variations1 = product.variations) === null || _product_variations1 === void 0 ? void 0 : (_product_variations_nodes1 = _product_variations1.nodes) === null || _product_variations_nodes1 === void 0 ? void 0 : _product_variations_nodes1.length) > 0) {\n        const prices = product.variations.nodes.map((variant)=>parseFloat(variant.price || \"0\")).filter((price)=>!isNaN(price));\n        if (prices.length > 0) {\n            priceRange = {\n                minVariantPrice: {\n                    amount: String(Math.min(...prices)),\n                    currencyCode: \"INR\"\n                },\n                maxVariantPrice: {\n                    amount: String(Math.max(...prices)),\n                    currencyCode: \"INR\"\n                }\n            };\n        }\n    }\n    // Extract and normalize images\n    const images = normalizeProductImages(product);\n    // Extract variant data\n    const variants = ((_product_variations2 = product.variations) === null || _product_variations2 === void 0 ? void 0 : (_product_variations_nodes2 = _product_variations2.nodes) === null || _product_variations_nodes2 === void 0 ? void 0 : _product_variations_nodes2.map((variant)=>{\n        var _variant_attributes_nodes, _variant_attributes;\n        return {\n            id: variant.id,\n            title: variant.name,\n            price: {\n                amount: variant.price || \"0\",\n                currencyCode: \"USD\"\n            },\n            availableForSale: variant.stockStatus === \"IN_STOCK\",\n            selectedOptions: ((_variant_attributes = variant.attributes) === null || _variant_attributes === void 0 ? void 0 : (_variant_attributes_nodes = _variant_attributes.nodes) === null || _variant_attributes_nodes === void 0 ? void 0 : _variant_attributes_nodes.map((attr)=>({\n                    name: attr.name,\n                    value: attr.value\n                }))) || [],\n            sku: variant.sku || \"\",\n            image: variant.image ? {\n                url: variant.image.sourceUrl,\n                altText: variant.image.altText || \"\"\n            } : null\n        };\n    })) || [];\n    // Extract options data for variable products\n    const options = ((_product_attributes = product.attributes) === null || _product_attributes === void 0 ? void 0 : (_product_attributes_nodes = _product_attributes.nodes) === null || _product_attributes_nodes === void 0 ? void 0 : _product_attributes_nodes.map((attr)=>({\n            name: attr.name,\n            values: attr.options || []\n        }))) || [];\n    // Extract category data\n    const collections = ((_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : (_product_productCategories_nodes = _product_productCategories.nodes) === null || _product_productCategories_nodes === void 0 ? void 0 : _product_productCategories_nodes.map((category)=>({\n            handle: category.slug,\n            title: category.name\n        }))) || [];\n    // Extract meta fields for custom data\n    const metafields = {};\n    if (product.metafields) {\n        product.metafields.forEach((meta)=>{\n            metafields[meta.key] = meta.value;\n        });\n    }\n    // Return normalized product object that matches existing frontend structure\n    return {\n        id: product.id,\n        handle: product.slug,\n        title: product.name,\n        description: product.description || \"\",\n        descriptionHtml: product.description || \"\",\n        priceRange,\n        options,\n        variants,\n        images,\n        collections,\n        availableForSale: product.stockStatus !== \"OUT_OF_STOCK\",\n        metafields,\n        // Add original data for reference if needed\n        _originalWooProduct: product\n    };\n}\n/**\n * Normalize product images array\n */ function normalizeProductImages(product) {\n    var _product_galleryImages_nodes, _product_galleryImages;\n    const images = [];\n    // Add main product image if available\n    if (product.image) {\n        images.push({\n            url: product.image.sourceUrl,\n            altText: product.image.altText || product.name || \"\"\n        });\n    }\n    // Add gallery images if available\n    if ((_product_galleryImages = product.galleryImages) === null || _product_galleryImages === void 0 ? void 0 : (_product_galleryImages_nodes = _product_galleryImages.nodes) === null || _product_galleryImages_nodes === void 0 ? void 0 : _product_galleryImages_nodes.length) {\n        product.galleryImages.nodes.forEach((img)=>{\n            // Avoid duplicating the main image if it's already in the gallery\n            const isMainImage = product.image && img.sourceUrl === product.image.sourceUrl;\n            if (!isMainImage) {\n                images.push({\n                    url: img.sourceUrl,\n                    altText: img.altText || product.name || \"\"\n                });\n            }\n        });\n    }\n    return images;\n}\n/**\n * Normalize category data to match existing frontend structure\n */ function normalizeCategory(category) {\n    var _category_products_nodes, _category_products;\n    if (!category) return null;\n    return {\n        id: category.id,\n        handle: category.slug,\n        title: category.name,\n        description: category.description || \"\",\n        image: category.image ? {\n            url: category.image.sourceUrl,\n            altText: category.image.altText || \"\"\n        } : null,\n        products: ((_category_products = category.products) === null || _category_products === void 0 ? void 0 : (_category_products_nodes = _category_products.nodes) === null || _category_products_nodes === void 0 ? void 0 : _category_products_nodes.map(normalizeProduct)) || []\n    };\n}\n/**\n * Get custom meta field from product\n */ const getMetafield = function(product, key, namespace) {\n    let defaultValue = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"\";\n    if (!product || !product.metafields) return defaultValue;\n    // Find the meta field by key\n    if (namespace) {\n        const metaKey = \"\".concat(namespace, \":\").concat(key);\n        return product.metafields[metaKey] || defaultValue;\n    }\n    return product.metafields[key] || defaultValue;\n};\n/**\n * Normalize cart data to match existing frontend structure\n */ function normalizeCart(cart) {\n    var _cart_contents_nodes, _cart_contents, _cart_appliedCoupons_nodes, _cart_appliedCoupons;\n    if (!cart) return null;\n    const lineItems = ((_cart_contents = cart.contents) === null || _cart_contents === void 0 ? void 0 : (_cart_contents_nodes = _cart_contents.nodes) === null || _cart_contents_nodes === void 0 ? void 0 : _cart_contents_nodes.map((item)=>{\n        var _item_product, _item_variation, _variation_attributes_nodes, _variation_attributes;\n        const product = (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.node;\n        const variation = (_item_variation = item.variation) === null || _item_variation === void 0 ? void 0 : _item_variation.node;\n        return {\n            id: item.key,\n            quantity: item.quantity,\n            merchandise: {\n                id: (variation === null || variation === void 0 ? void 0 : variation.id) || (product === null || product === void 0 ? void 0 : product.id),\n                title: (variation === null || variation === void 0 ? void 0 : variation.name) || (product === null || product === void 0 ? void 0 : product.name),\n                product: {\n                    id: product === null || product === void 0 ? void 0 : product.id,\n                    handle: product === null || product === void 0 ? void 0 : product.slug,\n                    title: product === null || product === void 0 ? void 0 : product.name,\n                    image: (product === null || product === void 0 ? void 0 : product.image) ? {\n                        url: product === null || product === void 0 ? void 0 : product.image.sourceUrl,\n                        altText: (product === null || product === void 0 ? void 0 : product.image.altText) || \"\"\n                    } : null\n                },\n                selectedOptions: (variation === null || variation === void 0 ? void 0 : (_variation_attributes = variation.attributes) === null || _variation_attributes === void 0 ? void 0 : (_variation_attributes_nodes = _variation_attributes.nodes) === null || _variation_attributes_nodes === void 0 ? void 0 : _variation_attributes_nodes.map((attr)=>({\n                        name: attr.name,\n                        value: attr.value\n                    }))) || []\n            },\n            cost: {\n                totalAmount: {\n                    amount: item.total || \"0\",\n                    currencyCode: \"USD\"\n                }\n            }\n        };\n    })) || [];\n    const discountCodes = ((_cart_appliedCoupons = cart.appliedCoupons) === null || _cart_appliedCoupons === void 0 ? void 0 : (_cart_appliedCoupons_nodes = _cart_appliedCoupons.nodes) === null || _cart_appliedCoupons_nodes === void 0 ? void 0 : _cart_appliedCoupons_nodes.map((coupon)=>({\n            code: coupon.code,\n            amount: coupon.discountAmount || \"0\"\n        }))) || [];\n    // Calculate total quantity from line items instead of using contentsCount\n    const totalQuantity = lineItems.reduce((sum, item)=>sum + item.quantity, 0);\n    return {\n        id: cart.id,\n        checkoutUrl: \"\",\n        totalQuantity: totalQuantity,\n        cost: {\n            subtotalAmount: {\n                amount: cart.subtotal || \"0\",\n                currencyCode: \"USD\"\n            },\n            totalAmount: {\n                amount: cart.total || \"0\",\n                currencyCode: \"USD\"\n            }\n        },\n        lines: lineItems,\n        discountCodes\n    };\n}\n/**\n * Generates a checkout URL for WooCommerce\n * \n * @param cartId The cart ID to associate with checkout\n * @param isLoggedIn Whether the user is logged in\n * @returns The WooCommerce checkout URL\n */ function getWooCommerceCheckoutUrl(cartId) {\n    let isLoggedIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    // Base checkout URL\n    const baseUrl = \"\".concat(wooConfig.storeUrl, \"/checkout\");\n    // Add cart parameter if needed\n    const cartParam = cartId ? \"?cart=\".concat(cartId) : \"\";\n    // Add comprehensive guest checkout parameters to ensure login is bypassed\n    // These parameters will work across different WooCommerce configurations and plugins\n    let guestParams = \"\";\n    if (!isLoggedIn) {\n        const separator = cartParam ? \"&\" : \"?\";\n        guestParams = \"\".concat(separator, \"guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1\");\n    }\n    // Construct the full URL\n    return \"\".concat(baseUrl).concat(cartParam).concat(guestParams);\n}\n/**\n * Get a product by its ID\n * @param id The product ID\n * @param revalidate Revalidation time in seconds\n * @returns The product data or a fallback product if not found\n */ async function getProductById(id) {\n    let revalidate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 60;\n    try {\n        // Check if ID is in a valid format\n        if (!id || id === \"undefined\" || id === \"null\") {\n            console.warn(\"Invalid product ID format: \".concat(id, \", returning fallback product\"));\n            return createFallbackProduct(id);\n        }\n        // Validate and transform the product ID\n        const validatedId = await (0,_wooInventoryMapping__WEBPACK_IMPORTED_MODULE_2__.validateProductId)(id);\n        // Define cache tags for this product\n        const tags = [\n            \"product-\".concat(validatedId),\n            \"products\",\n            \"inventory\"\n        ];\n        // Define the query\n        const QUERY_PRODUCT_BY_ID = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject21());\n        try {\n            // Fetch the product with tags\n            const data = await fetchFromWooCommerce(QUERY_PRODUCT_BY_ID, {\n                id: validatedId\n            }, tags, revalidate);\n            // Check if product exists\n            if (!(data === null || data === void 0 ? void 0 : data.product)) {\n                console.warn(\"No product found with ID: \".concat(id, \", returning fallback product\"));\n                return createFallbackProduct(id);\n            }\n            return data.product;\n        } catch (error) {\n            console.error(\"Error fetching product with ID \".concat(id, \":\"), error);\n            // Return a fallback product instead of throwing an error\n            return createFallbackProduct(id);\n        }\n    } catch (error) {\n        console.error(\"Error in getProductById for ID \".concat(id, \":\"), error);\n        // Return a fallback product instead of throwing an error\n        return createFallbackProduct(id);\n    }\n}\n/**\n * Create a fallback product for when a product cannot be found\n * @param id The original product ID\n * @returns A fallback product object\n */ function createFallbackProduct(id) {\n    return {\n        id: id,\n        databaseId: 0,\n        name: \"Product Not Found\",\n        slug: \"product-not-found\",\n        description: \"This product is no longer available.\",\n        shortDescription: \"Product not found\",\n        price: \"0.00\",\n        regularPrice: \"0.00\",\n        salePrice: null,\n        onSale: false,\n        stockStatus: \"OUT_OF_STOCK\",\n        stockQuantity: 0,\n        image: {\n            id: null,\n            sourceUrl: \"/placeholder-product.jpg\",\n            altText: \"Product not found\"\n        },\n        productCategories: {\n            nodes: []\n        }\n    };\n}\n/**\n * Search products by keyword with advanced options\n * @param query Search query\n * @param options Search options including pagination, sorting, filtering\n * @returns Products matching the search query\n */ async function searchProducts(query) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    // Handle case where options is passed as a number for backward compatibility\n    const first = typeof options === \"number\" ? options : options.first || 10;\n    const searchQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject22());\n    try {\n        const data = await graphQLClient.request(searchQuery, {\n            query,\n            first\n        });\n        return (data === null || data === void 0 ? void 0 : data.products) || {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    } catch (error) {\n        console.error(\"Error searching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get a single product by ID\n * @param id Product ID\n * @returns Product data\n */ async function getProduct(id) {\n    const productQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject23());\n    try {\n        const data = await graphQLClient.request(productQuery, {\n            id\n        });\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        throw new Error(\"Failed to fetch product\");\n    }\n}\nasync function getCategoryProducts(slug) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const { first = 20 } = options;\n        console.log('\\uD83D\\uDD0D Fetching category products for slug: \"'.concat(slug, '\" with first: ').concat(first));\n        console.log(\"\\uD83D\\uDCE1 Using GraphQL endpoint: \".concat(process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"));\n        const data = await wooGraphQLFetch({\n            query: QUERY_CATEGORY_PRODUCTS,\n            variables: {\n                slug,\n                first\n            }\n        });\n        console.log('\\uD83D\\uDCCA Raw response for category \"'.concat(slug, '\":'), JSON.stringify(data, null, 2));\n        if (!(data === null || data === void 0 ? void 0 : data.productCategory)) {\n            console.log('⚠️ No productCategory found in response for slug: \"'.concat(slug, '\"'));\n            // Try alternative approach - search by ID if slug doesn't work\n            if (slug && !isNaN(Number(slug))) {\n                console.log(\"\\uD83D\\uDD04 Trying to fetch category by ID: \".concat(slug));\n                const dataById = await wooGraphQLFetch({\n                    query: QUERY_CATEGORY_PRODUCTS.replace(\"idType: SLUG\", \"idType: DATABASE_ID\"),\n                    variables: {\n                        slug: Number(slug),\n                        first\n                    }\n                });\n                console.log(\"\\uD83D\\uDCCA Response by ID:\", JSON.stringify(dataById, null, 2));\n                return (dataById === null || dataById === void 0 ? void 0 : dataById.productCategory) || null;\n            }\n        }\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"❌ Error fetching category products with slug \".concat(slug, \":\"), error);\n        // Log more details about the error\n        if (error instanceof Error) {\n            console.error(\"Error message: \".concat(error.message));\n            console.error(\"Error stack: \".concat(error.stack));\n        }\n        return null;\n    }\n}\n/**\n * Get products by category - alias for getCategoryProducts for compatibility\n */ async function getProductsByCategory(categorySlug) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { limit = 20, page = 1, sort = \"DATE\" } = options;\n    const after = page > 1 ? btoa(\"arrayconnection:\".concat((page - 1) * limit - 1)) : undefined;\n    return getCategoryProducts(categorySlug, {\n        first: limit,\n        after,\n        orderby: sort,\n        order: \"DESC\"\n    });\n}\n/**\n * Get products by tag\n */ async function getProductsByTag(tagSlug) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { limit = 20, page = 1, sort = \"DATE\" } = options;\n    // For now, return empty result as tag functionality needs proper GraphQL query\n    // This prevents build errors while maintaining API compatibility\n    console.warn(\"getProductsByTag called with tag: \".concat(tagSlug, \" - functionality not yet implemented\"));\n    return {\n        tag: {\n            id: \"\",\n            name: tagSlug,\n            slug: tagSlug,\n            description: \"\"\n        },\n        products: [],\n        pageInfo: {\n            hasNextPage: false,\n            endCursor: null\n        }\n    };\n}\n// Customer Mutations\nconst CREATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject24());\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject25());\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject26());\nconst CREATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject27());\nconst UPDATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject28());\nconst DELETE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject29());\nconst SET_DEFAULT_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject30());\nconst UPDATE_CART_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject31());\n/**\n * Update customer profile\n */ async function updateCustomer(token, customerData) {\n    try {\n        console.log(\"Updating customer with data:\", customerData);\n        console.log(\"Using token:\", token ? \"Token present\" : \"No token\");\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        const variables = {\n            input: {\n                clientMutationId: \"updateCustomer\",\n                ...customerData\n            }\n        };\n        console.log(\"GraphQL variables:\", variables);\n        const response = await client.request(UPDATE_CUSTOMER_MUTATION, variables);\n        console.log(\"GraphQL response:\", response);\n        if (response.updateCustomer.customerUserErrors && response.updateCustomer.customerUserErrors.length > 0) {\n            const errorMessage = response.updateCustomer.customerUserErrors[0].message;\n            console.error(\"Customer update error:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        return response.updateCustomer;\n    } catch (error) {\n        console.error(\"Error updating customer:\", error);\n        throw error;\n    }\n}\n/**\n * Create a new address for the customer\n */ async function createAddress(token, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(CREATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error creating address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing address\n */ async function updateAddress(token, id, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(UPDATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error updating address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete an address\n * Note: In WooCommerce, we don't actually delete addresses but clear them\n */ async function deleteAddress(token, id) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer to determine which address to clear\n        const customer = await getCustomer(token);\n        // Determine if this is a billing or shipping address\n        // In this implementation, we're using the id to determine which address to clear\n        // This is a simplification - you might need a different approach\n        const addressType = id === \"billing\" ? \"billing\" : \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: \"\",\n                    lastName: \"\",\n                    company: \"\",\n                    address1: \"\",\n                    address2: \"\",\n                    city: \"\",\n                    state: \"\",\n                    postcode: \"\",\n                    country: \"\",\n                    ...addressType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(DELETE_ADDRESS_MUTATION, variables);\n        return {\n            deletedCustomerAddressId: id,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error deleting address:\", error);\n        throw error;\n    }\n}\n/**\n * Set default address\n * Note: In WooCommerce, the concept of \"default\" address is different\n * This implementation copies the address from one type to another\n */ async function setDefaultAddress(token, addressId) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer\n        const customer = await getCustomer(token);\n        // Determine source and target address types\n        // This is a simplification - you might need a different approach\n        const sourceType = addressId === \"billing\" ? \"billing\" : \"shipping\";\n        const targetType = sourceType === \"billing\" ? \"shipping\" : \"billing\";\n        // Copy the address from source to target\n        const sourceAddress = customer[sourceType];\n        const variables = {\n            input: {\n                [\"\".concat(targetType)]: {\n                    ...sourceAddress,\n                    ...targetType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: sourceAddress.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(SET_DEFAULT_ADDRESS_MUTATION, variables);\n        return {\n            customer: response.updateCustomer.customer,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Update cart items\n */ async function updateCart(items) {\n    try {\n        const variables = {\n            input: {\n                items\n            }\n        };\n        const response = await wooGraphQLFetch({\n            query: UPDATE_CART_MUTATION,\n            variables\n        });\n        return response.updateItemQuantities.cart;\n    } catch (error) {\n        console.error(\"Error updating cart:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/woocommerce.ts\n"));

/***/ })

}]);