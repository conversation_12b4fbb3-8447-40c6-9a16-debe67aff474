"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_com"],{

/***/ "(app-pages-browser)/./src/components/ui/loader.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/loader.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Loader = (param)=>{\n    let { size = \"md\", color = \"#2c2c27\", className = \"\" } = param;\n    // Size mappings\n    const sizeMap = {\n        sm: {\n            container: \"w-6 h-6\",\n            dot: \"w-1 h-1\"\n        },\n        md: {\n            container: \"w-10 h-10\",\n            dot: \"w-1.5 h-1.5\"\n        },\n        lg: {\n            container: \"w-16 h-16\",\n            dot: \"w-2 h-2\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: \"\\n        @keyframes loaderRotate {\\n          0% {\\n            transform: rotate(0deg);\\n          }\\n          100% {\\n            transform: rotate(360deg);\\n          }\\n        }\\n        \\n        @keyframes loaderDot1 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          25% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot2 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          50% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot3 {\\n          0%, 100% {\\n            opacity: 0.2;\\n          }\\n          75% {\\n            opacity: 1;\\n          }\\n        }\\n        \\n        @keyframes loaderDot4 {\\n          0%, 100% {\\n            opacity: 1;\\n          }\\n          50% {\\n            opacity: 0.2;\\n          }\\n        }\\n      \"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center \".concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative \".concat(sizeMap[size].container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot1 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot2 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot3 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 left-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\"),\n                            style: {\n                                backgroundColor: color,\n                                animation: \"loaderDot4 1.5s infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 rounded-full\",\n                            style: {\n                                border: \"2px solid \".concat(color),\n                                borderTopColor: \"transparent\",\n                                animation: \"loaderRotate 1s linear infinite\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = Loader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Loader);\nvar _c;\n$RefreshReg$(_c, \"Loader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xvYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFMEI7QUFRMUIsTUFBTUMsU0FBUztRQUFDLEVBQUVDLE9BQU8sSUFBSSxFQUFFQyxRQUFRLFNBQVMsRUFBRUMsWUFBWSxFQUFFLEVBQWU7SUFDN0UsZ0JBQWdCO0lBQ2hCLE1BQU1DLFVBQVU7UUFDZEMsSUFBSTtZQUNGQyxXQUFXO1lBQ1hDLEtBQUs7UUFDUDtRQUNBQyxJQUFJO1lBQ0ZGLFdBQVc7WUFDWEMsS0FBSztRQUNQO1FBQ0FFLElBQUk7WUFDRkgsV0FBVztZQUNYQyxLQUFLO1FBQ1A7SUFDRjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ0c7MEJBQVE7Ozs7OzswQkE4Q1QsOERBQUNDO2dCQUFJUixXQUFXLG9DQUE4QyxPQUFWQTswQkFDbEQsNEVBQUNRO29CQUFJUixXQUFXLFlBQW9DLE9BQXhCQyxPQUFPLENBQUNILEtBQUssQ0FBQ0ssU0FBUzs7c0NBRWpELDhEQUFDSzs0QkFDQ1IsV0FBVyw0Q0FBOEQsT0FBbEJDLE9BQU8sQ0FBQ0gsS0FBSyxDQUFDTSxHQUFHLEVBQUM7NEJBQ3pFRyxPQUFPO2dDQUFFRSxpQkFBaUJWO2dDQUFPVyxXQUFXOzRCQUEyQjs7Ozs7O3NDQUV6RSw4REFBQ0Y7NEJBQ0NSLFdBQVcsNkNBQStELE9BQWxCQyxPQUFPLENBQUNILEtBQUssQ0FBQ00sR0FBRyxFQUFDOzRCQUMxRUcsT0FBTztnQ0FBRUUsaUJBQWlCVjtnQ0FBT1csV0FBVzs0QkFBMkI7Ozs7OztzQ0FFekUsOERBQUNGOzRCQUNDUixXQUFXLCtDQUFpRSxPQUFsQkMsT0FBTyxDQUFDSCxLQUFLLENBQUNNLEdBQUcsRUFBQzs0QkFDNUVHLE9BQU87Z0NBQUVFLGlCQUFpQlY7Z0NBQU9XLFdBQVc7NEJBQTJCOzs7Ozs7c0NBRXpFLDhEQUFDRjs0QkFDQ1IsV0FBVyw0Q0FBOEQsT0FBbEJDLE9BQU8sQ0FBQ0gsS0FBSyxDQUFDTSxHQUFHLEVBQUM7NEJBQ3pFRyxPQUFPO2dDQUFFRSxpQkFBaUJWO2dDQUFPVyxXQUFXOzRCQUEyQjs7Ozs7O3NDQUl6RSw4REFBQ0Y7NEJBQ0NSLFdBQVU7NEJBQ1ZPLE9BQU87Z0NBQ0xJLFFBQVEsYUFBbUIsT0FBTlo7Z0NBQ3JCYSxnQkFBZ0I7Z0NBQ2hCRixXQUFXOzRCQUNiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVo7S0FsR01iO0FBb0dOLCtEQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3VpL2xvYWRlci50c3g/MDE3YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuaW50ZXJmYWNlIExvYWRlclByb3BzIHtcclxuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xyXG4gIGNvbG9yPzogc3RyaW5nO1xyXG4gIGNsYXNzTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgTG9hZGVyID0gKHsgc2l6ZSA9ICdtZCcsIGNvbG9yID0gJyMyYzJjMjcnLCBjbGFzc05hbWUgPSAnJyB9OiBMb2FkZXJQcm9wcykgPT4ge1xyXG4gIC8vIFNpemUgbWFwcGluZ3NcclxuICBjb25zdCBzaXplTWFwID0ge1xyXG4gICAgc206IHtcclxuICAgICAgY29udGFpbmVyOiAndy02IGgtNicsXHJcbiAgICAgIGRvdDogJ3ctMSBoLTEnLFxyXG4gICAgfSxcclxuICAgIG1kOiB7XHJcbiAgICAgIGNvbnRhaW5lcjogJ3ctMTAgaC0xMCcsXHJcbiAgICAgIGRvdDogJ3ctMS41IGgtMS41JyxcclxuICAgIH0sXHJcbiAgICBsZzoge1xyXG4gICAgICBjb250YWluZXI6ICd3LTE2IGgtMTYnLFxyXG4gICAgICBkb3Q6ICd3LTIgaC0yJyxcclxuICAgIH0sXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxzdHlsZT57YFxyXG4gICAgICAgIEBrZXlmcmFtZXMgbG9hZGVyUm90YXRlIHtcclxuICAgICAgICAgIDAlIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICAxMDAlIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgQGtleWZyYW1lcyBsb2FkZXJEb3QxIHtcclxuICAgICAgICAgIDAlLCAxMDAlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMC4yO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgMjUlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgQGtleWZyYW1lcyBsb2FkZXJEb3QyIHtcclxuICAgICAgICAgIDAlLCAxMDAlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMC4yO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgNTAlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgQGtleWZyYW1lcyBsb2FkZXJEb3QzIHtcclxuICAgICAgICAgIDAlLCAxMDAlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMC4yO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgNzUlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgQGtleWZyYW1lcyBsb2FkZXJEb3Q0IHtcclxuICAgICAgICAgIDAlLCAxMDAlIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIDUwJSB7XHJcbiAgICAgICAgICAgIG9wYWNpdHk6IDAuMjtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIGB9PC9zdHlsZT5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke2NsYXNzTmFtZX1gfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHJlbGF0aXZlICR7c2l6ZU1hcFtzaXplXS5jb250YWluZXJ9YH0+XHJcbiAgICAgICAgICB7LyogRG90cyAqL31cclxuICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdG9wLTAgbGVmdC0xLzIgLXRyYW5zbGF0ZS14LTEvMiAke3NpemVNYXBbc2l6ZV0uZG90fSByb3VuZGVkLWZ1bGxgfVxyXG4gICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9yLCBhbmltYXRpb246ICdsb2FkZXJEb3QxIDEuNXMgaW5maW5pdGUnIH19XHJcbiAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIHRvcC0xLzIgcmlnaHQtMCAtdHJhbnNsYXRlLXktMS8yICR7c2l6ZU1hcFtzaXplXS5kb3R9IHJvdW5kZWQtZnVsbGB9XHJcbiAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogY29sb3IsIGFuaW1hdGlvbjogJ2xvYWRlckRvdDIgMS41cyBpbmZpbml0ZScgfX1cclxuICAgICAgICAgID48L2Rpdj5cclxuICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0xLzIgLXRyYW5zbGF0ZS14LTEvMiAke3NpemVNYXBbc2l6ZV0uZG90fSByb3VuZGVkLWZ1bGxgfVxyXG4gICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9yLCBhbmltYXRpb246ICdsb2FkZXJEb3QzIDEuNXMgaW5maW5pdGUnIH19XHJcbiAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIHRvcC0xLzIgbGVmdC0wIC10cmFuc2xhdGUteS0xLzIgJHtzaXplTWFwW3NpemVdLmRvdH0gcm91bmRlZC1mdWxsYH1cclxuICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjb2xvciwgYW5pbWF0aW9uOiAnbG9hZGVyRG90NCAxLjVzIGluZmluaXRlJyB9fVxyXG4gICAgICAgICAgPjwvZGl2PlxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICB7LyogUm90YXRpbmcgY2lyY2xlICovfVxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtZnVsbFwiXHJcbiAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgYm9yZGVyOiBgMnB4IHNvbGlkICR7Y29sb3J9YCxcclxuICAgICAgICAgICAgICBib3JkZXJUb3BDb2xvcjogJ3RyYW5zcGFyZW50JyxcclxuICAgICAgICAgICAgICBhbmltYXRpb246ICdsb2FkZXJSb3RhdGUgMXMgbGluZWFyIGluZmluaXRlJyxcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID48L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTG9hZGVyOyJdLCJuYW1lcyI6WyJSZWFjdCIsIkxvYWRlciIsInNpemUiLCJjb2xvciIsImNsYXNzTmFtZSIsInNpemVNYXAiLCJzbSIsImNvbnRhaW5lciIsImRvdCIsIm1kIiwibGciLCJzdHlsZSIsImRpdiIsImJhY2tncm91bmRDb2xvciIsImFuaW1hdGlvbiIsImJvcmRlciIsImJvcmRlclRvcENvbG9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/loader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/eventBus */ \"(app-pages-browser)/./src/lib/eventBus.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth provider component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize auth state from cookies/localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeAuth();\n    }, []);\n    const initializeAuth = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check if user is already authenticated\n            const response = await fetch(\"/api/auth/me\", {\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.user) {\n                    setUser(data.user);\n                    setToken(data.token || \"authenticated\"); // Fallback for cookie-based auth\n                }\n            }\n        } catch (error) {\n            console.error(\"Failed to initialize auth:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"login\",\n                    username: email,\n                    password\n                }),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                setToken(result.token || \"authenticated\");\n                // Emit success event for other components\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginSuccess(result.user, result.token || \"authenticated\");\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Login successful!\", \"success\");\n            } else {\n                const errorMessage = result.message || \"Login failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Login failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.loginError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"register\",\n                    ...userData\n                }),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                setToken(result.token || \"authenticated\");\n                // Emit success event for other components\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerSuccess(result.user, result.token || \"authenticated\");\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Registration successful!\", \"success\");\n            } else {\n                const errorMessage = result.message || \"Registration failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Registration failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.registerError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setIsLoading(true);\n        try {\n            await fetch(\"/api/auth\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"logout\"\n                }),\n                credentials: \"include\"\n            });\n        } catch (error) {\n            console.error(\"Logout API call failed:\", error);\n        }\n        // Clear state regardless of API call result\n        setUser(null);\n        setToken(null);\n        setError(null);\n        // Emit logout event for other components\n        _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.logout();\n        _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Logged out successfully\", \"info\");\n        setIsLoading(false);\n    };\n    const refreshSession = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\", {\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.user) {\n                    setUser(data.user);\n                    setToken(data.token || \"authenticated\");\n                } else {\n                    // Session invalid, clear state\n                    setUser(null);\n                    setToken(null);\n                }\n            } else {\n                // Session invalid, clear state\n                setUser(null);\n                setToken(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to refresh session:\", error);\n            setUser(null);\n            setToken(null);\n        }\n    };\n    const updateProfile = async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/auth/update-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data),\n                credentials: \"include\"\n            });\n            const result = await response.json();\n            if (result.success) {\n                setUser(result.user);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.authEvents.profileUpdated(result.user);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(\"Profile updated successfully!\", \"success\");\n                return result.user;\n            } else {\n                const errorMessage = result.message || \"Profile update failed\";\n                setError(errorMessage);\n                _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n                throw new Error(errorMessage);\n            }\n        } catch (error) {\n            const errorMessage = error.message || \"Profile update failed\";\n            setError(errorMessage);\n            _lib_eventBus__WEBPACK_IMPORTED_MODULE_2__.notificationEvents.show(errorMessage, \"error\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    const isAuthenticated = !!user && !!token;\n    const value = {\n        user,\n        token,\n        isAuthenticated,\n        isLoading,\n        error,\n        login,\n        register,\n        logout,\n        refreshSession,\n        updateProfile,\n        clearError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"mzZbxlz3rJTcku+Jn+vbpbhpSMU=\");\n_c = AuthProvider;\n// Hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

}]);