/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-_app-pages-browser_node_modules_lucide-react_dist_esm_icons_check_js-_app-pages-brows-790947"],{

/***/ "(app-pages-browser)/./node_modules/@formspree/core/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@formspree/core/dist/index.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var g=Object.defineProperty;var j=Object.getOwnPropertyDescriptor;var V=Object.getOwnPropertyNames;var L=Object.prototype.hasOwnProperty;var Y=(e,r)=>{for(var t in r)g(e,t,{get:r[t],enumerable:!0})},K=(e,r,t,o)=>{if(r&&typeof r==\"object\"||typeof r==\"function\")for(let s of V(r))!L.call(e,s)&&s!==t&&g(e,s,{get:()=>r[s],enumerable:!(o=j(r,s))||o.enumerable});return e};var $=e=>K(g({},\"__esModule\",{value:!0}),e);var h=(e,r,t)=>new Promise((o,s)=>{var i=a=>{try{l(t.next(a))}catch(m){s(m)}},c=a=>{try{l(t.throw(a))}catch(m){s(m)}},l=a=>a.done?o(a.value):Promise.resolve(a.value).then(i,c);l((t=t.apply(e,r)).next())});var W={};Y(W,{SubmissionError:()=>p,appendExtraData:()=>E,createClient:()=>R,getDefaultClient:()=>U,isSubmissionError:()=>A});module.exports=$(W);var u=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",J=/^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;function I(e){e=String(e);for(var r,t,o,s,i=\"\",c=0,l=e.length%3;c<e.length;){if((t=e.charCodeAt(c++))>255||(o=e.charCodeAt(c++))>255||(s=e.charCodeAt(c++))>255)throw new TypeError(\"Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.\");r=t<<16|o<<8|s,i+=u.charAt(r>>18&63)+u.charAt(r>>12&63)+u.charAt(r>>6&63)+u.charAt(r&63)}return l?i.slice(0,l-3)+\"===\".substring(l):i}function O(e){if(e=String(e).replace(/[\\t\\n\\f\\r ]+/g,\"\"),!J.test(e))throw new TypeError(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\");e+=\"==\".slice(2-(e.length&3));for(var r,t=\"\",o,s,i=0;i<e.length;)r=u.indexOf(e.charAt(i++))<<18|u.indexOf(e.charAt(i++))<<12|(o=u.indexOf(e.charAt(i++)))<<6|(s=u.indexOf(e.charAt(i++))),t+=o===64?String.fromCharCode(r>>16&255):s===64?String.fromCharCode(r>>16&255,r>>8&255):String.fromCharCode(r>>16&255,r>>8&255,r&255);return t}var G=()=>navigator.webdriver||!!document.documentElement.getAttribute(O(\"d2ViZHJpdmVy\"))||!!window.callPhantom||!!window._phantom,y=class{constructor(){this.loadedAt=Date.now(),this.webdriver=G()}data(){return{loadedAt:this.loadedAt,webdriver:this.webdriver}}};var S=class{constructor(r){this.kind=\"success\";this.next=r.next}};function w(e){return\"next\"in e&&typeof e.next==\"string\"}var b=class{constructor(r,t){this.paymentIntentClientSecret=r;this.resubmitKey=t;this.kind=\"stripePluginPending\"}};function _(e){if(\"stripe\"in e&&\"resubmitKey\"in e&&typeof e.resubmitKey==\"string\"){let{stripe:r}=e;return typeof r==\"object\"&&r!=null&&\"paymentIntentClientSecret\"in r&&typeof r.paymentIntentClientSecret==\"string\"}return!1}function A(e){return e.kind===\"error\"}var p=class{constructor(...r){this.kind=\"error\";this.formErrors=[];this.fieldErrors=new Map;var t;for(let o of r){if(!o.field){this.formErrors.push({code:o.code&&z(o.code)?o.code:\"UNSPECIFIED\",message:o.message});continue}let s=(t=this.fieldErrors.get(o.field))!=null?t:[];s.push({code:o.code&&Q(o.code)?o.code:\"UNSPECIFIED\",message:o.message}),this.fieldErrors.set(o.field,s)}}getFormErrors(){return[...this.formErrors]}getFieldErrors(r){var t;return(t=this.fieldErrors.get(r))!=null?t:[]}getAllFieldErrors(){return Array.from(this.fieldErrors)}};function z(e){return e in B}var B={BLOCKED:\"BLOCKED\",EMPTY:\"EMPTY\",FILES_TOO_BIG:\"FILES_TOO_BIG\",FORM_NOT_FOUND:\"FORM_NOT_FOUND\",INACTIVE:\"INACTIVE\",NO_FILE_UPLOADS:\"NO_FILE_UPLOADS\",PROJECT_NOT_FOUND:\"PROJECT_NOT_FOUND\",TOO_MANY_FILES:\"TOO_MANY_FILES\"};function Q(e){return e in Z}var Z={REQUIRED_FIELD_EMPTY:\"REQUIRED_FIELD_EMPTY\",REQUIRED_FIELD_MISSING:\"REQUIRED_FIELD_MISSING\",STRIPE_CLIENT_ERROR:\"STRIPE_CLIENT_ERROR\",STRIPE_SCA_ERROR:\"STRIPE_SCA_ERROR\",TYPE_EMAIL:\"TYPE_EMAIL\",TYPE_NUMERIC:\"TYPE_NUMERIC\",TYPE_TEXT:\"TYPE_TEXT\"};function P(e){return\"errors\"in e&&Array.isArray(e.errors)&&e.errors.every(r=>typeof r.message==\"string\")||\"error\"in e&&typeof e.error==\"string\"}var D=\"4.0.0\";var v=e=>I(JSON.stringify(e)),N=e=>{let r=`@formspree/core@${D}`;return e?`${e} ${r}`:r};function E(e,r,t){e instanceof FormData?e.append(r,t):e[r]=t}function M(e){return e!==null&&typeof e==\"object\"}var C=class{constructor(r={}){this.project=r.project,this.stripe=r.stripe,typeof window!=\"undefined\"&&(this.session=new y)}submitForm(s,i){return h(this,arguments,function*(r,t,o={}){let c=o.endpoint||\"https://formspree.io\",l=this.project?`${c}/p/${this.project}/f/${r}`:`${c}/f/${r}`,a={Accept:\"application/json\",\"Formspree-Client\":N(o.clientName)};this.session&&(a[\"Formspree-Session-Data\"]=v(this.session.data())),t instanceof FormData||(a[\"Content-Type\"]=\"application/json\");function m(f){return h(this,null,function*(){try{let n=yield(yield fetch(l,{method:\"POST\",mode:\"cors\",body:f instanceof FormData?f:JSON.stringify(f),headers:a})).json();if(M(n)){if(P(n))return Array.isArray(n.errors)?new p(...n.errors):new p({message:n.error});if(_(n))return new b(n.stripe.paymentIntentClientSecret,n.resubmitKey);if(w(n))return new S({next:n.next})}return new p({message:\"Unexpected response format\"})}catch(d){let n=d instanceof Error?d.message:`Unknown error while posting to Formspree: ${JSON.stringify(d)}`;return new p({message:n})}})}if(this.stripe&&o.createPaymentMethod){let f=yield o.createPaymentMethod();if(f.error)return new p({code:\"STRIPE_CLIENT_ERROR\",field:\"paymentMethod\",message:\"Error creating payment method\"});E(t,\"paymentMethod\",f.paymentMethod.id);let d=yield m(t);if(d.kind===\"error\")return d;if(d.kind===\"stripePluginPending\"){let n=yield this.stripe.handleCardAction(d.paymentIntentClientSecret);if(n.error)return new p({code:\"STRIPE_CLIENT_ERROR\",field:\"paymentMethod\",message:\"Stripe SCA error\"});t instanceof FormData?t.delete(\"paymentMethod\"):delete t.paymentMethod,E(t,\"paymentIntent\",n.paymentIntent.id),E(t,\"resubmitKey\",d.resubmitKey);let x=yield m(t);return k(x),x}return d}let T=yield m(t);return k(T),T})}};function k(e){let{kind:r}=e;if(r!==\"success\"&&r!==\"error\")throw new Error(`Unexpected submission result (kind: ${r})`)}var R=e=>new C(e),U=()=>(F||(F=R()),F),F;0&&(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formspree/core/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("(function (global, factory) {\n   true ? factory(exports, __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\")) :\n  0;\n})(this, (function (exports, React) { 'use strict';\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n\n    return target;\n  }\n\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n\n    var key, i;\n\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n\n    return target;\n  }\n\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n\n  function _iterableToArrayLimit(arr, i) {\n    var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n\n    var _s, _e;\n\n    try {\n      for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  function getDefaultExportFromCjs (x) {\n  \treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n  }\n\n  var propTypes = {exports: {}};\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var ReactPropTypesSecret_1;\n  var hasRequiredReactPropTypesSecret;\n\n  function requireReactPropTypesSecret() {\n    if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n    hasRequiredReactPropTypesSecret = 1;\n\n    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n    ReactPropTypesSecret_1 = ReactPropTypesSecret;\n    return ReactPropTypesSecret_1;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var factoryWithThrowingShims;\n  var hasRequiredFactoryWithThrowingShims;\n\n  function requireFactoryWithThrowingShims() {\n    if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n    hasRequiredFactoryWithThrowingShims = 1;\n\n    var ReactPropTypesSecret = requireReactPropTypesSecret();\n\n    function emptyFunction() {}\n\n    function emptyFunctionWithReset() {}\n\n    emptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n    factoryWithThrowingShims = function () {\n      function shim(props, propName, componentName, location, propFullName, secret) {\n        if (secret === ReactPropTypesSecret) {\n          // It is still safe when called from React.\n          return;\n        }\n\n        var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n        err.name = 'Invariant Violation';\n        throw err;\n      }\n      shim.isRequired = shim;\n\n      function getShim() {\n        return shim;\n      }\n      // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\n      var ReactPropTypes = {\n        array: shim,\n        bool: shim,\n        func: shim,\n        number: shim,\n        object: shim,\n        string: shim,\n        symbol: shim,\n        any: shim,\n        arrayOf: getShim,\n        element: shim,\n        elementType: shim,\n        instanceOf: getShim,\n        node: shim,\n        objectOf: getShim,\n        oneOf: getShim,\n        oneOfType: getShim,\n        shape: getShim,\n        exact: getShim,\n        checkPropTypes: emptyFunctionWithReset,\n        resetWarningCache: emptyFunction\n      };\n      ReactPropTypes.PropTypes = ReactPropTypes;\n      return ReactPropTypes;\n    };\n\n    return factoryWithThrowingShims;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    propTypes.exports = requireFactoryWithThrowingShims()();\n  }\n\n  var propTypesExports = propTypes.exports;\n  var PropTypes = /*@__PURE__*/getDefaultExportFromCjs(propTypesExports);\n\n  var useAttachEvent = function useAttachEvent(element, event, cb) {\n    var cbDefined = !!cb;\n    var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n    // Using a ref saves us from calling element.on/.off every render.\n\n    React.useEffect(function () {\n      cbRef.current = cb;\n    }, [cb]);\n    React.useEffect(function () {\n      if (!cbDefined || !element) {\n        return function () {};\n      }\n\n      var decoratedCb = function decoratedCb() {\n        if (cbRef.current) {\n          cbRef.current.apply(cbRef, arguments);\n        }\n      };\n\n      element.on(event, decoratedCb);\n      return function () {\n        element.off(event, decoratedCb);\n      };\n    }, [cbDefined, event, element, cbRef]);\n  };\n\n  var usePrevious = function usePrevious(value) {\n    var ref = React.useRef(value);\n    React.useEffect(function () {\n      ref.current = value;\n    }, [value]);\n    return ref.current;\n  };\n\n  var isUnknownObject = function isUnknownObject(raw) {\n    return raw !== null && _typeof(raw) === 'object';\n  };\n  var isPromise = function isPromise(raw) {\n    return isUnknownObject(raw) && typeof raw.then === 'function';\n  }; // We are using types to enforce the `stripe` prop in this lib,\n  // but in an untyped integration `stripe` could be anything, so we need\n  // to do some sanity validation to prevent type errors.\n\n  var isStripe = function isStripe(raw) {\n    return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n  };\n\n  var PLAIN_OBJECT_STR = '[object Object]';\n  var isEqual = function isEqual(left, right) {\n    if (!isUnknownObject(left) || !isUnknownObject(right)) {\n      return left === right;\n    }\n\n    var leftArray = Array.isArray(left);\n    var rightArray = Array.isArray(right);\n    if (leftArray !== rightArray) return false;\n    var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n    var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n    if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n    // fallback to reference check.\n\n    if (!leftPlainObject && !leftArray) return left === right;\n    var leftKeys = Object.keys(left);\n    var rightKeys = Object.keys(right);\n    if (leftKeys.length !== rightKeys.length) return false;\n    var keySet = {};\n\n    for (var i = 0; i < leftKeys.length; i += 1) {\n      keySet[leftKeys[i]] = true;\n    }\n\n    for (var _i = 0; _i < rightKeys.length; _i += 1) {\n      keySet[rightKeys[_i]] = true;\n    }\n\n    var allKeys = Object.keys(keySet);\n\n    if (allKeys.length !== leftKeys.length) {\n      return false;\n    }\n\n    var l = left;\n    var r = right;\n\n    var pred = function pred(key) {\n      return isEqual(l[key], r[key]);\n    };\n\n    return allKeys.every(pred);\n  };\n\n  var extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n    if (!isUnknownObject(options)) {\n      return null;\n    }\n\n    return Object.keys(options).reduce(function (newOptions, key) {\n      var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n      if (immutableKeys.includes(key)) {\n        if (isUpdated) {\n          console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n        }\n\n        return newOptions;\n      }\n\n      if (!isUpdated) {\n        return newOptions;\n      }\n\n      return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n    }, null);\n  };\n\n  var INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n  // integration `stripe` could be anything, so we need to do some sanity\n  // validation to prevent type errors.\n\n  var validateStripe = function validateStripe(maybeStripe) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (maybeStripe === null || isStripe(maybeStripe)) {\n      return maybeStripe;\n    }\n\n    throw new Error(errorMsg);\n  };\n\n  var parseStripeProp = function parseStripeProp(raw) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (isPromise(raw)) {\n      return {\n        tag: 'async',\n        stripePromise: Promise.resolve(raw).then(function (result) {\n          return validateStripe(result, errorMsg);\n        })\n      };\n    }\n\n    var stripe = validateStripe(raw, errorMsg);\n\n    if (stripe === null) {\n      return {\n        tag: 'empty'\n      };\n    }\n\n    return {\n      tag: 'sync',\n      stripe: stripe\n    };\n  };\n\n  var registerWithStripeJs = function registerWithStripeJs(stripe) {\n    if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n      return;\n    }\n\n    stripe._registerWrapper({\n      name: 'react-stripe-js',\n      version: \"3.5.1\"\n    });\n\n    stripe.registerAppInfo({\n      name: 'react-stripe-js',\n      version: \"3.5.1\",\n      url: 'https://stripe.com/docs/stripe-js/react'\n    });\n  };\n\n  var ElementsContext = /*#__PURE__*/React.createContext(null);\n  ElementsContext.displayName = 'ElementsContext';\n  var parseElementsContext = function parseElementsContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n    }\n\n    return ctx;\n  };\n  /**\n   * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n   * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n   *\n   * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n   * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n   * Pass the returned `Promise` to `Elements`.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n   */\n\n  var Elements = function Elements(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp);\n    }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n    var _React$useState = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n      };\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      var isMounted = true;\n\n      var safeSetContext = function safeSetContext(stripe) {\n        setContext(function (ctx) {\n          // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n          if (ctx.stripe) return ctx;\n          return {\n            stripe: stripe,\n            elements: stripe.elements(options)\n          };\n        });\n      }; // For an async stripePromise, store it in context once resolved\n\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted) {\n            // Only update Elements context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            safeSetContext(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !ctx.stripe) {\n        // Or, handle a sync stripe instance going from null -> populated\n        safeSetContext(parsed.stripe);\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!ctx.elements) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n      if (updates) {\n        ctx.elements.update(updates);\n      }\n    }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n      value: ctx\n    }, children);\n  };\n  Elements.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.object\n  };\n  var useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n    var ctx = React.useContext(ElementsContext);\n    return parseElementsContext(ctx, useCaseMessage);\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n   */\n\n  var useElements = function useElements() {\n    var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n        elements = _useElementsContextWi.elements;\n\n    return elements;\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n   */\n\n  var ElementsConsumer = function ElementsConsumer(_ref2) {\n    var children = _ref2.children;\n    var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n    return children(ctx);\n  };\n  ElementsConsumer.propTypes = {\n    children: PropTypes.func.isRequired\n  };\n\n  var _excluded$1 = [\"on\", \"session\"];\n  var CheckoutSdkContext = /*#__PURE__*/React.createContext(null);\n  CheckoutSdkContext.displayName = 'CheckoutSdkContext';\n  var parseCheckoutSdkContext = function parseCheckoutSdkContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find CheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CheckoutProvider> provider.\"));\n    }\n\n    return ctx;\n  };\n  var CheckoutContext = /*#__PURE__*/React.createContext(null);\n  CheckoutContext.displayName = 'CheckoutContext';\n  var extractCheckoutContextValue = function extractCheckoutContextValue(checkoutSdk, sessionState) {\n    if (!checkoutSdk) {\n      return null;\n    }\n\n    checkoutSdk.on;\n        checkoutSdk.session;\n        var actions = _objectWithoutProperties(checkoutSdk, _excluded$1);\n\n    if (!sessionState) {\n      return Object.assign(checkoutSdk.session(), actions);\n    }\n\n    return Object.assign(sessionState, actions);\n  };\n  var INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var CheckoutProvider = function CheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n    }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        session = _React$useState2[0],\n        setSession = _React$useState2[1];\n\n    var _React$useState3 = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        checkoutSdk: null\n      };\n    }),\n        _React$useState4 = _slicedToArray(_React$useState3, 2),\n        ctx = _React$useState4[0],\n        setContext = _React$useState4[1];\n\n    var safeSetContext = function safeSetContext(stripe, checkoutSdk) {\n      setContext(function (ctx) {\n        if (ctx.stripe && ctx.checkoutSdk) {\n          return ctx;\n        }\n\n        return {\n          stripe: stripe,\n          checkoutSdk: checkoutSdk\n        };\n      });\n    }; // Ref used to avoid calling initCheckout multiple times when options changes\n\n\n    var initCheckoutCalledRef = React.useRef(false);\n    React.useEffect(function () {\n      var isMounted = true;\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted && !initCheckoutCalledRef.current) {\n            // Only update context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            initCheckoutCalledRef.current = true;\n            stripe.initCheckout(options).then(function (checkoutSdk) {\n              if (checkoutSdk) {\n                safeSetContext(stripe, checkoutSdk);\n                checkoutSdk.on('change', setSession);\n              }\n            });\n          }\n        });\n      } else if (parsed.tag === 'sync' && parsed.stripe && !initCheckoutCalledRef.current) {\n        initCheckoutCalledRef.current = true;\n        parsed.stripe.initCheckout(options).then(function (checkoutSdk) {\n          if (checkoutSdk) {\n            safeSetContext(parsed.stripe, checkoutSdk);\n            checkoutSdk.on('change', setSession);\n          }\n        });\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      var _prevOptions$elements, _options$elementsOpti;\n\n      if (!ctx.checkoutSdk) {\n        return;\n      }\n\n      var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n      var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n\n      if (currentAppearance && !isEqual(currentAppearance, previousAppearance)) {\n        ctx.checkoutSdk.changeAppearance(currentAppearance);\n      }\n    }, [options, prevOptions, ctx.checkoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    var checkoutContextValue = React.useMemo(function () {\n      return extractCheckoutContextValue(ctx.checkoutSdk, session);\n    }, [ctx.checkoutSdk, session]);\n\n    if (!ctx.checkoutSdk) {\n      return null;\n    }\n\n    return /*#__PURE__*/React.createElement(CheckoutSdkContext.Provider, {\n      value: ctx\n    }, /*#__PURE__*/React.createElement(CheckoutContext.Provider, {\n      value: checkoutContextValue\n    }, children));\n  };\n  CheckoutProvider.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.shape({\n      fetchClientSecret: PropTypes.func.isRequired,\n      elementsOptions: PropTypes.object\n    }).isRequired\n  };\n  var useCheckoutSdkContextWithUseCase = function useCheckoutSdkContextWithUseCase(useCaseString) {\n    var ctx = React.useContext(CheckoutSdkContext);\n    return parseCheckoutSdkContext(ctx, useCaseString);\n  };\n  var useElementsOrCheckoutSdkContextWithUseCase = function useElementsOrCheckoutSdkContextWithUseCase(useCaseString) {\n    var checkoutSdkContext = React.useContext(CheckoutSdkContext);\n    var elementsContext = React.useContext(ElementsContext);\n\n    if (checkoutSdkContext && elementsContext) {\n      throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CheckoutProvider> and <Elements> providers.\"));\n    }\n\n    if (checkoutSdkContext) {\n      return parseCheckoutSdkContext(checkoutSdkContext, useCaseString);\n    }\n\n    return parseElementsContext(elementsContext, useCaseString);\n  };\n  var useCheckout = function useCheckout() {\n    // ensure it's in CheckoutProvider\n    useCheckoutSdkContextWithUseCase('calls useCheckout()');\n    var ctx = React.useContext(CheckoutContext);\n\n    if (!ctx) {\n      throw new Error('Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.');\n    }\n\n    return ctx;\n  };\n\n  var _excluded = [\"mode\"];\n\n  var capitalized = function capitalized(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n\n  var createElementComponent = function createElementComponent(type, isServer) {\n    var displayName = \"\".concat(capitalized(type), \"Element\");\n\n    var ClientElement = function ClientElement(_ref) {\n      var id = _ref.id,\n          className = _ref.className,\n          _ref$options = _ref.options,\n          options = _ref$options === void 0 ? {} : _ref$options,\n          onBlur = _ref.onBlur,\n          onFocus = _ref.onFocus,\n          onReady = _ref.onReady,\n          onChange = _ref.onChange,\n          onEscape = _ref.onEscape,\n          onClick = _ref.onClick,\n          onLoadError = _ref.onLoadError,\n          onLoaderStart = _ref.onLoaderStart,\n          onNetworksChange = _ref.onNetworksChange,\n          onConfirm = _ref.onConfirm,\n          onCancel = _ref.onCancel,\n          onShippingAddressChange = _ref.onShippingAddressChange,\n          onShippingRateChange = _ref.onShippingRateChange;\n      var ctx = useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var elements = 'elements' in ctx ? ctx.elements : null;\n      var checkoutSdk = 'checkoutSdk' in ctx ? ctx.checkoutSdk : null;\n\n      var _React$useState = React.useState(null),\n          _React$useState2 = _slicedToArray(_React$useState, 2),\n          element = _React$useState2[0],\n          setElement = _React$useState2[1];\n\n      var elementRef = React.useRef(null);\n      var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n      // with that callback. If the merchant ever changes the callback, removes\n      // the old callback with element.off and then call element.on with the new one.\n\n      useAttachEvent(element, 'blur', onBlur);\n      useAttachEvent(element, 'focus', onFocus);\n      useAttachEvent(element, 'escape', onEscape);\n      useAttachEvent(element, 'click', onClick);\n      useAttachEvent(element, 'loaderror', onLoadError);\n      useAttachEvent(element, 'loaderstart', onLoaderStart);\n      useAttachEvent(element, 'networkschange', onNetworksChange);\n      useAttachEvent(element, 'confirm', onConfirm);\n      useAttachEvent(element, 'cancel', onCancel);\n      useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n      useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n      useAttachEvent(element, 'change', onChange);\n      var readyCallback;\n\n      if (onReady) {\n        if (type === 'expressCheckout') {\n          // Passes through the event, which includes visible PM types\n          readyCallback = onReady;\n        } else {\n          // For other Elements, pass through the Element itself.\n          readyCallback = function readyCallback() {\n            onReady(element);\n          };\n        }\n      }\n\n      useAttachEvent(element, 'ready', readyCallback);\n      React.useLayoutEffect(function () {\n        if (elementRef.current === null && domNode.current !== null && (elements || checkoutSdk)) {\n          var newElement = null;\n\n          if (checkoutSdk) {\n            switch (type) {\n              case 'payment':\n                newElement = checkoutSdk.createPaymentElement(options);\n                break;\n\n              case 'address':\n                if ('mode' in options) {\n                  var mode = options.mode,\n                      restOptions = _objectWithoutProperties(options, _excluded);\n\n                  if (mode === 'shipping') {\n                    newElement = checkoutSdk.createShippingAddressElement(restOptions);\n                  } else if (mode === 'billing') {\n                    newElement = checkoutSdk.createBillingAddressElement(restOptions);\n                  } else {\n                    throw new Error(\"Invalid options.mode. mode must be 'billing' or 'shipping'.\");\n                  }\n                } else {\n                  throw new Error(\"You must supply options.mode. mode must be 'billing' or 'shipping'.\");\n                }\n\n                break;\n\n              case 'expressCheckout':\n                newElement = checkoutSdk.createExpressCheckoutElement(options);\n                break;\n\n              case 'currencySelector':\n                newElement = checkoutSdk.createCurrencySelectorElement();\n                break;\n\n              default:\n                throw new Error(\"Invalid Element type \".concat(displayName, \". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />.\"));\n            }\n          } else if (elements) {\n            newElement = elements.create(type, options);\n          } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n          elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n          setElement(newElement);\n\n          if (newElement) {\n            newElement.mount(domNode.current);\n          }\n        }\n      }, [elements, checkoutSdk, options]);\n      var prevOptions = usePrevious(options);\n      React.useEffect(function () {\n        if (!elementRef.current) {\n          return;\n        }\n\n        var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n        if (updates && 'update' in elementRef.current) {\n          elementRef.current.update(updates);\n        }\n      }, [options, prevOptions]);\n      React.useLayoutEffect(function () {\n        return function () {\n          if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n            try {\n              elementRef.current.destroy();\n              elementRef.current = null;\n            } catch (error) {// Do nothing\n            }\n          }\n        };\n      }, []);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className,\n        ref: domNode\n      });\n    }; // Only render the Element wrapper in a server environment.\n\n\n    var ServerElement = function ServerElement(props) {\n      useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var id = props.id,\n          className = props.className;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className\n      });\n    };\n\n    var Element = isServer ? ServerElement : ClientElement;\n    Element.propTypes = {\n      id: PropTypes.string,\n      className: PropTypes.string,\n      onChange: PropTypes.func,\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      onReady: PropTypes.func,\n      onEscape: PropTypes.func,\n      onClick: PropTypes.func,\n      onLoadError: PropTypes.func,\n      onLoaderStart: PropTypes.func,\n      onNetworksChange: PropTypes.func,\n      onConfirm: PropTypes.func,\n      onCancel: PropTypes.func,\n      onShippingAddressChange: PropTypes.func,\n      onShippingRateChange: PropTypes.func,\n      options: PropTypes.object\n    };\n    Element.displayName = displayName;\n    Element.__elementType = type;\n    return Element;\n  };\n\n  var isServer = typeof window === 'undefined';\n\n  var EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\n  EmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\n  var useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n    var ctx = React.useContext(EmbeddedCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n    }\n\n    return ctx;\n  };\n  var INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n    }, [rawStripeProp]);\n    var embeddedCheckoutPromise = React.useRef(null);\n    var loadedStripe = React.useRef(null);\n\n    var _React$useState = React.useState({\n      embeddedCheckout: null\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      // Don't support any ctx updates once embeddedCheckout or stripe is set.\n      if (loadedStripe.current || embeddedCheckoutPromise.current) {\n        return;\n      }\n\n      var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n        if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n        loadedStripe.current = stripe;\n        embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n          setContext({\n            embeddedCheckout: embeddedCheckout\n          });\n        });\n      }; // For an async stripePromise, store it once resolved\n\n\n      if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe) {\n            setStripeAndInitEmbeddedCheckout(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        // Or, handle a sync stripe instance going from null -> populated\n        setStripeAndInitEmbeddedCheckout(parsed.stripe);\n      }\n    }, [parsed, options, ctx, loadedStripe]);\n    React.useEffect(function () {\n      // cleanup on unmount\n      return function () {\n        // If embedded checkout is fully initialized, destroy it.\n        if (ctx.embeddedCheckout) {\n          embeddedCheckoutPromise.current = null;\n          ctx.embeddedCheckout.destroy();\n        } else if (embeddedCheckoutPromise.current) {\n          // If embedded checkout is still initializing, destroy it once\n          // it's done. This could be caused by unmounting very quickly\n          // after mounting.\n          embeddedCheckoutPromise.current.then(function () {\n            embeddedCheckoutPromise.current = null;\n\n            if (ctx.embeddedCheckout) {\n              ctx.embeddedCheckout.destroy();\n            }\n          });\n        }\n      };\n    }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(loadedStripe);\n    }, [loadedStripe]); // Warn on changes to stripe prop.\n    // The stripe prop value can only go from null to non-null once and\n    // can't be changed after that.\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (prevOptions == null) {\n        return;\n      }\n\n      if (options == null) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n        return;\n      }\n\n      if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n        console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n      }\n\n      if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n      }\n\n      if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n      }\n\n      if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n      }\n    }, [prevOptions, options]);\n    return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n      value: ctx\n    }, children);\n  };\n\n  var EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className;\n\n    var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n        embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n    var isMounted = React.useRef(false);\n    var domNode = React.useRef(null);\n    React.useLayoutEffect(function () {\n      if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n        embeddedCheckout.mount(domNode.current);\n        isMounted.current = true;\n      } // Clean up on unmount\n\n\n      return function () {\n        if (isMounted.current && embeddedCheckout) {\n          try {\n            embeddedCheckout.unmount();\n            isMounted.current = false;\n          } catch (e) {// Do nothing.\n            // Parent effects are destroyed before child effects, so\n            // in cases where both the EmbeddedCheckoutProvider and\n            // the EmbeddedCheckout component are removed at the same\n            // time, the embeddedCheckout instance will be destroyed,\n            // which causes an error when calling unmount.\n          }\n        }\n      };\n    }, [embeddedCheckout]);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: domNode,\n      id: id,\n      className: className\n    });\n  }; // Only render the wrapper in a server environment.\n\n\n  var EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n    var id = _ref2.id,\n        className = _ref2.className;\n    // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n    useEmbeddedCheckoutContext();\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n   */\n\n  var useStripe = function useStripe() {\n    var _useElementsOrCheckou = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()'),\n        stripe = _useElementsOrCheckou.stripe;\n\n    return stripe;\n  };\n\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardElement = createElementComponent('card', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardNumberElement = createElementComponent('cardNumber', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardExpiryElement = createElementComponent('cardExpiry', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardCvcElement = createElementComponent('cardCvc', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var FpxBankElement = createElementComponent('fpxBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IbanElement = createElementComponent('iban', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IdealBankElement = createElementComponent('idealBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var P24BankElement = createElementComponent('p24Bank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var EpsBankElement = createElementComponent('epsBank', isServer);\n  var PaymentElement = createElementComponent('payment', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   */\n\n  var CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AddressElement = createElementComponent('address', isServer);\n  /**\n   * @deprecated\n   * Use `AddressElement` instead.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\n  exports.AddressElement = AddressElement;\n  exports.AffirmMessageElement = AffirmMessageElement;\n  exports.AfterpayClearpayMessageElement = AfterpayClearpayMessageElement;\n  exports.AuBankAccountElement = AuBankAccountElement;\n  exports.CardCvcElement = CardCvcElement;\n  exports.CardElement = CardElement;\n  exports.CardExpiryElement = CardExpiryElement;\n  exports.CardNumberElement = CardNumberElement;\n  exports.CheckoutProvider = CheckoutProvider;\n  exports.CurrencySelectorElement = CurrencySelectorElement;\n  exports.Elements = Elements;\n  exports.ElementsConsumer = ElementsConsumer;\n  exports.EmbeddedCheckout = EmbeddedCheckout;\n  exports.EmbeddedCheckoutProvider = EmbeddedCheckoutProvider;\n  exports.EpsBankElement = EpsBankElement;\n  exports.ExpressCheckoutElement = ExpressCheckoutElement;\n  exports.FpxBankElement = FpxBankElement;\n  exports.IbanElement = IbanElement;\n  exports.IdealBankElement = IdealBankElement;\n  exports.LinkAuthenticationElement = LinkAuthenticationElement;\n  exports.P24BankElement = P24BankElement;\n  exports.PaymentElement = PaymentElement;\n  exports.PaymentMethodMessagingElement = PaymentMethodMessagingElement;\n  exports.PaymentRequestButtonElement = PaymentRequestButtonElement;\n  exports.ShippingAddressElement = ShippingAddressElement;\n  exports.useCheckout = useCheckout;\n  exports.useElements = useElements;\n  exports.useStripe = useStripe;\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@stripe/stripe-js/dist/pure.js":
/*!*****************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/pure.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nvar RELEASE_TRAIN = 'v3';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/v3\") ;\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"5.10.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise !== null) {\n    return stripePromise;\n  }\n\n  stripePromise = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise[\"catch\"](function (error) {\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"5.10.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar validateLoadParams = function validateLoadParams(params) {\n  var errorMessage = \"invalid load parameters; expected object of shape\\n\\n    {advancedFraudSignals: boolean}\\n\\nbut received\\n\\n    \".concat(JSON.stringify(params), \"\\n\");\n\n  if (params === null || _typeof(params) !== 'object') {\n    throw new Error(errorMessage);\n  }\n\n  if (Object.keys(params).length === 1 && typeof params.advancedFraudSignals === 'boolean') {\n    return params;\n  }\n\n  throw new Error(errorMessage);\n};\n\nvar loadParams;\nvar loadStripeCalled = false;\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadStripeCalled = true;\n  var startTime = Date.now();\n  return loadScript(loadParams).then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\nloadStripe.setLoadParameters = function (params) {\n  // we won't throw an error if setLoadParameters is called with the same values as before\n  if (loadStripeCalled && loadParams) {\n    var validatedParams = validateLoadParams(params);\n    var parameterKeys = Object.keys(validatedParams);\n    var sameParameters = parameterKeys.reduce(function (previousValue, currentValue) {\n      var _loadParams;\n\n      return previousValue && params[currentValue] === ((_loadParams = loadParams) === null || _loadParams === void 0 ? void 0 : _loadParams[currentValue]);\n    }, true);\n\n    if (sameParameters) {\n      return;\n    }\n  }\n\n  if (loadStripeCalled) {\n    throw new Error('You cannot change load parameters after calling loadStripe');\n  }\n\n  loadParams = validateLoadParams(params);\n};\n\nexports.loadStripe = loadStripe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@stripe/stripe-js/dist/pure.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@stripe/stripe-js/pure/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/pure/index.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ../dist/pure */ \"(app-pages-browser)/./node_modules/@stripe/stripe-js/dist/pure.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3RyaXBlL3N0cmlwZS1qcy9wdXJlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLDZIQUF3QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvcHVyZS9pbmRleC5qcz9mZTQzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi4vZGlzdC9wdXJlJyk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@stripe/stripe-js/pure/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Check; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Check\", [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n]);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxRQUFRQyxnRUFBZ0JBLENBQUMsU0FBUztJQUN0QztRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUFtQkMsS0FBSztRQUFBO0tBQVU7Q0FDakQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9pY29ucy9jaGVjay50cz83NjE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hlY2tcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qQWdOaUE1SURFM2JDMDFMVFVpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hlY2tcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGVjayA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZWNrJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMjAgNiA5IDE3bC01LTUnLCBrZXk6ICcxZ21mMmMnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZWNrO1xuIl0sIm5hbWVzIjpbIkNoZWNrIiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronLeft; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronLeft\", [\n    [\n        \"path\",\n        {\n            d: \"m15 18-6-6 6-6\",\n            key: \"1wnfg3\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1sZWZ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsY0FBY0MsZ0VBQWdCQSxDQUFDLGVBQWU7SUFDbEQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBa0JDLEtBQUs7UUFBQTtLQUFVO0NBQ2hEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvaWNvbnMvY2hldnJvbi1sZWZ0LnRzPzA0MTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uTGVmdFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRVZ01UZ3ROaTAySURZdE5pSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLWxlZnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGV2cm9uTGVmdCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25MZWZ0JywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdtMTUgMTgtNi02IDYtNicsIGtleTogJzF3bmZnMycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvbkxlZnQ7XG4iXSwibmFtZXMiOlsiQ2hldnJvbkxlZnQiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronRight; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronRight\", [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1yaWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLE1BQUFBLGVBQWVDLGdFQUFnQkEsQ0FBQyxnQkFBZ0I7SUFDcEQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBaUJDLEtBQUs7UUFBQTtLQUFVO0NBQy9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvaWNvbnMvY2hldnJvbi1yaWdodC50cz9mNDY0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hldnJvblJpZ2h0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnRPU0F4T0NBMkxUWXROaTAySWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1yaWdodFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25SaWdodCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25SaWdodCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTkgMTggNi02LTYtNicsIGtleTogJ210aGh3cScgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblJpZ2h0O1xuIl0sIm5hbWVzIjpbIkNoZXZyb25SaWdodCIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/scissors.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Scissors; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Scissors = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Scissors\", [\n    [\n        \"circle\",\n        {\n            cx: \"6\",\n            cy: \"6\",\n            r: \"3\",\n            key: \"1lh9wr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.12 8.12 12 12\",\n            key: \"1alkpv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 4 8.12 15.88\",\n            key: \"xgtan2\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"6\",\n            cy: \"18\",\n            r: \"3\",\n            key: \"fqmcym\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14.8 14.8 20 20\",\n            key: \"ptml3r\"\n        }\n    ]\n]);\n //# sourceMappingURL=scissors.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2Npc3NvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxXQUFXQyxnRUFBZ0JBLENBQUMsWUFBWTtJQUM1QztRQUFDO1FBQVU7WUFBRUMsSUFBSTtZQUFLQyxJQUFJO1lBQUtDLEdBQUc7WUFBS0MsS0FBSztRQUFBO0tBQVU7SUFDdEQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBb0JELEtBQUs7UUFBQTtLQUFVO0lBQ2pEO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQW9CRCxLQUFLO1FBQUE7S0FBVTtJQUNqRDtRQUFDO1FBQVU7WUFBRUgsSUFBSTtZQUFLQyxJQUFJO1lBQU1DLEdBQUc7WUFBS0MsS0FBSztRQUFBO0tBQVU7SUFDdkQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBb0JELEtBQUs7UUFBQTtLQUFVO0NBQ2xEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvaWNvbnMvc2Npc3NvcnMudHM/YmI5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFNjaXNzb3JzXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0kySWlCamVUMGlOaUlnY2owaU15SWdMejRLSUNBOGNHRjBhQ0JrUFNKTk9DNHhNaUE0TGpFeUlERXlJREV5SWlBdlBnb2dJRHh3WVhSb0lHUTlJazB5TUNBMElEZ3VNVElnTVRVdU9EZ2lJQzgrQ2lBZ1BHTnBjbU5zWlNCamVEMGlOaUlnWTNrOUlqRTRJaUJ5UFNJeklpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE5DNDRJREUwTGpnZ01qQWdNakFpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvc2Npc3NvcnNcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTY2lzc29ycyA9IGNyZWF0ZUx1Y2lkZUljb24oJ1NjaXNzb3JzJywgW1xuICBbJ2NpcmNsZScsIHsgY3g6ICc2JywgY3k6ICc2JywgcjogJzMnLCBrZXk6ICcxbGg5d3InIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNOC4xMiA4LjEyIDEyIDEyJywga2V5OiAnMWFsa3B2JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTIwIDQgOC4xMiAxNS44OCcsIGtleTogJ3hndGFuMicgfV0sXG4gIFsnY2lyY2xlJywgeyBjeDogJzYnLCBjeTogJzE4JywgcjogJzMnLCBrZXk6ICdmcW1jeW0nIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTQuOCAxNC44IDIwIDIwJywga2V5OiAncHRtbDNyJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBTY2lzc29ycztcbiJdLCJuYW1lcyI6WyJTY2lzc29ycyIsImNyZWF0ZUx1Y2lkZUljb24iLCJjeCIsImN5IiwiciIsImtleSIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shirt.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/shirt.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Shirt; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Shirt = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Shirt\", [\n    [\n        \"path\",\n        {\n            d: \"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z\",\n            key: \"1wgbhj\"\n        }\n    ]\n]);\n //# sourceMappingURL=shirt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shirt.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formspree/react/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@formspree/react/dist/index.mjs ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardElement: function() { return /* reexport safe */ _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.CardElement; },\n/* harmony export */   FormspreeProvider: function() { return /* binding */ N; },\n/* harmony export */   ValidationError: function() { return /* binding */ V; },\n/* harmony export */   useForm: function() { return /* binding */ J; },\n/* harmony export */   useFormspree: function() { return /* binding */ b; },\n/* harmony export */   useSubmit: function() { return /* binding */ F; }\n/* harmony export */ });\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"(app-pages-browser)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _formspree_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formspree/core */ \"(app-pages-browser)/./node_modules/@formspree/core/dist/index.js\");\n/* harmony import */ var _stripe_stripe_js_pure_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @stripe/stripe-js/pure/index.js */ \"(app-pages-browser)/./node_modules/@stripe/stripe-js/pure/index.js\");\nfunction V(e){let{prefix:t,field:r,errors:o,...s}=e;if(o==null)return null;let n=r?o.getFieldErrors(r):o.getFormErrors();return n.length===0?null:react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{...s},t?`${t} `:null,n.map(a=>a.message).join(\", \"))}var E=(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({elements:null});function P(e){let{children:t}=e,r=(0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.useElements)();return react__WEBPACK_IMPORTED_MODULE_1__.createElement(E.Provider,{value:{elements:r}},t)}function v(){return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(E)}var h=react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);function N(e){let{children:t,project:r,stripePK:o}=e,[s,n]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({project:r})),a=(0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>o?(0,_stripe_stripe_js_pure_index_js__WEBPACK_IMPORTED_MODULE_2__.loadStripe)(o):null,[o]);return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{let i=!0;return i&&n(l=>l.project!==r?(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({...l,project:r}):l),()=>{i=!1}},[r]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{let i=!0;return a?.then(l=>{i&&l&&n(p=>(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({...p,stripe:l}))}),()=>{i=!1}},[a]),react__WEBPACK_IMPORTED_MODULE_1__.createElement(h.Provider,{value:{client:s}},a?react__WEBPACK_IMPORTED_MODULE_1__.createElement(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.Elements,{stripe:a},react__WEBPACK_IMPORTED_MODULE_1__.createElement(P,null,t)):t)}function b(){return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(h)??{client:(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.getDefaultClient)()}}var D=\"3.0.0\";var z=`@formspree/react@${D}`;function F(e,t={}){let r=b(),{client:o=r.client,extraData:s,origin:n}=t,{elements:a}=v(),{stripe:i}=o;return async function(p){let m=I(p)?$(p):p;if(typeof s==\"object\")for(let[u,g]of Object.entries(s)){let d;typeof g==\"function\"?d=await g():d=g,d!==void 0&&(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.appendExtraData)(m,u,d)}let c=a?.getElement(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.CardElement),x=i&&c?()=>i.createPaymentMethod({type:\"card\",card:c,billing_details:G(m)}):void 0;return o.submitForm(e,m,{endpoint:n,clientName:z,createPaymentMethod:x})}}function I(e){return\"preventDefault\"in e&&typeof e.preventDefault==\"function\"}function $(e){e.preventDefault();let t=e.currentTarget;if(t.tagName!=\"FORM\")throw new Error(\"submit was triggered for a non-form element\");return new FormData(t)}function G(e){let t={address:Y(e)};for(let r of[\"name\",\"email\",\"phone\"]){let o=e instanceof FormData?e.get(r):e[r];o&&typeof o==\"string\"&&(t[r]=o)}return t}function Y(e){let t={};for(let[r,o]of[[\"address_line1\",\"line1\"],[\"address_line2\",\"line2\"],[\"address_city\",\"city\"],[\"address_country\",\"country\"],[\"address_state\",\"state\"],[\"address_postal_code\",\"postal_code\"]]){let s=e instanceof FormData?e.get(r):e[r];s&&typeof s==\"string\"&&(t[o]=s)}return t}function J(e,t={}){let[r,o]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),[s,n]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),[a,i]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[l,p]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1);if(!e)throw new Error('You must provide a form key or hashid (e.g. useForm(\"myForm\") or useForm(\"123xyz\")');let m=F(e,{client:t.client,extraData:t.data,origin:t.endpoint});return[{errors:r,result:s,submitting:a,succeeded:l},async function(x){i(!0);let u=await m(x);i(!1),(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.isSubmissionError)(u)?(o(u),p(!1)):(o(null),n(u),p(!0))},function(){o(null),n(null),i(!1),p(!1)}]}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formspree/react/dist/index.mjs\n"));

/***/ })

}]);