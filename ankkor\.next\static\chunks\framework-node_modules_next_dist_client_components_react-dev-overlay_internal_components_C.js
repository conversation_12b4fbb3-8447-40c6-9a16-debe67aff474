"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js ***!
  \***************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return CodeFrame;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"(app-pages-browser)/./node_modules/next/dist/compiled/anser/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _stackframe = __webpack_require__(/*! ../../helpers/stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../helpers/use-open-in-editor */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/hot-linked-text/index.js\");\nconst CodeFrame = function CodeFrame(param) {\n    _s();\n    let { stackFrame, codeFrame } = param;\n    // Strip leading spaces out of the code frame:\n    const formattedFrame = _react.useMemo(()=>{\n        const lines = codeFrame.split(/\\r?\\n/g);\n        // Find the minimum length of leading spaces after `|` in the code frame\n        const miniLeadingSpacesLength = lines.map((line)=>/^>? +\\d+ +\\| [ ]+/.exec((0, _stripansi.default)(line)) === null ? null : /^>? +\\d+ +\\| ( *)/.exec((0, _stripansi.default)(line))).filter(Boolean).map((v)=>v.pop()).reduce((c, n)=>isNaN(c) ? n.length : Math.min(c, n.length), NaN);\n        // When the minimum length of leading spaces is greater than 1, remove them\n        // from the code frame to help the indentation looks better when there's a lot leading spaces.\n        if (miniLeadingSpacesLength > 1) {\n            return lines.map((line, a)=>~(a = line.indexOf(\"|\")) ? line.substring(0, a) + line.substring(a).replace(\"^\\\\ {\" + miniLeadingSpacesLength + \"}\", \"\") : line).join(\"\\n\");\n        }\n        return lines.join(\"\\n\");\n    }, [\n        codeFrame\n    ]);\n    const decoded = _react.useMemo(()=>{\n        return _anser.default.ansiToJson(formattedFrame, {\n            json: true,\n            use_classes: true,\n            remove_empty: true\n        });\n    }, [\n        formattedFrame\n    ]);\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: stackFrame.file,\n        lineNumber: stackFrame.lineNumber,\n        column: stackFrame.column\n    });\n    // TODO: make the caret absolute\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                    role: \"link\",\n                    onClick: open,\n                    tabIndex: 1,\n                    title: \"Click to open in your editor\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                            children: [\n                                (0, _stackframe.getFrameSource)(stackFrame),\n                                \" @\",\n                                \" \",\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                    text: stackFrame.methodName\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\",\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                    d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                                    points: \"15 3 21 3 21 9\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                                    x1: \"10\",\n                                    y1: \"14\",\n                                    x2: \"21\",\n                                    y2: \"3\"\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                children: decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        style: {\n                            color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                            ...entry.decoration === \"bold\" ? {\n                                fontWeight: 800\n                            } : entry.decoration === \"italic\" ? {\n                                fontStyle: \"italic\"\n                            } : undefined\n                        },\n                        children: entry.content\n                    }, \"frame-\" + index))\n            })\n        ]\n    });\n};\n_s(CodeFrame, \"4Gt+UceGaRqMOFv99GzJu8D2nx8=\");\n_c = CodeFrame;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=CodeFrame.js.map\nvar _c;\n$RefreshReg$(_c, \"CodeFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js ***!
  \***********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CodeFrame\", ({\n    enumerable: true,\n    get: function() {\n        return _CodeFrame.CodeFrame;\n    }\n}));\nconst _CodeFrame = __webpack_require__(/*! ./CodeFrame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9Db2RlRnJhbWUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs2Q0FBU0E7OztlQUFBQSxXQUFBQSxTQUFTOzs7dUNBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0NvZGVGcmFtZS9pbmRleC50c3g/ZWYzNiJdLCJuYW1lcyI6WyJDb2RlRnJhbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js ***!
  \************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-codeframe] {\\n    overflow: auto;\\n    border-radius: var(--size-gap-half);\\n    background-color: var(--color-ansi-bg);\\n    color: var(--color-ansi-fg);\\n  }\\n  [data-nextjs-codeframe]::selection,\\n  [data-nextjs-codeframe] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n  [data-nextjs-codeframe] * {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-codeframe] > * {\\n    margin: 0;\\n    padding: calc(var(--size-gap) + var(--size-gap-half))\\n      calc(var(--size-gap-double) + var(--size-gap-half));\\n  }\\n  [data-nextjs-codeframe] > div {\\n    display: inline-block;\\n    width: auto;\\n    min-width: 100%;\\n    border-bottom: 1px solid var(--color-ansi-bright-black);\\n  }\\n  [data-nextjs-codeframe] > div > p {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    cursor: pointer;\\n    margin: 0;\\n  }\\n  [data-nextjs-codeframe] > div > p:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-nextjs-codeframe] div > p > svg {\\n    width: auto;\\n    height: 1em;\\n    margin-left: 8px;\\n  }\\n  [data-nextjs-codeframe] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9Db2RlRnJhbWUvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBbURTQTs7O2VBQUFBOzs7OzBDQW5EbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvQ29kZUZyYW1lL3N0eWxlcy50c3g/ZDdjZCJdLCJuYW1lcyI6WyJzdHlsZXMiLCJjc3MiLCJfdGVtcGxhdGVPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/CodeFrame/styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js ***!
  \*********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Dialog\", ({\n    enumerable: true,\n    get: function() {\n        return Dialog;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _useonclickoutside = __webpack_require__(/*! ../../hooks/use-on-click-outside */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.js\");\nconst Dialog = function Dialog(param) {\n    _s();\n    let { children, type, onClose, ...props } = param;\n    const [dialog, setDialog] = _react.useState(null);\n    const [role, setRole] = _react.useState(typeof document !== \"undefined\" && document.hasFocus() ? \"dialog\" : undefined);\n    const onDialog = _react.useCallback((node)=>{\n        setDialog(node);\n    }, []);\n    (0, _useonclickoutside.useOnClickOutside)(dialog, (e)=>{\n        e.preventDefault();\n        return onClose == null ? void 0 : onClose();\n    });\n    // Make HTMLElements with `role=link` accessible to be triggered by the\n    // keyboard, i.e. [Enter].\n    _react.useEffect(()=>{\n        if (dialog == null) {\n            return;\n        }\n        const root = dialog.getRootNode();\n        // Always true, but we do this for TypeScript:\n        if (!(root instanceof ShadowRoot)) {\n            return;\n        }\n        const shadowRoot = root;\n        function handler(e) {\n            const el = shadowRoot.activeElement;\n            if (e.key === \"Enter\" && el instanceof HTMLElement && el.getAttribute(\"role\") === \"link\") {\n                e.preventDefault();\n                e.stopPropagation();\n                el.click();\n            }\n        }\n        function handleFocus() {\n            // safari will force itself as the active application when a background page triggers any sort of autofocus\n            // this is a workaround to only set the dialog role if the document has focus\n            setRole(document.hasFocus() ? \"dialog\" : undefined);\n        }\n        shadowRoot.addEventListener(\"keydown\", handler);\n        window.addEventListener(\"focus\", handleFocus);\n        window.addEventListener(\"blur\", handleFocus);\n        return ()=>{\n            shadowRoot.removeEventListener(\"keydown\", handler);\n            window.removeEventListener(\"focus\", handleFocus);\n            window.removeEventListener(\"blur\", handleFocus);\n        };\n    }, [\n        dialog\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        ref: onDialog,\n        \"data-nextjs-dialog\": true,\n        tabIndex: -1,\n        role: role,\n        \"aria-labelledby\": props[\"aria-labelledby\"],\n        \"aria-describedby\": props[\"aria-describedby\"],\n        \"aria-modal\": \"true\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-banner\": true,\n                className: \"banner-\" + type\n            }),\n            children\n        ]\n    });\n};\n_s(Dialog, \"2HYcMztUKT19xxjUDXr42PZl9rE=\");\n_c = Dialog;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=Dialog.js.map\nvar _c;\n$RefreshReg$(_c, \"Dialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js ***!
  \*************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogBody\", ({\n    enumerable: true,\n    get: function() {\n        return DialogBody;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogBody = function DialogBody(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-body\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogBody;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=DialogBody.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogBody\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvRGlhbG9nQm9keS5qcyIsIm1hcHBpbmdzIjoiOzs7OzhDQWtCU0E7OztlQUFBQTs7Ozs7NkVBbEJjO0FBT3ZCLE1BQU1BLGFBQXdDLFNBQVNBLFdBQVdDLEtBR2pFO0lBSGlFLE1BQ2hFQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVixHQUhpRUY7SUFJaEUsT0FDRSxXQURGLEdBQ0UsSUFBQUcsWUFBQUMsR0FBQSxFQUFDQyxPQUFBQTtRQUFJQywyQkFBdUI7UUFBQ0osV0FBV0E7a0JBQ3JDRDs7QUFHUDtLQVRNRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvRGlhbG9nL0RpYWxvZ0JvZHkudHN4P2I1OWMiXSwibmFtZXMiOlsiRGlhbG9nQm9keSIsInBhcmFtIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJfanN4cnVudGltZSIsImpzeCIsImRpdiIsImRhdGEtbmV4dGpzLWRpYWxvZy1ib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js ***!
  \****************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogContent\", ({\n    enumerable: true,\n    get: function() {\n        return DialogContent;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogContent = function DialogContent(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-content\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogContent;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=DialogContent.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvRGlhbG9nQ29udGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O2lEQWtCU0E7OztlQUFBQTs7Ozs7NkVBbEJjO0FBT3ZCLE1BQU1BLGdCQUE4QyxTQUFTQSxjQUFjQyxLQUcxRTtJQUgwRSxNQUN6RUMsUUFBUSxFQUNSQyxTQUFTLEVBQ1YsR0FIMEVGO0lBSXpFLE9BQ0UsV0FERixHQUNFLElBQUFHLFlBQUFDLEdBQUEsRUFBQ0MsT0FBQUE7UUFBSUMsOEJBQTBCO1FBQUNKLFdBQVdBO2tCQUN4Q0Q7O0FBR1A7S0FUTUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0RpYWxvZy9EaWFsb2dDb250ZW50LnRzeD9jNjdmIl0sIm5hbWVzIjpbIkRpYWxvZ0NvbnRlbnQiLCJwYXJhbSIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctY29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js ***!
  \***************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogHeader\", ({\n    enumerable: true,\n    get: function() {\n        return DialogHeader;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogHeader = function DialogHeader(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-header\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogHeader;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=DialogHeader.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvRGlhbG9nSGVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBa0JTQTs7O2VBQUFBOzs7Ozs2RUFsQmM7QUFPdkIsTUFBTUEsZUFBNEMsU0FBU0EsYUFBYUMsS0FHdkU7SUFIdUUsTUFDdEVDLFFBQVEsRUFDUkMsU0FBUyxFQUNWLEdBSHVFRjtJQUl0RSxPQUNFLFdBREYsR0FDRSxJQUFBRyxZQUFBQyxHQUFBLEVBQUNDLE9BQUFBO1FBQUlDLDZCQUF5QjtRQUFDSixXQUFXQTtrQkFDdkNEOztBQUdQO0tBVE1GIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvRGlhbG9nSGVhZGVyLnRzeD9jZmQ5Il0sIm5hbWVzIjpbIkRpYWxvZ0hlYWRlciIsInBhcmFtIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJfanN4cnVudGltZSIsImpzeCIsImRpdiIsImRhdGEtbmV4dGpzLWRpYWxvZy1oZWFkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js ***!
  \********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Dialog: function() {\n        return _Dialog.Dialog;\n    },\n    DialogBody: function() {\n        return _DialogBody.DialogBody;\n    },\n    DialogContent: function() {\n        return _DialogContent.DialogContent;\n    },\n    DialogHeader: function() {\n        return _DialogHeader.DialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _Dialog = __webpack_require__(/*! ./Dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/Dialog.js\");\nconst _DialogBody = __webpack_require__(/*! ./DialogBody */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.js\");\nconst _DialogContent = __webpack_require__(/*! ./DialogContent */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.js\");\nconst _DialogHeader = __webpack_require__(/*! ./DialogHeader */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLFFBQU07ZUFBTkEsUUFBQUEsTUFBTTs7SUFDTkMsWUFBVTtlQUFWQSxZQUFBQSxVQUFVOztJQUNWQyxlQUFhO2VBQWJBLGVBQUFBLGFBQWE7O0lBQ2JDLGNBQVk7ZUFBWkEsY0FBQUEsWUFBWTs7SUFDWkMsUUFBTTtlQUFOQSxRQUFBQSxNQUFNOzs7b0NBSlE7d0NBQ0k7MkNBQ0c7MENBQ0Q7b0NBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9jbGllbnQvY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9pbnRlcm5hbC9jb21wb25lbnRzL0RpYWxvZy9pbmRleC50cz9jMDg5Il0sIm5hbWVzIjpbIkRpYWxvZyIsIkRpYWxvZ0JvZHkiLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwic3R5bGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js ***!
  \*********************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _nooptemplate = __webpack_require__(/*! ../../helpers/noop-template */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/helpers/noop-template.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  [data-nextjs-dialog] {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    margin-right: auto;\\n    margin-left: auto;\\n    outline: none;\\n    background: var(--color-background);\\n    border-radius: var(--size-gap);\\n    box-shadow: 0 var(--size-gap-half) var(--size-gap-double)\\n      rgba(0, 0, 0, 0.25);\\n    max-height: calc(100% - 56px);\\n    overflow-y: hidden;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      max-height: calc(100% - 15px);\\n    }\\n  }\\n\\n  @media (min-width: 576px) {\\n    [data-nextjs-dialog] {\\n      max-width: 540px;\\n      box-shadow: 0 var(--size-gap) var(--size-gap-quad) rgba(0, 0, 0, 0.25);\\n    }\\n  }\\n\\n  @media (min-width: 768px) {\\n    [data-nextjs-dialog] {\\n      max-width: 720px;\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    [data-nextjs-dialog] {\\n      max-width: 960px;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-banner] {\\n    position: relative;\\n  }\\n  [data-nextjs-dialog-banner].banner-warning {\\n    border-color: var(--color-ansi-yellow);\\n  }\\n  [data-nextjs-dialog-banner].banner-error {\\n    border-color: var(--color-ansi-red);\\n  }\\n\\n  [data-nextjs-dialog-banner]::after {\\n    z-index: 2;\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    width: 100%;\\n    /* banner width: */\\n    border-top-width: var(--size-gap-half);\\n    border-bottom-width: 0;\\n    border-top-style: solid;\\n    border-bottom-style: solid;\\n    border-top-color: inherit;\\n    border-bottom-color: transparent;\\n  }\\n\\n  [data-nextjs-dialog-content] {\\n    overflow-y: auto;\\n    border: none;\\n    margin: 0;\\n    /* calc(padding + banner width offset) */\\n    padding: calc(var(--size-gap-double) + var(--size-gap-half))\\n      var(--size-gap-double);\\n    height: 100%;\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\\n    flex-shrink: 0;\\n    margin-bottom: var(--size-gap-double);\\n  }\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\\n    position: relative;\\n    flex: 1 1 auto;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst styles = (0, _nooptemplate.noop)(_templateObject());\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvaW50ZXJuYWwvY29tcG9uZW50cy9EaWFsb2cvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBMEZTQTs7O2VBQUFBOzs7OzBDQTFGbUI7Ozs7Ozs7Ozs7QUFFNUIsTUFBTUEsU0FBQUEsQ0FBQUEsR0FBU0MsY0FBQUEsSUFBRyxFQUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L2ludGVybmFsL2NvbXBvbmVudHMvRGlhbG9nL3N0eWxlcy50cz8wNjk1Il0sIm5hbWVzIjpbInN0eWxlcyIsImNzcyIsIl90ZW1wbGF0ZU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/components/Dialog/styles.js\n"));

/***/ })

}]);